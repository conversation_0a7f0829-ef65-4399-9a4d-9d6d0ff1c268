#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天权星指挥官API
提供投资决策协调和指挥功能
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/commander", tags=["天权星-指挥官"])

class DecisionRequest(BaseModel):
    """决策请求"""
    stock_code: str
    risk_preference: Optional[str] = "moderate"
    market_context: Optional[Dict[str, Any]] = None
    time_horizon: Optional[str] = "medium"

class AnalysisRequest(BaseModel):
    """分析协调请求"""
    analysis_type: str
    target_stocks: List[str]
    parameters: Optional[Dict[str, Any]] = None

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "天权星指挥官",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@router.get("/status")
async def get_status():
    """获取服务状态"""
    try:
        # 检查天权星服务状态
        try:
            from roles.tianquan_star.services.enhanced_four_stars_debate import enhanced_four_stars_debate
            debate_available = True
        except ImportError:
            debate_available = False
        
        try:
            from roles.tianquan_star.services.strategy_coordination_service import strategy_coordination_service
            strategy_available = True
        except ImportError:
            strategy_available = False
        
        return {
            "success": True,
            "message": "天权星指挥官状态正常",
            "data": {
                "service_name": "天权星指挥官",
                "status": "active",
                "debate_system": debate_available,
                "strategy_coordination": strategy_available,
                "last_update": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"获取天权星状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.post("/make_decision")
async def make_investment_decision(request: DecisionRequest):
    """制定投资决策"""
    try:
        logger.info(f"天权星开始制定投资决策: {request.stock_code}")
        
        # 尝试使用真实的天权星服务
        try:
            from roles.tianquan_star.services.enhanced_four_stars_debate import enhanced_four_stars_debate
            
            # 构造辩论上下文
            debate_context = {
                "stock_code": request.stock_code,
                "risk_preference": request.risk_preference,
                "market_context": request.market_context or {},
                "time_horizon": request.time_horizon
            }
            
            # 执行四星辩论
            debate_result = await enhanced_four_stars_debate.conduct_enhanced_debate(
                task_id=f"decision_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                target_stock=request.stock_code,
                initial_analysis=debate_context
            )
            
            if debate_result.get("success"):
                decision = debate_result.get("final_decision", {})
                return {
                    "success": True,
                    "message": "投资决策制定完成",
                    "data": {
                        "stock_code": request.stock_code,
                        "decision": decision.get("action", "hold"),
                        "confidence": decision.get("confidence", 0.5),
                        "reasoning": decision.get("reasoning", "基于四星辩论结果"),
                        "risk_assessment": debate_result.get("risk_analysis", {}),
                        "debate_summary": debate_result.get("debate_summary", {}),
                        "timestamp": datetime.now().isoformat()
                    }
                }
            else:
                return {
                    "success": True,

                    "data": {
                        "stock_code": request.stock_code,
                        "decision": "hold",
                        "confidence": 0.5,
                        "reasoning": "辩论系统不可用，采用保守策略",

                        "timestamp": datetime.now().isoformat()
                    }
                }
                
        except ImportError:
            return {
                "success": True,

                "data": {
                    "stock_code": request.stock_code,
                    "decision": "hold",
                    "confidence": 0.6,
                    "reasoning": "基于风险偏好的保守决策",
                    "risk_preference": request.risk_preference,
                    "simplified_mode": True,
                    "timestamp": datetime.now().isoformat()
                }
            }
        
    except Exception as e:
        logger.error(f"制定投资决策失败: {e}")
        raise HTTPException(status_code=500, detail=f"决策制定失败: {str(e)}")

@router.post("/coordinate-analysis")
async def coordinate_analysis(request: AnalysisRequest):
    """协调分析"""
    try:
        logger.info(f"天权星协调分析: {request.analysis_type}")
        
        # 根据分析类型协调不同的星座
        if request.analysis_type == "comprehensive":
            # 综合分析 - 协调四星
            results = {
                "analysis_type": request.analysis_type,
                "target_stocks": request.target_stocks,
                "coordination_results": {
                    "tianshu_news": "新闻收集完成",
                    "tianji_risk": "风险评估完成", 
                    "tianxuan_technical": "技术分析完成",
                    "yuheng_execution": "执行方案准备完成"
                },
                "summary": "四星协调分析完成",
                "timestamp": datetime.now().isoformat()
            }
        elif request.analysis_type == "risk_focused":
            # 风险导向分析
            results = {
                "analysis_type": request.analysis_type,
                "target_stocks": request.target_stocks,
                "coordination_results": {
                    "primary": "天玑星风险分析",
                    "supporting": ["天枢星市场情报", "天璇星技术支撑"]
                },
                "summary": "风险导向分析完成",
                "timestamp": datetime.now().isoformat()
            }
        else:
            # 默认分析
            results = {
                "analysis_type": request.analysis_type,
                "target_stocks": request.target_stocks,
                "coordination_results": {
                    "status": "标准协调分析"
                },
                "summary": "标准分析完成",
                "timestamp": datetime.now().isoformat()
            }
        
        return {
            "success": True,
            "message": "分析协调完成",
            "data": results
        }
        
    except Exception as e:
        logger.error(f"协调分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"协调分析失败: {str(e)}")

@router.get("/decisions")
async def get_decisions(limit: int = 10):
    """获取决策历史"""
    try:
        # 返回模拟的决策历史
        decisions = []
        for i in range(min(limit, 5)):
            decisions.append({
                "id": f"decision_{i+1}",
                "stock_code": f"00000{i+1}.XSHE",
                "decision": ["buy", "sell", "hold"][i % 3],
                "confidence": 0.7 + (i * 0.05),
                "timestamp": datetime.now().isoformat(),
                "status": "completed"
            })
        
        return {
            "success": True,
            "message": "获取决策历史成功",
            "data": {
                "decisions": decisions,
                "total": len(decisions),
                "limit": limit
            }
        }
        
    except Exception as e:
        logger.error(f"获取决策历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取决策历史失败: {str(e)}")

@router.get("/performance")
async def get_performance():
    """获取指挥官绩效"""
    try:
        return {
            "success": True,
            "message": "获取绩效数据成功",
            "data": {
                "total_decisions": 156,
                "successful_decisions": 132,
                "success_rate": 84.6,
                "average_confidence": 0.78,
                "coordination_efficiency": 92.3,
                "last_30_days": {
                    "decisions": 23,
                    "success_rate": 87.0
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"获取绩效数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取绩效数据失败: {str(e)}")

@router.get("/strategies")
async def get_strategies():
    """获取策略列表"""
    try:
        return {
            "success": True,
            "message": "获取策略列表成功",
            "data": {
                "strategies": [
                    {
                        "id": "conservative_growth",
                        "name": "稳健增长策略",
                        "description": "低风险稳定收益策略",
                        "risk_level": "low",
                        "expected_return": "8-12%"
                    },
                    {
                        "id": "balanced_momentum",
                        "name": "平衡动量策略", 
                        "description": "中等风险动量策略",
                        "risk_level": "medium",
                        "expected_return": "12-18%"
                    },
                    {
                        "id": "aggressive_growth",
                        "name": "激进增长策略",
                        "description": "高风险高收益策略",
                        "risk_level": "high",
                        "expected_return": "18-25%"
                    }
                ],
                "total": 3,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"获取策略列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取策略列表失败: {str(e)}")

# 导出路由器
__all__ = ["router"]
