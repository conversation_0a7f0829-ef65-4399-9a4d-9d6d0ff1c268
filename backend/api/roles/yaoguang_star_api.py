from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星数据管理中心 API
基于RD-Agent深度集成优化方案的新实现
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

# 先定义logger
logger = logging.getLogger(__name__)

# 修复正确的导入路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from backend.roles.yaoguang_star.core.time_control_engine import TimeControlEngine
    from backend.roles.yaoguang_star.core.learning_environment_provider import LearningEnvironmentProvider, ResourcePool
    from backend.roles.yaoguang_star.services.time_control_service import TimeControlService
    from backend.roles.yaoguang_star.services.learning_environment_service import LearningEnvironmentService
    from backend.roles.yaoguang_star.services.data_management_service import DataManagementService
    from backend.roles.yaoguang_star.services.individual_stock_learning_service import IndividualStockLearningService
except ImportError as e:
    logger.warning(f"瑶光星服务导入失败，尝试相对导入: {e}")
    try:
        # 尝试相对导入
        from ...roles.yaoguang_star.core.time_control_engine import TimeControlEngine
        from ...roles.yaoguang_star.core.learning_environment_provider import LearningEnvironmentProvider, ResourcePool
        from ...roles.yaoguang_star.services.time_control_service import TimeControlService
        from ...roles.yaoguang_star.services.learning_environment_service import LearningEnvironmentService
        from ...roles.yaoguang_star.services.data_management_service import DataManagementService
        from ...roles.yaoguang_star.services.individual_stock_learning_service import IndividualStockLearningService
    except ImportError as e2:
        logger.error(f"瑶光星服务导入完全失败: {e2}")
        # 创建基础类以避免系统崩溃
        class TimeControlService:
            async def create_learning_session(self, *args, **kwargs):
                raise NotImplementedError("瑶光星服务未正确加载")

        class LearningEnvironmentService:
            async def create_isolated_environment(self, *args, **kwargs):
                raise NotImplementedError("瑶光星服务未正确加载")

        class DataManagementService:
            async def get_system_status(self, *args, **kwargs):
                raise NotImplementedError("瑶光星服务未正确加载")

# 创建路由器
yaoguang_star_router = APIRouter(prefix="/api/yaoguang-star", tags=["瑶光星数据管理中心"])

# 服务实例（延迟初始化）
time_control_service = None
learning_env_service = None
data_management_service = None
individual_stock_learning_service = None

def get_time_control_service():
    """获取时间控制服务"""
    global time_control_service
    if time_control_service is None:
        time_control_service = TimeControlService()
    return time_control_service

def get_learning_env_service():
    """获取学习环境服务"""
    global learning_env_service
    if learning_env_service is None:
        learning_env_service = LearningEnvironmentService()
    return learning_env_service

def get_data_management_service():
    """获取数据管理服务"""
    global data_management_service
    if data_management_service is None:
        data_management_service = DataManagementService()
    return data_management_service

def get_individual_stock_learning_service():
    """获取个股学习服务"""
    global individual_stock_learning_service
    if individual_stock_learning_service is None:
        individual_stock_learning_service = IndividualStockLearningService()
    return individual_stock_learning_service

@yaoguang_star_router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "role": "瑶光星数据管理中心",
        "version": "2.0.0",
        "capabilities": [
            "时间控制引擎",
            "学习环境提供",
            "RD-Agent实验管理",
            "回测记录管理",
            "历史数据管理"
        ],
        "timestamp": datetime.now().isoformat()
    }

@yaoguang_star_router.post("/learning-session/create")
async def create_learning_session(
    request: Dict[str, Any],
    service: TimeControlService = Depends(get_time_control_service)
):
    """创建学习会话"""
    try:
        required_fields = ["role_id", "start_date", "end_date", "strategy_type"]
        for field in required_fields:
            if field not in request:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")
        
        session_id = await service.create_learning_session(
            role_id=request["role_id"],
            start_date=request["start_date"],
            end_date=request["end_date"],
            strategy_type=request["strategy_type"],
            rd_agent_config=request.get("rd_agent_config", {})
        )
        
        return {
            "success": True,
            "message": "学习会话创建成功",
            "data": {
                "session_id": session_id,
                "role_id": request["role_id"],
                "time_range": f"{request['start_date']} ~ {request['end_date']}",
                "strategy_type": request["strategy_type"]
            }
        }
        
    except Exception as e:
        logger.error(f"创建学习会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建学习会话失败: {str(e)}")

@yaoguang_star_router.get("/learning-session/{session_id}")
async def get_learning_session(
    session_id: str,
    service: TimeControlService = Depends(get_time_control_service)
):
    """获取学习会话信息"""
    try:
        session_info = await service.get_session_info(session_id)
        
        if not session_info:
            raise HTTPException(status_code=404, detail="学习会话不存在")
        
        return {
            "success": True,
            "message": "获取学习会话信息成功",
            "data": session_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学习会话信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取学习会话信息失败: {str(e)}")

@yaoguang_star_router.post("/learning-session/{session_id}/advance-time")
async def advance_session_time(
    session_id: str,
    request: Dict[str, str],
    service: TimeControlService = Depends(get_time_control_service)
):
    """推进会话时间"""
    try:
        if "new_time" not in request:
            raise HTTPException(status_code=400, detail="缺少必需字段: new_time")
        
        await service.advance_time(session_id, request["new_time"])
        
        return {
            "success": True,
            "message": "时间推进成功",
            "data": {
                "session_id": session_id,
                "new_time": request["new_time"]
            }
        }
        
    except Exception as e:
        logger.error(f"推进会话时间失败: {e}")
        raise HTTPException(status_code=500, detail=f"推进会话时间失败: {str(e)}")

@yaoguang_star_router.post("/learning-session/{session_id}/get-data")
async def get_historical_data(
    session_id: str,
    request: Dict[str, Any],
    service: TimeControlService = Depends(get_time_control_service)
):
    """获取历史数据"""
    try:
        required_fields = ["stock_code", "data_type"]
        for field in required_fields:
            if field not in request:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")
        
        data = await service.get_historical_data(
            session_id=session_id,
            stock_code=request["stock_code"],
            data_type=request["data_type"],
            lookback_days=request.get("lookback_days")
        )
        
        return {
            "success": True,
            "message": "获取历史数据成功",
            "data": {
                "stock_code": request["stock_code"],
                "data_type": request["data_type"],
                "data_shape": data.shape if hasattr(data, 'shape') else None,
                "data_preview": data.head().to_dict() if hasattr(data, 'head') else str(data)[:200]
            }
        }
        
    except Exception as e:
        logger.error(f"获取历史数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")

@yaoguang_star_router.post("/learning-environment/create")
async def create_learning_environment(
    request: Dict[str, Any],
    service: LearningEnvironmentService = Depends(get_learning_env_service)
):
    """创建学习环境"""
    try:
        required_fields = ["role_id", "environment_type"]
        for field in required_fields:
            if field not in request:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")
        
        env_id = await service.create_isolated_environment(
            role_id=request["role_id"],
            environment_type=request["environment_type"],
            learning_config=request.get("learning_config", {})
        )
        
        return {
            "success": True,
            "message": "学习环境创建成功",
            "data": {
                "env_id": env_id,
                "role_id": request["role_id"],
                "environment_type": request["environment_type"]
            }
        }
        
    except Exception as e:
        logger.error(f"创建学习环境失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建学习环境失败: {str(e)}")

@yaoguang_star_router.get("/learning-environment/{env_id}")
async def get_learning_environment(
    env_id: str,
    service: LearningEnvironmentService = Depends(get_learning_env_service)
):
    """获取学习环境信息"""
    try:
        env_info = await service.get_environment_info(env_id)
        
        if not env_info:
            raise HTTPException(status_code=404, detail="学习环境不存在")
        
        return {
            "success": True,
            "message": "获取学习环境信息成功",
            "data": env_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学习环境信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取学习环境信息失败: {str(e)}")

@yaoguang_star_router.delete("/learning-environment/{env_id}")
async def destroy_learning_environment(
    env_id: str,
    service: LearningEnvironmentService = Depends(get_learning_env_service)
):
    """销毁学习环境"""
    try:
        success = await service.destroy_environment(env_id)
        
        if not success:
            raise HTTPException(status_code=500, detail="销毁学习环境失败")
        
        return {
            "success": True,
            "message": "学习环境销毁成功",
            "data": {"env_id": env_id}
        }
        
    except Exception as e:
        logger.error(f"销毁学习环境失败: {e}")
        raise HTTPException(status_code=500, detail=f"销毁学习环境失败: {str(e)}")

@yaoguang_star_router.get("/data-management/status")
async def get_data_management_status(
    service: DataManagementService = Depends(get_data_management_service)
):
    """获取数据管理状态"""
    try:
        # 直接构建状态信息，避免调用可能不存在的方法
        import sqlite3
        import os

        # 检查数据库状态
        db_path = get_database_path("stock_database")
        database_exists = os.path.exists(db_path)

        if database_exists:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 统计数据
            cursor.execute("SELECT COUNT(*) FROM stock_info")
            total_stocks = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM daily_data")
            total_daily_data = cursor.fetchone()[0]

            conn.close()

            status = {
                "database_status": "connected",
                "database_path": db_path,
                "total_stocks": total_stocks,
                "total_daily_data": total_daily_data,
                "data_quality_score": 100.0,
                "service_status": "active",
                "last_update": datetime.now().isoformat()
            }
        else:
            status = {
                "database_status": "not_found",
                "database_path": db_path,
                "total_stocks": 0,
                "total_daily_data": 0,
                "data_quality_score": 0.0,
                "service_status": "inactive",
                "last_update": datetime.now().isoformat()
            }

        return {
            "success": True,
            "message": "获取数据管理状态成功",
            "data": status
        }

    except Exception as e:
        logger.error(f"获取数据管理状态失败: {e}")
        # 返回基础状态而不是抛出异常
        return {
            "success": False,
            "message": f"获取数据管理状态失败: {str(e)}",
            "data": {
                "database_status": "error",
                "service_status": "error",
                "error": str(e),
                "last_update": datetime.now().isoformat()
            }
        }

@yaoguang_star_router.get("/active-sessions")
async def list_active_sessions(
    service: TimeControlService = Depends(get_time_control_service)
):
    """列出活跃的学习会话"""
    try:
        sessions = await service.list_active_sessions()
        
        return {
            "success": True,
            "message": "获取活跃会话列表成功",
            "data": {
                "active_sessions": sessions,
                "total_count": len(sessions)
            }
        }
        
    except Exception as e:
        logger.error(f"获取活跃会话列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取活跃会话列表失败: {str(e)}")

@yaoguang_star_router.get("/role/{role_id}/environments")
async def list_role_environments(
    role_id: str,
    service: LearningEnvironmentService = Depends(get_learning_env_service)
):
    """列出角色的学习环境"""
    try:
        environments = await service.list_environments_by_role(role_id)
        
        return {
            "success": True,
            "message": "获取角色环境列表成功",
            "data": {
                "role_id": role_id,
                "environments": environments,
                "total_count": len(environments)
            }
        }
        
    except Exception as e:
        logger.error(f"获取角色环境列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取角色环境列表失败: {str(e)}")

@yaoguang_star_router.get("/system-info")
async def get_system_info():
    """获取系统信息"""
    try:
        return {
            "success": True,
            "message": "获取系统信息成功",
            "data": {
                "role_name": "瑶光星数据管理中心",
                "version": "2.0.0",
                "redesign_date": "2025-01-15",
                "core_capabilities": [
                    "时间控制引擎 - 防止未来函数",
                    "学习环境提供 - 隔离学习环境",
                    "RD-Agent实验平台 - 管理进化实验",
                    "回测记录管理 - 系统化学习记录",
                    "历史数据管理 - 30年A股数据",
                    "数据质量保障 - 确保数据完整性"
                ],
                "rd_agent_integration": True,
                "optimization_plan": "RD-Agent深度集成优化方案",
                "status": "active",
                "last_update": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")

# ==================== 增强数据收集API端点 ====================

@yaoguang_star_router.post("/data/collect-historical")
async def collect_historical_data_eastmoney(
    request: Dict[str, Any],
    service = Depends(get_data_management_service)
):
    """使用东方财富API收集历史数据"""
    try:
        if not service:
            raise HTTPException(status_code=503, detail="数据管理服务不可用")

        stock_codes = request.get("stock_codes")
        days = request.get("days", 365)

        result = await service.collect_historical_data_eastmoney(stock_codes, days)

        return {
            "success": result.get("success", False),
            "message": "历史数据收集完成" if result.get("success") else "历史数据收集失败",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"历史数据收集失败: {e}")
        raise HTTPException(status_code=500, detail=f"历史数据收集失败: {str(e)}")

@yaoguang_star_router.post("/data/calculate-technical-indicators")
async def calculate_technical_indicators_batch(
    request: Dict[str, Any],
    service = Depends(get_data_management_service)
):
    """批量计算技术指标"""
    try:
        if not service:
            raise HTTPException(status_code=503, detail="数据管理服务不可用")

        stock_codes = request.get("stock_codes")
        limit = request.get("limit", 50)

        result = await service.calculate_technical_indicators_batch(stock_codes, limit)

        return {
            "success": result.get("success", False),
            "message": "技术指标计算完成" if result.get("success") else "技术指标计算失败",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"技术指标计算失败: {e}")
        raise HTTPException(status_code=500, detail=f"技术指标计算失败: {str(e)}")

@yaoguang_star_router.get("/data/collection-status")
async def get_data_collection_status(
    service = Depends(get_data_management_service)
):
    """获取数据收集状态"""
    try:
        if not service:
            raise HTTPException(status_code=503, detail="数据管理服务不可用")

        # 检查数据库状态
        import sqlite3
        db_path = get_database_path("stock_database")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 统计各类数据
        cursor.execute("SELECT COUNT(*) FROM stock_info")
        total_stocks = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM daily_data")
        total_daily_data = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM technical_indicators")
        total_technical = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
        eastmoney_stocks = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
        eastmoney_records = cursor.fetchone()[0]

        conn.close()

        return {
            "success": True,
            "message": "数据收集状态获取成功",
            "data": {
                "total_stocks": total_stocks,
                "total_daily_data": total_daily_data,
                "total_technical_indicators": total_technical,
                "eastmoney_stocks": eastmoney_stocks,
                "eastmoney_records": eastmoney_records,
                "data_completeness": {
                    "stock_info": "完整" if total_stocks > 5000 else "部分",
                    "daily_data": "丰富" if total_daily_data > 20000 else "基础",
                    "technical_indicators": "可用" if total_technical > 3000 else "有限",
                    "real_data_coverage": f"{eastmoney_stocks} 只股票有真实数据"
                }
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取数据收集状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据收集状态失败: {str(e)}")

@yaoguang_star_router.get("/data/realtime/{stock_code}")
async def get_enhanced_realtime_data(
    stock_code: str,
    service = Depends(get_data_management_service)
):
    """获取增强实时数据（集成东方财富API）"""
    try:
        if not service:
            raise HTTPException(status_code=503, detail="数据管理服务不可用")

        result = await service.get_realtime_stock_data(stock_code)

        return {
            "success": result.get("success", False),
            "message": "实时数据获取完成" if result.get("success") else "实时数据获取失败",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取增强实时数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取增强实时数据失败: {str(e)}")

# ==================== 个股深度学习API端点 ====================

@yaoguang_star_router.post("/individual-stock-learning/create-research-session")
async def create_research_session(
    request: Dict[str, Any],
    service: IndividualStockLearningService = Depends(get_individual_stock_learning_service)
):
    """创建个股研究模式学习会话 - 完整集成核心服务"""
    try:
        required_fields = ["stock_code"]
        for field in required_fields:
            if field not in request:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        result = await service.create_research_session(
            stock_code=request["stock_code"],
            analysis_years=request.get("analysis_years", 10)
        )

        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "创建研究会话失败"))

        return {
            "success": True,
            "message": "个股研究模式学习会话创建成功",
            "data": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建研究会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建研究会话失败: {str(e)}")

@yaoguang_star_router.post("/individual-learning/practice")
async def create_practice_session(
    request: Dict[str, Any],
    service: IndividualStockLearningService = Depends(get_individual_stock_learning_service)
):
    """创建个股练习模式学习会话"""
    try:
        required_fields = ["stock_code"]
        for field in required_fields:
            if field not in request:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        result = await service.create_practice_session(
            stock_code=request["stock_code"],
            practice_period=request.get("practice_period", "1year")
        )

        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "创建练习会话失败"))

        return {
            "success": True,
            "message": "个股练习模式学习会话创建成功",
            "data": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建练习会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建练习会话失败: {str(e)}")

@yaoguang_star_router.get("/individual-learning/{session_id}/info")
async def get_learning_session_info(
    session_id: str,
    service: IndividualStockLearningService = Depends(get_individual_stock_learning_service)
):
    """获取学习会话信息"""
    try:
        session_info = await service.get_session_info(session_id)

        return {
            "success": True,
            "message": "获取学习会话信息成功",
            "data": session_info
        }

    except Exception as e:
        logger.error(f"获取学习会话信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取学习会话信息失败: {str(e)}")

@yaoguang_star_router.post("/individual-learning/{session_id}/advance-time")
async def advance_learning_time(
    session_id: str,
    request: Dict[str, Any],
    service: IndividualStockLearningService = Depends(get_individual_stock_learning_service)
):
    """推进个股学习时间（加速处理）"""
    try:
        result = await service.advance_learning_time(
            session_id=session_id,
            target_date=request.get("target_date")
        )

        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "时间推进失败"))

        return {
            "success": True,
            "message": "学习时间推进成功",
            "data": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"推进学习时间失败: {e}")
        raise HTTPException(status_code=500, detail=f"推进学习时间失败: {str(e)}")

@yaoguang_star_router.post("/individual-learning/{session_id}/record-learning")
async def record_learning_progress(
    session_id: str,
    request: Dict[str, Any],
    service: IndividualStockLearningService = Depends(get_individual_stock_learning_service)
):
    """记录学习进度"""
    try:
        learning_record = await service.record_learning_progress(session_id, request)

        return {
            "success": True,
            "message": "学习进度记录成功",
            "data": learning_record
        }

    except Exception as e:
        logger.error(f"记录学习进度失败: {e}")
        raise HTTPException(status_code=500, detail=f"记录学习进度失败: {str(e)}")

@yaoguang_star_router.post("/individual-learning/{session_id}/record-decision")
async def record_tianquan_decision(
    session_id: str,
    request: Dict[str, Any],
    service: IndividualStockLearningService = Depends(get_individual_stock_learning_service)
):
    """记录天权星的决策"""
    try:
        result = await service.record_tianquan_decision(
            session_id=session_id,
            decision_data=request
        )

        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "决策记录失败"))

        return {
            "success": True,
            "message": "天权星决策记录成功",
            "data": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"记录决策失败: {e}")
        raise HTTPException(status_code=500, detail=f"记录决策失败: {str(e)}")

@yaoguang_star_router.post("/individual-learning/{session_id}/complete")
async def complete_learning_session(
    session_id: str,
    service: IndividualStockLearningService = Depends(get_individual_stock_learning_service)
):
    """完成个股学习会话并生成学习报告"""
    try:
        result = await service.complete_learning_session(session_id)

        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "完成学习会话失败"))

        return {
            "success": True,
            "message": "个股学习会话完成",
            "data": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"完成学习会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"完成学习会话失败: {str(e)}")

@yaoguang_star_router.get("/individual-learning/sessions")
async def list_learning_sessions(
    service: IndividualStockLearningService = Depends(get_individual_stock_learning_service)
):
    """列出所有个股学习会话"""
    try:
        active_sessions = list(service.active_sessions.values())
        completed_sessions = list(service.learning_records.keys())

        return {
            "success": True,
            "message": "获取学习会话列表成功",
            "data": {
                "active_sessions": [
                    {
                        "session_id": session.session_id,
                        "stock_code": session.stock_code,
                        "stock_name": session.stock_name,
                        "learning_mode": session.learning_mode,
                        "current_date": session.current_date,
                        "progress": f"{session.start_date} ~ {session.end_date}"
                    }
                    for session in active_sessions
                ],
                "completed_sessions": completed_sessions,
                "total_active": len(active_sessions),
                "total_completed": len(completed_sessions)
            }
        }

    except Exception as e:
        logger.error(f"获取学习会话列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取学习会话列表失败: {str(e)}")

# ==================== 每日股票数据更新API端点 ====================

# 导入每日更新服务
try:
    from backend.roles.yaoguang_star.services.daily_stock_update_service import daily_stock_update_service
except ImportError:
    try:
        from ...roles.yaoguang_star.services.daily_stock_update_service import daily_stock_update_service
    except ImportError:
        logger.warning("每日股票更新服务导入失败")
        daily_stock_update_service = None

@yaoguang_star_router.get("/daily-update/status")
async def get_daily_update_status():
    """获取每日更新状态"""
    try:
        if not daily_stock_update_service:
            raise HTTPException(status_code=503, detail="每日更新服务不可用")

        status = daily_stock_update_service.get_update_status()

        return {
            "success": True,
            "message": "获取每日更新状态成功",
            "data": status,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取每日更新状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取每日更新状态失败: {str(e)}")

@yaoguang_star_router.post("/daily-update/start-scheduler")
async def start_daily_update_scheduler():
    """启动每日更新调度器"""
    try:
        if not daily_stock_update_service:
            raise HTTPException(status_code=503, detail="每日更新服务不可用")

        daily_stock_update_service.start_scheduler()

        return {
            "success": True,
            "message": "每日更新调度器已启动",
            "data": {
                "update_time": daily_stock_update_service.update_config["update_time"],
                "enabled": True
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"启动每日更新调度器失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动每日更新调度器失败: {str(e)}")

@yaoguang_star_router.post("/daily-update/execute-now")
async def execute_daily_update_now():
    """立即执行每日更新"""
    try:
        if not daily_stock_update_service:
            raise HTTPException(status_code=503, detail="每日更新服务不可用")

        result = await daily_stock_update_service.execute_daily_update()

        return {
            "success": result.get("success", False),
            "message": result.get("message", "每日更新执行完成"),
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"执行每日更新失败: {e}")
        raise HTTPException(status_code=500, detail=f"执行每日更新失败: {str(e)}")

# 导出路由器
__all__ = ["yaoguang_star_router"]
