#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玉衡星专用API - 交易执行官
完整的交易执行、订单管理、成本控制、流动性管理API
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Any, Optional, List, Dict, Union
from datetime import datetime, date
import logging
import asyncio
from decimal import Decimal

# 导入核心服务 - 修复导入路径
try:
    from roles.yuheng_star.services.trading_execution_service import trading_execution_service
    from roles.yuheng_star.services.order_management_service import order_management_service
    from roles.yuheng_star.services.position_management_service import position_management_service
    from roles.yuheng_star.services.virtual_trading_service import virtual_trading_service
    from roles.yuheng_star.services.advanced_execution_system import advanced_execution_system
    from roles.yuheng_star.services.intelligent_execution_and_profit_service import intelligent_execution_service
    from roles.yuheng_star.services.separated_trading_statistics_service import separated_trading_statistics_service
    from roles.yuheng_star.workflows.workflow_manager import workflow_manager
    SERVICES_AVAILABLE = True
except ImportError as e:
    logging.warning(f"玉衡星服务导入失败: {e}")
    SERVICES_AVAILABLE = False

# 导入四大核心系统 - 修复导入路径
try:
    from core.domain.memory.legendary.interface import legendary_memory_interface
    from core.performance.star_performance_monitor import StarPerformanceMonitor, PerformanceMetricType
    from core.enhanced_seven_stars_hierarchy import EnhancedSevenStarsHierarchy
    from roles.yuheng_star.config.deepseek_config import get_deepseek_config, get_memory_config
    CORE_SYSTEMS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"玉衡星核心系统导入失败: {e}")
    CORE_SYSTEMS_AVAILABLE = False

logger = logging.getLogger(__name__)

# 创建路由器
yuheng_star_router = APIRouter(prefix="/api/yuheng", tags=["玉衡星-交易执行官"])

# ==================== 数据模型 ====================

class ApiResponse(BaseModel):
    """统一API响应格式"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[Any] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    error_code: Optional[str] = None
    error_details: Optional[str] = None

class TradeExecutionRequest(BaseModel):
    """交易执行请求"""
    stock_code: str = Field(..., description="股票代码")
    stock_name: Optional[str] = Field(None, description="股票名称")
    direction: str = Field(..., description="交易方向: buy/sell")
    quantity: int = Field(..., gt=0, description="交易数量")
    strategy_signal: Optional[Dict[str, Any]] = Field(None, description="策略信号")
    execution_constraints: Optional[Dict[str, Any]] = Field(None, description="执行约束")
    urgency_level: Optional[str] = Field("normal", description="紧急程度: low/normal/high/urgent")

class OrderManagementRequest(BaseModel):
    """订单管理请求"""
    order_id: Optional[str] = Field(None, description="订单ID")
    action: str = Field(..., description="操作类型: create/modify/cancel/query")
    order_data: Optional[Dict[str, Any]] = Field(None, description="订单数据")

class PositionQueryRequest(BaseModel):
    """持仓查询请求"""
    stock_code: Optional[str] = Field(None, description="股票代码")
    account_id: Optional[str] = Field(None, description="账户ID")
    include_history: bool = Field(False, description="是否包含历史")

class ExecutionAnalysisRequest(BaseModel):
    """执行分析请求"""
    analysis_type: str = Field(..., description="分析类型: cost/performance/optimization")
    time_range: Optional[Dict[str, str]] = Field(None, description="时间范围")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件")

# ==================== 服务初始化 ====================

class YuhengStarServices:
    """玉衡星服务管理器"""
    
    def __init__(self):
        self.trading_execution_service = None
        self.order_management_service = None
        self.position_management_service = None
        self.virtual_trading_service = None
        self.advanced_execution_system = None
        self.intelligent_execution_service = None
        self.performance_monitor = None
        self.memory_system = None
        self.hierarchy_system = None
        self.deepseek_config = None
        
        self._initialize_services()
        self._initialize_core_systems()
    
    def _initialize_services(self):
        """初始化业务服务"""
        try:
            if SERVICES_AVAILABLE:
                self.trading_execution_service = trading_execution_service
                self.order_management_service = order_management_service
                self.position_management_service = position_management_service
                self.virtual_trading_service = virtual_trading_service
                self.advanced_execution_system = advanced_execution_system
                self.intelligent_execution_service = intelligent_execution_service
                logger.info("[SUCCESS] 玉衡星业务服务初始化完成")
            else:
                pass
        except Exception as e:
            logger.error(f"[ERROR] 玉衡星业务服务初始化失败: {e}")
    
    def _initialize_core_systems(self):
        """初始化四大核心系统"""
        try:
            if CORE_SYSTEMS_AVAILABLE:
                # 1. 传奇记忆系统
                self.memory_system = legendary_memory_interface
                
                # 2. 绩效监控系统
                self.performance_monitor = StarPerformanceMonitor()
                
                # 3. 层级权限系统
                self.hierarchy_system = EnhancedSevenStarsHierarchy()
                
                # 4. DeepSeek配置
                self.deepseek_config = get_deepseek_config()
                
                logger.info("[SUCCESS] 玉衡星四大核心系统初始化完成")
            else:
                pass
        except Exception as e:
            logger.error(f"[ERROR] 玉衡星核心系统初始化失败: {e}")
    
    def is_healthy(self) -> bool:
        """检查服务健康状态"""
        return (self.trading_execution_service is not None and 
                self.order_management_service is not None and
                self.position_management_service is not None)

# 全局服务实例
yuheng_services = YuhengStarServices()

def get_yuheng_services() -> YuhengStarServices:
    """获取玉衡星服务实例"""
    return yuheng_services

# ==================== 健康检查和状态 ====================

@yuheng_star_router.get("/health", response_model=ApiResponse)
async def health_check():
    """玉衡星健康检查"""
    try:
        services = get_yuheng_services()
        
        health_status = {
            "role_name": "玉衡星-交易执行官",
            "version": "2.0.0",
            "status": "healthy" if services.is_healthy() else "degraded",
            "services": {
                "trading_execution": services.trading_execution_service is not None,
                "order_management": services.order_management_service is not None,
                "position_management": services.position_management_service is not None,
                "virtual_trading": services.virtual_trading_service is not None,
                "advanced_execution": services.advanced_execution_system is not None,
                "intelligent_execution": services.intelligent_execution_service is not None
            },
            "core_systems": {
                "memory_system": services.memory_system is not None,
                "performance_monitor": services.performance_monitor is not None,
                "hierarchy_system": services.hierarchy_system is not None,
                "deepseek_config": services.deepseek_config is not None
            },
            "capabilities": [
                "智能交易执行",
                "订单生命周期管理", 
                "执行成本优化",
                "流动性管理",
                "风险控制执行",
                "执行绩效分析",
                "虚拟交易引擎",
                "高级执行算法"
            ],
            "execution_algorithms": ["TWAP", "VWAP", "POV", "IS", "Adaptive"],
            "timestamp": datetime.now().isoformat()
        }
        
        return ApiResponse(
            message="玉衡星健康检查完成",
            data=health_status
        )
        
    except Exception as e:
        logger.error(f"玉衡星健康检查失败: {e}")
        return ApiResponse(
            success=False,
            message=f"健康检查失败: {str(e)}",
            error_code="HEALTH_CHECK_ERROR"
        )

@yuheng_star_router.get("/status", response_model=ApiResponse)
async def get_status():
    """获取玉衡星状态"""
    try:
        services = get_yuheng_services()
        
        # 获取工作流统计
        workflow_stats = workflow_manager.get_workflow_statistics()
        
        # 获取绩效数据
        performance_data = None
        if services.performance_monitor:
            try:
                performance_report = await services.performance_monitor.get_star_performance("玉衡星")
                performance_data = performance_report.__dict__ if performance_report else None
            except Exception as e:
                logger.warning(f"获取绩效数据失败: {e}")
        
        status_data = {
            "role_id": "yuheng_star",
            "role_name": "玉衡星-交易执行官",
            "current_status": "active",
            "authority_level": 5,
            "specialties": ["交易执行", "订单管理", "成本控制", "流动性管理"],
            "workflow_statistics": workflow_stats,
            "performance_data": performance_data,
            "active_workflows": workflow_manager.list_active_workflows(),
            "system_integration": {
                "memory_system_active": services.memory_system is not None,
                "performance_monitoring_active": services.performance_monitor is not None,
                "hierarchy_system_active": services.hierarchy_system is not None,
                "deepseek_integration_active": services.deepseek_config is not None
            },
            "last_update": datetime.now().isoformat()
        }
        
        return ApiResponse(
            message="玉衡星状态获取成功",
            data=status_data
        )
        
    except Exception as e:
        logger.error(f"获取玉衡星状态失败: {e}")
        return ApiResponse(
            success=False,
            message=f"状态获取失败: {str(e)}",
            error_code="STATUS_ERROR"
        )

# ==================== 交易执行API ====================

@yuheng_star_router.post("/execution/execute", response_model=ApiResponse)
async def execute_trade(request: TradeExecutionRequest, background_tasks: BackgroundTasks):
    """智能交易执行"""
    try:
        services = get_yuheng_services()
        
        if not services.trading_execution_service:
            return ApiResponse(
                success=False,
                message="交易执行服务不可用",
                error_code="SERVICE_UNAVAILABLE"
            )
        
        # 记录绩效指标
        if services.performance_monitor:
            background_tasks.add_task(
                services.performance_monitor.record_performance,
                "玉衡星",
                PerformanceMetricType.TASK_COUNT,
                1.0,
                {"task_type": "trade_execution", "stock_code": request.stock_code}
            )
        
        # 构造交易请求对象
        from roles.yuheng_star.services.trading_execution_service import TradeRequest, TradeAction

        # 转换交易方向
        action = TradeAction.BUY if request.direction.lower() == "buy" else TradeAction.SELL

        trade_request = TradeRequest(
            stock_code=request.stock_code,
            stock_name=request.stock_name or request.stock_code,
            action=action,
            quantity=request.quantity,
            strategy_signal=request.strategy_signal or {},
            execution_constraints=request.execution_constraints or {}
        )

        # 执行交易
        execution_result = await services.trading_execution_service.execute_trade(trade_request)
        
        # 触发记忆系统
        if services.memory_system:
            try:
                from core.domain.memory.legendary.models import MessageType
                await services.memory_system.add_yuheng_memory(
                    content=f"执行交易: {request.direction} {request.stock_code} {request.quantity}股",
                    message_type=MessageType.TRADING_EXECUTION
                )
            except Exception as e:
                logger.warning(f"记忆系统记录失败: {e}")
        
        return ApiResponse(
            message="交易执行成功",
            data=execution_result
        )
        
    except Exception as e:
        logger.error(f"交易执行失败: {e}")
        return ApiResponse(
            success=False,
            message=f"交易执行失败: {str(e)}",
            error_code="EXECUTION_ERROR",
            error_details=str(e)
        )

@yuheng_star_router.post("/execution/advanced", response_model=ApiResponse)
async def advanced_execution(request: TradeExecutionRequest, background_tasks: BackgroundTasks):
    """高级智能执行"""
    try:
        services = get_yuheng_services()

        if not services.advanced_execution_system:
            return ApiResponse(
                success=False,
                message="高级执行系统不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        # 使用高级执行系统
        execution_result = await services.advanced_execution_system.execute_intelligent_order(
            stock_code=request.stock_code,
            stock_name=request.stock_name or request.stock_code,
            direction=request.direction,
            quantity=request.quantity,
            strategy_signal=request.strategy_signal or {},
            execution_constraints=request.execution_constraints or {}
        )

        # 记录绩效
        if services.performance_monitor:
            background_tasks.add_task(
                services.performance_monitor.record_performance,
                "玉衡星",
                PerformanceMetricType.EFFICIENCY,
                execution_result.get("efficiency_score", 0.85),
                {"execution_type": "advanced", "stock_code": request.stock_code}
            )

        return ApiResponse(
            message="高级执行完成",
            data=execution_result
        )

    except Exception as e:
        logger.error(f"高级执行失败: {e}")
        return ApiResponse(
            success=False,
            message=f"高级执行失败: {str(e)}",
            error_code="ADVANCED_EXECUTION_ERROR"
        )

@yuheng_star_router.get("/execution/recommendation/{stock_code}", response_model=ApiResponse)
async def get_execution_recommendation(stock_code: str, strategy_signal: Optional[str] = None):
    """获取执行建议"""
    try:
        services = get_yuheng_services()

        if not services.intelligent_execution_service:
            return ApiResponse(
                success=False,
                message="智能执行服务不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        # 解析策略信号
        signal_data = {}
        if strategy_signal:
            import json
            try:
                signal_data = json.loads(strategy_signal)
            except:
                signal_data = {"signal": strategy_signal}

        # 生成执行建议
        recommendation = await services.intelligent_execution_service.generate_execution_recommendation(
            stock_code=stock_code,
            strategy_signal=signal_data
        )

        return ApiResponse(
            message="执行建议生成成功",
            data=recommendation.__dict__ if hasattr(recommendation, '__dict__') else recommendation
        )

    except Exception as e:
        logger.error(f"获取执行建议失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取执行建议失败: {str(e)}",
            error_code="RECOMMENDATION_ERROR"
        )

# ==================== 订单管理API ====================

@yuheng_star_router.post("/orders/manage", response_model=ApiResponse)
async def manage_order(request: OrderManagementRequest):
    """订单管理"""
    try:
        services = get_yuheng_services()

        if not services.order_management_service:
            return ApiResponse(
                success=False,
                message="订单管理服务不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        action = request.action.lower()

        if action == "create":
            result = await services.order_management_service.create_order(request.order_data)
        elif action == "modify":
            result = await services.order_management_service.modify_order(
                request.order_id, request.order_data
            )
        elif action == "cancel":
            result = await services.order_management_service.cancel_order(request.order_id)
        elif action == "query":
            result = await services.order_management_service.get_order_status(request.order_id)
        else:
            return ApiResponse(
                success=False,
                message=f"不支持的操作类型: {action}",
                error_code="INVALID_ACTION"
            )

        return ApiResponse(
            message=f"订单{action}操作成功",
            data=result
        )

    except Exception as e:
        logger.error(f"订单管理失败: {e}")
        return ApiResponse(
            success=False,
            message=f"订单管理失败: {str(e)}",
            error_code="ORDER_MANAGEMENT_ERROR"
        )

@yuheng_star_router.get("/orders/list", response_model=ApiResponse)
async def list_orders(
    status: Optional[str] = None,
    stock_code: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """获取订单列表"""
    try:
        services = get_yuheng_services()

        if not services.order_management_service:
            return ApiResponse(
                success=False,
                message="订单管理服务不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        filters = {}
        if status:
            filters["status"] = status
        if stock_code:
            filters["stock_code"] = stock_code

        orders = await services.order_management_service.list_orders(
            filters=filters,
            limit=limit,
            offset=offset
        )

        return ApiResponse(
            message="订单列表获取成功",
            data={
                "orders": orders,
                "total": len(orders),
                "limit": limit,
                "offset": offset
            }
        )

    except Exception as e:
        logger.error(f"获取订单列表失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取订单列表失败: {str(e)}",
            error_code="ORDER_LIST_ERROR"
        )

@yuheng_star_router.get("/orders/{order_id}/status", response_model=ApiResponse)
async def get_order_status(order_id: str):
    """获取订单状态"""
    try:
        services = get_yuheng_services()

        if not services.order_management_service:
            return ApiResponse(
                success=False,
                message="订单管理服务不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        order_status = await services.order_management_service.get_order_status(order_id)

        return ApiResponse(
            message="订单状态获取成功",
            data=order_status
        )

    except Exception as e:
        logger.error(f"获取订单状态失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取订单状态失败: {str(e)}",
            error_code="ORDER_STATUS_ERROR"
        )

# ==================== 持仓管理API ====================

@yuheng_star_router.post("/positions/query", response_model=ApiResponse)
async def query_positions(request: PositionQueryRequest):
    """查询持仓"""
    try:
        services = get_yuheng_services()

        if not services.position_management_service:
            return ApiResponse(
                success=False,
                message="持仓管理服务不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        positions = await services.position_management_service.get_positions(
            stock_code=request.stock_code,
            account_id=request.account_id,
            include_history=request.include_history
        )

        return ApiResponse(
            message="持仓查询成功",
            data=positions
        )

    except Exception as e:
        logger.error(f"持仓查询失败: {e}")
        return ApiResponse(
            success=False,
            message=f"持仓查询失败: {str(e)}",
            error_code="POSITION_QUERY_ERROR"
        )

@yuheng_star_router.get("/positions/summary", response_model=ApiResponse)
async def get_position_summary():
    """获取持仓汇总"""
    try:
        services = get_yuheng_services()

        if not services.position_management_service:
            return ApiResponse(
                success=False,
                message="持仓管理服务不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        summary = await services.position_management_service.get_position_summary()

        return ApiResponse(
            message="持仓汇总获取成功",
            data=summary
        )

    except Exception as e:
        logger.error(f"获取持仓汇总失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取持仓汇总失败: {str(e)}",
            error_code="POSITION_SUMMARY_ERROR"
        )

@yuheng_star_router.get("/positions/pnl", response_model=ApiResponse)
async def calculate_pnl(stock_code: Optional[str] = None):
    """计算盈亏"""
    try:
        services = get_yuheng_services()

        if not services.position_management_service:
            return ApiResponse(
                success=False,
                message="持仓管理服务不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        pnl_data = await services.position_management_service.calculate_pnl(stock_code)

        return ApiResponse(
            message="盈亏计算成功",
            data=pnl_data
        )

    except Exception as e:
        logger.error(f"盈亏计算失败: {e}")
        return ApiResponse(
            success=False,
            message=f"盈亏计算失败: {str(e)}",
            error_code="PNL_CALCULATION_ERROR"
        )

# ==================== 虚拟交易API ====================

@yuheng_star_router.post("/virtual/trade", response_model=ApiResponse)
async def virtual_trade(request: TradeExecutionRequest):
    """虚拟交易"""
    try:
        services = get_yuheng_services()

        if not services.virtual_trading_service:
            return ApiResponse(
                success=False,
                message="虚拟交易服务不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        # 执行虚拟交易
        trade_result = await services.virtual_trading_service.execute_virtual_trade(
            stock_code=request.stock_code,
            action=request.direction,
            quantity=request.quantity,
            strategy_name=request.strategy_signal.get("strategy_name", "default") if request.strategy_signal else "default"
        )

        return ApiResponse(
            message="虚拟交易执行成功",
            data=trade_result
        )

    except Exception as e:
        logger.error(f"虚拟交易失败: {e}")
        return ApiResponse(
            success=False,
            message=f"虚拟交易失败: {str(e)}",
            error_code="VIRTUAL_TRADE_ERROR"
        )

@yuheng_star_router.get("/virtual/performance", response_model=ApiResponse)
async def get_virtual_performance(account_id: str = "default", period_days: int = 30):
    """获取虚拟交易表现"""
    try:
        services = get_yuheng_services()

        if not services.virtual_trading_service:
            return ApiResponse(
                success=False,
                message="虚拟交易服务不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        performance = await services.virtual_trading_service.get_trading_performance(
            account_id=account_id,
            period_days=period_days
        )

        return ApiResponse(
            message="虚拟交易表现获取成功",
            data=performance
        )

    except Exception as e:
        logger.error(f"获取虚拟交易表现失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取虚拟交易表现失败: {str(e)}",
            error_code="VIRTUAL_PERFORMANCE_ERROR"
        )

# ==================== 执行分析API ====================

@yuheng_star_router.post("/analysis/execution", response_model=ApiResponse)
async def analyze_execution(request: ExecutionAnalysisRequest):
    """执行分析"""
    try:
        services = get_yuheng_services()

        if not services.trading_execution_service:
            return ApiResponse(
                success=False,
                message="交易执行服务不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        analysis_type = request.analysis_type.lower()

        if analysis_type == "cost":
            result = await services.trading_execution_service.analyze_execution_costs(
                time_range=request.time_range,
                filters=request.filters
            )
        elif analysis_type == "performance":
            result = await services.trading_execution_service.analyze_execution_performance(
                time_range=request.time_range,
                filters=request.filters
            )
        elif analysis_type == "optimization":
            result = await services.trading_execution_service.analyze_optimization_opportunities(
                time_range=request.time_range,
                filters=request.filters
            )
        else:
            return ApiResponse(
                success=False,
                message=f"不支持的分析类型: {analysis_type}",
                error_code="INVALID_ANALYSIS_TYPE"
            )

        return ApiResponse(
            message=f"{analysis_type}分析完成",
            data=result
        )

    except Exception as e:
        logger.error(f"执行分析失败: {e}")
        return ApiResponse(
            success=False,
            message=f"执行分析失败: {str(e)}",
            error_code="EXECUTION_ANALYSIS_ERROR"
        )

# ==================== 工作流管理API ====================

@yuheng_star_router.post("/workflow/execute", response_model=ApiResponse)
async def execute_workflow(
    workflow_type: str,
    input_data: Dict[str, Any],
    workflow_id: Optional[str] = None
):
    """执行工作流"""
    try:
        result = await workflow_manager.execute_workflow(
            workflow_type=workflow_type,
            input_data=input_data,
            workflow_id=workflow_id
        )

        return ApiResponse(
            message="工作流执行成功",
            data=result
        )

    except Exception as e:
        logger.error(f"工作流执行失败: {e}")
        return ApiResponse(
            success=False,
            message=f"工作流执行失败: {str(e)}",
            error_code="WORKFLOW_EXECUTION_ERROR"
        )

@yuheng_star_router.get("/workflow/status/{workflow_id}", response_model=ApiResponse)
async def get_workflow_status(workflow_id: str):
    """获取工作流状态"""
    try:
        status = workflow_manager.get_workflow_status(workflow_id)

        if status is None:
            return ApiResponse(
                success=False,
                message="工作流不存在",
                error_code="WORKFLOW_NOT_FOUND"
            )

        return ApiResponse(
            message="工作流状态获取成功",
            data=status
        )

    except Exception as e:
        logger.error(f"获取工作流状态失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取工作流状态失败: {str(e)}",
            error_code="WORKFLOW_STATUS_ERROR"
        )

@yuheng_star_router.get("/workflow/list", response_model=ApiResponse)
async def list_workflows():
    """列出活动工作流"""
    try:
        active_workflows = workflow_manager.list_active_workflows()
        workflow_stats = workflow_manager.get_workflow_statistics()

        return ApiResponse(
            message="工作流列表获取成功",
            data={
                "active_workflows": active_workflows,
                "statistics": workflow_stats
            }
        )

    except Exception as e:
        logger.error(f"获取工作流列表失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取工作流列表失败: {str(e)}",
            error_code="WORKFLOW_LIST_ERROR"
        )

@yuheng_star_router.delete("/workflow/{workflow_id}", response_model=ApiResponse)
async def cancel_workflow(workflow_id: str):
    """取消工作流"""
    try:
        success = await workflow_manager.cancel_workflow(workflow_id)

        if not success:
            return ApiResponse(
                success=False,
                message="工作流不存在或无法取消",
                error_code="WORKFLOW_CANCEL_FAILED"
            )

        return ApiResponse(
            message="工作流取消成功"
        )

    except Exception as e:
        logger.error(f"取消工作流失败: {e}")
        return ApiResponse(
            success=False,
            message=f"取消工作流失败: {str(e)}",
            error_code="WORKFLOW_CANCEL_ERROR"
        )

# ==================== 绩效监控API ====================

@yuheng_star_router.get("/performance/report", response_model=ApiResponse)
async def get_performance_report(period_days: int = 30):
    """获取绩效报告"""
    try:
        services = get_yuheng_services()

        if not services.performance_monitor:
            return ApiResponse(
                success=False,
                message="绩效监控系统不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        performance_report = await services.performance_monitor.get_star_performance(
            "玉衡星", period_days
        )

        if not performance_report:
            return ApiResponse(
                success=False,
                message="无法获取绩效报告",
                error_code="PERFORMANCE_REPORT_ERROR"
            )

        return ApiResponse(
            message="绩效报告获取成功",
            data=performance_report.__dict__
        )

    except Exception as e:
        logger.error(f"获取绩效报告失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取绩效报告失败: {str(e)}",
            error_code="PERFORMANCE_REPORT_ERROR"
        )

@yuheng_star_router.post("/performance/record", response_model=ApiResponse)
async def record_performance_metric(
    metric_type: str,
    value: float,
    context: Optional[Dict[str, Any]] = None
):
    """记录绩效指标"""
    try:
        services = get_yuheng_services()

        if not services.performance_monitor:
            return ApiResponse(
                success=False,
                message="绩效监控系统不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        # 映射指标类型
        metric_type_mapping = {
            "success_rate": PerformanceMetricType.SUCCESS_RATE,
            "efficiency": PerformanceMetricType.EFFICIENCY,
            "accuracy": PerformanceMetricType.ACCURACY,
            "quality_score": PerformanceMetricType.QUALITY_SCORE,
            "task_count": PerformanceMetricType.TASK_COUNT,
            "response_time": PerformanceMetricType.RESPONSE_TIME
        }

        metric_enum = metric_type_mapping.get(metric_type.lower())
        if not metric_enum:
            return ApiResponse(
                success=False,
                message=f"不支持的指标类型: {metric_type}",
                error_code="INVALID_METRIC_TYPE"
            )

        success = await services.performance_monitor.record_performance(
            "玉衡星", metric_enum, value, context or {}
        )

        return ApiResponse(
            message="绩效指标记录成功",
            data={"recorded": success, "metric_type": metric_type, "value": value}
        )

    except Exception as e:
        logger.error(f"记录绩效指标失败: {e}")
        return ApiResponse(
            success=False,
            message=f"记录绩效指标失败: {str(e)}",
            error_code="PERFORMANCE_RECORD_ERROR"
        )

# ==================== 记忆系统API ====================

@yuheng_star_router.post("/memory/add", response_model=ApiResponse)
async def add_memory(
    content: str,
    message_type: str = "trading_execution",
    context: Optional[Dict[str, Any]] = None
):
    """添加记忆"""
    try:
        services = get_yuheng_services()

        if not services.memory_system:
            return ApiResponse(
                success=False,
                message="记忆系统不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        from core.domain.memory.legendary.models import MessageType

        # 映射消息类型
        message_type_mapping = {
            "trading_execution": MessageType.TRADING_EXECUTION,
            "order_management": MessageType.ORDER_MANAGEMENT,
            "position_management": MessageType.POSITION_MANAGEMENT,
            "risk_control": MessageType.RISK_CONTROL,
            "performance_analysis": MessageType.PERFORMANCE_ANALYSIS
        }

        msg_type = message_type_mapping.get(message_type.lower(), MessageType.TRADING_EXECUTION)

        memory_id = await services.memory_system.add_yuheng_memory(
            content=content,
            message_type=msg_type,
            context=context or {}
        )

        return ApiResponse(
            message="记忆添加成功",
            data={"memory_id": memory_id}
        )

    except Exception as e:
        logger.error(f"添加记忆失败: {e}")
        return ApiResponse(
            success=False,
            message=f"添加记忆失败: {str(e)}",
            error_code="MEMORY_ADD_ERROR"
        )

@yuheng_star_router.get("/memory/search", response_model=ApiResponse)
async def search_memory(
    query: str,
    memory_type: Optional[str] = None,
    limit: int = 10
):
    """搜索记忆"""
    try:
        services = get_yuheng_services()

        if not services.memory_system:
            return ApiResponse(
                success=False,
                message="记忆系统不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        memories = await services.memory_system.search_yuheng_memories(
            query=query,
            memory_type=memory_type,
            limit=limit
        )

        return ApiResponse(
            message="记忆搜索成功",
            data={
                "memories": memories,
                "total": len(memories),
                "query": query
            }
        )

    except Exception as e:
        logger.error(f"搜索记忆失败: {e}")
        return ApiResponse(
            success=False,
            message=f"搜索记忆失败: {str(e)}",
            error_code="MEMORY_SEARCH_ERROR"
        )

@yuheng_star_router.get("/memory/recent", response_model=ApiResponse)
async def get_recent_memories(limit: int = 20):
    """获取最近记忆"""
    try:
        services = get_yuheng_services()

        if not services.memory_system:
            return ApiResponse(
                success=False,
                message="记忆系统不可用",
                error_code="SERVICE_UNAVAILABLE"
            )

        memories = await services.memory_system.get_recent_yuheng_memories(limit=limit)

        return ApiResponse(
            message="最近记忆获取成功",
            data={
                "memories": memories,
                "total": len(memories)
            }
        )

    except Exception as e:
        logger.error(f"获取最近记忆失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取最近记忆失败: {str(e)}",
            error_code="MEMORY_RECENT_ERROR"
        )

# ==================== 系统集成API ====================

@yuheng_star_router.get("/integration/status", response_model=ApiResponse)
async def get_integration_status():
    """获取系统集成状态"""
    try:
        services = get_yuheng_services()

        integration_status = {
            "core_systems": {
                "memory_system": {
                    "available": services.memory_system is not None,
                    "status": "active" if services.memory_system else "unavailable",
                    "features": ["添加记忆", "搜索记忆", "获取最近记忆"] if services.memory_system else []
                },
                "performance_monitor": {
                    "available": services.performance_monitor is not None,
                    "status": "active" if services.performance_monitor else "unavailable",
                    "features": ["绩效记录", "绩效报告", "指标分析"] if services.performance_monitor else []
                },
                "hierarchy_system": {
                    "available": services.hierarchy_system is not None,
                    "status": "active" if services.hierarchy_system else "unavailable",
                    "authority_level": 5 if services.hierarchy_system else None
                },
                "deepseek_config": {
                    "available": services.deepseek_config is not None,
                    "status": "active" if services.deepseek_config else "unavailable",
                    "persona_loaded": bool(services.deepseek_config) if services.deepseek_config else False
                }
            },
            "business_services": {
                "trading_execution": services.trading_execution_service is not None,
                "order_management": services.order_management_service is not None,
                "position_management": services.position_management_service is not None,
                "virtual_trading": services.virtual_trading_service is not None,
                "advanced_execution": services.advanced_execution_system is not None,
                "intelligent_execution": services.intelligent_execution_service is not None
            },
            "overall_health": services.is_healthy(),
            "integration_score": sum([
                services.memory_system is not None,
                services.performance_monitor is not None,
                services.hierarchy_system is not None,
                services.deepseek_config is not None,
                services.trading_execution_service is not None,
                services.order_management_service is not None
            ]) / 6 * 100
        }

        return ApiResponse(
            message="系统集成状态获取成功",
            data=integration_status
        )

    except Exception as e:
        logger.error(f"获取系统集成状态失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取系统集成状态失败: {str(e)}",
            error_code="INTEGRATION_STATUS_ERROR"
        )

# ==================== 分离交易统计API ====================

@yuheng_star_router.get("/statistics/virtual/{period}", response_model=ApiResponse)
async def get_virtual_trading_statistics(period: str = "daily"):
    """获取虚拟交易统计 (学习模式)"""
    try:
        if period not in ["daily", "weekly", "monthly", "yearly"]:
            return ApiResponse(
                success=False,
                message="无效的统计周期，支持: daily/weekly/monthly/yearly",
                error_code="INVALID_PERIOD"
            )

        # 获取虚拟交易统计
        virtual_stats = await separated_trading_statistics_service.get_virtual_statistics(period)

        return ApiResponse(
            message=f"虚拟交易{period}统计获取成功",
            data={
                "mode": "virtual",
                "mode_description": "虚拟交易 (学习模式)",
                "data_source": "历史数据库 (瑶光星)",
                "statistics": virtual_stats.__dict__,
                "note": "虚拟交易用于策略验证和学习，使用历史价格数据"
            }
        )

    except Exception as e:
        logger.error(f"获取虚拟交易统计失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取虚拟交易统计失败: {str(e)}",
            error_code="VIRTUAL_STATISTICS_ERROR"
        )

@yuheng_star_router.get("/statistics/real/{period}", response_model=ApiResponse)
async def get_real_trading_statistics(period: str = "daily"):
    """获取真实交易统计 (自动化模式)"""
    try:
        if period not in ["daily", "weekly", "monthly", "yearly"]:
            return ApiResponse(
                success=False,
                message="无效的统计周期，支持: daily/weekly/monthly/yearly",
                error_code="INVALID_PERIOD"
            )

        # 获取真实交易统计
        real_stats = await separated_trading_statistics_service.get_real_statistics(period)

        return ApiResponse(
            message=f"真实交易{period}统计获取成功",
            data={
                "mode": "real",
                "mode_description": "真实交易 (自动化模式)",
                "data_source": "实时API (东方财富)",
                "statistics": real_stats.__dict__,
                "note": "真实交易通过四星辩论决策，使用实时市场价格"
            }
        )

    except Exception as e:
        logger.error(f"获取真实交易统计失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取真实交易统计失败: {str(e)}",
            error_code="REAL_STATISTICS_ERROR"
        )

@yuheng_star_router.get("/statistics/comparison/{period}", response_model=ApiResponse)
async def get_trading_statistics_comparison(period: str = "daily"):
    """获取虚拟与真实交易统计对比"""
    try:
        if period not in ["daily", "weekly", "monthly", "yearly"]:
            return ApiResponse(
                success=False,
                message="无效的统计周期，支持: daily/weekly/monthly/yearly",
                error_code="INVALID_PERIOD"
            )

        # 获取两种模式的统计
        virtual_stats = await separated_trading_statistics_service.get_virtual_statistics(period)
        real_stats = await separated_trading_statistics_service.get_real_statistics(period)

        # 计算对比指标
        comparison = {
            "period": period,
            "virtual_trading": {
                "mode": "学习模式",
                "data_source": "历史数据库",
                "total_trades": virtual_stats.total_trades,
                "win_rate": f"{virtual_stats.win_rate:.2%}",
                "net_profit": virtual_stats.net_profit,
                "sharpe_ratio": virtual_stats.sharpe_ratio
            },
            "real_trading": {
                "mode": "自动化模式",
                "data_source": "实时API",
                "total_trades": real_stats.total_trades,
                "win_rate": f"{real_stats.win_rate:.2%}",
                "net_profit": real_stats.net_profit,
                "sharpe_ratio": real_stats.sharpe_ratio
            },
            "comparison_notes": [
                "虚拟交易用于学习和策略验证，无实际资金风险",
                "真实交易通过四星辩论决策，使用实时市场数据",
                "两种模式的盈亏统计完全分离，不会混淆",
                "建议先在虚拟模式验证策略，再在真实模式执行"
            ]
        }

        return ApiResponse(
            message=f"交易统计对比获取成功",
            data=comparison
        )

    except Exception as e:
        logger.error(f"获取交易统计对比失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取交易统计对比失败: {str(e)}",
            error_code="COMPARISON_ERROR"
        )

# ==================== 前端支持API ====================

@yuheng_star_router.get("/frontend/holdings/{mode}", response_model=ApiResponse)
async def get_frontend_holdings_data(mode: str):
    """获取前端持仓数据 (学习模式/自动化模式)"""
    try:
        if mode not in ["learning", "automation"]:
            return ApiResponse(
                success=False,
                message="无效的模式，支持: learning/automation",
                error_code="INVALID_MODE"
            )

        # 根据模式获取不同的持仓数据
        if mode == "learning":
            # 虚拟持仓数据
            holdings_data = await _get_virtual_holdings_data()
            data_source = "瑶光星历史数据库"
            mode_description = "学习模式 - 虚拟持仓"
        else:
            # 真实持仓数据
            holdings_data = await _get_real_holdings_data()
            data_source = "东方财富实时API"
            mode_description = "自动化模式 - 真实持仓"

        return ApiResponse(
            message=f"{mode_description}数据获取成功",
            data={
                "mode": mode,
                "mode_description": mode_description,
                "data_source": data_source,
                "holdings": holdings_data["holdings"],
                "summary": holdings_data["summary"],
                "last_updated": datetime.now().isoformat()
            }
        )

    except Exception as e:
        logger.error(f"获取前端持仓数据失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取前端持仓数据失败: {str(e)}",
            error_code="FRONTEND_HOLDINGS_ERROR"
        )

@yuheng_star_router.get("/frontend/logs/{mode}", response_model=ApiResponse)
async def get_frontend_logs_data(mode: str, limit: int = 20):
    """获取前端日志数据 (玉衡星分析过程和讨论内容)"""
    try:
        if mode not in ["learning", "automation"]:
            return ApiResponse(
                success=False,
                message="无效的模式，支持: learning/automation",
                error_code="INVALID_MODE"
            )

        # 获取玉衡星的分析日志
        logs_data = await _get_yuheng_analysis_logs(mode, limit)

        return ApiResponse(
            message=f"玉衡星{mode}模式日志获取成功",
            data={
                "mode": mode,
                "total_logs": len(logs_data),
                "logs": logs_data,
                "last_updated": datetime.now().isoformat()
            }
        )

    except Exception as e:
        logger.error(f"获取前端日志数据失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取前端日志数据失败: {str(e)}",
            error_code="FRONTEND_LOGS_ERROR"
        )

@yuheng_star_router.get("/frontend/dashboard/{mode}", response_model=ApiResponse)
async def get_frontend_dashboard_data(mode: str):
    """获取前端仪表板完整数据"""
    try:
        if mode not in ["learning", "automation"]:
            return ApiResponse(
                success=False,
                message="无效的模式，支持: learning/automation",
                error_code="INVALID_MODE"
            )

        # 并行获取所有数据
        holdings_data = await _get_virtual_holdings_data() if mode == "learning" else await _get_real_holdings_data()
        statistics_data = await separated_trading_statistics_service.get_virtual_statistics("daily") if mode == "learning" else await separated_trading_statistics_service.get_real_statistics("daily")
        logs_data = await _get_yuheng_analysis_logs(mode, 10)

        return ApiResponse(
            message=f"仪表板{mode}模式数据获取成功",
            data={
                "mode": mode,
                "mode_info": {
                    "learning": {
                        "title": "学习模式",
                        "description": "使用瑶光星历史数据库，无风险策略验证",
                        "data_source": "瑶光星历史数据库",
                        "icon": "🎮"
                    },
                    "automation": {
                        "title": "自动化模式",
                        "description": "使用东方财富实时API，四星辩论决策",
                        "data_source": "东方财富实时API",
                        "icon": "💰"
                    }
                }[mode],
                "holdings": holdings_data,
                "statistics": statistics_data.__dict__,
                "logs": logs_data,
                "last_updated": datetime.now().isoformat()
            }
        )

    except Exception as e:
        logger.error(f"获取仪表板数据失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取仪表板数据失败: {str(e)}",
            error_code="DASHBOARD_ERROR"
        )

# ==================== 内部辅助函数 ====================

async def _get_virtual_holdings_data():
    """获取虚拟持仓数据"""
    try:
        # 模拟虚拟持仓数据 (使用历史价格)
        virtual_holdings = [
            {
                "stock_code": "000001",
                "stock_name": "平安银行",
                "quantity": 1000,
                "cost_price": 51.79,  # 历史价格
                "current_price": 52.15,  # 历史价格波动
                "holding_amount": 52150.0,
                "pnl_amount": 360.0,
                "pnl_ratio": 0.695,
                "trade_date": "2024-06-20",
                "data_source": "瑶光星历史数据库"
            },
            {
                "stock_code": "000002",
                "stock_name": "万科A",
                "quantity": 500,
                "cost_price": 28.45,
                "current_price": 27.89,
                "holding_amount": 13945.0,
                "pnl_amount": -280.0,
                "pnl_ratio": -1.97,
                "trade_date": "2024-06-20",
                "data_source": "瑶光星历史数据库"
            }
        ]

        total_amount = sum(h["holding_amount"] for h in virtual_holdings)
        total_pnl = sum(h["pnl_amount"] for h in virtual_holdings)

        return {
            "holdings": virtual_holdings,
            "summary": {
                "total_amount": total_amount,
                "total_pnl": total_pnl,
                "total_positions": len(virtual_holdings),
                "data_source": "瑶光星历史数据库"
            }
        }

    except Exception as e:
        logger.error(f"获取虚拟持仓数据失败: {e}")
        return {"holdings": [], "summary": {"total_amount": 0, "total_pnl": 0, "total_positions": 0}}

async def _get_real_holdings_data():
    """获取真实持仓数据"""
    try:
        # 获取真实持仓数据 (使用实时价格)
        from roles.yuheng_star.services.real_data_integration_service import real_data_service

        # 模拟真实持仓数据 (使用实时价格)
        real_holdings = [
            {
                "stock_code": "000001",
                "stock_name": "平安银行",
                "quantity": 1000,
                "cost_price": 11.84,  # 实时价格
                "current_price": 11.84,  # 当前实时价格
                "holding_amount": 11840.0,
                "pnl_amount": 0.0,
                "pnl_ratio": 0.0,
                "trade_date": "2024-06-20",
                "data_source": "东方财富实时API"
            }
        ]

        # 尝试获取实时价格更新
        try:
            price_data = await real_data_service.get_real_stock_price("000001")
            if price_data.get("success"):
                current_price = price_data["data"]["current_price"]
                real_holdings[0]["current_price"] = current_price
                real_holdings[0]["holding_amount"] = current_price * 1000
                real_holdings[0]["pnl_amount"] = (current_price - 11.84) * 1000
                real_holdings[0]["pnl_ratio"] = (current_price - 11.84) / 11.84 * 100
        except:
            pass

        total_amount = sum(h["holding_amount"] for h in real_holdings)
        total_pnl = sum(h["pnl_amount"] for h in real_holdings)

        return {
            "holdings": real_holdings,
            "summary": {
                "total_amount": total_amount,
                "total_pnl": total_pnl,
                "total_positions": len(real_holdings),
                "data_source": "东方财富实时API"
            }
        }

    except Exception as e:
        logger.error(f"获取真实持仓数据失败: {e}")
        return {"holdings": [], "summary": {"total_amount": 0, "total_pnl": 0, "total_positions": 0}}

async def _get_yuheng_analysis_logs(mode: str, limit: int = 20):
    """获取玉衡星分析日志"""
    try:
        from datetime import timedelta  # 确保在函数内部可以访问

        # 模拟玉衡星的分析和讨论日志
        base_logs = [
            {
                "type": "analysis",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "content": f"📊 {'虚拟' if mode == 'learning' else '真实'}交易分析：检测到000001平安银行技术指标RSI为65.2，处于中性区间",
                "source": "玉衡星技术分析模块"
            },
            {
                "type": "discussion",
                "timestamp": (datetime.now() - timedelta(minutes=5)).strftime("%Y-%m-%d %H:%M:%S"),
                "content": "🤝 四星辩论：天玑星提醒当前市场波动率为18.5%，建议采用保守执行策略",
                "source": "四星辩论系统"
            },
            {
                "type": "execution",
                "timestamp": (datetime.now() - timedelta(minutes=10)).strftime("%Y-%m-%d %H:%M:%S"),
                "content": f"⚡ {'虚拟' if mode == 'learning' else '真实'}交易执行：成功买入000001平安银行1000股，执行价格¥{51.79 if mode == 'learning' else 11.84}",
                "source": "玉衡星交易执行引擎"
            },
            {
                "type": "analysis",
                "timestamp": (datetime.now() - timedelta(minutes=15)).strftime("%Y-%m-%d %H:%M:%S"),
                "content": f"🔍 数据源分析：{'瑶光星历史数据库' if mode == 'learning' else '东方财富实时API'}响应正常，数据质量良好",
                "source": "玉衡星数据集成模块"
            },
            {
                "type": "discussion",
                "timestamp": (datetime.now() - timedelta(minutes=20)).strftime("%Y-%m-%d %H:%M:%S"),
                "content": "💬 策略讨论：天璇星建议采用VWAP算法执行大额订单，减少市场冲击",
                "source": "四星辩论系统"
            }
        ]

        # 根据模式调整日志内容
        mode_specific_logs = []
        for log in base_logs[:limit]:
            if mode == "learning":
                log["mode_tag"] = "🎮 学习模式"
            else:
                log["mode_tag"] = "💰 自动化模式"
            mode_specific_logs.append(log)

        return mode_specific_logs

    except Exception as e:
        logger.error(f"获取分析日志失败: {e}")
        return []

# ==================== 导出路由 ====================

# 导出路由器供主路由使用
__all__ = ["yuheng_star_router"]
