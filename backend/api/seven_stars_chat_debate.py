#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
七星聊天辩论系统
专门为前端聊天功能设计的独立辩论系统，不影响现有工作流
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Any, Optional, List, Dict
from datetime import datetime
import logging
import asyncio
from enum import Enum

# 添加ApiResponse导入
class ApiResponse(BaseModel):
    """API响应模型"""
    success: bool = True
    message: str = ""
    data: Any = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

logger = logging.getLogger(__name__)

# 创建七星聊天辩论路由器
seven_stars_chat_debate_router = APIRouter(prefix="/api/seven-stars-chat-debate", tags=["七星聊天辩论"])

# ==================== 辩论相关模型 ====================

class DebateMode(str, Enum):
    """辩论模式"""
    SEQUENTIAL = "sequential"  # 顺序发言
    PARALLEL = "parallel"     # 并行发言
    DEBATE = "debate"         # 辩论模式
    CONSENSUS = "consensus"   # 共识模式

class ChatDebateRequest(BaseModel):
    """聊天辩论请求"""
    message: str = Field(..., description="用户消息")
    mentioned_roles: List[str] = Field(..., description="@的角色列表")
    debate_mode: DebateMode = Field(default=DebateMode.DEBATE, description="辩论模式")
    session_id: Optional[str] = Field(default=None, description="会话ID")
    max_rounds: int = Field(default=3, description="最大辩论轮数")

class DebateResponse(BaseModel):
    """辩论响应"""
    success: bool = True
    message: str = ""
    debate_id: str = ""
    debate_result: Dict[str, Any] = {}
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

class ChatRequest(BaseModel):
    """聊天请求"""
    message: str = Field(..., description="用户消息")
    mentioned_roles: List[str] = Field(..., description="@的角色列表")
    session_id: Optional[str] = Field(default=None, description="会话ID")

# ==================== 七星角色配置 ====================

SEVEN_STARS_CHAT_CONFIG = {
    "tianquan": {
        "name": "👑 天权星",
        "role": "决策指挥官",
        "personality": "理性、权威、善于综合分析",
        "debate_style": "总结观点、寻求平衡、做出决策",
        "config_path": "roles.tianquan_star.config.deepseek_config"
    },
    "tianji": {
        "name": "🛡️ 天玑星", 
        "role": "风险管理专家",
        "personality": "谨慎、严谨、注重风险控制",
        "debate_style": "质疑风险、提出警告、保守建议",
        "config_path": "roles.tianji_star.config.deepseek_config"
    },
    "tianxuan": {
        "name": "📈 天璇星",
        "role": "技术分析师", 
        "personality": "理性、数据驱动、技术导向",
        "debate_style": "提供技术证据、图表分析、量化观点",
        "config_path": "roles.tianxuan_star.config.deepseek_config"
    },
    "tianshu": {
        "name": "🔍 天枢星",
        "role": "智能情报官",
        "personality": "敏锐、信息丰富、善于发现趋势",
        "debate_style": "提供市场情报、新闻分析、情绪判断",
        "config_path": "roles.tianshu_star.config.deepseek_config"
    },
    "yuheng": {
        "name": "⚡ 玉衡星",
        "role": "交易执行官",
        "personality": "果断、实用、注重执行效率",
        "debate_style": "关注可操作性、执行难度、时机把握",
        "config_path": "roles.yuheng_star.config.deepseek_config"
    },
    "kaiyang": {
        "name": "🎯 开阳星",
        "role": "股票检测员",
        "personality": "细致、专业、善于发现机会",
        "debate_style": "提供个股分析、机会发现、价值评估",
        "config_path": "roles.kaiyang_star.config.deepseek_config"
    },
    "yaoguang": {
        "name": "🧠 瑶光星",
        "role": "学习训练专家",
        "personality": "智慧、深度、善于总结规律",
        "debate_style": "历史经验、模式识别、学习建议",
        "config_path": "roles.yaoguang_star.config.deepseek_config"
    }
}

# ==================== 七星聊天辩论系统 ====================

class SevenStarsChatDebate:
    """七星聊天辩论系统"""

    def __init__(self):
        self.active_debates: Dict[str, Dict] = {}
        self.debate_config = {
            "max_rounds": 3,
            "consensus_threshold": 0.7,
            "timeout_minutes": 10
        }
        # 辩论触发关键词
        self.debate_triggers = {
            "investment_decision": ["投资", "买入", "卖出", "建议", "决策", "选择"],
            "risk_assessment": ["风险", "亏损", "波动", "安全", "保守", "激进"],
            "market_analysis": ["市场", "趋势", "分析", "预测", "走势", "行情"],
            "strategy_discussion": ["策略", "方法", "计划", "方案", "思路", "操作"],
            "complex_topics": ["如何", "为什么", "什么时候", "哪个更好", "比较", "评估"]
        }
        logger.info("🌟 七星聊天辩论系统初始化完成")

    async def should_trigger_debate(self, message: str, mentioned_roles: List[str]) -> Dict[str, Any]:
        """智能判断是否需要触发辩论"""
        try:
            # 1. 基础条件检查
            if len(mentioned_roles) < 2:
                return {
                    "should_debate": False,
                    "reason": "单角色对话，无需辩论",
                    "suggested_mode": "normal_chat"
                }

            # 2. 内容复杂度分析
            complexity_score = self._analyze_message_complexity(message)

            # 3. 辩论需求评估
            debate_necessity = self._assess_debate_necessity(message, mentioned_roles)

            # 4. 角色相关性分析
            role_relevance = self._analyze_role_relevance(message, mentioned_roles)

            # 5. 综合判断
            final_score = (complexity_score * 0.4 + debate_necessity * 0.4 + role_relevance * 0.2)

            should_debate = final_score > 0.6  # 阈值可调整

            # 6. 确定辩论模式
            if should_debate:
                if len(mentioned_roles) >= 5 or "all" in mentioned_roles:
                    suggested_mode = "debate"  # 多角色深度辩论
                elif complexity_score > 0.8:
                    suggested_mode = "consensus"  # 复杂问题寻求共识
                else:
                    suggested_mode = "sequential"  # 顺序讨论
            else:
                suggested_mode = "parallel"  # 简单并行回复

            return {
                "should_debate": should_debate,
                "confidence": final_score,
                "suggested_mode": suggested_mode,
                "analysis": {
                    "complexity_score": complexity_score,
                    "debate_necessity": debate_necessity,
                    "role_relevance": role_relevance,
                    "trigger_keywords": self._extract_trigger_keywords(message)
                },
                "reason": self._generate_decision_reason(should_debate, final_score, mentioned_roles)
            }

        except Exception as e:
            logger.error(f"辩论判断失败: {e}")
            return {
                "should_debate": False,
                "reason": "判断系统异常，使用默认模式",
                "suggested_mode": "parallel"
            }

    def _analyze_message_complexity(self, message: str) -> float:
        """分析消息复杂度"""
        complexity_indicators = {
            "question_words": ["如何", "为什么", "什么", "哪个", "怎么", "何时"],
            "comparison_words": ["比较", "对比", "更好", "优劣", "差异", "选择"],
            "analysis_words": ["分析", "评估", "判断", "考虑", "研究", "探讨"],
            "complex_concepts": ["策略", "风险", "收益", "市场", "趋势", "模型"]
        }

        score = 0.0
        message_lower = message.lower()

        # 检查复杂度指标
        for category, words in complexity_indicators.items():
            matches = sum(1 for word in words if word in message_lower)
            if category == "question_words":
                score += matches * 0.3
            elif category == "comparison_words":
                score += matches * 0.25
            elif category == "analysis_words":
                score += matches * 0.2
            else:
                score += matches * 0.15

        # 消息长度因子
        length_factor = min(len(message) / 100, 1.0) * 0.2
        score += length_factor

        return min(score, 1.0)

    def _assess_debate_necessity(self, message: str, mentioned_roles: List[str]) -> float:
        """评估辩论必要性"""
        necessity_score = 0.0
        message_lower = message.lower()

        # 检查辩论触发关键词
        for category, keywords in self.debate_triggers.items():
            matches = sum(1 for keyword in keywords if keyword in message_lower)
            if matches > 0:
                if category == "investment_decision":
                    necessity_score += 0.4  # 投资决策最需要辩论
                elif category == "risk_assessment":
                    necessity_score += 0.3  # 风险评估次之
                elif category == "complex_topics":
                    necessity_score += 0.25
                else:
                    necessity_score += 0.2

        # 角色数量因子
        role_factor = min(len(mentioned_roles) / 7, 1.0) * 0.3
        necessity_score += role_factor

        return min(necessity_score, 1.0)

    def _analyze_role_relevance(self, message: str, mentioned_roles: List[str]) -> float:
        """分析角色相关性"""
        role_expertise = {
            "tianquan": ["决策", "指挥", "协调", "管理", "领导"],
            "tianji": ["风险", "控制", "安全", "保守", "防范"],
            "tianxuan": ["技术", "分析", "指标", "图表", "模型"],
            "tianshu": ["新闻", "信息", "情报", "市场", "情绪"],
            "yuheng": ["交易", "执行", "操作", "买卖", "订单"],
            "kaiyang": ["股票", "选择", "筛选", "机会", "标的"],
            "yaoguang": ["学习", "训练", "数据", "历史", "经验"]
        }

        relevance_score = 0.0
        message_lower = message.lower()

        for role_id in mentioned_roles:
            if role_id == "all":
                relevance_score += 0.8  # @all表示高相关性
                continue

            if role_id in role_expertise:
                expertise_words = role_expertise[role_id]
                matches = sum(1 for word in expertise_words if word in message_lower)
                if matches > 0:
                    relevance_score += matches * 0.2

        return min(relevance_score, 1.0)

    def _extract_trigger_keywords(self, message: str) -> List[str]:
        """提取触发关键词"""
        found_keywords = []
        message_lower = message.lower()

        for category, keywords in self.debate_triggers.items():
            for keyword in keywords:
                if keyword in message_lower:
                    found_keywords.append(keyword)

        return found_keywords

    def _generate_decision_reason(self, should_debate: bool, score: float, mentioned_roles: List[str]) -> str:
        """生成决策原因"""
        if should_debate:
            if score > 0.8:
                return f"高复杂度问题({score:.2f})，{len(mentioned_roles)}个角色需要深度讨论"
            elif len(mentioned_roles) >= 4:
                return f"多角色参与({len(mentioned_roles)}个)，适合辩论模式"
            else:
                return f"中等复杂度({score:.2f})，建议结构化讨论"
        else:
            if len(mentioned_roles) == 1:
                return "单角色对话，直接回复即可"
            else:
                return f"简单问题({score:.2f})，并行回复更高效"

    async def start_chat_debate(self, request: ChatDebateRequest) -> Dict[str, Any]:
        """开始聊天辩论"""
        try:
            # 创建辩论ID
            debate_id = f"chat_debate_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            logger.info(f"🗣️ 开始七星聊天辩论: {debate_id}")
            logger.info(f"📝 用户消息: {request.message}")
            logger.info(f"👥 参与角色: {request.mentioned_roles}")
            logger.info(f"🎯 辩论模式: {request.debate_mode}")
            
            # 处理@all的情况
            if "all" in request.mentioned_roles:
                participants = list(SEVEN_STARS_CHAT_CONFIG.keys())
            else:
                participants = request.mentioned_roles
            
            # 验证角色
            for role_id in participants:
                if role_id not in SEVEN_STARS_CHAT_CONFIG:
                    raise ValueError(f"未知角色: {role_id}")
            
            # 根据辩论模式执行不同逻辑
            if request.debate_mode == DebateMode.DEBATE:
                result = await self._conduct_debate_mode(debate_id, request.message, participants, request.max_rounds)
            elif request.debate_mode == DebateMode.CONSENSUS:
                result = await self._conduct_consensus_mode(debate_id, request.message, participants)
            elif request.debate_mode == DebateMode.PARALLEL:
                result = await self._conduct_parallel_mode(debate_id, request.message, participants)
            else:  # SEQUENTIAL
                result = await self._conduct_sequential_mode(debate_id, request.message, participants)
            
            return {
                "debate_id": debate_id,
                "mode": request.debate_mode,
                "participants": participants,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"聊天辩论失败: {e}")
            raise e
    
    async def _conduct_debate_mode(self, debate_id: str, message: str, participants: List[str], max_rounds: int) -> Dict[str, Any]:
        """执行辩论模式"""
        logger.info(f"🔥 执行辩论模式: {len(participants)}个角色，{max_rounds}轮")
        
        debate_rounds = []
        current_topic = message
        
        for round_num in range(1, max_rounds + 1):
            logger.info(f"  第{round_num}轮辩论...")
            
            round_responses = []
            
            # 每轮让所有角色都发表观点
            for role_id in participants:
                role_config = SEVEN_STARS_CHAT_CONFIG[role_id]
                
                # 构建辩论提示词
                debate_prompt = self._build_debate_prompt(
                    role_config, current_topic, round_num, debate_rounds
                )
                
                # 调用角色AI
                response = await self._call_role_ai(role_id, debate_prompt)
                
                round_responses.append({
                    "role_id": role_id,
                    "role_name": role_config["name"],
                    "content": response,
                    "round": round_num,
                    "timestamp": datetime.now().isoformat()
                })
            
            debate_rounds.append({
                "round": round_num,
                "topic": current_topic,
                "responses": round_responses
            })
            
            # 更新下一轮的话题（基于本轮的争议点）
            if round_num < max_rounds:
                current_topic = await self._extract_debate_focus(round_responses)
        
        # 生成辩论总结
        summary = await self._generate_debate_summary(debate_rounds, participants)
        
        return {
            "mode": "debate",
            "rounds": debate_rounds,
            "summary": summary,
            "total_rounds": len(debate_rounds)
        }
    
    async def _conduct_consensus_mode(self, debate_id: str, message: str, participants: List[str]) -> Dict[str, Any]:
        """执行共识模式"""
        logger.info(f"🤝 执行共识模式: {len(participants)}个角色")
        
        # 第一轮：收集初始观点
        initial_responses = []
        for role_id in participants:
            role_config = SEVEN_STARS_CHAT_CONFIG[role_id]
            prompt = f"作为{role_config['name']}，请对以下问题给出你的专业观点：{message}"
            response = await self._call_role_ai(role_id, prompt)
            
            initial_responses.append({
                "role_id": role_id,
                "role_name": role_config["name"],
                "content": response,
                "timestamp": datetime.now().isoformat()
            })
        
        # 第二轮：寻求共识
        consensus_prompt = f"基于以上各角色的观点，请寻求共识并给出综合建议：{message}"
        consensus_responses = []
        
        for role_id in participants:
            role_config = SEVEN_STARS_CHAT_CONFIG[role_id]
            full_prompt = self._build_consensus_prompt(role_config, message, initial_responses)
            response = await self._call_role_ai(role_id, full_prompt)
            
            consensus_responses.append({
                "role_id": role_id,
                "role_name": role_config["name"],
                "content": response,
                "timestamp": datetime.now().isoformat()
            })
        
        return {
            "mode": "consensus",
            "initial_round": initial_responses,
            "consensus_round": consensus_responses
        }
    
    async def _conduct_parallel_mode(self, debate_id: str, message: str, participants: List[str]) -> Dict[str, Any]:
        """执行并行模式"""
        logger.info(f"⚡ 执行并行模式: {len(participants)}个角色同时回复")
        
        # 并行调用所有角色
        tasks = []
        for role_id in participants:
            role_config = SEVEN_STARS_CHAT_CONFIG[role_id]
            prompt = f"作为{role_config['name']}，请对以下问题给出你的专业观点：{message}"
            task = self._call_role_ai(role_id, prompt)
            tasks.append((role_id, role_config, task))
        
        # 等待所有响应
        responses = []
        for role_id, role_config, task in tasks:
            try:
                response = await task
                responses.append({
                    "role_id": role_id,
                    "role_name": role_config["name"],
                    "content": response,
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                logger.error(f"角色{role_id}响应失败: {e}")
                responses.append({
                    "role_id": role_id,
                    "role_name": role_config["name"],
                    "content": f"抱歉，{role_config['name']}暂时无法回复",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
        
        return {
            "mode": "parallel",
            "responses": responses
        }
    
    async def _conduct_sequential_mode(self, debate_id: str, message: str, participants: List[str]) -> Dict[str, Any]:
        """执行顺序模式"""
        logger.info(f"📝 执行顺序模式: {len(participants)}个角色依次回复")
        
        responses = []
        accumulated_context = message
        
        for i, role_id in enumerate(participants):
            role_config = SEVEN_STARS_CHAT_CONFIG[role_id]
            
            # 构建包含前面角色回复的上下文
            if i > 0:
                context_prompt = f"原始问题：{message}\n\n前面角色的回复：\n"
                for prev_resp in responses:
                    context_prompt += f"{prev_resp['role_name']}：{prev_resp['content']}\n\n"
                context_prompt += f"现在请你作为{role_config['name']}给出回复："
            else:
                context_prompt = f"作为{role_config['name']}，请对以下问题给出你的专业观点：{message}"
            
            response = await self._call_role_ai(role_id, context_prompt)
            
            responses.append({
                "role_id": role_id,
                "role_name": role_config["name"],
                "content": response,
                "order": i + 1,
                "timestamp": datetime.now().isoformat()
            })
        
        return {
            "mode": "sequential",
            "responses": responses
        }

    def _build_debate_prompt(self, role_config: Dict, topic: str, round_num: int, previous_rounds: List) -> str:
        """构建辩论提示词"""
        base_prompt = f"""你是{role_config['name']} - {role_config['role']}。
你的性格特点：{role_config['personality']}
你的辩论风格：{role_config['debate_style']}

当前辩论话题：{topic}
当前是第{round_num}轮辩论。

"""

        if previous_rounds:
            base_prompt += "前面轮次的辩论内容：\n"
            for round_data in previous_rounds:
                base_prompt += f"\n第{round_data['round']}轮：\n"
                for response in round_data['responses']:
                    base_prompt += f"{response['role_name']}：{response['content']}\n"
            base_prompt += "\n"

        base_prompt += f"""请基于你的专业角色和性格特点，对当前话题发表观点。
如果与其他角色有不同意见，请明确表达你的立场和理由。
保持你的角色特色，回复长度控制在200字以内。"""

        return base_prompt

    def _build_consensus_prompt(self, role_config: Dict, message: str, initial_responses: List) -> str:
        """构建共识提示词"""
        prompt = f"""你是{role_config['name']} - {role_config['role']}。

原始问题：{message}

各角色的初始观点：
"""
        for resp in initial_responses:
            prompt += f"{resp['role_name']}：{resp['content']}\n\n"

        prompt += f"""现在请你作为{role_config['name']}，在考虑其他角色观点的基础上，
寻求共识并给出你的综合建议。如果有分歧，请说明如何协调。
回复长度控制在150字以内。"""

        return prompt

    async def _call_role_ai(self, role_id: str, prompt: str) -> str:
        """调用角色AI服务"""
        try:
            role_config = SEVEN_STARS_CHAT_CONFIG[role_id]
            config_path = role_config["config_path"]

            # 动态导入角色配置
            try:
                module_parts = config_path.split('.')
                module = __import__(config_path, fromlist=[module_parts[-1]])
                role_setting = module.get_role_setting()
            except ImportError:
                role_setting = f"你是{role_config['name']}，{role_config['role']}。{role_config['personality']}"

            # 获取DeepSeek服务
            from shared.infrastructure.deepseek_service import deepseek_service

            # 构建消息
            messages = [
                {"role": "system", "content": role_setting},
                {"role": "user", "content": prompt}
            ]

            # 调用AI服务
            response = await deepseek_service.chat_completion(messages, max_tokens=500)

            if response.get("success"):
                return response.get("content", response.get("response", "收到您的消息"))
            else:
                return f"抱歉，{role_config['name']}暂时无法回复"

        except Exception as e:
            logger.error(f"调用{role_id}AI服务失败: {e}")
            return f"抱歉，{role_config['name']}暂时无法回复"

    async def _extract_debate_focus(self, round_responses: List) -> str:
        """提取辩论焦点"""
        contents = [resp["content"] for resp in round_responses]
        combined_content = " ".join(contents)

        # 这里可以用更复杂的NLP分析，现在用简单的关键词提取
        if "风险" in combined_content:
            return "风险评估和控制策略"
        elif "技术" in combined_content:
            return "技术分析方法和指标选择"
        elif "时机" in combined_content:
            return "投资时机和执行策略"
        else:
            return "投资策略的可行性和优化"

    async def _generate_debate_summary(self, debate_rounds: List, participants: List[str]) -> Dict[str, Any]:
        """生成辩论总结"""
        total_responses = sum(len(round_data["responses"]) for round_data in debate_rounds)

        # 统计各角色参与度
        role_participation = {}
        for role_id in participants:
            role_config = SEVEN_STARS_CHAT_CONFIG[role_id]
            role_participation[role_id] = {
                "name": role_config["name"],
                "responses": 0,
                "key_points": []
            }

        # 收集关键观点
        for round_data in debate_rounds:
            for response in round_data["responses"]:
                role_id = response["role_id"]
                if role_id in role_participation:
                    role_participation[role_id]["responses"] += 1
                    if len(response["content"]) > 50:
                        role_participation[role_id]["key_points"].append(
                            response["content"][:100] + "..."
                        )

        return {
            "total_rounds": len(debate_rounds),
            "total_responses": total_responses,
            "participants": len(participants),
            "role_participation": role_participation,
            "debate_quality": "高质量" if total_responses >= len(participants) * 2 else "标准",
            "summary": f"经过{len(debate_rounds)}轮辩论，{len(participants)}个角色共产生{total_responses}次回复，形成了多维度的分析视角。"
        }

# 创建辩论系统实例
seven_stars_debate_system = SevenStarsChatDebate()

# ==================== API端点 ====================

@seven_stars_chat_debate_router.post("/analyze", response_model=ApiResponse)
async def analyze_debate_necessity(request: ChatRequest):
    """智能分析是否需要辩论"""
    try:
        logger.info(f"分析辩论必要性: {request.mentioned_roles}")

        analysis = await seven_stars_debate_system.should_trigger_debate(
            request.message,
            request.mentioned_roles
        )

        return ApiResponse(
            message="辩论必要性分析完成",
            data=analysis
        )

    except Exception as e:
        logger.error(f"辩论分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@seven_stars_chat_debate_router.post("/start", response_model=DebateResponse)
async def start_chat_debate(request: ChatDebateRequest):
    """开始七星聊天辩论"""
    try:
        logger.info(f"收到七星聊天辩论请求: {request.debate_mode}")

        result = await seven_stars_debate_system.start_chat_debate(request)

        return DebateResponse(
            message="七星聊天辩论完成",
            debate_id=result["debate_id"],
            debate_result=result
        )

    except Exception as e:
        logger.error(f"七星聊天辩论失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@seven_stars_chat_debate_router.get("/modes")
async def get_debate_modes():
    """获取可用的辩论模式"""
    return {
        "success": True,
        "data": {
            "modes": [
                {
                    "id": "sequential",
                    "name": "顺序模式",
                    "description": "角色依次发言，形成连贯对话"
                },
                {
                    "id": "parallel",
                    "name": "并行模式",
                    "description": "角色同时回复，多维度分析"
                },
                {
                    "id": "debate",
                    "name": "辩论模式",
                    "description": "多轮辩论，观点碰撞"
                },
                {
                    "id": "consensus",
                    "name": "共识模式",
                    "description": "寻求共识，协调分歧"
                }
            ]
        }
    }

@seven_stars_chat_debate_router.get("/roles")
async def get_chat_roles():
    """获取聊天角色信息"""
    return {
        "success": True,
        "data": {
            "roles": [
                {
                    "id": role_id,
                    "name": config["name"],
                    "role": config["role"],
                    "personality": config["personality"],
                    "debate_style": config["debate_style"]
                }
                for role_id, config in SEVEN_STARS_CHAT_CONFIG.items()
            ]
        }
    }

@seven_stars_chat_debate_router.get("/health")
async def chat_debate_health():
    """聊天辩论系统健康检查"""
    return {
        "success": True,
        "message": "七星聊天辩论系统运行正常",
        "data": {
            "service": "七星聊天辩论系统",
            "status": "healthy",
            "available_roles": len(SEVEN_STARS_CHAT_CONFIG),
            "supported_modes": ["sequential", "parallel", "debate", "consensus"],
            "features": [
                "多角色辩论",
                "智能共识",
                "并行分析",
                "顺序对话"
            ]
        }
    }
