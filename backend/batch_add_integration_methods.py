#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量为所有角色自动化系统添加集成方法
"""

import os
import json

def add_integration_methods_to_role(role_name: str, file_path: str):
    """为角色自动化系统添加集成方法"""
    
    integration_methods = f'''
    async def _call_role_deepseek(self, prompt: str, context_type: str = "analysis", context_data: dict = None) -> dict:
        """调用{role_name}专用DeepSeek分析"""
        try:
            from roles.{role_name}.config.deepseek_config import get_deepseek_config, get_role_setting
            from shared.infrastructure.deepseek_service import deepseek_service
            
            # 获取角色专用配置
            config = get_deepseek_config()
            role_setting = get_role_setting()
            
            # 构建角色专用提示词
            role_prompt = f"{{role_setting}}\\n\\n请分析：{{prompt}}"
            if context_data:
                try:
                    import json
                    context_str = json.dumps(context_data, ensure_ascii=False, default=str)
                    role_prompt += f"\\n\\n上下文数据：{{context_str}}"
                except:
                    role_prompt += f"\\n\\n上下文数据：{{str(context_data)}}"
            
            # 调用DeepSeek服务
            messages = [
                {{"role": "system", "content": role_setting}},
                {{"role": "user", "content": prompt}}
            ]
            
            result = await deepseek_service.chat_completion(messages, **config)
            
            return {{
                "success": result.get("success", False),
                "analysis": result.get("response", ""),
                "role": "{role_name}",
                "context_type": context_type,
                "timestamp": datetime.now().isoformat()
            }}
            
        except Exception as e:
            return {{
                "success": False,
                "error": str(e),
                "role": "{role_name}",
                "timestamp": datetime.now().isoformat()
            }}
    
    async def store_memory(self, content: str, memory_type: str = "analysis", priority: str = "normal", metadata: dict = None) -> dict:
        """存储记忆到传奇记忆系统"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface
            
            result = await legendary_memory_interface.store_memory(
                content=content,
                message_type=memory_type,
                role_source="{role_name}",
                priority=priority,
                metadata=metadata or {{}}
            )
            
            return result
            
        except Exception as e:
            return {{"success": False, "error": str(e)}}
    
    async def retrieve_memories(self, query: str, limit: int = 10) -> list:
        """从传奇记忆系统检索记忆"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface
            
            memories = await legendary_memory_interface.search_memories(
                query=query,
                role_filter="{role_name}",
                limit=limit
            )
            
            return memories
            
        except Exception as e:
            return []
    
    async def record_performance(self, metric_name: str, value: float, context: dict = None) -> dict:
        """记录绩效到监控系统"""
        try:
            from core.performance.star_performance_monitor import star_performance_monitor
            
            result = await star_performance_monitor.record_performance(
                star_name="{role_name}",
                metric_type=metric_name,
                value=value,
                context=context or {{}}
            )
            
            return {{"success": result, "metric": metric_name, "value": value}}
            
        except Exception as e:
            return {{"success": False, "error": str(e)}}
    
    def get_performance_stats(self) -> dict:
        """获取角色绩效统计"""
        try:
            from core.performance.star_performance_monitor import star_performance_monitor
            
            return star_performance_monitor.get_star_performance("{role_name}")
            
        except Exception as e:
            return {{"error": str(e)}}
'''
    
    try:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有集成方法
        if '_call_role_deepseek' in content:
            print(f"  ✅ {role_name} 已有集成方法")
            return True
        
        # 找到类的结束位置（在全局实例之前）
        lines = content.split('\n')
        insert_index = -1
        
        # 找到最后一个方法的结束位置
        for i in range(len(lines) - 1, -1, -1):
            line = lines[i].strip()
            if line.startswith('# 全局') or line.startswith('# Global') or 'global' in line.lower():
                insert_index = i
                break
        
        if insert_index == -1:
            # 如果没找到全局实例标记，在文件末尾插入
            insert_index = len(lines)
        
        # 插入集成方法
        lines.insert(insert_index, integration_methods)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print(f"  ✅ {role_name} 集成方法添加成功")
        return True
        
    except Exception as e:
        print(f"  ❌ {role_name} 集成方法添加失败: {e}")
        return False

def main():
    """主函数"""
    print('🔧 批量为所有角色自动化系统添加集成方法')
    print('=' * 60)
    
    # 角色配置
    roles_config = {
        'tianxuan_star': 'roles/tianxuan_star/services/tianxuan_automation_system.py',
        'tianji_star': 'roles/tianji_star/services/tianji_automation_system.py',
        'tianquan_star': 'roles/tianquan_star/core/tianquan_automation_system.py',
        'yuheng_star': 'roles/yuheng_star/services/yuheng_automation_system.py',
        'kaiyang_star': 'roles/kaiyang_star/services/kaiyang_automation_system.py',
        'yaoguang_star': 'roles/yaoguang_star/automation/quantitative_research_automation.py'
    }
    
    success_count = 0
    
    for role_name, file_path in roles_config.items():
        print(f'\n🔍 处理 {role_name}...')
        
        if add_integration_methods_to_role(role_name, file_path):
            success_count += 1
    
    print(f'\n📋 处理完成:')
    print(f'  成功: {success_count}/{len(roles_config)}')
    print(f'  失败: {len(roles_config) - success_count}/{len(roles_config)}')

if __name__ == "__main__":
    main()
