#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查股票数据库的实际情况
"""

import sqlite3
import os
from pathlib import Path

def check_database():
    """检查数据库内容"""
    # 检查数据库文件
    db_path = Path('backend/data/stock_database.db')
    print(f'数据库路径: {db_path.absolute()}')
    print(f'数据库存在: {db_path.exists()}')

    if db_path.exists():
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 查看所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f'数据库中的表: {[t[0] for t in tables]}')
        
        # 检查每个表的数据
        for table in tables:
            table_name = table[0]
            try:
                cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                count = cursor.fetchone()[0]
                print(f'表 {table_name}: {count} 条记录')
                
                if count > 0:
                    cursor.execute(f'SELECT * FROM {table_name} LIMIT 3')
                    columns = [description[0] for description in cursor.description]
                    print(f'  列名: {columns}')
                    rows = cursor.fetchall()
                    for i, row in enumerate(rows):
                        print(f'  样例{i+1}: {dict(zip(columns, row))}')
                        
                    # 特别检查是否有000001相关数据
                    if 'ts_code' in columns:
                        cursor.execute(f"SELECT * FROM {table_name} WHERE ts_code LIKE '000001%' LIMIT 3")
                        rows_000001 = cursor.fetchall()
                        if rows_000001:
                            print(f'  000001相关数据: {len(rows_000001)}条')
                            for i, row in enumerate(rows_000001):
                                print(f'    000001样例{i+1}: {dict(zip(columns, row))}')
                        else:
                            print(f'  ❌ 表{table_name}中没有000001相关数据')
                    
                    if 'code' in columns:
                        cursor.execute(f"SELECT * FROM {table_name} WHERE code LIKE '000001%' LIMIT 3")
                        rows_000001 = cursor.fetchall()
                        if rows_000001:
                            print(f'  000001相关数据: {len(rows_000001)}条')
                            for i, row in enumerate(rows_000001):
                                print(f'    000001样例{i+1}: {dict(zip(columns, row))}')
                        else:
                            print(f'  ❌ 表{table_name}中没有000001相关数据')
                            
            except Exception as e:
                print(f'表 {table_name} 查询失败: {e}')
        
        conn.close()
    else:
        print('❌ 数据库文件不存在!')

if __name__ == "__main__":
    check_database()
