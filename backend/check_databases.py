from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库状态检查脚本
"""

import sqlite3
import os
from datetime import datetime

def check_database(db_path):
    """检查单个数据库"""
    if not os.path.exists(db_path):
        print(f"⚠️ 数据库不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取表信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"\n📊 数据库: {db_path}")
        print(f"   文件大小: {os.path.getsize(db_path) / 1024 / 1024:.2f} MB")
        print(f"   表数量: {len(tables)}")
        
        for table in tables:
            table_name = table[0]
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   - {table_name}: {count:,} 条记录")
            except Exception as e:
                print(f"   - {table_name}: 查询失败 ({e})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库 {db_path} 检查失败: {e}")

def main():
    """主函数"""
    print("🔍 数据库状态检查")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查主要数据库
    databases = [
        get_database_path("stock_database"),
        "backend/data/legendary_memory.db", 
        "backend/data/news_knowledge_base.db",
        "backend/data/risk_database.db",
        "backend/data/historical_data.db",
        "backend/data/trading_execution.db"
    ]
    
    for db_path in databases:
        check_database(db_path)
    
    print("\n" + "=" * 50)
    print("✅ 数据库检查完成")

if __name__ == "__main__":
    main()
