from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查股票数据库结构并修复问题
"""

import sqlite3
from pathlib import Path
import json

def check_stock_database():
    """检查股票数据库结构"""
    print("💾 检查股票数据库结构...")
    
    db_path = Path(get_database_path("stock_database"))
    if not db_path.exists():
        print("❌ 股票数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"数据库表: {tables}")
        
        # 检查每个表的结构和数据
        for table in tables:
            print(f"\n表: {table}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            print(f"  列结构: {[col[1] for col in columns]}")
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  记录数: {count}")
            
            # 如果有数据，显示示例
            if count > 0:
                cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                samples = cursor.fetchall()
                print(f"  示例数据: {samples[0] if samples else 'None'}")
        
        conn.close()
        print("\n✅ 股票数据库检查完成")
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")

def check_all_databases():
    """检查所有数据库"""
    print("🔍 检查所有数据库文件...")
    
    data_dir = Path("backend/data")
    if not data_dir.exists():
        print("❌ 数据目录不存在")
        return
    
    # 列出所有数据库文件
    db_files = list(data_dir.glob("*.db"))
    print(f"发现数据库文件: {[db.name for db in db_files]}")
    
    for db_file in db_files:
        print(f"\n📋 检查数据库: {db_file.name}")
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            total_records = 0
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                total_records += count
            
            size_mb = round(db_file.stat().st_size / 1024 / 1024, 2)
            
            print(f"  表数: {len(tables)}")
            print(f"  总记录数: {total_records}")
            print(f"  文件大小: {size_mb} MB")
            print(f"  表名: {tables}")
            
            conn.close()
            
        except Exception as e:
            print(f"  ❌ 检查失败: {e}")

def fix_stock_database_verification():
    """修复股票数据库验证问题"""
    print("\n🔧 修复股票数据库验证问题...")
    
    db_path = Path(get_database_path("stock_database"))
    if not db_path.exists():
        print("❌ 股票数据库文件不存在，无法修复")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查实际的表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"实际表名: {tables}")
        
        # 查找股票相关的表
        stock_tables = [t for t in tables if 'stock' in t.lower()]
        daily_tables = [t for t in tables if 'daily' in t.lower() or 'price' in t.lower()]
        indicator_tables = [t for t in tables if 'indicator' in t.lower() or 'technical' in t.lower()]
        
        print(f"股票表: {stock_tables}")
        print(f"日线表: {daily_tables}")
        print(f"指标表: {indicator_tables}")
        
        # 生成正确的验证代码
        verification_code = f"""
# 修正后的股票数据库验证代码
stock_tables = {stock_tables}
daily_tables = {daily_tables}
indicator_tables = {indicator_tables}

# 检查股票基础信息
if stock_tables:
    cursor.execute("SELECT COUNT(*) FROM {stock_tables[0] if stock_tables else 'stocks'}")
    stock_count = cursor.fetchone()[0]
else:
    stock_count = 0

# 检查日线数据
if daily_tables:
    cursor.execute("SELECT COUNT(*) FROM {daily_tables[0] if daily_tables else 'daily_data'}")
    daily_data_count = cursor.fetchone()[0]
else:
    daily_data_count = 0

# 检查技术指标
if indicator_tables:
    cursor.execute("SELECT COUNT(*) FROM {indicator_tables[0] if indicator_tables else 'technical_indicators'}")
    indicator_count = cursor.fetchone()[0]
else:
    indicator_count = 0
"""
        
        print("生成的修正代码:")
        print(verification_code)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    check_all_databases()
    print("\n" + "="*60)
    check_stock_database()
    print("\n" + "="*60)
    fix_stock_database_verification()
