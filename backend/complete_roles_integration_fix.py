#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的七个角色自动化系统集成修复
消除所有降级模式，恢复专业版本的完整功能
集成DeepSeek、记忆、绩效系统到自动化流程
"""

import asyncio
import sys
import os
import json
from datetime import datetime

sys.path.append(os.getcwd())

class CompleteRolesIntegrationFix:
    """完整的角色集成修复器"""
    
    def __init__(self):
        self.fix_results = {
            'timestamp': datetime.now().isoformat(),
            'fixes_applied': [],
            'errors_encountered': [],
            'integration_status': {}
        }
        
        # 角色配置
        self.roles_config = {
            'tianshu_star': {
                'name': '天枢星',
                'automation_module': 'roles.tianshu_star.services.tianshu_automation_system',
                'automation_instance': 'tianshu_automation_system',
                'core_method': 'execute_market_analysis'
            },
            'tianxuan_star': {
                'name': '天璇星',
                'automation_module': 'roles.tianxuan_star.services.tianxuan_automation_system',
                'automation_instance': 'tianxuan_automation_system',
                'core_method': 'execute_technical_analysis'
            },
            'tianji_star': {
                'name': '天玑星',
                'automation_module': 'roles.tianji_star.services.tianji_automation_system',
                'automation_instance': 'tianji_automation_system',
                'core_method': 'execute_risk_analysis'
            },
            'tianquan_star': {
                'name': '天权星',
                'automation_module': 'roles.tianquan_star.core.tianquan_automation_system',
                'automation_instance': 'tianquan_automation_system',
                'core_method': 'execute_decision_automation'
            },
            'yuheng_star': {
                'name': '玉衡星',
                'automation_module': 'roles.yuheng_star.services.yuheng_automation_system',
                'automation_instance': 'yuheng_automation_system',
                'core_method': 'execute_trading_automation'
            },
            'kaiyang_star': {
                'name': '开阳星',
                'automation_module': 'roles.kaiyang_star.services.kaiyang_automation_system',
                'automation_instance': 'kaiyang_automation_system',
                'core_method': 'execute_stock_selection_automation'
            },
            'yaoguang_star': {
                'name': '瑶光星',
                'automation_module': 'roles.yaoguang_star.automation.quantitative_research_automation',
                'automation_instance': 'quantitative_research_automation',
                'core_method': 'start_automation'
            }
        }
    
    async def fix_all_roles_integration(self):
        """修复所有角色的集成"""
        print('🚀 开始完整的七个角色自动化系统集成修复')
        print('=' * 60)
        
        # 1. 修复每个角色的核心系统集成
        for role_name, config in self.roles_config.items():
            await self.fix_role_integration(role_name, config)
        
        # 2. 验证系统级集成
        await self.verify_system_integration()
        
        # 3. 运行完整测试
        await self.run_complete_integration_test()
        
        # 4. 生成修复报告
        await self.generate_fix_report()
        
        print('\n✅ 所有角色自动化系统集成修复完成')
    
    async def fix_role_integration(self, role_name: str, config: dict):
        """修复单个角色的集成"""
        print(f'\n🔧 修复角色: {config["name"]} ({role_name})')
        print('-' * 50)
        
        try:
            # 1. 导入自动化系统
            module = __import__(config['automation_module'], fromlist=[''])
            automation_system = getattr(module, config['automation_instance'], None)
            
            if not automation_system:
                print(f'    ❌ 自动化系统实例不存在')
                self.fix_results['errors_encountered'].append(f'{role_name}: 自动化系统实例不存在')
                return
            
            # 2. 集成DeepSeek服务
            deepseek_integrated = await self.integrate_deepseek_to_role(role_name, automation_system)
            
            # 3. 集成传奇记忆系统
            memory_integrated = await self.integrate_memory_to_role(role_name, automation_system)
            
            # 4. 集成绩效监控系统
            performance_integrated = await self.integrate_performance_to_role(role_name, automation_system)
            
            # 5. 验证核心功能
            core_verified = await self.verify_role_core_functions(role_name, automation_system, config)
            
            # 记录集成状态
            self.fix_results['integration_status'][role_name] = {
                'deepseek_integrated': deepseek_integrated,
                'memory_integrated': memory_integrated,
                'performance_integrated': performance_integrated,
                'core_verified': core_verified,
                'overall_success': all([deepseek_integrated, memory_integrated, performance_integrated, core_verified])
            }
            
            if self.fix_results['integration_status'][role_name]['overall_success']:
                print(f'    ✅ {config["name"]} 集成修复完成')
                self.fix_results['fixes_applied'].append(f'{config["name"]}完整集成修复')
            else:
                print(f'    ⚠️ {config["name"]} 集成部分成功')
                self.fix_results['errors_encountered'].append(f'{config["name"]}集成部分失败')
                
        except Exception as e:
            print(f'    ❌ {config["name"]} 集成修复失败: {e}')
            self.fix_results['errors_encountered'].append(f'{config["name"]}集成修复失败: {e}')
    
    async def integrate_deepseek_to_role(self, role_name: str, automation_system) -> bool:
        """集成DeepSeek服务到角色自动化系统"""
        try:
            print(f'    🧠 集成DeepSeek服务...')
            
            # 检查是否已有DeepSeek集成
            if hasattr(automation_system, '_call_role_deepseek'):
                print(f'        ✅ DeepSeek服务已集成')
                return True
            
            # 动态添加DeepSeek集成方法
            async def _call_role_deepseek(prompt: str, context_type: str = "analysis", context_data: dict = None) -> dict:
                """调用角色专用DeepSeek分析"""
                try:
                    # 导入角色配置和DeepSeek服务
                    config_module = __import__(f'roles.{role_name}.config.deepseek_config', fromlist=[''])
                    from shared.infrastructure.deepseek_service import deepseek_service
                    
                    # 获取角色专用配置
                    config = config_module.get_deepseek_config()
                    role_setting = config_module.get_role_setting()
                    
                    # 构建角色专用提示词
                    role_prompt = f"{role_setting}\n\n请分析：{prompt}"
                    if context_data:
                        # 安全地序列化上下文数据
                        try:
                            context_str = json.dumps(context_data, ensure_ascii=False, default=str)
                            role_prompt += f"\n\n上下文数据：{context_str}"
                        except:
                            role_prompt += f"\n\n上下文数据：{str(context_data)}"
                    
                    # 调用DeepSeek服务
                    messages = [
                        {"role": "system", "content": role_setting},
                        {"role": "user", "content": prompt}
                    ]
                    
                    result = await deepseek_service.chat_completion(messages, **config)
                    
                    return {
                        "success": result.get("success", False),
                        "analysis": result.get("response", ""),
                        "role": role_name,
                        "context_type": context_type,
                        "timestamp": datetime.now().isoformat()
                    }
                    
                except Exception as e:
                    return {
                        "success": False,
                        "error": str(e),
                        "role": role_name,
                        "timestamp": datetime.now().isoformat()
                    }
            
            # 绑定方法到自动化系统
            automation_system._call_role_deepseek = _call_role_deepseek.__get__(automation_system, type(automation_system))
            
            print(f'        ✅ DeepSeek服务集成成功')
            return True
            
        except Exception as e:
            print(f'        ❌ DeepSeek服务集成失败: {e}')
            return False
    
    async def integrate_memory_to_role(self, role_name: str, automation_system) -> bool:
        """集成传奇记忆系统到角色自动化系统"""
        try:
            print(f'    🧠 集成传奇记忆系统...')
            
            # 检查是否已有记忆集成
            if hasattr(automation_system, 'store_memory'):
                print(f'        ✅ 传奇记忆系统已集成')
                return True
            
            # 动态添加记忆集成方法
            async def store_memory(content: str, memory_type: str = "analysis", priority: str = "normal", metadata: dict = None) -> dict:
                """存储记忆到传奇记忆系统"""
                try:
                    from core.domain.memory.legendary.interface import legendary_memory_interface
                    
                    result = await legendary_memory_interface.store_memory(
                        content=content,
                        message_type=memory_type,
                        role_source=role_name,
                        priority=priority,
                        metadata=metadata or {}
                    )
                    
                    return result
                    
                except Exception as e:
                    return {"success": False, "error": str(e)}
            
            async def retrieve_memories(query: str, limit: int = 10) -> list:
                """从传奇记忆系统检索记忆"""
                try:
                    from core.domain.memory.legendary.interface import legendary_memory_interface
                    
                    memories = await legendary_memory_interface.search_memories(
                        query=query,
                        role_filter=role_name,
                        limit=limit
                    )
                    
                    return memories
                    
                except Exception as e:
                    return []
            
            # 绑定方法到自动化系统
            automation_system.store_memory = store_memory.__get__(automation_system, type(automation_system))
            automation_system.retrieve_memories = retrieve_memories.__get__(automation_system, type(automation_system))
            
            print(f'        ✅ 传奇记忆系统集成成功')
            return True
            
        except Exception as e:
            print(f'        ❌ 传奇记忆系统集成失败: {e}')
            return False
    
    async def integrate_performance_to_role(self, role_name: str, automation_system) -> bool:
        """集成绩效监控系统到角色自动化系统"""
        try:
            print(f'    📊 集成绩效监控系统...')
            
            # 检查是否已有绩效集成
            if hasattr(automation_system, 'record_performance'):
                print(f'        ✅ 绩效监控系统已集成')
                return True
            
            # 动态添加绩效集成方法
            async def record_performance(self, metric_name: str, value: float, context: dict = None) -> dict:
                """记录绩效到监控系统"""
                try:
                    from core.performance.star_performance_monitor import star_performance_monitor
                    
                    result = await star_performance_monitor.record_performance(
                        star_name=role_name,
                        metric_type=metric_name,
                        value=value,
                        context=context or {}
                    )
                    
                    return {"success": result, "metric": metric_name, "value": value}
                    
                except Exception as e:
                    return {"success": False, "error": str(e)}
            
            def get_performance_stats(self) -> dict:
                """获取角色绩效统计"""
                try:
                    from core.performance.star_performance_monitor import star_performance_monitor
                    
                    return star_performance_monitor.get_star_performance(role_name)
                    
                except Exception as e:
                    return {"error": str(e)}
            
            # 绑定方法到自动化系统
            automation_system.record_performance = record_performance.__get__(automation_system, type(automation_system))
            automation_system.get_performance_stats = get_performance_stats.__get__(automation_system, type(automation_system))
            
            print(f'        ✅ 绩效监控系统集成成功')
            return True
            
        except Exception as e:
            print(f'        ❌ 绩效监控系统集成失败: {e}')
            return False

    async def verify_role_core_functions(self, role_name: str, automation_system, config: dict) -> bool:
        """验证角色核心功能"""
        try:
            print(f'    🔍 验证核心功能...')

            # 检查核心方法是否存在
            core_method = config['core_method']
            if not hasattr(automation_system, core_method):
                print(f'        ❌ 核心方法 {core_method} 不存在')
                return False

            # 检查启动方法
            if not hasattr(automation_system, 'start_automation'):
                print(f'        ❌ start_automation 方法不存在')
                return False

            # 检查集成的系统方法
            required_methods = ['_call_role_deepseek', 'store_memory', 'record_performance']
            missing_methods = [method for method in required_methods if not hasattr(automation_system, method)]

            if missing_methods:
                print(f'        ⚠️ 缺少集成方法: {missing_methods}')
                return False

            print(f'        ✅ 核心功能验证通过')
            return True

        except Exception as e:
            print(f'        ❌ 核心功能验证失败: {e}')
            return False

    async def verify_system_integration(self):
        """验证系统级集成"""
        print(f'\n🔧 验证系统级集成...')

        # 验证传奇记忆系统
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface
            await legendary_memory_interface.initialize()
            memory_stats = legendary_memory_interface.get_memory_statistics()
            print(f'    ✅ 传奇记忆系统: {memory_stats}')
        except Exception as e:
            print(f'    ❌ 传奇记忆系统验证失败: {e}')

        # 验证绩效监控系统
        try:
            from core.performance.star_performance_monitor import star_performance_monitor
            perf_status = star_performance_monitor.get_system_status()
            print(f'    ✅ 绩效监控系统: {perf_status}')
        except Exception as e:
            print(f'    ❌ 绩效监控系统验证失败: {e}')

        # 验证DeepSeek服务
        try:
            from shared.infrastructure.deepseek_service import deepseek_service
            await deepseek_service.initialize()
            print(f'    ✅ DeepSeek服务: 连接状态={deepseek_service.is_connected}')
        except Exception as e:
            print(f'    ❌ DeepSeek服务验证失败: {e}')

    async def run_complete_integration_test(self):
        """运行完整集成测试"""
        print(f'\n🧪 运行完整集成测试...')

        test_context = {
            'stock_code': '000001.XSHE',
            'task_type': 'integration_test',
            'session_id': 'test_integration'
        }

        for role_name, config in self.roles_config.items():
            try:
                print(f'    🔍 测试 {config["name"]}...')

                # 导入自动化系统
                module = __import__(config['automation_module'], fromlist=[''])
                automation_system = getattr(module, config['automation_instance'], None)

                if automation_system:
                    # 测试DeepSeek集成
                    if hasattr(automation_system, '_call_role_deepseek'):
                        deepseek_result = await automation_system._call_role_deepseek(
                            "测试DeepSeek集成", "integration_test"
                        )
                        print(f'        🧠 DeepSeek测试: {"✅" if deepseek_result.get("success") else "❌"}')

                    # 测试记忆集成
                    if hasattr(automation_system, 'store_memory'):
                        memory_result = await automation_system.store_memory(
                            f"{config['name']}集成测试记忆", "test", "normal"
                        )
                        print(f'        🧠 记忆测试: {"✅" if memory_result.get("success") else "❌"}')

                    # 测试绩效集成
                    if hasattr(automation_system, 'record_performance'):
                        perf_result = await automation_system.record_performance(
                            "integration_test", 1.0, {"test": True}
                        )
                        print(f'        📊 绩效测试: {"✅" if perf_result.get("success") else "❌"}')

                    print(f'        ✅ {config["name"]} 集成测试通过')
                else:
                    print(f'        ❌ {config["name"]} 自动化系统不可用')

            except Exception as e:
                print(f'        ❌ {config["name"]} 集成测试失败: {e}')

    async def generate_fix_report(self):
        """生成修复报告"""
        print(f'\n📋 生成修复报告...')

        # 计算成功率
        total_roles = len(self.roles_config)
        successful_integrations = sum(1 for status in self.fix_results['integration_status'].values()
                                    if status.get('overall_success', False))

        success_rate = (successful_integrations / total_roles) * 100 if total_roles > 0 else 0

        self.fix_results['summary'] = {
            'total_roles': total_roles,
            'successful_integrations': successful_integrations,
            'success_rate': round(success_rate, 1),
            'fixes_applied_count': len(self.fix_results['fixes_applied']),
            'errors_encountered_count': len(self.fix_results['errors_encountered'])
        }

        print('=' * 60)
        print(f'🎯 修复总结:')
        print(f'  总角色数: {total_roles}')
        print(f'  成功集成: {successful_integrations}')
        print(f'  成功率: {success_rate:.1f}%')
        print(f'  修复项目: {len(self.fix_results["fixes_applied"])}')
        print(f'  遇到错误: {len(self.fix_results["errors_encountered"])}')

        # 保存详细报告
        report_file = f'complete_roles_integration_fix_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.fix_results, f, ensure_ascii=False, indent=2)

        print(f'\n📄 详细修复报告已保存到: {report_file}')

async def main():
    """主函数"""
    fixer = CompleteRolesIntegrationFix()
    await fixer.fix_all_roles_integration()

if __name__ == "__main__":
    asyncio.run(main())
