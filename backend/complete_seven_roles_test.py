#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的七个角色自动化系统测试
验证DeepSeek人设配置、传奇记忆系统、绩效系统、层级系统的深度集成
"""

import asyncio
import sys
import os
import json
from datetime import datetime

sys.path.append(os.getcwd())

async def complete_seven_roles_test():
    """完整的七个角色自动化系统测试"""
    print('🚀 完整的七个角色自动化系统测试')
    print('=' * 60)
    
    test_results = {
        'timestamp': datetime.now().isoformat(),
        'role_tests': {},
        'system_integration_tests': {},
        'deepseek_integration_tests': {},
        'summary': {}
    }
    
    # 1. 测试每个角色的自动化系统
    print('\n🤖 测试角色自动化系统...')
    
    roles_config = {
        '天枢星': {
            'module': 'roles.tianshu_star.services.tianshu_automation_system',
            'instance_name': 'tianshu_automation_system',
            'test_method': 'execute_market_analysis'
        },
        '天璇星': {
            'module': 'roles.tianxuan_star.services.tianxuan_automation_system',
            'instance_name': 'tianxuan_automation_system',
            'test_method': 'execute_technical_analysis'
        },
        '天玑星': {
            'module': 'roles.tianji_star.services.tianji_automation_system',
            'instance_name': 'tianji_automation_system',
            'test_method': 'execute_risk_analysis'
        },
        '天权星': {
            'module': 'roles.tianquan_star.core.tianquan_automation_system',
            'instance_name': 'tianquan_automation_system',
            'test_method': 'execute_decision_automation'
        },
        '玉衡星': {
            'module': 'roles.yuheng_star.services.yuheng_automation_system',
            'instance_name': 'yuheng_automation_system',
            'test_method': 'execute_trading_automation'
        },
        '开阳星': {
            'module': 'roles.kaiyang_star.services.kaiyang_automation_system',
            'instance_name': 'kaiyang_automation_system',
            'test_method': 'execute_stock_selection_automation'
        },
        '瑶光星': {
            'module': 'roles.yaoguang_star.automation.quantitative_research_automation',
            'instance_name': 'quantitative_research_automation',
            'test_method': 'start_automation'
        }
    }
    
    working_roles = 0
    
    for role_name, config in roles_config.items():
        print(f'\n  🔍 测试 {role_name}...')
        
        role_test_result = {
            'automation_system_loaded': False,
            'has_required_methods': False,
            'deepseek_integration': False,
            'memory_integration': False,
            'performance_integration': False,
            'test_execution': False,
            'errors': []
        }
        
        try:
            # 导入自动化系统
            module = __import__(config['module'], fromlist=[''])
            automation_system = getattr(module, config['instance_name'], None)
            
            if automation_system:
                role_test_result['automation_system_loaded'] = True
                print(f'    ✅ 自动化系统加载成功')
                
                # 检查必需方法
                required_methods = ['start_automation', config['test_method']]
                has_methods = all(hasattr(automation_system, method) for method in required_methods)
                role_test_result['has_required_methods'] = has_methods
                
                if has_methods:
                    print(f'    ✅ 必需方法存在')
                else:
                    print(f'    ❌ 缺少必需方法')
                    role_test_result['errors'].append('缺少必需方法')
                
                # 检查DeepSeek集成
                deepseek_methods = ['_call_role_deepseek', 'deepseek_service', '_enhance_with_deepseek_analysis']
                has_deepseek = any(hasattr(automation_system, method) for method in deepseek_methods)
                role_test_result['deepseek_integration'] = has_deepseek
                
                if has_deepseek:
                    print(f'    ✅ DeepSeek集成存在')
                else:
                    print(f'    ⚠️ DeepSeek集成缺失')
                
                # 检查记忆系统集成
                memory_methods = ['memory_system', 'store_memory', '_init_memory_system']
                has_memory = any(hasattr(automation_system, method) for method in memory_methods)
                role_test_result['memory_integration'] = has_memory
                
                if has_memory:
                    print(f'    ✅ 记忆系统集成存在')
                else:
                    print(f'    ⚠️ 记忆系统集成缺失')
                
                # 检查绩效监控集成
                performance_methods = ['performance_monitor', 'record_performance', '_init_performance_monitor']
                has_performance = any(hasattr(automation_system, method) for method in performance_methods)
                role_test_result['performance_integration'] = has_performance
                
                if has_performance:
                    print(f'    ✅ 绩效监控集成存在')
                else:
                    print(f'    ⚠️ 绩效监控集成缺失')
                
                # 测试自动化执行
                try:
                    test_context = {
                        'stock_code': '000001.XSHE',
                        'task_type': 'test_automation',
                        'session_id': 'test_session'
                    }
                    
                    if hasattr(automation_system, config['test_method']):
                        # 尝试执行测试方法（不等待完成，只测试调用）
                        test_method = getattr(automation_system, config['test_method'])
                        if asyncio.iscoroutinefunction(test_method):
                            # 异步方法，创建任务但不等待
                            task = asyncio.create_task(test_method(test_context))
                            await asyncio.sleep(0.1)  # 短暂等待确保方法开始执行
                            if not task.done():
                                task.cancel()
                        else:
                            # 同步方法
                            test_method(test_context)
                        
                        role_test_result['test_execution'] = True
                        print(f'    ✅ 自动化方法执行测试通过')
                    else:
                        print(f'    ❌ 测试方法不存在: {config["test_method"]}')
                        role_test_result['errors'].append(f'测试方法不存在: {config["test_method"]}')
                        
                except Exception as e:
                    print(f'    ⚠️ 自动化方法执行测试失败: {e}')
                    role_test_result['errors'].append(f'执行测试失败: {e}')
                
                # 计算角色集成度
                integration_score = sum([
                    role_test_result['automation_system_loaded'],
                    role_test_result['has_required_methods'],
                    role_test_result['deepseek_integration'],
                    role_test_result['memory_integration'],
                    role_test_result['performance_integration']
                ])
                
                if integration_score >= 3:  # 至少3/5的集成才算成功
                    working_roles += 1
                    print(f'    🎯 {role_name} 集成度: {integration_score}/5 - 合格')
                else:
                    print(f'    ⚠️ {role_name} 集成度: {integration_score}/5 - 需要改进')
                
            else:
                print(f'    ❌ 自动化系统实例未找到')
                role_test_result['errors'].append('自动化系统实例未找到')
                
        except Exception as e:
            print(f'    ❌ 测试失败: {e}')
            role_test_result['errors'].append(f'测试失败: {e}')
        
        test_results['role_tests'][role_name] = role_test_result
    
    # 2. 测试系统级集成
    print('\n🔧 测试系统级集成...')
    
    # 测试传奇记忆系统
    print('  🧠 测试传奇记忆系统...')
    try:
        from core.domain.memory.legendary.interface import legendary_memory_interface
        
        memory_initialized = await legendary_memory_interface.initialize()
        memory_stats = legendary_memory_interface.get_memory_statistics()
        
        test_results['system_integration_tests']['legendary_memory'] = {
            'available': True,
            'initialized': memory_initialized,
            'statistics': memory_stats,
            'test_passed': memory_initialized and 'total_memories' in memory_stats
        }
        
        print(f'    ✅ 传奇记忆系统: 初始化={memory_initialized}, 统计={memory_stats}')
        
    except Exception as e:
        test_results['system_integration_tests']['legendary_memory'] = {
            'available': False,
            'error': str(e),
            'test_passed': False
        }
        print(f'    ❌ 传奇记忆系统测试失败: {e}')
    
    # 测试绩效监控系统
    print('  📊 测试绩效监控系统...')
    try:
        from core.performance.star_performance_monitor import star_performance_monitor
        
        perf_status = star_performance_monitor.get_system_status()
        
        test_results['system_integration_tests']['performance_monitor'] = {
            'available': True,
            'status': perf_status,
            'test_passed': 'monitoring_active' in perf_status
        }
        
        print(f'    ✅ 绩效监控系统: {perf_status}')
        
    except Exception as e:
        test_results['system_integration_tests']['performance_monitor'] = {
            'available': False,
            'error': str(e),
            'test_passed': False
        }
        print(f'    ❌ 绩效监控系统测试失败: {e}')
    
    # 3. 测试DeepSeek配置集成
    print('\n🧠 测试DeepSeek配置集成...')
    
    role_modules = ['tianshu_star', 'tianxuan_star', 'tianji_star', 'tianquan_star', 'yuheng_star', 'kaiyang_star', 'yaoguang_star']
    deepseek_working = 0
    
    for role_name in role_modules:
        try:
            print(f'  🔍 测试 {role_name} DeepSeek配置...')
            
            config_module = __import__(f'roles.{role_name}.config.deepseek_config', fromlist=[''])
            
            deepseek_test = {
                'config_loaded': True,
                'has_config_function': hasattr(config_module, 'get_deepseek_config'),
                'has_role_setting': False,
                'config_valid': False,
                'role_setting_content': None
            }
            
            # 测试配置函数
            if deepseek_test['has_config_function']:
                config = config_module.get_deepseek_config()
                deepseek_test['config_valid'] = 'temperature' in config and 'model' in config
            
            # 测试角色设定
            role_setting_attrs = [f'{role_name.upper()}_ROLE_SETTING', 'get_role_setting']
            for attr in role_setting_attrs:
                if hasattr(config_module, attr):
                    deepseek_test['has_role_setting'] = True
                    if callable(getattr(config_module, attr)):
                        deepseek_test['role_setting_content'] = getattr(config_module, attr)()
                    else:
                        deepseek_test['role_setting_content'] = getattr(config_module, attr)
                    break
            
            # 验证角色设定内容
            if deepseek_test['role_setting_content'] and len(deepseek_test['role_setting_content']) > 50:
                deepseek_working += 1
                print(f'    ✅ {role_name} DeepSeek配置完整')
            else:
                print(f'    ⚠️ {role_name} DeepSeek配置不完整')
            
            test_results['deepseek_integration_tests'][role_name] = deepseek_test
            
        except Exception as e:
            test_results['deepseek_integration_tests'][role_name] = {
                'config_loaded': False,
                'error': str(e)
            }
            print(f'    ❌ {role_name} DeepSeek配置测试失败: {e}')
    
    # 4. 生成测试总结
    print('\n📋 生成测试总结...')
    
    system_tests_passed = sum(1 for test in test_results['system_integration_tests'].values() 
                             if test.get('test_passed', False))
    
    test_results['summary'] = {
        'automation_systems_working': f'{working_roles}/7',
        'deepseek_configs_working': f'{deepseek_working}/7',
        'system_integration_tests_passed': f'{system_tests_passed}/{len(test_results["system_integration_tests"])}',
        'overall_integration_score': ((working_roles + deepseek_working + system_tests_passed) / 16) * 100,
        'test_timestamp': datetime.now().isoformat()
    }
    
    print('=' * 60)
    print(f'🎯 测试总结:')
    print(f'  自动化系统: {working_roles}/7 个角色正常')
    print(f'  DeepSeek配置: {deepseek_working}/7 个角色正常')
    print(f'  系统集成测试: {system_tests_passed}/{len(test_results["system_integration_tests"])} 通过')
    print(f'  整体集成度: {test_results["summary"]["overall_integration_score"]:.1f}%')
    
    # 保存测试报告
    report_file = f'complete_seven_roles_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(test_results, f, ensure_ascii=False, indent=2)
    
    print(f'\n📄 完整测试报告已保存到: {report_file}')
    
    return test_results

if __name__ == "__main__":
    result = asyncio.run(complete_seven_roles_test())
