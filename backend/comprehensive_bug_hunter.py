#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面Bug猎手 - 彻底排查所有问题并修复
"""

import os
import re
import json
import sqlite3
import requests
from pathlib import Path
from typing import Dict, List, Any

class ComprehensiveBugHunter:
    """全面Bug猎手"""
    
    def __init__(self):
        self.project_root = Path(".")
        self.bugs_found = []
        self.fixes_applied = []
        
    def hunt_tianshu_news_bugs(self):
        """猎杀天枢星新闻Bug"""
        print("🔍 猎杀天枢星新闻Bug...")
        
        # 1. 检查所有新闻网站配置
        news_config_files = [
            "backend/roles/tianshu_star/services/news_collection_service.py",
            "backend/roles/tianshu_star/services/crawl4ai_intelligence_service.py",
            "backend/roles/tianshu_star/services/unified_data_collector.py",
            "backend/services/core/enhanced_crawl4ai_service.py",
            "backend/services/news/config/news_sources.json"
        ]
        
        total_websites = 0
        for config_file in news_config_files:
            if Path(config_file).exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 查找网站配置
                    website_patterns = [
                        r'https?://[^\s"\']+',
                        r'"[^"]*\.com[^"]*"',
                        r'"[^"]*\.cn[^"]*"',
                        r'"[^"]*\.net[^"]*"'
                    ]
                    
                    websites = set()
                    for pattern in website_patterns:
                        matches = re.findall(pattern, content)
                        for match in matches:
                            if any(domain in match.lower() for domain in ['finance', 'money', 'stock', 'news', 'sina', 'eastmoney', 'xueqiu']):
                                websites.add(match.strip('"\''))
                    
                    if websites:
                        print(f"   📄 {Path(config_file).name}: 发现{len(websites)}个网站")
                        for site in sorted(websites)[:5]:  # 显示前5个
                            print(f"      - {site}")
                        total_websites += len(websites)
                        
                except Exception as e:
                    print(f"   ❌ 无法检查 {config_file}: {e}")
        
        print(f"   📊 总计发现 {total_websites} 个新闻网站配置")
        
        # 2. 检查Crawl4AI使用情况
        crawl4ai_usage = self._check_crawl4ai_usage()
        print(f"   🕷️ Crawl4AI使用情况: {crawl4ai_usage}")
        
        pass  # 专业版模式
        pass  # 专业版模式
        
        return {
            "total_websites": total_websites,
            "crawl4ai_usage": crawl4ai_usage,
        pass  # 专业版模式
        }
    
    def _check_crawl4ai_usage(self):
        """检查Crawl4AI使用情况"""
        crawl4ai_files = [
            "backend/roles/tianshu_star/services/news_collection_service.py",
            "backend/roles/tianshu_star/services/crawl4ai_service.py",
            "backend/roles/tianshu_star/services/crawl4ai_intelligence_service.py"
        ]
        
        usage_info = {}
        for file_path in crawl4ai_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查关键指标
                    has_import = "from crawl4ai import AsyncWebCrawler" in content
                    has_arun = "await crawler.arun(" in content or "await self.crawler.arun(" in content
                    has_real_crawl = "AsyncWebCrawler(" in content
        pass  # 专业版模式
                    has_sample = "sample" in content.lower() or "示例" in content
                    
                    usage_info[Path(file_path).name] = {
                        "has_import": has_import,
                        "has_arun": has_arun,
                        "has_real_crawl": has_real_crawl,
        pass  # 专业版模式
                        "has_sample": has_sample
                    }
                    
                except Exception as e:
                    usage_info[Path(file_path).name] = {"error": str(e)}
        
        return usage_info
    
    def _check_fallback_logic(self):
        pass  # 专业版模式
        issues = []
        
        python_files = list(self.project_root.glob("backend/**/*.py"))
        
        pass  # 专业版模式
        pass  # 专业版模式
            r'sample.*data',
            r'示例.*数据',
        pass  # 专业版模式
            r'_generate_sample',
            r'backup.*data',
            r'mock.*data'
        ]
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                
                for i, line in enumerate(lines, 1):
        pass  # 专业版模式
                        if re.search(pattern, line, re.IGNORECASE):
                            issues.append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "line": i,
                                "content": line.strip(),
                                "pattern": pattern,
                                "severity": "HIGH" if "sample" in pattern or "mock" in pattern else "MEDIUM"
                            })
                            
            except Exception as e:
                continue
        
        return issues
    
    def hunt_database_path_bugs(self):
        """猎杀数据库路径Bug"""
        print("\n🔍 猎杀数据库路径Bug...")
        
        # 检查所有被替换的路径是否正确
        audit_file = Path("backend/database_audit_results.json")
        if audit_file.exists():
            with open(audit_file, 'r', encoding='utf-8') as f:
                audit_data = json.load(f)
            
            # 检查每个引用
            path_issues = []
            for file_path, refs in audit_data.get("database_references", {}).items():
                for ref in refs.get("references", []):
                    if ref.endswith('.db'):
                        # 检查这个路径是否存在
                        if not Path(ref).exists():
                            path_issues.append({
                                "file": file_path,
                                "path": ref,
                                "issue": "路径不存在"
                            })
            
            print(f"   📊 发现路径问题: {len(path_issues)}个")
            for issue in path_issues[:10]:  # 显示前10个
                print(f"      ❌ {issue['file']}: {issue['path']}")
            
            return path_issues
        
        return []
    
    def hunt_intelligence_api_bugs(self):
        """猎杀intelligence API Bug"""
        print("\n🔍 猎杀intelligence API Bug...")
        
        # 测试intelligence API
        try:
            response = requests.get("http://127.0.0.1:8003/api/intelligence/news", timeout=10)
            if response.status_code == 200:
                data = response.json()
                response_text = str(data).lower()
                
                # 检查问题
                issues = []
                if "test" in response_text:
                    issues.append("包含test标识")
                if "sample" in response_text:
                    issues.append("包含sample标识")
                if "mock" in response_text:
                    issues.append("包含mock标识")
                if "example" in response_text:
                    issues.append("包含example标识")
                
                print(f"   📊 API问题: {issues}")
                return issues
            else:
                print(f"   ❌ API调用失败: {response.status_code}")
                return [f"API调用失败: {response.status_code}"]
                
        except Exception as e:
            print(f"   ❌ API测试失败: {e}")
            return [f"API测试失败: {e}"]
    
    def hunt_all_mock_data(self):
        """猎杀所有模拟数据"""
        print("\n🔍 猎杀所有模拟数据...")
        
        mock_patterns = [
            r'mock.*data',
            r'sample.*data',
            r'test.*data',
            r'fake.*data',
            r'dummy.*data',
            r'示例.*数据',
            r'模拟.*数据',
            r'测试.*数据',
        pass  # 专业版模式
            r'backup.*data'
        ]
        
        mock_issues = []
        python_files = list(self.project_root.glob("backend/**/*.py"))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                
                for i, line in enumerate(lines, 1):
                    for pattern in mock_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            mock_issues.append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "line": i,
                                "content": line.strip(),
                                "pattern": pattern
                            })
                            
            except Exception as e:
                continue
        
        print(f"   📊 发现模拟数据问题: {len(mock_issues)}个")
        return mock_issues
    
    def generate_fix_plan(self, tianshu_bugs, db_bugs, api_bugs, mock_bugs):
        """生成修复计划"""
        print("\n📋 生成修复计划...")
        
        fix_plan = {
            "timestamp": "2025-06-21 20:30:00",
        pass  # 专业版模式
            "fixes": []
        }
        
        # 天枢星修复
        pass  # 专业版模式
            fix_plan["fixes"].append({
                "category": "天枢星新闻系统",
                "priority": "CRITICAL",
        pass  # 专业版模式
        pass  # 专业版模式
            })
        
        # 数据库路径修复
        if db_bugs:
            fix_plan["fixes"].append({
                "category": "数据库路径",
                "priority": "HIGH",
                "issues": len(db_bugs),
                "action": "修复所有错误的数据库路径引用"
            })
        
        # API修复
        if api_bugs:
            fix_plan["fixes"].append({
                "category": "Intelligence API",
                "priority": "HIGH",
                "issues": len(api_bugs),
                "action": "移除所有test/sample标识"
            })
        
        # 模拟数据修复
        if mock_bugs:
            fix_plan["fixes"].append({
                "category": "模拟数据清理",
                "priority": "CRITICAL",
                "issues": len(mock_bugs),
                "action": "彻底清除所有模拟数据引用"
            })
        
        return fix_plan
    
    def run_comprehensive_hunt(self):
        """运行全面Bug猎杀"""
        print("🚀 开始全面Bug猎杀...")
        print("=" * 80)
        
        # 1. 猎杀天枢星Bug
        tianshu_bugs = self.hunt_tianshu_news_bugs()
        
        # 2. 猎杀数据库路径Bug
        db_bugs = self.hunt_database_path_bugs()
        
        # 3. 猎杀intelligence API Bug
        api_bugs = self.hunt_intelligence_api_bugs()
        
        # 4. 猎杀所有模拟数据
        mock_bugs = self.hunt_all_mock_data()
        
        # 5. 生成修复计划
        fix_plan = self.generate_fix_plan(tianshu_bugs, db_bugs, api_bugs, mock_bugs)
        
        print("\n" + "=" * 80)
        print("📊 Bug猎杀总结")
        print("=" * 80)
        print(f"🎯 总Bug数: {fix_plan['total_bugs']}")
        print(f"🔧 修复类别: {len(fix_plan['fixes'])}")
        
        for fix in fix_plan["fixes"]:
            print(f"   {fix['priority']} - {fix['category']}: {fix['issues']}个问题")
        
        # 保存详细报告
        with open("backend/comprehensive_bug_report.json", "w", encoding="utf-8") as f:
            json.dump({
                "tianshu_bugs": tianshu_bugs,
                "database_bugs": db_bugs,
                "api_bugs": api_bugs,
                "mock_bugs": mock_bugs,
                "fix_plan": fix_plan
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存: backend/comprehensive_bug_report.json")
        
        return fix_plan

def main():
    """主函数"""
    hunter = ComprehensiveBugHunter()
    fix_plan = hunter.run_comprehensive_hunt()
    
    print(f"\n🎯 发现 {fix_plan['total_bugs']} 个Bug，准备修复...")

if __name__ == "__main__":
    main()
