from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面审计数据库路径和调用情况
"""

import os
import re
import sqlite3
from pathlib import Path
from typing import Dict, List, Any

class DatabaseAudit:
    """数据库审计器"""
    
    def __init__(self):
        self.project_root = Path(".")
        self.database_references = {}
        self.actual_databases = {}
        
    def scan_actual_databases(self):
        """扫描实际存在的数据库文件"""
        print("🔍 扫描实际数据库文件...")
        
        data_dir = Path("backend/data")
        if data_dir.exists():
            db_files = list(data_dir.glob("*.db"))
            
            for db_file in db_files:
                try:
                    conn = sqlite3.connect(db_file)
                    cursor = conn.cursor()
                    
                    # 获取表信息
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    
                    # 统计记录数
                    total_records = 0
                    table_info = {}
                    for table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        total_records += count
                        table_info[table] = count
                    
                    size_mb = round(db_file.stat().st_size / 1024 / 1024, 2)
                    
                    self.actual_databases[db_file.name] = {
                        "path": str(db_file),
                        "tables": tables,
                        "table_counts": table_info,
                        "total_records": total_records,
                        "size_mb": size_mb
                    }
                    
                    print(f"   ✅ {db_file.name}: {len(tables)}表, {total_records}记录, {size_mb}MB")
                    
                    conn.close()
                    
                except Exception as e:
                    print(f"   ❌ {db_file.name}: 检查失败 - {e}")
        
        print(f"\n发现数据库文件: {list(self.actual_databases.keys())}")
    
    def scan_database_references(self):
        """扫描代码中的数据库引用"""
        print("\n🔍 扫描代码中的数据库引用...")
        
        # 扫描Python文件
        python_files = list(self.project_root.glob("backend/**/*.py"))
        
        db_patterns = [
            r'sqlite3\.connect\(["\']([^"\']+)["\']',
            r'Database\(["\']([^"\']+)["\']',
            r'db_path\s*=\s*["\']([^"\']+)["\']',
            r'database_path\s*=\s*["\']([^"\']+)["\']',
            r'\.db["\']',
            r'backend/data/[^"\']+\.db'
        ]
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                file_refs = []
                for pattern in db_patterns:
                    matches = re.findall(pattern, content)
                    file_refs.extend(matches)
                
                # 查找所有包含.db的行
                lines = content.split('\n')
                db_lines = []
                for i, line in enumerate(lines, 1):
                    if '.db' in line and ('sqlite' in line or 'database' in line or 'db_path' in line):
                        db_lines.append(f"第{i}行: {line.strip()}")
                
                if file_refs or db_lines:
                    rel_path = str(py_file.relative_to(self.project_root))
                    self.database_references[rel_path] = {
                        "references": file_refs,
                        "db_lines": db_lines
                    }
                    
            except Exception as e:
                print(f"   ⚠️ 无法读取 {py_file}: {e}")
        
        print(f"发现 {len(self.database_references)} 个文件包含数据库引用")
    
    def analyze_database_consistency(self):
        """分析数据库一致性"""
        print("\n📊 分析数据库一致性...")
        
        # 统计引用的数据库路径
        referenced_paths = set()
        for file_path, refs in self.database_references.items():
            for ref in refs["references"]:
                if ref.endswith('.db'):
                    referenced_paths.add(ref)
        
        actual_paths = set(self.actual_databases.keys())
        
        print(f"代码中引用的数据库: {len(referenced_paths)}")
        for path in sorted(referenced_paths):
            print(f"   - {path}")
        
        print(f"\n实际存在的数据库: {len(actual_paths)}")
        for path in sorted(actual_paths):
            print(f"   - {path}")
        
        # 找出不一致的地方
        missing_files = referenced_paths - actual_paths
        unused_files = actual_paths - referenced_paths
        
        if missing_files:
            print(f"\n❌ 代码引用但文件不存在: {missing_files}")
        
        if unused_files:
            print(f"\n⚠️ 文件存在但代码未引用: {unused_files}")
        
        return missing_files, unused_files
    
    def check_seven_stars_database_usage(self):
        """检查七星角色的数据库使用情况"""
        print("\n⭐ 检查七星角色数据库使用...")
        
        star_roles = [
            "tianshu_star",
            "tianxuan_star", 
            "tianji_star",
            "tianquan_star",
            "yuheng_star",
            "kaiyang_star",
            "yaoguang_star"
        ]
        
        for role in star_roles:
            print(f"\n🌟 {role}:")
            role_files = list(self.project_root.glob(f"backend/roles/{role}/**/*.py"))
            
            role_db_refs = []
            for py_file in role_files:
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 查找数据库相关代码
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if '.db' in line and ('sqlite' in line or 'database' in line or 'db_path' in line):
                            rel_path = str(py_file.relative_to(self.project_root))
                            role_db_refs.append(f"   {rel_path}:{i} - {line.strip()}")
                            
                except Exception as e:
                    continue
            
            if role_db_refs:
                for ref in role_db_refs[:5]:  # 显示前5个
                    print(ref)
                if len(role_db_refs) > 5:
                    print(f"   ... 还有 {len(role_db_refs)-5} 个引用")
            else:
                print("   未发现数据库引用")
    
    def generate_unified_database_config(self):
        """生成统一的数据库配置"""
        print("\n🔧 生成统一数据库配置建议...")
        
        # 基于实际存在的数据库文件生成配置
        config = {
            "database_config": {
                "base_path": "backend/data",
                "databases": {}
            }
        }
        
        # 数据库映射
        db_mapping = {
            "stock_database.db": {
                "name": "stock_database",
                "description": "股票数据库",
                "tables": ["stock_info", "daily_data", "technical_indicators"],
                "roles": ["yaoguang_star", "kaiyang_star", "yuheng_star"]
            },
            "legendary_memory.db": {
                "name": "legendary_memory",
                "description": "传奇记忆数据库", 
                "tables": ["memories"],
                "roles": ["all_stars"]
            },
            "news_knowledge_base.db": {
                "name": "news_knowledge",
                "description": "新闻知识库",
                "tables": ["news_items", "news_analysis"],
                "roles": ["tianshu_star"]
            },
            "risk_database.db": {
                "name": "risk_database",
                "description": "风险数据库",
                "tables": ["risk_items"],
                "roles": ["tianji_star"]
            },
            "historical_data.db": {
                "name": "historical_data",
                "description": "历史数据库",
                "tables": ["historical_records"],
                "roles": ["yaoguang_star", "tianxuan_star"]
            },
            "trading_execution.db": {
                "name": "trading_execution",
                "description": "交易执行数据库",
                "tables": ["execution_records"],
                "roles": ["yuheng_star"]
            },
            "tianshu_news_analysis.db": {
                "name": "tianshu_news_analysis",
                "description": "天枢星新闻分析库",
                "tables": ["news_analysis", "signal_evaluations"],
                "roles": ["tianshu_star"]
            },
            "tianji_strategy_risk.db": {
                "name": "tianji_strategy_risk", 
                "description": "天玑星风险策略库",
                "tables": ["strategy_risk"],
                "roles": ["tianji_star"]
            }
        }
        
        for db_file, info in self.actual_databases.items():
            if db_file in db_mapping:
                mapping = db_mapping[db_file]
                config["database_config"]["databases"][mapping["name"]] = {
                    "file": db_file,
                    "path": f"backend/data/{db_file}",
                    "description": mapping["description"],
                    "actual_tables": info["tables"],
                    "expected_tables": mapping["tables"],
                    "total_records": info["total_records"],
                    "size_mb": info["size_mb"],
                    "used_by_roles": mapping["roles"]
                }
        
        print("统一数据库配置:")
        for db_name, db_info in config["database_config"]["databases"].items():
            print(f"   {db_name}:")
            print(f"     文件: {db_info['file']}")
            print(f"     记录数: {db_info['total_records']}")
            print(f"     使用角色: {db_info['used_by_roles']}")
        
        return config
    
    def run_audit(self):
        """运行完整审计"""
        print("🚀 开始数据库全面审计...")
        print("=" * 80)
        
        # 1. 扫描实际数据库
        self.scan_actual_databases()
        
        # 2. 扫描代码引用
        self.scan_database_references()
        
        # 3. 分析一致性
        missing, unused = self.analyze_database_consistency()
        
        # 4. 检查七星角色使用
        self.check_seven_stars_database_usage()
        
        # 5. 生成统一配置
        config = self.generate_unified_database_config()
        
        print("\n" + "=" * 80)
        print("📊 审计总结")
        print("=" * 80)
        print(f"✅ 实际数据库文件: {len(self.actual_databases)}")
        print(f"📋 代码引用文件: {len(self.database_references)}")
        print(f"❌ 缺失文件: {len(missing)}")
        print(f"⚠️ 未使用文件: {len(unused)}")
        
        if missing:
            print(f"\n🔧 需要修复的问题:")
            for missing_file in missing:
                print(f"   - 代码引用了不存在的数据库: {missing_file}")
        
        return {
            "actual_databases": self.actual_databases,
            "database_references": self.database_references,
            "missing_files": missing,
            "unused_files": unused,
            "unified_config": config
        }

def main():
    """主函数"""
    auditor = DatabaseAudit()
    results = auditor.run_audit()
    
    # 保存审计结果
    import json
    with open("backend/database_audit_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 审计结果已保存: backend/database_audit_results.json")

if __name__ == "__main__":
    main()
