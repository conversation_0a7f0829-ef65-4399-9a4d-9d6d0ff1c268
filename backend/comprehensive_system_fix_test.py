#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合系统修复测试
验证瑶光星数据收集和七个角色自动化系统的修复效果
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any, List
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComprehensiveSystemFixTest:
    """综合系统修复测试"""
    
    def __init__(self):
        self.test_results = {
            "yaoguang_data_collection": {},
            "seven_roles_automation": {},
            "deepseek_integration": {},
            "system_integration": {},
            "overall_status": {}
        }
        
    async def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始综合系统修复测试")
        print("=" * 80)
        
        # 1. 测试瑶光星数据收集服务
        await self.test_yaoguang_data_collection()
        
        # 2. 测试七个角色自动化系统
        await self.test_seven_roles_automation()
        
        # 3. 测试DeepSeek集成
        await self.test_deepseek_integration()
        
        # 4. 测试系统集成
        await self.test_system_integration()
        
        # 5. 生成综合报告
        await self.generate_comprehensive_report()
        
    async def test_yaoguang_data_collection(self):
        """测试瑶光星数据收集服务"""
        print("\n📊 测试瑶光星数据收集服务...")
        print("-" * 50)
        
        try:
            from roles.yaoguang_star.services.comprehensive_data_collection_service import comprehensive_data_collection_service
            
            # 测试服务初始化
            print("  🔍 测试服务初始化...")
            if hasattr(comprehensive_data_collection_service, 'db_path'):
                print("    ✅ 数据库路径配置正确")
                self.test_results["yaoguang_data_collection"]["db_config"] = True
            else:
                print("    ❌ 数据库路径配置错误")
                self.test_results["yaoguang_data_collection"]["db_config"] = False
            
            # 测试统计信息获取
            print("  📈 测试统计信息获取...")
            stats = await comprehensive_data_collection_service.get_collection_stats()
            if stats:
                print(f"    ✅ 统计信息获取成功: {len(stats)} 项")
                self.test_results["yaoguang_data_collection"]["stats"] = True
            else:
                print("    ❌ 统计信息获取失败")
                self.test_results["yaoguang_data_collection"]["stats"] = False
            
            # 测试开阳星选股注册
            print("  🎯 测试开阳星选股注册...")
            register_result = await comprehensive_data_collection_service.register_kaiyang_selection(
                "000001", "测试选股"
            )
            if register_result:
                print("    ✅ 开阳星选股注册成功")
                self.test_results["yaoguang_data_collection"]["kaiyang_register"] = True
            else:
                print("    ❌ 开阳星选股注册失败")
                self.test_results["yaoguang_data_collection"]["kaiyang_register"] = False
            
            # 测试股票数据获取
            print("  📋 测试股票数据获取...")
            stock_data = await comprehensive_data_collection_service.get_stock_data_for_other_stars("000001")
            if stock_data:
                print(f"    ✅ 股票数据获取成功: {stock_data.get('stock_code', 'N/A')}")
                self.test_results["yaoguang_data_collection"]["data_retrieval"] = True
            else:
                print("    ❌ 股票数据获取失败")
                self.test_results["yaoguang_data_collection"]["data_retrieval"] = False
            
            print("  🌟 瑶光星数据收集服务测试完成")
            
        except Exception as e:
            print(f"  ❌ 瑶光星数据收集服务测试失败: {e}")
            self.test_results["yaoguang_data_collection"]["error"] = str(e)
    
    async def test_seven_roles_automation(self):
        """测试七个角色自动化系统"""
        print("\n🤖 测试七个角色自动化系统...")
        print("-" * 50)
        
        roles_config = {
            "tianshu_star": "roles.tianshu_star.services.tianshu_automation_system",
            "tianxuan_star": "roles.tianxuan_star.services.tianxuan_automation_system",
            "tianji_star": "roles.tianji_star.services.tianji_automation_system",
            "tianquan_star": "roles.tianquan_star.core.tianquan_automation_system",
            "yuheng_star": "roles.yuheng_star.services.yuheng_automation_system",
            "kaiyang_star": "roles.kaiyang_star.services.kaiyang_automation_system",
            "yaoguang_star": "roles.yaoguang_star.automation.quantitative_research_automation"
        }
        
        for role_name, module_path in roles_config.items():
            print(f"  🔍 测试 {role_name}...")
            
            try:
                # 动态导入模块
                module_parts = module_path.split('.')
                module = __import__(module_path, fromlist=[module_parts[-1]])
                
                # 获取自动化系统实例
                automation_system = None
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if hasattr(attr, 'start_automation') or hasattr(attr, 'execute_automation'):
                        automation_system = attr
                        break
                
                if automation_system:
                    print(f"    ✅ {role_name} 自动化系统加载成功")
                    
                    # 测试DeepSeek集成 - 检查方法而不是属性
                    if hasattr(automation_system, '_call_role_deepseek'):
                        print(f"    ✅ {role_name} DeepSeek集成存在")
                        self.test_results["seven_roles_automation"][f"{role_name}_deepseek"] = True
                    else:
                        print(f"    ⚠️ {role_name} DeepSeek集成缺失")
                        self.test_results["seven_roles_automation"][f"{role_name}_deepseek"] = False

                    # 测试记忆系统集成 - 检查方法而不是属性
                    if hasattr(automation_system, 'store_memory') and hasattr(automation_system, 'retrieve_memories'):
                        print(f"    ✅ {role_name} 记忆系统集成存在")
                        self.test_results["seven_roles_automation"][f"{role_name}_memory"] = True
                    else:
                        print(f"    ⚠️ {role_name} 记忆系统集成缺失")
                        self.test_results["seven_roles_automation"][f"{role_name}_memory"] = False

                    # 测试绩效监控集成 - 检查方法而不是属性
                    if hasattr(automation_system, 'record_performance') and hasattr(automation_system, 'get_performance_stats'):
                        print(f"    ✅ {role_name} 绩效监控集成存在")
                        self.test_results["seven_roles_automation"][f"{role_name}_performance"] = True
                    else:
                        print(f"    ⚠️ {role_name} 绩效监控集成缺失")
                        self.test_results["seven_roles_automation"][f"{role_name}_performance"] = False
                    
                    # 检查是否有降级模式
                    module_source = str(module.__file__) if hasattr(module, '__file__') else ""
                    if "fallback" in module_source.lower() or "简化" in module_source:
                        print(f"    ⚠️ {role_name} 可能存在降级模式")
                        self.test_results["seven_roles_automation"][f"{role_name}_fallback"] = True
                    else:
                        print(f"    ✅ {role_name} 无降级模式")
                        self.test_results["seven_roles_automation"][f"{role_name}_fallback"] = False
                    
                    self.test_results["seven_roles_automation"][f"{role_name}_status"] = "success"
                    
                else:
                    print(f"    ❌ {role_name} 自动化系统未找到")
                    self.test_results["seven_roles_automation"][f"{role_name}_status"] = "not_found"
                    
            except Exception as e:
                print(f"    ❌ {role_name} 测试失败: {e}")
                self.test_results["seven_roles_automation"][f"{role_name}_status"] = f"error: {str(e)}"
        
        print("  🤖 七个角色自动化系统测试完成")
    
    async def test_deepseek_integration(self):
        """测试DeepSeek集成"""
        print("\n🧠 测试DeepSeek集成...")
        print("-" * 50)
        
        try:
            from shared.infrastructure.deepseek_service import deepseek_service
            
            # 测试服务初始化
            print("  🔍 测试DeepSeek服务初始化...")
            await deepseek_service.initialize()
            
            if deepseek_service.is_connected:
                print("    ✅ DeepSeek服务连接成功")
                self.test_results["deepseek_integration"]["connection"] = True
            else:
                print("    ⚠️ DeepSeek服务连接失败，使用备用模式")
                self.test_results["deepseek_integration"]["connection"] = False
            
            # 测试基本功能
            print("  💬 测试基本聊天功能...")
            test_messages = [
                {"role": "user", "content": "测试消息"}
            ]
            
            result = await deepseek_service.chat_completion(test_messages, max_tokens=50)
            
            if result.get("success"):
                print("    ✅ DeepSeek聊天功能正常")
                self.test_results["deepseek_integration"]["chat"] = True
            else:
                print(f"    ⚠️ DeepSeek聊天功能异常: {result.get('error', 'Unknown error')}")
                self.test_results["deepseek_integration"]["chat"] = False
            
            # 测试备用分析功能
            print("  🔄 测试备用分析功能...")
            if hasattr(deepseek_service, '_fallback_analysis'):
                print("    ✅ 备用分析功能存在")
                self.test_results["deepseek_integration"]["fallback"] = True
            else:
                print("    ❌ 备用分析功能缺失")
                self.test_results["deepseek_integration"]["fallback"] = False
            
            print("  🧠 DeepSeek集成测试完成")
            
        except Exception as e:
            print(f"  ❌ DeepSeek集成测试失败: {e}")
            self.test_results["deepseek_integration"]["error"] = str(e)
    
    async def test_system_integration(self):
        """测试系统集成"""
        print("\n🔗 测试系统集成...")
        print("-" * 50)
        
        try:
            # 测试传奇记忆系统
            print("  🧠 测试传奇记忆系统...")
            try:
                from core.domain.memory.legendary.interface import legendary_memory_interface
                await legendary_memory_interface.initialize()
                
                if legendary_memory_interface.is_initialized:
                    print("    ✅ 传奇记忆系统初始化成功")
                    self.test_results["system_integration"]["legendary_memory"] = True
                else:
                    print("    ❌ 传奇记忆系统初始化失败")
                    self.test_results["system_integration"]["legendary_memory"] = False
                    
            except Exception as e:
                print(f"    ❌ 传奇记忆系统测试失败: {e}")
                self.test_results["system_integration"]["legendary_memory"] = False
            
            # 测试绩效监控系统
            print("  📊 测试绩效监控系统...")
            try:
                from core.performance.star_performance_monitor import star_performance_monitor

                # 测试绩效监控的核心方法
                if hasattr(star_performance_monitor, 'record_performance'):
                    print("    ✅ 绩效监控系统存在")
                    self.test_results["system_integration"]["performance_monitor"] = True
                else:
                    print("    ❌ 绩效监控系统缺失")
                    self.test_results["system_integration"]["performance_monitor"] = False
                    
            except Exception as e:
                print(f"    ❌ 绩效监控系统测试失败: {e}")
                self.test_results["system_integration"]["performance_monitor"] = False
            
            print("  🔗 系统集成测试完成")
            
        except Exception as e:
            print(f"  ❌ 系统集成测试失败: {e}")
            self.test_results["system_integration"]["error"] = str(e)
    
    async def generate_comprehensive_report(self):
        """生成综合报告"""
        print("\n📋 生成综合测试报告...")
        print("=" * 80)
        
        # 计算总体状态
        total_tests = 0
        passed_tests = 0
        
        for category, results in self.test_results.items():
            if category != "overall_status":
                for test_name, result in results.items():
                    if isinstance(result, bool):
                        total_tests += 1
                        if result:
                            passed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        self.test_results["overall_status"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": success_rate,
            "test_time": datetime.now().isoformat()
        }
        
        # 显示结果
        print(f"📊 测试统计:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过测试: {passed_tests}")
        print(f"  成功率: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 系统修复效果优秀!")
        elif success_rate >= 75:
            print("✅ 系统修复效果良好!")
        elif success_rate >= 60:
            print("⚠️ 系统修复效果一般，需要进一步优化")
        else:
            print("❌ 系统修复效果不佳，需要重新修复")
        
        # 保存报告
        report_filename = f"comprehensive_fix_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细报告已保存到: {report_filename}")

async def main():
    """主函数"""
    test_runner = ComprehensiveSystemFixTest()
    await test_runner.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
