#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据库配置
所有七星角色使用此配置文件获取正确的数据库路径
"""

from pathlib import Path
import os

# 数据库基础路径 - 相对于项目根目录
# 无论从哪里运行，都能正确找到数据库
project_root = Path(__file__).parent.parent.parent  # 回到项目根目录
DATABASE_BASE_PATH = project_root / "backend" / "data"

# 统一数据库路径配置
DATABASE_PATHS = {
    # 主要数据库
    "stock_database": DATABASE_BASE_PATH / "stock_database.db",
    "legendary_memory": DATABASE_BASE_PATH / "legendary_memory.db",
    "news_knowledge": DATABASE_BASE_PATH / "news_knowledge_base.db",
    "risk_database": DATABASE_BASE_PATH / "risk_database.db",
    "historical_data": DATABASE_BASE_PATH / "historical_data.db",
    "trading_execution": DATABASE_BASE_PATH / "trading_execution.db",
    
    # 角色专用数据库
    "tianshu_news_analysis": DATABASE_BASE_PATH / "tianshu_news_analysis.db",
    "tianxuan_trigger_research": DATABASE_BASE_PATH / "tianxuan_trigger_research.db",
    "tianji_strategy_risk": DATABASE_BASE_PATH / "tianji_strategy_risk.db",
    "tianquan_strategies": DATABASE_BASE_PATH / "tianquan_strategies.db",
    "yuheng_execution": DATABASE_BASE_PATH / "yuheng_execution_optimization.db",
    "kaiyang_portfolio": DATABASE_BASE_PATH / "kaiyang_portfolio.db",
    "yaoguang_distribution": DATABASE_BASE_PATH / "yaoguang_distribution.db",
    
    # 系统数据库
    "advanced_execution": DATABASE_BASE_PATH / "advanced_execution_system.db",
    "separated_trading": DATABASE_BASE_PATH / "separated_trading_statistics.db",
    "alerts": DATABASE_BASE_PATH / "alerts.db",
    "decision_making": DATABASE_BASE_PATH / "decision_making.db"
}

def get_database_path(db_name: str) -> str:
    """获取数据库路径"""
    if db_name in DATABASE_PATHS:
        return str(DATABASE_PATHS[db_name])
    else:
        raise ValueError(f"未知的数据库名称: {db_name}")

def get_all_database_paths() -> dict:
    """获取所有数据库路径"""
    return {name: str(path) for name, path in DATABASE_PATHS.items()}
