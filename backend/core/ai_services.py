#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一AI服务模块
整合DeepSeek、DISC-FinLLM等AI服务
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class UnifiedAIService:
    """统一AI服务"""
    
    def __init__(self):
        self.service_name = "UnifiedAIService"
        self.version = "1.0.0"
        
        # AI服务实例
        self.deepseek_service = None
        self.disc_finllm_service = None
        
        # 服务状态
        self.services_status = {
            "deepseek": False,
            "disc_finllm": False
        }
        
        # 初始化AI服务
        self._initialize_ai_services()
        
        logger.info(f" {self.service_name} v{self.version} 初始化完成")
    
    def _initialize_ai_services(self):
        """初始化AI服务"""
        try:
            # 初始化DeepSeek服务
            try:
                from shared.infrastructure.deepseek_service import deepseek_service
                self.deepseek_service = deepseek_service
                self.services_status["deepseek"] = True
                logger.info(" DeepSeek服务初始化成功")
            except Exception as e:
                logger.warning(f"⚠️ DeepSeek服务初始化失败: {e}")
                self.services_status["deepseek"] = False
            
            # 初始化DISC-FinLLM服务
            try:
                from services.ai.disc_finllm_intelligence_service import DISCFinLLMIntelligenceService
                self.disc_finllm_service = DISCFinLLMIntelligenceService()
                self.services_status["disc_finllm"] = True
                logger.info(" DISC-FinLLM服务初始化成功")
            except Exception as e:
                logger.warning(f"⚠️ DISC-FinLLM服务初始化失败: {e}")
                self.services_status["disc_finllm"] = False
                
        except Exception as e:
            logger.error(f"AI服务初始化失败: {e}")
    
    async def chat_completion(self, messages: List[Dict[str, str]], model: str = "auto", **kwargs) -> Dict[str, Any]:
        """统一聊天完成接口"""
        try:
            # 优先使用DeepSeek
            if self.services_status["deepseek"] and self.deepseek_service:
                try:
                    response = await self.deepseek_service.chat_completion(
                        messages=messages,
                        model="deepseek-chat",
                        **kwargs
                    )
                    if response.get("success"):
                        return {
                            "success": True,
                            "content": response.get("content", response.get("response", "")),
                            "model": "deepseek-chat",
                            "service": "deepseek"
                        }
                except Exception as e:
                    logger.warning(f"DeepSeek调用失败: {e}")
            
            # 备用：使用DISC-FinLLM
            if self.services_status["disc_finllm"] and self.disc_finllm_service:
                try:
                    # 提取用户消息
                    user_message = ""
                    for msg in messages:
                        if msg.get("role") == "user":
                            user_message = msg.get("content", "")
                            break
                    
                    if user_message:
                        response = await self.disc_finllm_service.analyze_financial_query(user_message)
                        return {
                            "success": True,
                            "content": response.get("analysis", ""),
                            "model": "disc-finllm",
                            "service": "disc_finllm"
                        }
                except Exception as e:
                    logger.warning(f"DISC-FinLLM调用失败: {e}")
            
            # 如果所有服务都不可用，返回基础回复
            return {
                "success": False,
                "content": "AI服务暂时不可用，请稍后重试",

                "service": "none",
                "error": "所有AI服务不可用"
            }
            
        except Exception as e:
            logger.error(f"统一AI服务调用失败: {e}")
            return {
                "success": False,
                "content": "AI服务调用失败",
                "error": str(e)
            }
    
    async def analyze_financial_text(self, text: str, analysis_type: str = "general") -> Dict[str, Any]:
        """金融文本分析"""
        try:
            # 构建分析提示
            prompt = self._build_financial_analysis_prompt(text, analysis_type)
            
            # 调用AI服务
            response = await self.chat_completion([
                {"role": "user", "content": prompt}
            ])
            
            if response.get("success"):
                return {
                    "success": True,
                    "analysis": response.get("content", ""),
                    "analysis_type": analysis_type,
                    "service_used": response.get("service", "unknown"),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "error": response.get("error", "分析失败"),
                    "analysis_type": analysis_type
                }
                
        except Exception as e:
            logger.error(f"金融文本分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis_type": analysis_type
            }
    
    def _build_financial_analysis_prompt(self, text: str, analysis_type: str) -> str:
        """构建金融分析提示"""
        base_prompt = f"""
作为专业的金融分析师，请分析以下文本：

文本内容：
{text}

分析类型：{analysis_type}

请提供：
1. 核心观点总结
2. 市场影响分析
3. 投资建议（如适用）
4. 风险提示

请用专业、客观的语言回答。
"""
        
        if analysis_type == "news":
            base_prompt += "\n特别关注：新闻的真实性、时效性和对市场的潜在影响。"
        elif analysis_type == "sentiment":
            base_prompt += "\n特别关注：文本的情感倾向和市场情绪指标。"
        elif analysis_type == "risk":
            base_prompt += "\n特别关注：潜在风险因素和风险等级评估。"
        
        return base_prompt
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "service_name": self.service_name,
            "version": self.version,
            "services_status": self.services_status,
            "available_services": [k for k, v in self.services_status.items() if v],
            "total_services": len(self.services_status),
            "healthy_services": sum(self.services_status.values()),
            "timestamp": datetime.now().isoformat()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            "overall_status": "healthy",
            "services": {},
            "timestamp": datetime.now().isoformat()
        }
        
        # 检查DeepSeek服务
        if self.deepseek_service:
            try:
                test_response = await self.deepseek_service.chat_completion(
                    messages=[{"role": "user", "content": "Hello"}],
                    model="deepseek-chat",
                    max_tokens=10
                )
                health_status["services"]["deepseek"] = {
                    "status": "healthy" if test_response.get("success") else "unhealthy",
                    "response_time": "< 1s",
                    "last_check": datetime.now().isoformat()
                }
            except Exception as e:
                health_status["services"]["deepseek"] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "last_check": datetime.now().isoformat()
                }
        else:
            health_status["services"]["deepseek"] = {
                "status": "not_available",
                "last_check": datetime.now().isoformat()
            }
        
        # 检查DISC-FinLLM服务
        if self.disc_finllm_service:
            try:
                test_response = await self.disc_finllm_service.analyze_financial_query("测试")
                health_status["services"]["disc_finllm"] = {
                    "status": "healthy" if test_response else "unhealthy",
                    "response_time": "< 1s",
                    "last_check": datetime.now().isoformat()
                }
            except Exception as e:
                health_status["services"]["disc_finllm"] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "last_check": datetime.now().isoformat()
                }
        else:
            health_status["services"]["disc_finllm"] = {
                "status": "not_available",
                "last_check": datetime.now().isoformat()
            }
        
        # 计算整体状态
        unhealthy_count = sum(1 for service in health_status["services"].values() 
                             if service["status"] == "unhealthy")
        
        if unhealthy_count == 0:
            health_status["overall_status"] = "healthy"
        elif unhealthy_count < len(health_status["services"]):
            health_status["overall_status"] = "degraded"
        else:
            health_status["overall_status"] = "unhealthy"
        
        return health_status

# 全局实例
unified_ai_service = UnifiedAIService()

# 便捷函数
async def ai_chat_completion(messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
    """便捷的AI聊天完成函数"""
    return await unified_ai_service.chat_completion(messages, **kwargs)

async def ai_analyze_financial_text(text: str, analysis_type: str = "general") -> Dict[str, Any]:
    """便捷的金融文本分析函数"""
    return await unified_ai_service.analyze_financial_text(text, analysis_type)

def get_ai_service_status() -> Dict[str, Any]:
    """便捷的服务状态获取函数"""
    return unified_ai_service.get_service_status()

__all__ = [
    "UnifiedAIService", "unified_ai_service",
    "ai_chat_completion", "ai_analyze_financial_text", "get_ai_service_status"
]
