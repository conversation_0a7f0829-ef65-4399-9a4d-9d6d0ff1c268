from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础领域模型
定义系统中使用的基础数据结构和模型
"""

import asyncio
import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict, field
from enum import Enum
import sqlite3
import aiosqlite
from pathlib import Path

logger = logging.getLogger(__name__)

# ==================== 枚举类型 ====================

class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ImportanceLevel(Enum):
    """重要性等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class SentimentLabel(Enum):
    """情感标签"""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"

class EventType(Enum):
    """事件类型"""
    CORPORATE = "corporate"
    POLICY = "policy"
    MARKET = "market"
    INDUSTRY = "industry"
    INTERNATIONAL = "international"
    EMERGENCY = "emergency"

# ==================== 基础数据模型 ====================

@dataclass
class BaseRequest:
    """基础请求模型"""
    request_id: str = field(default_factory=lambda: f"req_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}")
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    user_id: Optional[str] = None
    session_id: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

@dataclass
class BaseResponse:
    """基础响应模型"""
    success: bool
    message: str
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    request_id: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

@dataclass
class BaseModel:
    """基础模型"""
    id: str
    created_time: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_time: str = field(default_factory=lambda: datetime.now().isoformat())

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)

@dataclass
class RiskItem:
    """风险项模型"""
    id: str
    risk_type: str
    risk_level: str
    description: str
    affected_symbols: List[str] = field(default_factory=list)
    risk_score: float = 0.0
    mitigation_suggestions: List[str] = field(default_factory=list)
    source: str = ""
    detection_time: str = field(default_factory=lambda: datetime.now().isoformat())
    is_active: bool = True
    tags: List[str] = field(default_factory=list)
    created_time: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_time: str = field(default_factory=lambda: datetime.now().isoformat())

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)

@dataclass
class MarketEvent:
    """市场事件模型"""
    id: str
    event_type: str
    title: str
    description: str
    importance_level: str
    impact_scope: str
    confidence_score: float
    event_time: str
    related_symbols: List[str] = field(default_factory=list)
    source_news_id: str = ""
    key_entities: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    created_time: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_time: str = field(default_factory=lambda: datetime.now().isoformat())

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)

@dataclass
class NewsItem:
    """新闻项模型"""
    id: str
    title: str
    content: str
    source: str
    url: str
    timestamp: str
    sentiment_score: float
    sentiment_label: str
    importance_level: str
    related_symbols: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    authority_score: float = 0.0
    relevance_score: float = 0.0
    created_time: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_time: str = field(default_factory=lambda: datetime.now().isoformat())

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)

@dataclass
class AnalysisResult:
    """分析结果模型"""
    id: str
    analysis_type: str
    target_id: str  # 分析目标ID（新闻ID、事件ID等）
    result_data: Dict[str, Any] = field(default_factory=dict)
    confidence_score: float = 0.0
    analyzer_version: str = ""
    analysis_time: str = field(default_factory=lambda: datetime.now().isoformat())
    created_time: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_time: str = field(default_factory=lambda: datetime.now().isoformat())

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)

@dataclass
class PushMessage:
    """推送消息模型"""
    id: str
    message_type: str
    content: Dict[str, Any]
    targets: List[str]
    priority: str
    status: str = "pending"
    retry_count: int = 0
    scheduled_time: str = field(default_factory=lambda: datetime.now().isoformat())
    sent_time: Optional[str] = None
    created_time: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_time: str = field(default_factory=lambda: datetime.now().isoformat())

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)

# ==================== 风险数据库服务 ====================

class RiskDatabaseService:
    """风险数据库服务"""
    
    def __init__(self, db_path: str = None):
        self.service_name = "RiskDatabaseService"
        self.version = "1.0.0"
        
        # 数据库路径
        if db_path is None:
            db_path = "backend/data/risk_database.db"
        
        self.db_path = db_path
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        logger.info(f" {self.service_name} v{self.version} 初始化完成")
        logger.info(f" 数据库路径: {self.db_path}")
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建风险项表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS risk_items (
                    id TEXT PRIMARY KEY,
                    risk_type TEXT NOT NULL,
                    risk_level TEXT NOT NULL,
                    description TEXT,
                    affected_symbols TEXT,  -- JSON格式存储
                    risk_score REAL,
                    mitigation_suggestions TEXT,  -- JSON格式存储
                    source TEXT,
                    detection_time TEXT,
                    is_active BOOLEAN,
                    tags TEXT,  -- JSON格式存储
                    created_time TEXT,
                    updated_time TEXT
                )
            ''')
            
            # 创建市场事件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_events (
                    id TEXT PRIMARY KEY,
                    event_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    importance_level TEXT,
                    impact_scope TEXT,
                    confidence_score REAL,
                    event_time TEXT,
                    related_symbols TEXT,  -- JSON格式存储
                    source_news_id TEXT,
                    key_entities TEXT,  -- JSON格式存储
                    tags TEXT,  -- JSON格式存储
                    created_time TEXT,
                    updated_time TEXT
                )
            ''')
            
            # 创建分析结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analysis_results (
                    id TEXT PRIMARY KEY,
                    analysis_type TEXT NOT NULL,
                    target_id TEXT NOT NULL,
                    result_data TEXT,  -- JSON格式存储
                    confidence_score REAL,
                    analyzer_version TEXT,
                    analysis_time TEXT,
                    created_time TEXT,
                    updated_time TEXT
                )
            ''')
            
            # 创建推送消息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS push_messages (
                    id TEXT PRIMARY KEY,
                    message_type TEXT NOT NULL,
                    content TEXT,  -- JSON格式存储
                    targets TEXT,  -- JSON格式存储
                    priority TEXT,
                    status TEXT,
                    retry_count INTEGER,
                    scheduled_time TEXT,
                    sent_time TEXT,
                    created_time TEXT,
                    updated_time TEXT
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_risk_level ON risk_items (risk_level)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_risk_active ON risk_items (is_active)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_event_type ON market_events (event_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_event_time ON market_events (event_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_type ON analysis_results (analysis_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_push_status ON push_messages (status)')
            
            conn.commit()
            conn.close()
            
            logger.info(" 风险数据库表初始化完成")
            
        except Exception as e:
            logger.error(f"风险数据库初始化失败: {e}")
            raise
    
    async def store_risk_item(self, risk_item: RiskItem) -> bool:
        """存储风险项"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT OR REPLACE INTO risk_items (
                        id, risk_type, risk_level, description, affected_symbols,
                        risk_score, mitigation_suggestions, source, detection_time,
                        is_active, tags, created_time, updated_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    risk_item.id,
                    risk_item.risk_type,
                    risk_item.risk_level,
                    risk_item.description,
                    json.dumps(risk_item.affected_symbols, ensure_ascii=False),
                    risk_item.risk_score,
                    json.dumps(risk_item.mitigation_suggestions, ensure_ascii=False),
                    risk_item.source,
                    risk_item.detection_time,
                    risk_item.is_active,
                    json.dumps(risk_item.tags, ensure_ascii=False),
                    risk_item.created_time,
                    risk_item.updated_time
                ))
                await db.commit()
            
            logger.debug(f"风险项存储成功: {risk_item.id}")
            return True
            
        except Exception as e:
            logger.error(f"风险项存储失败: {e}")
            return False
    
    async def store_market_event(self, event: MarketEvent) -> bool:
        """存储市场事件"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT OR REPLACE INTO market_events (
                        id, event_type, title, description, importance_level,
                        impact_scope, confidence_score, event_time, related_symbols,
                        source_news_id, key_entities, tags, created_time, updated_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    event.id,
                    event.event_type,
                    event.title,
                    event.description,
                    event.importance_level,
                    event.impact_scope,
                    event.confidence_score,
                    event.event_time,
                    json.dumps(event.related_symbols, ensure_ascii=False),
                    event.source_news_id,
                    json.dumps(event.key_entities, ensure_ascii=False),
                    json.dumps(event.tags, ensure_ascii=False),
                    event.created_time,
                    event.updated_time
                ))
                await db.commit()
            
            logger.debug(f"市场事件存储成功: {event.id}")
            return True
            
        except Exception as e:
            logger.error(f"市场事件存储失败: {e}")
            return False
    
    async def get_active_risks(self, risk_level: str = None) -> List[RiskItem]:
        """获取活跃风险项"""
        try:
            query = "SELECT * FROM risk_items WHERE is_active = 1"
            params = []
            
            if risk_level:
                query += " AND risk_level = ?"
                params.append(risk_level)
            
            query += " ORDER BY risk_score DESC"
            
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute(query, params) as cursor:
                    rows = await cursor.fetchall()
                    return [self._row_to_risk_item(row) for row in rows]
                    
        except Exception as e:
            logger.error(f"获取活跃风险失败: {e}")
            return []
    
    async def get_recent_events(self, days: int = 7) -> List[MarketEvent]:
        """获取最近事件"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT * FROM market_events 
                    WHERE event_time >= ?
                    ORDER BY event_time DESC
                ''', (cutoff_date,)) as cursor:
                    rows = await cursor.fetchall()
                    return [self._row_to_market_event(row) for row in rows]
                    
        except Exception as e:
            logger.error(f"获取最近事件失败: {e}")
            return []
    
    def _row_to_risk_item(self, row) -> RiskItem:
        """将数据库行转换为RiskItem对象"""
        return RiskItem(
            id=row[0],
            risk_type=row[1],
            risk_level=row[2],
            description=row[3] or "",
            affected_symbols=json.loads(row[4]) if row[4] else [],
            risk_score=row[5] or 0.0,
            mitigation_suggestions=json.loads(row[6]) if row[6] else [],
            source=row[7] or "",
            detection_time=row[8] or "",
            is_active=bool(row[9]),
            tags=json.loads(row[10]) if row[10] else [],
            created_time=row[11] or "",
            updated_time=row[12] or ""
        )
    
    def _row_to_market_event(self, row) -> MarketEvent:
        """将数据库行转换为MarketEvent对象"""
        return MarketEvent(
            id=row[0],
            event_type=row[1],
            title=row[2],
            description=row[3] or "",
            importance_level=row[4] or "medium",
            impact_scope=row[5] or "individual",
            confidence_score=row[6] or 0.0,
            event_time=row[7] or "",
            related_symbols=json.loads(row[8]) if row[8] else [],
            source_news_id=row[9] or "",
            key_entities=json.loads(row[10]) if row[10] else [],
            tags=json.loads(row[11]) if row[11] else [],
            created_time=row[12] or "",
            updated_time=row[13] or ""
        )

# 全局实例 - 延迟初始化，避免导入时自动创建
risk_database_service = None

def get_risk_database_service():
    """获取风险数据库服务实例（延迟初始化）"""
    global risk_database_service
    if risk_database_service is None:
        try:
            risk_database_service = RiskDatabaseService()
        except Exception as e:
            logger.warning(f"风险数据库服务初始化失败，使用空实例: {e}")
            risk_database_service = None
    return risk_database_service

__all__ = [
    "BaseRequest", "BaseResponse", "BaseModel", "RiskItem", "MarketEvent", "NewsItem", "AnalysisResult", "PushMessage",
    "RiskLevel", "ImportanceLevel", "SentimentLabel", "EventType",
    "RiskDatabaseService", "get_risk_database_service"
]
