#!/usr/bin/env python3
"""
传奇级记忆协调器 - 修复版本
统一管理和协调所有记忆操作的核心组件
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from .interface import LegendaryMemoryInterface
from .storage import LegendaryMemoryStorage
from .models import (
    LegendaryMemoryRecord, MemoryContext, UserMarking, 
    MemoryOperationResult, MemoryScope, MemoryPriority,
    MessageType, UserMarkingType
)

logger = logging.getLogger(__name__)

# 现有系统可用性标志
EXISTING_SYSTEMS_AVAILABLE = False

class LegendaryMemoryCoordinator:
    """传奇级记忆协调器"""
    
    def __init__(self):
        self.storage = LegendaryMemoryStorage()
        self.memory_cache: Dict[str, LegendaryMemoryRecord] = {}
        self.cache_max_size = 1000
        self.is_initialized = False
        
        # 统计信息
        self.stats = {
            "operations_count": 0,
            "total_memories": 0,
            "user_marked_count": 0,
            "high_importance_count": 0
        }
        
        # 角色映射
        self.role_mappings = {
            "天权星": "tianquan_commander",
            "天璇星": "tianxuan_architect", 
            "天玑星": "tianji_risk_manager",
            "天枢星": "tianshu_intelligence",
            "玉衡星": "yuheng_executor",
            "瑶光星": "yaoguang_admin",
            "开阳星": "kaiyang_secretary"
        }
        
        logger.info("[COORDINATOR] 传奇级记忆协调器初始化完成")

    async def initialize(self) -> bool:
        """初始化协调器"""
        try:
            if self.is_initialized:
                logger.warning("协调器已初始化，跳过重复初始化")
                return True

            logger.info("[INIT] 开始初始化传奇级记忆协调器...")

            # 初始化存储系统
            storage_success = await self.storage.initialize()
            if not storage_success:
                raise Exception("存储系统初始化失败")

            # 加载现有记忆
            await self._load_existing_memories()

            # 初始化现有系统集成
            if EXISTING_SYSTEMS_AVAILABLE:
                await self._initialize_existing_systems_integration()

            # 更新统计信息
            await self._update_statistics()

            self.is_initialized = True
            logger.info("[SUCCESS] 传奇级记忆协调器初始化成功")

            return True

        except Exception as e:
            logger.error(f"[ERROR] 传奇级记忆协调器初始化失败: {e}")
            return False

    async def add_memory(self, content: str, context: MemoryContext,
                         user_marking: Optional[UserMarking] = None) -> MemoryOperationResult:
        """添加记忆 - 传奇级统一入口"""
        start_time = datetime.now()

        try:
            # 1. 创建传奇级记忆记录
            memory = await self._create_legendary_record(content, context)

            # 2. 处理用户标记 (最高优先级)
            if user_marking:
                await self._handle_user_marking(memory, user_marking)

            # 3. 计算重要性
            await self._calculate_importance(memory, context)

            # 4. 保存到传奇级存储
            save_success = await self.storage.save_memory(memory)
            if not save_success:
                raise Exception("传奇级存储保存失败")

            # 5. 添加到缓存
            self._add_to_cache(memory)

            # 6. 集成到现有系统 (保持兼容)
            if EXISTING_SYSTEMS_AVAILABLE:
                await self._integrate_with_existing_systems(memory)

            # 7. 更新统计
            self.stats["operations_count"] += 1
            self.stats["total_memories"] += 1
            if memory.user_marked:
                self.stats["user_marked_count"] += 1
            if memory.final_importance >= 0.8:
                self.stats["high_importance_count"] += 1

            execution_time = (datetime.now() - start_time).total_seconds()

            logger.info(f"[SUCCESS] 记忆添加成功: {memory.id} - 重要性: {memory.final_importance:.3f}")

            return MemoryOperationResult(
                success=True,
                memory_id=memory.id,
                message="记忆添加成功",
                details={
                    "final_importance": memory.final_importance,
                    "user_marked": memory.user_marked,
                    "role_source": memory.role_source,
                    "protection_level": memory.protection_level
                },
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"[ERROR] 记忆添加失败: {e}")

            return MemoryOperationResult(
                success=False,
                message=f"记忆添加失败: {str(e)}",
                execution_time=execution_time
            )

    async def get_memory(self, memory_id: str) -> Optional[LegendaryMemoryRecord]:
        """获取记忆"""
        try:
            # 1. 先从缓存查找
            if memory_id in self.memory_cache:
                memory = self.memory_cache[memory_id]
                memory.update_access()
                await self.storage.save_memory(memory)  # 更新访问信息
                return memory

            # 2. 从存储加载
            memory = await self.storage.load_memory(memory_id)
            if memory:
                memory.update_access()
                self._add_to_cache(memory)
                await self.storage.save_memory(memory)
                return memory

            return None

        except Exception as e:
            logger.error(f"[ERROR] 获取记忆失败 {memory_id}: {e}")
            return None

    def get_role_mappings(self) -> Dict[str, str]:
        """获取角色映射"""
        return self.role_mappings.copy()

    async def semantic_search(self, query_text: str, role: Optional[str] = None,
                            limit: int = 10) -> List[LegendaryMemoryRecord]:
        """语义搜索记忆"""
        try:
            # 1. 先获取基础搜索结果
            base_results = await self.storage.search_memories(role=role, limit=limit*3)  # 获取更多候选

            if not base_results:
                return []

            # 2. 尝试向量搜索
            vector_results = await self._vector_search(query_text, base_results, limit)
            if vector_results:
                return vector_results

            keyword_results = await self._keyword_search(query_text, base_results, limit)
            return keyword_results

        except Exception as e:
            logger.error(f"[ERROR] 语义搜索失败: {e}")
            return []

    async def _vector_search(self, query_text: str, candidates: List[LegendaryMemoryRecord],
                           limit: int) -> List[LegendaryMemoryRecord]:
        """向量搜索"""
        try:
            # 尝试使用scikit-learn进行向量搜索
            try:
                from sklearn.feature_extraction.text import TfidfVectorizer
                from sklearn.metrics.pairwise import cosine_similarity
                import numpy as np

                # 准备文本数据
                texts = [query_text] + [memory.content for memory in candidates]

                # TF-IDF向量化
                vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
                tfidf_matrix = vectorizer.fit_transform(texts)

                # 计算相似度
                query_vector = tfidf_matrix[0:1]
                doc_vectors = tfidf_matrix[1:]
                similarities = cosine_similarity(query_vector, doc_vectors).flatten()

                # 排序并返回结果
                scored_results = list(zip(candidates, similarities))
                scored_results.sort(key=lambda x: x[1], reverse=True)

                # 过滤低相似度结果
                filtered_results = [memory for memory, score in scored_results if score > 0.1]

                logger.debug(f"[VECTOR_SEARCH] 向量搜索完成: {len(filtered_results)}个结果")
                return filtered_results[:limit]

            except ImportError:
                logger.warning("[VECTOR_SEARCH] scikit-learn不可用，跳过向量搜索")
                return []

        except Exception as e:
            logger.error(f"[ERROR] 向量搜索失败: {e}")
            return []

    async def _keyword_search(self, query_text: str, candidates: List[LegendaryMemoryRecord],
                            limit: int) -> List[LegendaryMemoryRecord]:
        """关键词搜索"""
        try:
            query_lower = query_text.lower()
            query_keywords = set(query_lower.split())

            scored_results = []

            for memory in candidates:
                content_lower = memory.content.lower()
                content_words = set(content_lower.split())

                # 计算关键词匹配分数
                exact_matches = len([word for word in query_keywords if word in content_lower])
                word_matches = len(query_keywords.intersection(content_words))

                # 综合评分
                score = exact_matches * 2 + word_matches

                if score > 0:
                    scored_results.append((memory, score))

            # 排序并返回结果
            scored_results.sort(key=lambda x: x[1], reverse=True)

            logger.debug(f"[KEYWORD_SEARCH] 关键词搜索完成: {len(scored_results)}个结果")
            return [memory for memory, score in scored_results[:limit]]

        except Exception as e:
            logger.error(f"[ERROR] 关键词搜索失败: {e}")
            return []

    async def _create_legendary_record(self, content: str, context: MemoryContext) -> LegendaryMemoryRecord:
        """创建传奇级记忆记录"""
        return LegendaryMemoryRecord(
            content=content,
            message_type=context.message_type,
            role_source=context.role,
            scope=context.scope,
            priority=context.priority,
            metadata=context.metadata,
            tags=context.tags,
            created_at=datetime.now()
        )

    async def _handle_user_marking(self, memory: LegendaryMemoryRecord, marking: UserMarking):
        """处理用户标记"""
        memory.user_marked = True
        memory.user_marking_type = marking.type
        memory.user_marking_reason = marking.reason
        memory.updated_at = datetime.now()

        # 根据标记类型设置保护
        if marking.type == UserMarkingType.ESSENTIAL:
            memory.reminder_date = marking.reminder_date
            memory.protection_level = "essential"
        elif marking.type == UserMarkingType.PERMANENT:
            memory.forgetting_immunity = True
            memory.protection_level = "permanent"
            memory.priority = MemoryPriority.CRITICAL
        elif marking.type == UserMarkingType.TEMPORARY_IMPORTANT:
            memory.protection_level = "temporary_important"

        logger.debug(f"用户标记处理完成: {memory.id} - 类型: {marking.type.value}")

    async def _calculate_importance(self, memory: LegendaryMemoryRecord, context: MemoryContext):
        """计算记忆重要性"""
        # 基础重要性评估
        system_importance = await self._evaluate_system_importance(memory, context)
        role_importance = await self._evaluate_role_importance(memory, context)

        memory.system_importance = system_importance
        memory.role_importance = role_importance
        memory.final_importance = await self._calculate_final_importance(memory)

    async def _calculate_final_importance(self, memory: LegendaryMemoryRecord) -> float:
        """计算最终重要性"""
        # 基础权重
        weights = {
            "system": 0.4,  # 系统评估权重
            "role": 0.3,    # 角色专业权重
            "user": 0.3     # 用户标记权重
        }

        # 用户标记加权
        user_weight = 0.0
        if memory.user_marked and memory.user_marking_type:
            if memory.user_marking_type == UserMarkingType.PERMANENT:
                user_weight = 1.0  # 永久记忆最高权重
            elif memory.user_marking_type == UserMarkingType.ESSENTIAL:
                user_weight = 0.9  # 必要记忆很高权重
            elif memory.user_marking_type == UserMarkingType.TEMPORARY_IMPORTANT:
                user_weight = 0.7  # 临时重要中等权重

        # 计算加权平均
        final_importance = (
            memory.system_importance * weights["system"] +
            memory.role_importance * weights["role"] +
            user_weight * weights["user"]
        )

        return min(max(final_importance, 0.0), 1.0)

    async def _evaluate_system_importance(self, memory: LegendaryMemoryRecord, context: MemoryContext) -> float:
        """评估系统重要性"""
        # 消息类型权重
        type_weights = {
            MessageType.INVESTMENT_DECISION: 0.95,
            MessageType.STRATEGY_PLANNING: 0.85,
            MessageType.RISK_ASSESSMENT: 0.80,
            MessageType.MARKET_ANALYSIS: 0.75,
            MessageType.TRADING_EXECUTION: 0.65,
            MessageType.NEWS_UPDATE: 0.60,
            MessageType.SYSTEM_NOTIFICATION: 0.45,
            MessageType.GENERAL: 0.40
        }

        return type_weights.get(memory.message_type, 0.5)

    async def _evaluate_role_importance(self, memory: LegendaryMemoryRecord, context: MemoryContext) -> float:
        """评估角色专业重要性"""
        # 角色专业权重
        role_weights = {
            "天权星": 0.9,  # 决策指挥官 - 最高权重
            "天璇星": 0.8,  # 策略架构师
            "天玑星": 0.8,  # 风险管理师
            "天枢星": 0.7,  # 情报分析官
            "玉衡星": 0.6,  # 交易执行官
            "瑶光星": 0.7,  # 系统管理员
            "开阳星": 0.5   # 秘书接口
        }

        return role_weights.get(memory.role_source, 0.5)

    def _add_to_cache(self, memory: LegendaryMemoryRecord):
        """添加到缓存"""
        # 如果缓存已满，移除最旧的记忆
        if len(self.memory_cache) >= self.cache_max_size:
            oldest_id = min(self.memory_cache.keys(),
                           key=lambda k: self.memory_cache[k].last_accessed or self.memory_cache[k].created_at)
            del self.memory_cache[oldest_id]

        self.memory_cache[memory.id] = memory

    async def _load_existing_memories(self):
        """加载现有记忆到缓存"""
        try:
            logger.info(f"[CACHE] 初始化记忆缓存")
        except Exception as e:
            logger.error(f"[ERROR] 加载现有记忆失败: {e}")

    async def _update_statistics(self):
        """更新统计信息"""
        try:
            self.stats["cache_size"] = len(self.memory_cache)
            logger.debug(f"[STATS] 统计信息已更新")
        except Exception as e:
            logger.error(f"[ERROR] 更新统计信息失败: {e}")

    async def _initialize_existing_systems_integration(self):
        """初始化现有系统集成"""
        # 预留接口，暂时不实现
        pass

    async def _integrate_with_existing_systems(self, memory: LegendaryMemoryRecord):
        """集成到现有系统"""
        # 预留接口，暂时不实现
        pass
