#!/usr/bin/env python3
"""
传奇级记忆接口
为各角色提供统一的记忆访问接口
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from .models import (
    LegendaryMemoryRecord, MemoryContext, UserMarking,
    MemoryOperationResult, MemoryScope, MemoryPriority,
    MessageType, UserMarkingType
)

logger = logging.getLogger(__name__)

class LegendaryMemoryInterface:
    """传奇级记忆接口"""
    
    def __init__(self):
        self.coordinator = None
        self.is_initialized = False

        logger.info("[INTERFACE] 传奇级记忆接口初始化完成")

    async def initialize(self) -> bool:
        """初始化接口"""
        try:
            if self.is_initialized:
                return True

            # 延迟导入避免循环导入
            from .coordinator import LegendaryMemoryCoordinator
            self.coordinator = LegendaryMemoryCoordinator()

            # 初始化协调器
            success = await self.coordinator.initialize()
            if success:
                self.is_initialized = True
                logger.info("[INTERFACE] 传奇级记忆接口初始化成功")

            return success

        except Exception as e:
            logger.error(f"[ERROR] 传奇级记忆接口初始化失败: {e}")
            return False

    async def add_memory(self, content: str, role: str, message_type: MessageType = MessageType.GENERAL,
                         scope: MemoryScope = MemoryScope.ROLE, priority: MemoryPriority = MemoryPriority.NORMAL,
                         metadata: Optional[Dict[str, Any]] = None, tags: Optional[List[str]] = None,
                         user_marking: Optional[UserMarking] = None) -> MemoryOperationResult:
        """添加记忆"""
        try:
            # 确保已初始化
            if not self.is_initialized or not self.coordinator:
                await self.initialize()

            # 创建记忆上下文
            context = MemoryContext(
                role=role,
                message_type=message_type,
                scope=scope,
                priority=priority,
                metadata=metadata or {},
                tags=tags or []
            )

            # 通过协调器添加记忆
            result = await self.coordinator.add_memory(content, context, user_marking)
            
            logger.debug(f"[INTERFACE] 记忆添加: {role} - {result.success}")
            
            return result
            
        except Exception as e:
            logger.error(f"[ERROR] 接口添加记忆失败: {e}")
            return MemoryOperationResult(
                success=False,
                message=f"接口添加记忆失败: {str(e)}"
            )

    async def get_memory(self, memory_id: str) -> Optional[LegendaryMemoryRecord]:
        """获取记忆"""
        try:
            return await self.coordinator.get_memory(memory_id)
        except Exception as e:
            logger.error(f"[ERROR] 接口获取记忆失败: {e}")
            return None

    async def search_memories(self, role: Optional[str] = None, message_type: Optional[MessageType] = None,
                             tags: Optional[List[str]] = None, limit: int = 50) -> List[LegendaryMemoryRecord]:
        """搜索记忆"""
        try:
            # 确保已初始化
            if not self.is_initialized or not self.coordinator:
                await self.initialize()

            # 调用存储层的搜索功能
            results = await self.coordinator.storage.search_memories(role, message_type, limit)

            # 如果有标签过滤，进一步筛选
            if tags and results:
                filtered_results = []
                for memory in results:
                    memory_tags = set(memory.tags) if memory.tags else set()
                    search_tags = set(tags)
                    if memory_tags.intersection(search_tags):
                        filtered_results.append(memory)
                results = filtered_results[:limit]

            logger.debug(f"[INTERFACE] 记忆搜索: role={role}, type={message_type}, 结果={len(results)}条")
            return results

        except Exception as e:
            logger.error(f"[ERROR] 接口搜索记忆失败: {e}")
            return []

    async def semantic_search_memories(self, query_text: str, role: Optional[str] = None,
                                     limit: int = 10) -> List[LegendaryMemoryRecord]:
        """语义搜索记忆"""
        try:
            # 确保已初始化
            if not self.is_initialized or not self.coordinator:
                await self.initialize()

            # 调用协调器的语义搜索功能
            results = await self.coordinator.semantic_search(query_text, role, limit)

            logger.debug(f"[INTERFACE] 语义搜索: query='{query_text}', role={role}, 结果={len(results)}条")
            return results

        except Exception as e:
            logger.error(f"[ERROR] 语义搜索失败: {e}")
            return []

    def get_role_mappings(self) -> Dict[str, str]:
        """获取角色映射"""
        return self.coordinator.get_role_mappings()

    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            if not self.is_initialized or not self.coordinator:
                await self.initialize()

            if self.coordinator and hasattr(self.coordinator, 'storage'):
                return await self.coordinator.storage.get_statistics()
            else:
                return {"total_memories": 0, "role_stats": {}, "error": "coordinator not available"}
        except Exception as e:
            logger.error(f"[ERROR] 获取统计信息失败: {e}")
            return {"total_memories": 0, "role_stats": {}, "error": str(e)}

    def get_memory_statistics(self) -> Dict[str, Any]:
        """同步获取记忆统计信息（兼容性方法）"""
        try:
            if not self.is_initialized or not self.coordinator:
                return {"total_memories": 0, "role_stats": {}, "error": "not initialized"}

            # 简单的同步统计
            return {
                "total_memories": len(getattr(self.coordinator, 'memory_cache', {})),
                "role_stats": {},
                "cache_size": len(getattr(self.coordinator, 'memory_cache', {})),
                "is_initialized": self.is_initialized
            }
        except Exception as e:
            logger.error(f"[ERROR] 获取记忆统计失败: {e}")
            return {"total_memories": 0, "role_stats": {}, "error": str(e)}

    # 便捷方法 - 为各角色提供专用接口
    async def add_tianquan_memory(self, content: str, message_type: MessageType = MessageType.STRATEGY_PLANNING,
                                  user_marking: Optional[UserMarking] = None) -> MemoryOperationResult:
        """天权星专用记忆添加"""
        return await self.add_memory(content, "天权星", message_type, MemoryScope.SYSTEM, 
                                   MemoryPriority.HIGH, user_marking=user_marking)

    async def add_tianji_memory(self, content: str, message_type: MessageType = MessageType.RISK_ASSESSMENT,
                                user_marking: Optional[UserMarking] = None) -> MemoryOperationResult:
        """天玑星专用记忆添加"""
        return await self.add_memory(content, "天玑星", message_type, MemoryScope.ROLE, 
                                   MemoryPriority.HIGH, user_marking=user_marking)

    async def add_tianshu_memory(self, content: str, message_type: MessageType = MessageType.NEWS_UPDATE,
                                 user_marking: Optional[UserMarking] = None) -> MemoryOperationResult:
        """天枢星专用记忆添加"""
        return await self.add_memory(content, "天枢星", message_type, MemoryScope.ROLE, 
                                   MemoryPriority.NORMAL, user_marking=user_marking)

    async def add_tianxuan_memory(self, content: str, message_type: MessageType = MessageType.MARKET_ANALYSIS,
                                  user_marking: Optional[UserMarking] = None) -> MemoryOperationResult:
        """天璇星专用记忆添加"""
        return await self.add_memory(content, "天璇星", message_type, MemoryScope.ROLE, 
                                   MemoryPriority.NORMAL, user_marking=user_marking)

    async def add_kaiyang_memory(self, content: str, message_type: MessageType = MessageType.GENERAL,
                                 user_marking: Optional[UserMarking] = None) -> MemoryOperationResult:
        """开阳星专用记忆添加"""
        return await self.add_memory(content, "开阳星", message_type, MemoryScope.ROLE,
                                   MemoryPriority.LOW, user_marking=user_marking)

    async def add_yuheng_memory(self, content: str, message_type: MessageType = MessageType.TRADING_EXECUTION,
                                user_marking: Optional[UserMarking] = None) -> MemoryOperationResult:
        """玉衡星专用记忆添加"""
        return await self.add_memory(content, "玉衡星", message_type, MemoryScope.ROLE,
                                   MemoryPriority.HIGH, user_marking=user_marking)

    async def add_yaoguang_memory(self, content: str, message_type: MessageType = MessageType.GENERAL,
                                  user_marking: Optional[UserMarking] = None) -> MemoryOperationResult:
        """瑶光星专用记忆添加"""
        return await self.add_memory(content, "瑶光星", message_type, MemoryScope.ROLE,
                                   MemoryPriority.NORMAL, user_marking=user_marking)

    # 兼容性方法 - 支持旧版本的importance参数
    async def add_memory_simple(self, content: str, role: str, importance: float = 0.5) -> str:
        pass
        try:
            # 根据importance确定优先级
            if importance >= 0.8:
                priority = MemoryPriority.HIGH
            elif importance >= 0.5:
                priority = MemoryPriority.NORMAL
            else:
                priority = MemoryPriority.LOW

            result = await self.add_memory(
                content=content,
                role=role,
                message_type=MessageType.GENERAL,
                priority=priority
            )

            return result.memory_id if result.success else ""

        except Exception as e:
            pass
            return ""

    # 便捷搜索方法
    async def search_yuheng_memories(self, query: str, memory_type: Optional[str] = None,
                                   limit: int = 10) -> List[Dict[str, Any]]:
        """搜索玉衡星记忆"""
        try:
            # 转换消息类型
            msg_type = None
            if memory_type:
                type_mapping = {
                    "trading_execution": MessageType.TRADING_EXECUTION,
                    "order_management": MessageType.ORDER_MANAGEMENT,
                    "position_management": MessageType.POSITION_MANAGEMENT,
                    "risk_control": MessageType.RISK_CONTROL,
                    "performance_analysis": MessageType.PERFORMANCE_ANALYSIS
                }
                msg_type = type_mapping.get(memory_type.lower())

            # 搜索记忆
            memories = await self.search_memories(role="玉衡星", message_type=msg_type, limit=limit)

            # 转换为字典格式
            result = []
            for memory in memories:
                result.append({
                    "id": memory.id,
                    "content": memory.content,
                    "message_type": memory.message_type.value if memory.message_type else "general",
                    "timestamp": memory.created_at.isoformat(),
                    "metadata": memory.metadata
                })

            return result

        except Exception as e:
            logger.error(f"搜索玉衡星记忆失败: {e}")
            return []

    async def get_recent_yuheng_memories(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取玉衡星最近记忆"""
        try:
            # 搜索玉衡星的所有记忆
            memories = await self.search_memories(role="玉衡星", limit=limit)

            # 按时间排序（最新的在前）
            memories.sort(key=lambda x: x.created_at, reverse=True)

            # 转换为字典格式
            result = []
            for memory in memories[:limit]:
                result.append({
                    "id": memory.id,
                    "content": memory.content,
                    "message_type": memory.message_type.value if memory.message_type else "general",
                    "timestamp": memory.created_at.isoformat(),
                    "metadata": memory.metadata,
                    "tags": memory.tags or []
                })

            return result

        except Exception as e:
            logger.error(f"获取玉衡星最近记忆失败: {e}")
            return []

# 全局实例
legendary_memory_interface = LegendaryMemoryInterface()

__all__ = ['LegendaryMemoryInterface', 'legendary_memory_interface']
