from backend.config.database_config import get_database_path
#!/usr/bin/env python3
"""
传奇级记忆存储
负责记忆的持久化存储和检索
"""

import json
import sqlite3
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from .models import LegendaryMemoryRecord, MessageType, MemoryScope, MemoryPriority, UserMarkingType

logger = logging.getLogger(__name__)

class LegendaryMemoryStorage:
    """传奇级记忆存储"""
    
    def __init__(self, db_path: str = "backend/data/legendary_memory.db"):
        self.db_path = db_path
        self.is_initialized = False
        
        # 确保数据目录存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"[STORAGE] 传奇级记忆存储初始化: {db_path}")

    async def initialize(self) -> bool:
        """初始化存储"""
        try:
            if self.is_initialized:
                return True
                
            # 创建数据库表
            await self._create_tables()
            
            self.is_initialized = True
            logger.info("[STORAGE] 传奇级记忆存储初始化成功")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] 传奇级记忆存储初始化失败: {e}")
            return False

    async def _create_tables(self):
        """创建数据库表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建记忆表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS legendary_memories (
                    id TEXT PRIMARY KEY,
                    content TEXT NOT NULL,
                    message_type TEXT NOT NULL,
                    role_source TEXT NOT NULL,
                    scope TEXT NOT NULL,
                    priority TEXT NOT NULL,
                    metadata TEXT,
                    tags TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT,
                    last_accessed TEXT,
                    access_count INTEGER DEFAULT 0,
                    system_importance REAL DEFAULT 0.0,
                    role_importance REAL DEFAULT 0.0,
                    final_importance REAL DEFAULT 0.0,
                    user_marked BOOLEAN DEFAULT FALSE,
                    user_marking_type TEXT,
                    user_marking_reason TEXT,
                    reminder_date TEXT,
                    forgetting_immunity BOOLEAN DEFAULT FALSE,
                    protection_level TEXT DEFAULT 'normal',
                    content_vector TEXT
                )
            ''')

            # 创建向量存储表（如果使用ChromaDB）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memory_vectors (
                    memory_id TEXT PRIMARY KEY,
                    vector_data TEXT NOT NULL,
                    vector_model TEXT DEFAULT 'tfidf',
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (memory_id) REFERENCES legendary_memories (id)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_role_source ON legendary_memories(role_source)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_message_type ON legendary_memories(message_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_final_importance ON legendary_memories(final_importance)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_created_at ON legendary_memories(created_at)')
            
            conn.commit()
            conn.close()
            
            logger.debug("[STORAGE] 数据库表创建完成")
            
        except Exception as e:
            logger.error(f"[ERROR] 创建数据库表失败: {e}")
            raise

    async def save_memory(self, memory: LegendaryMemoryRecord) -> bool:
        """保存记忆"""
        try:
            # 确保数据库表已创建
            if not self.is_initialized:
                await self.initialize()

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 准备数据
            data = (
                memory.id,
                memory.content,
                memory.message_type.value,
                memory.role_source,
                memory.scope.value,
                memory.priority.value,
                json.dumps(memory.metadata),
                json.dumps(memory.tags),
                memory.created_at.isoformat(),
                memory.updated_at.isoformat() if memory.updated_at else None,
                memory.last_accessed.isoformat() if memory.last_accessed else None,
                memory.access_count,
                memory.system_importance,
                memory.role_importance,
                memory.final_importance,
                memory.user_marked,
                memory.user_marking_type.value if memory.user_marking_type else None,
                memory.user_marking_reason,
                memory.reminder_date.isoformat() if memory.reminder_date else None,
                memory.forgetting_immunity,
                memory.protection_level
            )
            
            # 插入或更新
            cursor.execute('''
                INSERT OR REPLACE INTO legendary_memories (
                    id, content, message_type, role_source, scope, priority,
                    metadata, tags, created_at, updated_at, last_accessed, access_count,
                    system_importance, role_importance, final_importance,
                    user_marked, user_marking_type, user_marking_reason, reminder_date,
                    forgetting_immunity, protection_level
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', data)
            
            conn.commit()
            conn.close()
            
            logger.debug(f"[STORAGE] 记忆保存成功: {memory.id}")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] 保存记忆失败: {e}")
            return False

    async def load_memory(self, memory_id: str) -> Optional[LegendaryMemoryRecord]:
        """加载记忆"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM legendary_memories WHERE id = ?', (memory_id,))
            row = cursor.fetchone()
            
            conn.close()
            
            if row:
                return self._row_to_memory(row)
            
            return None
            
        except Exception as e:
            logger.error(f"[ERROR] 加载记忆失败: {e}")
            return None

    async def search_memories(self, role: Optional[str] = None, message_type: Optional[MessageType] = None,
                             limit: int = 50) -> List[LegendaryMemoryRecord]:
        """搜索记忆"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 构建查询
            query = "SELECT * FROM legendary_memories WHERE 1=1"
            params = []
            
            if role:
                query += " AND role_source = ?"
                params.append(role)
                
            if message_type:
                query += " AND message_type = ?"
                params.append(message_type.value)
            
            query += " ORDER BY final_importance DESC, created_at DESC LIMIT ?"
            params.append(limit)
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            conn.close()
            
            return [self._row_to_memory(row) for row in rows]
            
        except Exception as e:
            logger.error(f"[ERROR] 搜索记忆失败: {e}")
            return []

    def _row_to_memory(self, row) -> LegendaryMemoryRecord:
        """将数据库行转换为记忆对象"""
        try:
            # 解析枚举值
            message_type = MessageType(row[2])
            scope = MemoryScope(row[4])
            priority = MemoryPriority(row[5])
            
            # 解析JSON字段
            metadata = json.loads(row[6]) if row[6] else {}
            tags = json.loads(row[7]) if row[7] else []
            
            # 解析日期时间
            created_at = datetime.fromisoformat(row[8])
            updated_at = datetime.fromisoformat(row[9]) if row[9] else None
            last_accessed = datetime.fromisoformat(row[10]) if row[10] else None
            reminder_date = datetime.fromisoformat(row[18]) if row[18] else None
            
            # 解析用户标记类型
            user_marking_type = UserMarkingType(row[16]) if row[16] else None
            
            # 创建记忆对象
            memory = LegendaryMemoryRecord(
                content=row[1],
                message_type=message_type,
                role_source=row[3],
                scope=scope,
                priority=priority,
                metadata=metadata,
                tags=tags,
                id=row[0],
                created_at=created_at,
                updated_at=updated_at,
                last_accessed=last_accessed,
                access_count=row[11],
                system_importance=row[12],
                role_importance=row[13],
                final_importance=row[14],
                user_marked=bool(row[15]),
                user_marking_type=user_marking_type,
                user_marking_reason=row[17],
                reminder_date=reminder_date,
                forgetting_immunity=bool(row[19]),
                protection_level=row[20]
            )
            
            return memory
            
        except Exception as e:
            logger.error(f"[ERROR] 转换数据库行失败: {e}")
            raise

    async def save_memory_vector(self, memory_id: str, vector_data: str, vector_model: str = 'tfidf') -> bool:
        """保存记忆向量"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO memory_vectors (memory_id, vector_data, vector_model, created_at)
                VALUES (?, ?, ?, ?)
            ''', (memory_id, vector_data, vector_model, datetime.now().isoformat()))

            conn.commit()
            conn.close()

            logger.debug(f"[STORAGE] 记忆向量保存成功: {memory_id}")
            return True

        except Exception as e:
            logger.error(f"[ERROR] 保存记忆向量失败: {e}")
            return False

    async def load_memory_vector(self, memory_id: str) -> Optional[Dict[str, str]]:
        """加载记忆向量"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT vector_data, vector_model, created_at FROM memory_vectors WHERE memory_id = ?',
                         (memory_id,))
            row = cursor.fetchone()

            conn.close()

            if row:
                return {
                    'vector_data': row[0],
                    'vector_model': row[1],
                    'created_at': row[2]
                }

            return None

        except Exception as e:
            logger.error(f"[ERROR] 加载记忆向量失败: {e}")
            return None

    async def search_memories_with_content(self, query_text: str, role: Optional[str] = None,
                                         message_type: Optional[MessageType] = None,
                                         limit: int = 50) -> List[LegendaryMemoryRecord]:
        """基于内容的记忆搜索"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建查询
            query = "SELECT * FROM legendary_memories WHERE content LIKE ?"
            params = [f"%{query_text}%"]

            if role:
                query += " AND role_source = ?"
                params.append(role)

            if message_type:
                query += " AND message_type = ?"
                params.append(message_type.value)

            query += " ORDER BY final_importance DESC, created_at DESC LIMIT ?"
            params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()

            conn.close()

            return [self._row_to_memory(row) for row in rows]

        except Exception as e:
            logger.error(f"[ERROR] 内容搜索失败: {e}")
            return []

    async def get_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 总记忆数
            cursor.execute('SELECT COUNT(*) FROM legendary_memories')
            total_memories = cursor.fetchone()[0]
            
            # 按角色统计
            cursor.execute('SELECT role_source, COUNT(*) FROM legendary_memories GROUP BY role_source')
            role_stats = dict(cursor.fetchall())
            
            # 按重要性统计
            cursor.execute('SELECT COUNT(*) FROM legendary_memories WHERE final_importance >= 0.8')
            high_importance = cursor.fetchone()[0]
            
            # 用户标记统计
            cursor.execute('SELECT COUNT(*) FROM legendary_memories WHERE user_marked = 1')
            user_marked = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                "total_memories": total_memories,
                "role_statistics": role_stats,
                "high_importance_count": high_importance,
                "user_marked_count": user_marked
            }
            
        except Exception as e:
            logger.error(f"[ERROR] 获取统计信息失败: {e}")
            return {}

__all__ = ['LegendaryMemoryStorage']
