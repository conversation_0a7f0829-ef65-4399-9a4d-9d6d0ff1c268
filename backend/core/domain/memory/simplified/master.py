#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""

"""

import logging
from enum import Enum
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class SimplifiedMemoryType(Enum):
    pass
    SHORT_TERM = "short_term"
    LONG_TERM = "long_term"
    WORKING = "working"

class MemoryImportance(Enum):
    """记忆重要性"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

class SimplifiedMemorySystem:
    pass
    def __init__(self):
        self.memories = {}

    def store_memory(self, key: str, value: Any, memory_type: SimplifiedMemoryType = SimplifiedMemoryType.SHORT_TERM):
        """存储记忆"""
        self.memories[key] = {
            "value": value,
            "type": memory_type,
            "timestamp": logger.info(f"存储记忆: {key}")
        }
    
    def get_memory(self, key: str) -> Any:
        """获取记忆"""
        return self.memories.get(key, {}).get("value")
    
    def get_all_memories(self) -> Dict[str, Any]:
        """获取所有记忆"""
        return self.memories

# 创建全局实例
simplified_memory_system = SimplifiedMemorySystem()
