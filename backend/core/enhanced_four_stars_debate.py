#!/usr/bin/env python3
"""
增强的四星辩论机制
基于TradingAgents架构优化的多智能体辩论决策系统
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, field
import json

logger = logging.getLogger(__name__)

class DebateRole(Enum):
    """辩论角色枚举"""
    TIANSHU = "tianshu"      # 天枢星 - 信息面分析
    TIANXUAN = "tianxuan"    # 天璇星 - 技术面分析
    TIANJI = "tianji"        # 天玑星 - 风险面分析
    YUHENG = "yuheng"        # 玉衡星 - 执行面分析

class DebateStage(Enum):
    """辩论阶段枚举"""
    INITIALIZATION = "initialization"
    INFORMATION_GATHERING = "information_gathering"
    POSITION_PRESENTATION = "position_presentation"
    CROSS_EXAMINATION = "cross_examination"
    CONSENSUS_BUILDING = "consensus_building"
    FINAL_DECISION = "final_decision"

class PositionType(Enum):
    """立场类型枚举"""
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    HOLD = "hold"
    SELL = "sell"
    STRONG_SELL = "strong_sell"

@dataclass
class DebatePosition:
    """辩论立场"""
    role: DebateRole
    position: PositionType
    confidence: float  # 0.0 - 1.0
    reasoning: str
    evidence: List[str]
    risk_factors: List[str]
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class DebateState:
    """辩论状态"""
    debate_id: str
    stock_code: str
    stage: DebateStage
    current_round: int
    max_rounds: int
    positions: Dict[DebateRole, DebatePosition]
    debate_history: List[Dict[str, Any]]
    consensus_threshold: float
    final_decision: Optional[Dict[str, Any]] = None
    created_at: datetime = field(default_factory=datetime.now)

class EnhancedFourStarsDebate:
    """增强的四星辩论机制"""
    
    def __init__(self):
        self.active_debates: Dict[str, DebateState] = {}
        self.debate_history: List[DebateState] = []
        self.performance_metrics: Dict[str, Any] = {}
        self.is_initialized = False
        
        # 辩论配置
        self.config = {
            "max_rounds": 5,
            "consensus_threshold": 0.75,
            "min_confidence": 0.6,
            "timeout_minutes": 30,
            "evidence_weight": 0.4,
            "confidence_weight": 0.3,
            "risk_weight": 0.3
        }
        
        logger.info("[DEBATE] 增强四星辩论机制初始化完成")

    async def initialize(self) -> bool:
        """初始化辩论机制"""
        try:
            if self.is_initialized:
                return True
            
            # 初始化DeepSeek接口
            await self._initialize_deepseek_interfaces()
            
            # 初始化记忆接口
            await self._initialize_memory_interfaces()
            
            # 初始化性能监控
            await self._initialize_performance_monitoring()
            
            self.is_initialized = True
            logger.info("[DEBATE] 增强四星辩论机制初始化成功")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] 增强四星辩论机制初始化失败: {e}")
            return False

    async def _initialize_deepseek_interfaces(self):
        """初始化DeepSeek接口"""
        try:
            self.deepseek_interfaces = {}
            
            config_mappings = {
                DebateRole.TIANSHU: "roles.tianshu_star.config.deepseek_config",
                DebateRole.TIANXUAN: "roles.tianxuan_star.config.deepseek_config",
                DebateRole.TIANJI: "roles.tianji_star.config.deepseek_config",
                DebateRole.YUHENG: "roles.yuheng_star.config.deepseek_config"
            }
            
            for role, module_path in config_mappings.items():
                try:
                    config_module = __import__(module_path, fromlist=['get_deepseek_config'])
                    deepseek_config = config_module.get_deepseek_config()
                    self.deepseek_interfaces[role] = deepseek_config
                    logger.debug(f"[DEBATE] {role.value} DeepSeek接口加载成功")
                except ImportError as e:
                    logger.warning(f"[DEBATE] {role.value} DeepSeek接口加载失败: {e}")
                    
        except Exception as e:
            logger.error(f"[ERROR] 初始化DeepSeek接口失败: {e}")

    async def _initialize_memory_interfaces(self):
        """初始化记忆接口"""
        try:
            from core.memory_integration_service import memory_integration_service
            self.memory_service = memory_integration_service
            
            logger.info("[DEBATE] 记忆接口初始化完成")
            
        except Exception as e:
            logger.error(f"[ERROR] 记忆接口初始化失败: {e}")

    async def _initialize_performance_monitoring(self):
        """初始化性能监控"""
        try:
            self.performance_metrics = {
                "total_debates": 0,
                "successful_consensus": 0,
                "average_rounds": 0.0,
                "average_duration": 0.0,
                "role_performance": {
                    role.value: {
                        "positions_taken": 0,
                        "consensus_contributions": 0,
                        "accuracy_score": 0.85
                    } for role in DebateRole
                }
            }
            
            logger.info("[DEBATE] 性能监控初始化完成")
            
        except Exception as e:
            logger.error(f"[ERROR] 性能监控初始化失败: {e}")

    async def start_debate(self, stock_code: str, market_data: Dict[str, Any]) -> str:
        """开始新的辩论"""
        try:
            # 创建辩论ID
            debate_id = f"debate_{stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 创建辩论状态
            debate_state = DebateState(
                debate_id=debate_id,
                stock_code=stock_code,
                stage=DebateStage.INITIALIZATION,
                current_round=0,
                max_rounds=self.config["max_rounds"],
                positions={},
                debate_history=[],
                consensus_threshold=self.config["consensus_threshold"]
            )
            
            # 添加到活跃辩论
            self.active_debates[debate_id] = debate_state
            
            # 开始信息收集阶段
            await self._information_gathering_stage(debate_state, market_data)
            
            logger.info(f"[DEBATE] 辩论开始: {debate_id} - {stock_code}")
            
            return debate_id
            
        except Exception as e:
            logger.error(f"[ERROR] 开始辩论失败: {e}")
            return ""

    async def _information_gathering_stage(self, debate_state: DebateState, market_data: Dict[str, Any]):
        """信息收集阶段"""
        try:
            debate_state.stage = DebateStage.INFORMATION_GATHERING
            
            # 记录信息收集开始
            debate_state.debate_history.append({
                "stage": "information_gathering",
                "timestamp": datetime.now().isoformat(),
                "action": "开始信息收集",
                "market_data": market_data
            })
            
            # 进入立场陈述阶段
            await self._position_presentation_stage(debate_state, market_data)
            
        except Exception as e:
            logger.error(f"[ERROR] 信息收集阶段失败: {e}")

    async def _position_presentation_stage(self, debate_state: DebateState, market_data: Dict[str, Any]):
        """立场陈述阶段"""
        try:
            debate_state.stage = DebateStage.POSITION_PRESENTATION
            debate_state.current_round = 1
            
            # 各角色陈述立场
            for role in DebateRole:
                position = await self._get_role_position(role, debate_state.stock_code, market_data)
                debate_state.positions[role] = position
                
                # 记录立场
                debate_state.debate_history.append({
                    "stage": "position_presentation",
                    "round": debate_state.current_round,
                    "role": role.value,
                    "position": position.position.value,
                    "confidence": position.confidence,
                    "reasoning": position.reasoning,
                    "timestamp": datetime.now().isoformat()
                })
            
            # 进入交叉质询阶段
            await self._cross_examination_stage(debate_state)
            
        except Exception as e:
            logger.error(f"[ERROR] 立场陈述阶段失败: {e}")

    async def _get_role_position(self, role: DebateRole, stock_code: str, market_data: Dict[str, Any]) -> DebatePosition:
        """获取角色立场"""
        try:
            # 根据角色类型生成不同的分析视角
            if role == DebateRole.TIANSHU:
                # 信息面分析
                position_type, confidence, reasoning, evidence, risks = await self._analyze_information_perspective(
                    stock_code, market_data
                )
            elif role == DebateRole.TIANXUAN:
                # 技术面分析
                position_type, confidence, reasoning, evidence, risks = await self._analyze_technical_perspective(
                    stock_code, market_data
                )
            elif role == DebateRole.TIANJI:
                # 风险面分析
                position_type, confidence, reasoning, evidence, risks = await self._analyze_risk_perspective(
                    stock_code, market_data
                )
            elif role == DebateRole.YUHENG:
                # 执行面分析
                position_type, confidence, reasoning, evidence, risks = await self._analyze_execution_perspective(
                    stock_code, market_data
                )
            else:
                # 默认分析
                position_type = PositionType.HOLD
                confidence = 0.5
                reasoning = "默认观望立场"
                evidence = ["缺少足够信息"]
                risks = ["信息不足风险"]
            
            return DebatePosition(
                role=role,
                position=position_type,
                confidence=confidence,
                reasoning=reasoning,
                evidence=evidence,
                risk_factors=risks
            )
            
        except Exception as e:
            logger.error(f"[ERROR] 获取{role.value}立场失败: {e}")
            return DebatePosition(
                role=role,
                position=PositionType.HOLD,
                confidence=0.5,
                reasoning=f"分析失败: {str(e)}",
                evidence=["分析异常"],
                risk_factors=["系统风险"]
            )

    async def _analyze_information_perspective(self, stock_code: str, market_data: Dict[str, Any]) -> Tuple[PositionType, float, str, List[str], List[str]]:
        """信息面分析视角"""
        news_sentiment = market_data.get("news_sentiment", 0.5)
        market_attention = market_data.get("market_attention", 0.5)
        
        if news_sentiment > 0.7 and market_attention > 0.6:
            return (
                PositionType.BUY,
                0.8,
                "新闻面积极，市场关注度高",
                ["正面新闻较多", "市场关注度提升", "信息面支持"],
                ["新闻真实性风险", "情绪波动风险"]
            )
        elif news_sentiment < 0.3:
            return (
                PositionType.SELL,
                0.7,
                "新闻面消极，建议规避",
                ["负面新闻增多", "市场情绪转差"],
                ["进一步下跌风险", "流动性风险"]
            )
        else:
            return (
                PositionType.HOLD,
                0.6,
                "信息面中性，建议观望",
                ["信息面平衡", "缺乏明确信号"],
                ["信息滞后风险", "突发事件风险"]
            )

    async def _analyze_technical_perspective(self, stock_code: str, market_data: Dict[str, Any]) -> Tuple[PositionType, float, str, List[str], List[str]]:
        """技术面分析视角"""
        technical_score = market_data.get("technical_score", 0.5)
        trend_strength = market_data.get("trend_strength", 0.5)
        
        if technical_score > 0.7 and trend_strength > 0.6:
            return (
                PositionType.BUY,
                0.85,
                "技术指标强势，趋势向上",
                ["技术指标突破", "趋势确认", "成交量配合"],
                ["技术回调风险", "假突破风险"]
            )
        elif technical_score < 0.3:
            return (
                PositionType.SELL,
                0.75,
                "技术指标疲弱，趋势向下",
                ["技术指标破位", "趋势转弱"],
                ["进一步下跌风险", "支撑失效风险"]
            )
        else:
            return (
                PositionType.HOLD,
                0.6,
                "技术面中性，等待信号",
                ["技术指标震荡", "趋势不明"],
                ["方向不明风险", "震荡风险"]
            )

    async def _analyze_risk_perspective(self, stock_code: str, market_data: Dict[str, Any]) -> Tuple[PositionType, float, str, List[str], List[str]]:
        """风险面分析视角"""
        risk_level = market_data.get("risk_level", 0.5)
        volatility = market_data.get("volatility", 0.4)
        
        if risk_level < 0.3 and volatility < 0.4:
            return (
                PositionType.BUY,
                0.8,
                "风险可控，波动率较低",
                ["风险指标良好", "波动率适中", "风险收益比合理"],
                ["隐藏风险", "市场系统性风险"]
            )
        elif risk_level > 0.7:
            return (
                PositionType.SELL,
                0.9,
                "风险过高，建议规避",
                ["风险指标恶化", "波动率过高"],
                ["重大损失风险", "流动性风险", "系统性风险"]
            )
        else:
            return (
                PositionType.HOLD,
                0.7,
                "风险中等，谨慎操作",
                ["风险适中", "需要观察"],
                ["风险上升可能", "市场变化风险"]
            )

    async def _analyze_execution_perspective(self, stock_code: str, market_data: Dict[str, Any]) -> Tuple[PositionType, float, str, List[str], List[str]]:
        """执行面分析视角"""
        liquidity = market_data.get("liquidity", 0.6)
        execution_cost = market_data.get("execution_cost", 0.4)
        
        if liquidity > 0.7 and execution_cost < 0.3:
            return (
                PositionType.BUY,
                0.8,
                "执行条件良好，流动性充足",
                ["流动性充足", "执行成本低", "市场深度好"],
                ["流动性突然恶化", "执行滑点风险"]
            )
        elif execution_cost > 0.6:
            return (
                PositionType.HOLD,
                0.6,
                "执行成本较高，建议等待",
                ["执行成本偏高", "时机不佳"],
                ["成本进一步上升", "错失机会风险"]
            )
        else:
            return (
                PositionType.HOLD,
                0.65,
                "执行条件一般，可以操作",
                ["执行条件可接受", "流动性适中"],
                ["执行风险", "时机风险"]
            )

    async def _cross_examination_stage(self, debate_state: DebateState):
        """交叉质询阶段"""
        try:
            debate_state.stage = DebateStage.CROSS_EXAMINATION
            
            # 进行多轮交叉质询
            for round_num in range(2, debate_state.max_rounds + 1):
                debate_state.current_round = round_num
                
                # 检查是否达成共识
                consensus_result = await self._check_consensus(debate_state)
                if consensus_result["has_consensus"]:
                    break
                
                # 继续辩论
                await self._conduct_debate_round(debate_state)
            
            # 进入共识建立阶段
            await self._consensus_building_stage(debate_state)
            
        except Exception as e:
            logger.error(f"[ERROR] 交叉质询阶段失败: {e}")

    async def _check_consensus(self, debate_state: DebateState) -> Dict[str, Any]:
        """检查是否达成共识"""
        try:
            positions = list(debate_state.positions.values())
            
            # 统计各立场的支持度
            position_counts = {}
            total_confidence = 0
            
            for pos in positions:
                position_type = pos.position.value
                if position_type not in position_counts:
                    position_counts[position_type] = {"count": 0, "confidence": 0}
                
                position_counts[position_type]["count"] += 1
                position_counts[position_type]["confidence"] += pos.confidence
                total_confidence += pos.confidence
            
            # 计算最高支持度
            max_support = 0
            consensus_position = None
            
            for pos_type, data in position_counts.items():
                support_ratio = data["count"] / len(positions)
                confidence_ratio = data["confidence"] / total_confidence
                combined_support = (support_ratio + confidence_ratio) / 2
                
                if combined_support > max_support:
                    max_support = combined_support
                    consensus_position = pos_type
            
            has_consensus = max_support >= debate_state.consensus_threshold
            
            return {
                "has_consensus": has_consensus,
                "consensus_position": consensus_position,
                "support_level": max_support,
                "position_distribution": position_counts
            }
            
        except Exception as e:
            logger.error(f"[ERROR] 检查共识失败: {e}")
            return {"has_consensus": False, "error": str(e)}

    async def _conduct_debate_round(self, debate_state: DebateState):
        """进行辩论轮次"""
        try:
            # 记录辩论轮次开始
            debate_state.debate_history.append({
                "stage": "cross_examination",
                "round": debate_state.current_round,
                "action": "辩论轮次开始",
                "timestamp": datetime.now().isoformat()
            })
            
            for role in DebateRole:
                debate_state.debate_history.append({
                    "stage": "cross_examination",
                    "round": debate_state.current_round,
                    "role": role.value,
                    "action": "参与辩论",
                    "timestamp": datetime.now().isoformat()
                })
            
        except Exception as e:
            logger.error(f"[ERROR] 进行辩论轮次失败: {e}")

    async def _consensus_building_stage(self, debate_state: DebateState):
        """共识建立阶段"""
        try:
            debate_state.stage = DebateStage.CONSENSUS_BUILDING
            
            # 最终共识检查
            consensus_result = await self._check_consensus(debate_state)
            
            # 生成最终决策
            await self._generate_final_decision(debate_state, consensus_result)
            
        except Exception as e:
            logger.error(f"[ERROR] 共识建立阶段失败: {e}")

    async def _generate_final_decision(self, debate_state: DebateState, consensus_result: Dict[str, Any]):
        """生成最终决策"""
        try:
            debate_state.stage = DebateStage.FINAL_DECISION
            
            # 创建最终决策
            final_decision = {
                "debate_id": debate_state.debate_id,
                "stock_code": debate_state.stock_code,
                "final_position": consensus_result.get("consensus_position", "hold"),
                "consensus_level": consensus_result.get("support_level", 0.5),
                "has_consensus": consensus_result.get("has_consensus", False),
                "total_rounds": debate_state.current_round,
                "participant_positions": {
                    role.value: {
                        "position": pos.position.value,
                        "confidence": pos.confidence,
                        "reasoning": pos.reasoning
                    } for role, pos in debate_state.positions.items()
                },
                "decision_timestamp": datetime.now().isoformat()
            }
            
            debate_state.final_decision = final_decision
            
            # 移动到历史记录
            self.debate_history.append(debate_state)
            if debate_state.debate_id in self.active_debates:
                del self.active_debates[debate_state.debate_id]
            
            # 更新性能指标
            await self._update_performance_metrics(debate_state)
            
            logger.info(f"[DEBATE] 辩论完成: {debate_state.debate_id} - 决策: {final_decision['final_position']}")
            
        except Exception as e:
            logger.error(f"[ERROR] 生成最终决策失败: {e}")

    async def _update_performance_metrics(self, debate_state: DebateState):
        """更新性能指标"""
        try:
            self.performance_metrics["total_debates"] += 1
            
            if debate_state.final_decision and debate_state.final_decision.get("has_consensus"):
                self.performance_metrics["successful_consensus"] += 1
            
            # 更新平均轮数
            total_debates = self.performance_metrics["total_debates"]
            current_avg = self.performance_metrics["average_rounds"]
            new_avg = ((current_avg * (total_debates - 1)) + debate_state.current_round) / total_debates
            self.performance_metrics["average_rounds"] = new_avg
            
        except Exception as e:
            logger.error(f"[ERROR] 更新性能指标失败: {e}")

    def get_debate_status(self, debate_id: str) -> Dict[str, Any]:
        """获取辩论状态"""
        try:
            if debate_id in self.active_debates:
                debate_state = self.active_debates[debate_id]
                return {
                    "debate_id": debate_id,
                    "status": "active",
                    "stage": debate_state.stage.value,
                    "current_round": debate_state.current_round,
                    "max_rounds": debate_state.max_rounds,
                    "positions": {
                        role.value: {
                            "position": pos.position.value,
                            "confidence": pos.confidence
                        } for role, pos in debate_state.positions.items()
                    }
                }
            else:
                # 查找历史记录
                for debate_state in self.debate_history:
                    if debate_state.debate_id == debate_id:
                        return {
                            "debate_id": debate_id,
                            "status": "completed",
                            "final_decision": debate_state.final_decision
                        }
                
                return {"error": "辩论不存在"}
                
        except Exception as e:
            logger.error(f"[ERROR] 获取辩论状态失败: {e}")
            return {"error": str(e)}

    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        try:
            total_debates = self.performance_metrics["total_debates"]
            successful_consensus = self.performance_metrics["successful_consensus"]
            
            return {
                "total_debates": total_debates,
                "successful_consensus": successful_consensus,
                "consensus_rate": successful_consensus / total_debates if total_debates > 0 else 0,
                "average_rounds": self.performance_metrics["average_rounds"],
                "active_debates": len(self.active_debates),
                "role_performance": self.performance_metrics["role_performance"]
            }
            
        except Exception as e:
            logger.error(f"[ERROR] 获取性能报告失败: {e}")
            return {"error": str(e)}

# 全局实例
enhanced_four_stars_debate = EnhancedFourStarsDebate()

__all__ = ['EnhancedFourStarsDebate', 'enhanced_four_stars_debate', 'DebateRole', 'PositionType', 'DebateStage']
