#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体辩论系统 - 每个角色作为独立智能体参与辩论
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
import json

logger = logging.getLogger(__name__)

class DebateStage(Enum):
    """辩论阶段"""
    INITIALIZATION = "initialization"
    POSITION_COLLECTION = "position_collection"
    DEBATE_ROUNDS = "debate_rounds"
    CONSENSUS_BUILDING = "consensus_building"
    FINAL_DECISION = "final_decision"
    COMPLETED = "completed"

class AgentRole(Enum):
    """智能体角色"""
    TIANSHU = "tianshu_star"      # 天枢星 - 情报收集
    TIANXUAN = "tianxuan_star"    # 天璇星 - 技术分析
    TIANJI = "tianji_star"        # 天玑星 - 风险评估
    TIANQUAN = "tianquan_star"    # 天权星 - 策略决策
    YUHENG = "yuheng_star"        # 玉衡星 - 交易执行
    KAIYANG = "kaiyang_star"      # 开阳星 - 股票筛选
    YAOGUANG = "yaoguang_star"    # 瑶光星 - 学习优化

@dataclass
class AgentArgument:
    """智能体论点"""
    agent_role: AgentRole
    content: str
    confidence: float
    supporting_evidence: List[str]
    timestamp: datetime = field(default_factory=datetime.now)
    round_number: int = 0

@dataclass
class DebateRound:
    """辩论轮次"""
    round_number: int
    topic: str
    arguments: List[AgentArgument]
    consensus_level: float
    key_disagreements: List[str]
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class DebateSession:
    """辩论会话"""
    session_id: str
    topic: str
    participants: List[AgentRole]
    stage: DebateStage
    rounds: List[DebateRound] = field(default_factory=list)
    final_consensus: Optional[Dict[str, Any]] = None
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None

class IntelligentAgentDebateSystem:
    """智能体辩论系统"""
    
    def __init__(self):
        self.active_sessions: Dict[str, DebateSession] = {}
        self.agent_services = {}
        self.debate_config = {
            "max_rounds": 5,
            "consensus_threshold": 0.8,
            "min_confidence_change": 0.1,
            "timeout_minutes": 30
        }
        
        # 初始化智能体服务
        self._initialize_agent_services()
        
        logger.info("🤖 智能体辩论系统初始化完成")
    
    def _initialize_agent_services(self):
        """初始化各角色的智能体服务"""
        try:
            # 延迟导入避免循环依赖
            self.agent_services = {
                AgentRole.TIANSHU: self._get_tianshu_agent,
                AgentRole.TIANXUAN: self._get_tianxuan_agent,
                AgentRole.TIANJI: self._get_tianji_agent,
                AgentRole.TIANQUAN: self._get_tianquan_agent,
                AgentRole.YUHENG: self._get_yuheng_agent,
                AgentRole.KAIYANG: self._get_kaiyang_agent,
                AgentRole.YAOGUANG: self._get_yaoguang_agent
            }
            logger.info("✅ 智能体服务初始化完成")
        except Exception as e:
            logger.error(f"❌ 智能体服务初始化失败: {e}")
    
    async def start_four_star_debate(self, 
                                   topic: str, 
                                   stock_code: str,
                                   context: Dict[str, Any] = None) -> str:
        """启动四星辩论（天枢、天璇、天玑、玉衡）"""
        participants = [
            AgentRole.TIANSHU,   # 天枢星 - 情报分析
            AgentRole.TIANXUAN,  # 天璇星 - 技术分析
            AgentRole.TIANJI,    # 天玑星 - 风险评估
            AgentRole.YUHENG     # 玉衡星 - 交易执行
        ]
        
        return await self.start_debate_session(
            topic=f"{topic} - {stock_code}",
            participants=participants,
            context=context or {"stock_code": stock_code}
        )
    
    async def start_seven_star_debate(self, 
                                    topic: str, 
                                    context: Dict[str, Any] = None) -> str:
        """启动七星辩论（所有角色）"""
        participants = list(AgentRole)
        
        return await self.start_debate_session(
            topic=topic,
            participants=participants,
            context=context or {}
        )
    
    async def start_debate_session(self, 
                                 topic: str,
                                 participants: List[AgentRole],
                                 context: Dict[str, Any] = None) -> str:
        """启动辩论会话"""
        try:
            # 创建会话ID
            session_id = f"debate_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 创建辩论会话
            session = DebateSession(
                session_id=session_id,
                topic=topic,
                participants=participants,
                stage=DebateStage.INITIALIZATION,
                rounds=[]
            )
            
            self.active_sessions[session_id] = session
            
            logger.info(f"🎭 启动智能体辩论会话: {session_id}")
            logger.info(f"   主题: {topic}")
            logger.info(f"   参与者: {[role.value for role in participants]}")
            
            # 执行辩论流程
            await self._execute_debate_flow(session_id, context or {})
            
            return session_id
            
        except Exception as e:
            logger.error(f"❌ 启动辩论会话失败: {e}")
            raise e
    
    async def _execute_debate_flow(self, session_id: str, context: Dict[str, Any]):
        """执行辩论流程"""
        session = self.active_sessions[session_id]
        
        try:
            # 阶段1：收集初始立场
            session.stage = DebateStage.POSITION_COLLECTION
            initial_positions = await self._collect_initial_positions(session, context)
            
            # 阶段2：多轮辩论
            session.stage = DebateStage.DEBATE_ROUNDS
            await self._conduct_debate_rounds(session, context)
            
            # 阶段3：构建共识
            session.stage = DebateStage.CONSENSUS_BUILDING
            consensus = await self._build_consensus(session)
            
            # 阶段4：最终决策
            session.stage = DebateStage.FINAL_DECISION
            final_decision = await self._make_final_decision(session, consensus)
            
            # 完成辩论
            session.stage = DebateStage.COMPLETED
            session.final_consensus = final_decision
            session.completed_at = datetime.now()
            
            logger.info(f"✅ 辩论会话完成: {session_id}")
            
        except Exception as e:
            logger.error(f"❌ 辩论流程执行失败: {e}")
            raise e
    
    async def _collect_initial_positions(self, session: DebateSession, context: Dict[str, Any]) -> Dict[AgentRole, AgentArgument]:
        """收集各智能体的初始立场"""
        logger.info(f"📝 收集初始立场...")
        
        positions = {}
        
        for agent_role in session.participants:
            try:
                # 调用智能体分析
                agent_service = await self._get_agent_service(agent_role)
                
                # 构建分析请求
                analysis_request = {
                    "topic": session.topic,
                    "context": context,
                    "task": "initial_position",
                    "role_perspective": self._get_role_perspective(agent_role)
                }
                
                # 获取智能体分析结果
                analysis_result = await self._call_agent_analysis(agent_service, analysis_request)
                
                # 创建论点
                argument = AgentArgument(
                    agent_role=agent_role,
                    content=analysis_result.get("position", ""),
                    confidence=analysis_result.get("confidence", 0.5),
                    supporting_evidence=analysis_result.get("evidence", []),
                    round_number=0
                )
                
                positions[agent_role] = argument
                
                logger.info(f"   ✅ {agent_role.value}: {argument.content[:100]}...")
                
            except Exception as e:
                logger.error(f"   ❌ {agent_role.value} 立场收集失败: {e}")
                # 创建默认立场
                positions[agent_role] = AgentArgument(
                    agent_role=agent_role,
                    content=f"基于{self._get_role_perspective(agent_role)}的分析观点",
                    confidence=0.3,
                    supporting_evidence=["分析服务暂时不可用"],
                    round_number=0
                )
        
        return positions
    
    async def _conduct_debate_rounds(self, session: DebateSession, context: Dict[str, Any]):
        """进行多轮辩论"""
        logger.info(f"🗣️ 开始多轮辩论...")
        
        current_positions = await self._collect_initial_positions(session, context)
        
        for round_num in range(1, self.debate_config["max_rounds"] + 1):
            logger.info(f"   第{round_num}轮辩论...")
            
            # 识别争议点
            disagreements = self._identify_disagreements(current_positions)
            
            if not disagreements:
                logger.info("   无明显争议，辩论结束")
                break
            
            # 进行本轮辩论
            round_arguments = await self._conduct_single_round(
                session, round_num, current_positions, disagreements, context
            )
            
            # 计算共识水平
            consensus_level = self._calculate_consensus_level(round_arguments)
            
            # 记录本轮辩论
            debate_round = DebateRound(
                round_number=round_num,
                topic=f"争议点: {', '.join(disagreements[:3])}",
                arguments=round_arguments,
                consensus_level=consensus_level,
                key_disagreements=disagreements
            )
            
            session.rounds.append(debate_round)
            
            # 更新立场
            current_positions = {arg.agent_role: arg for arg in round_arguments}
            
            # 检查是否达成共识
            if consensus_level >= self.debate_config["consensus_threshold"]:
                logger.info(f"   达成共识 (共识度: {consensus_level:.2f})")
                break
        
        logger.info(f"✅ 辩论轮次完成，共进行{len(session.rounds)}轮")
    
    def _get_role_perspective(self, agent_role: AgentRole) -> str:
        """获取角色视角"""
        perspectives = {
            AgentRole.TIANSHU: "情报收集与市场信息分析",
            AgentRole.TIANXUAN: "技术分析与图表模式识别",
            AgentRole.TIANJI: "风险评估与风险控制",
            AgentRole.TIANQUAN: "策略制定与决策统筹",
            AgentRole.YUHENG: "交易执行与成本控制",
            AgentRole.KAIYANG: "股票筛选与质量评估",
            AgentRole.YAOGUANG: "学习优化与系统改进"
        }
        return perspectives.get(agent_role, "综合分析")
    
    # 智能体服务获取方法（延迟加载）
    async def _get_agent_service(self, agent_role: AgentRole):
        """获取智能体服务"""
        service_getter = self.agent_services.get(agent_role)
        if service_getter:
            return await service_getter()
        else:
            raise Exception(f"未找到{agent_role.value}的智能体服务")
    
    async def _get_tianshu_agent(self):
        """获取天枢星智能体"""
        from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system
        return tianshu_automation_system

    async def _get_tianxuan_agent(self):
        """获取天璇星智能体"""
        from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system
        return tianxuan_automation_system

    async def _get_tianji_agent(self):
        """获取天玑星智能体"""
        from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
        return tianji_automation_system

    async def _get_tianquan_agent(self):
        """获取天权星智能体"""
        from roles.tianquan_star.core.tianquan_automation_system import tianquan_automation_system
        return tianquan_automation_system

    async def _get_yuheng_agent(self):
        """获取玉衡星智能体"""
        from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
        return yuheng_automation_system

    async def _get_kaiyang_agent(self):
        """获取开阳星智能体"""
        from roles.kaiyang_star.services.kaiyang_automation_system import kaiyang_automation_system
        return kaiyang_automation_system

    async def _get_yaoguang_agent(self):
        """获取瑶光星智能体"""
        from roles.yaoguang_star.automation.quantitative_research_automation import quantitative_research_automation
        return quantitative_research_automation

    async def _call_agent_analysis(self, agent_service, analysis_request: Dict[str, Any]) -> Dict[str, Any]:
        """调用智能体进行分析"""
        try:
            # 根据不同的智能体调用不同的方法
            agent_role = analysis_request.get("role_perspective", "")

            if "情报收集" in agent_role:
                # 天枢星 - 调用市场分析
                result = await agent_service.execute_market_analysis(
                    stock_code=analysis_request.get("context", {}).get("stock_code", "000001.XSHE"),
                    task_type="debate_analysis",
                    session_id=f"debate_{datetime.now().strftime('%H%M%S')}"
                )

                return {
                    "position": f"基于市场情报分析，{result.get('analysis_result', {}).get('sentiment_analysis', {}).get('overall_sentiment', '中性')}情绪主导",
                    "confidence": 0.8,
                    "evidence": [
                        f"新闻分析: {len(result.get('analysis_result', {}).get('news_analysis', []))}条相关新闻",
                        f"市场数据: {result.get('analysis_result', {}).get('basic_info', {}).get('current_price', 'N/A')}",
                        "情报收集系统正常运行"
                    ]
                }

            elif "技术分析" in agent_role:
                # 天璇星 - 调用技术分析
                result = await agent_service.execute_technical_analysis(
                    stock_code=analysis_request.get("context", {}).get("stock_code", "000001.XSHE"),
                    analysis_type="comprehensive",
                    session_id=f"debate_{datetime.now().strftime('%H%M%S')}"
                )

                indicators = result.get("analysis_result", {}).get("technical_indicators", {})
                rsi = indicators.get("rsi", 50)

                if rsi > 70:
                    position = "技术指标显示超买，建议谨慎"
                elif rsi < 30:
                    position = "技术指标显示超卖，存在反弹机会"
                else:
                    position = "技术指标处于正常区间，保持观察"

                return {
                    "position": position,
                    "confidence": 0.75,
                    "evidence": [
                        f"RSI指标: {rsi}",
                        f"移动平均线: {indicators.get('moving_averages', {})}",
                        f"技术模式: {len(result.get('analysis_result', {}).get('price_patterns', []))}个"
                    ]
                }

            elif "风险评估" in agent_role:
                # 天玑星 - 调用风险分析
                result = await agent_service.execute_risk_analysis(
                    stock_code=analysis_request.get("context", {}).get("stock_code", "000001.XSHE"),
                    analysis_depth="comprehensive",
                    session_id=f"debate_{datetime.now().strftime('%H%M%S')}"
                )

                risk_level = result.get("analysis_result", {}).get("overall_risk_level", "medium")

                return {
                    "position": f"风险评估结果为{risk_level}级别，需要相应的风险控制措施",
                    "confidence": 0.85,
                    "evidence": [
                        f"整体风险等级: {risk_level}",
                        f"风险因子数量: {len(result.get('analysis_result', {}).get('risk_factors', []))}",
                        "风险模型运行正常"
                    ]
                }

            elif "交易执行" in agent_role:
                # 玉衡星 - 调用交易分析
                result = await agent_service.execute_trading_automation(
                    stock_code=analysis_request.get("context", {}).get("stock_code", "000001.XSHE"),
                    action="analyze",
                    quantity=1000,
                    session_id=f"debate_{datetime.now().strftime('%H%M%S')}"
                )

                return {
                    "position": "从交易执行角度分析，当前市场流动性和执行成本需要考虑",
                    "confidence": 0.7,
                    "evidence": [
                        f"交易分析结果: {result.get('success', False)}",
                        "流动性评估完成",
                        "执行成本分析完成"
                    ]
                }

            else:
                # 其他角色的通用分析
                return {
                    "position": f"基于{agent_role}的专业分析观点",
                    "confidence": 0.6,
                    "evidence": ["专业分析完成", "数据处理正常", "系统运行稳定"]
                }

        except Exception as e:
            logger.error(f"智能体分析调用失败: {e}")
            return {
                "position": f"基于{analysis_request.get('role_perspective', '综合')}的分析，需要进一步数据支持",
                "confidence": 0.3,
                "evidence": [f"分析过程中遇到技术问题: {str(e)}"]
            }

    async def _conduct_single_round(self, session: DebateSession, round_num: int,
                                  current_positions: Dict[AgentRole, AgentArgument],
                                  disagreements: List[str], context: Dict[str, Any]) -> List[AgentArgument]:
        """进行单轮辩论"""
        round_arguments = []

        for agent_role in session.participants:
            try:
                # 构建辩论上下文
                debate_context = {
                    "round_number": round_num,
                    "topic": session.topic,
                    "disagreements": disagreements,
                    "other_positions": {k.value: v.content for k, v in current_positions.items() if k != agent_role},
                    "previous_rounds": [r.arguments for r in session.rounds],
                    "context": context
                }

                # 获取智能体服务
                agent_service = await self._get_agent_service(agent_role)

                # 构建辩论请求
                debate_request = {
                    "topic": session.topic,
                    "context": debate_context,
                    "task": "debate_response",
                    "role_perspective": self._get_role_perspective(agent_role)
                }

                # 调用智能体进行辩论分析
                debate_result = await self._call_agent_analysis(agent_service, debate_request)

                # 创建本轮论点
                argument = AgentArgument(
                    agent_role=agent_role,
                    content=debate_result.get("position", ""),
                    confidence=debate_result.get("confidence", 0.5),
                    supporting_evidence=debate_result.get("evidence", []),
                    round_number=round_num
                )

                round_arguments.append(argument)

                logger.info(f"     {agent_role.value}: {argument.content[:80]}...")

            except Exception as e:
                logger.error(f"     {agent_role.value} 辩论失败: {e}")
                # 创建默认论点
                argument = AgentArgument(
                    agent_role=agent_role,
                    content=f"基于{self._get_role_perspective(agent_role)}，维持之前的分析观点",
                    confidence=0.3,
                    supporting_evidence=["技术问题导致无法深入分析"],
                    round_number=round_num
                )
                round_arguments.append(argument)

        return round_arguments

    def _identify_disagreements(self, positions: Dict[AgentRole, AgentArgument]) -> List[str]:
        """识别争议点"""
        disagreements = []

        # 分析置信度差异
        confidences = [pos.confidence for pos in positions.values()]
        if max(confidences) - min(confidences) > 0.3:
            disagreements.append("置信度存在显著差异")

        # 分析观点关键词
        contents = [pos.content.lower() for pos in positions.values()]

        # 检查买卖倾向
        buy_signals = sum(1 for content in contents if any(word in content for word in ["买入", "看涨", "上涨", "机会"]))
        sell_signals = sum(1 for content in contents if any(word in content for word in ["卖出", "看跌", "下跌", "风险"]))

        if buy_signals > 0 and sell_signals > 0:
            disagreements.append("买卖信号存在分歧")

        # 检查风险评估
        risk_high = sum(1 for content in contents if any(word in content for word in ["高风险", "谨慎", "危险"]))
        risk_low = sum(1 for content in contents if any(word in content for word in ["低风险", "安全", "稳定"]))

        if risk_high > 0 and risk_low > 0:
            disagreements.append("风险评估存在分歧")

        return disagreements

    def _calculate_consensus_level(self, arguments: List[AgentArgument]) -> float:
        """计算共识水平"""
        if not arguments:
            return 0.0

        # 基于置信度的一致性
        confidences = [arg.confidence for arg in arguments]
        confidence_variance = sum((c - sum(confidences)/len(confidences))**2 for c in confidences) / len(confidences)
        confidence_consensus = max(0, 1 - confidence_variance * 2)

        # 基于内容相似性的一致性（简化版）
        contents = [arg.content.lower() for arg in arguments]

        # 检查关键词一致性
        positive_words = ["买入", "看涨", "上涨", "机会", "积极"]
        negative_words = ["卖出", "看跌", "下跌", "风险", "谨慎"]

        positive_count = sum(1 for content in contents if any(word in content for word in positive_words))
        negative_count = sum(1 for content in contents if any(word in content for word in negative_words))

        if positive_count == 0 and negative_count == 0:
            sentiment_consensus = 0.5  # 中性
        elif positive_count == 0 or negative_count == 0:
            sentiment_consensus = 1.0  # 完全一致
        else:
            sentiment_consensus = 1 - abs(positive_count - negative_count) / len(contents)

        # 综合共识水平
        overall_consensus = (confidence_consensus + sentiment_consensus) / 2

        return min(1.0, max(0.0, overall_consensus))

    async def _build_consensus(self, session: DebateSession) -> Dict[str, Any]:
        """构建共识"""
        logger.info("🤝 构建最终共识...")

        if not session.rounds:
            return {"consensus": "无有效辩论轮次", "confidence": 0.0}

        # 获取最后一轮的论点
        last_round = session.rounds[-1]
        final_arguments = last_round.arguments

        # 计算最终共识
        consensus_level = self._calculate_consensus_level(final_arguments)

        # 汇总各角色观点
        role_summaries = {}
        for arg in final_arguments:
            role_summaries[arg.agent_role.value] = {
                "position": arg.content,
                "confidence": arg.confidence,
                "evidence": arg.supporting_evidence
            }

        # 生成共识总结
        if consensus_level >= 0.8:
            consensus_summary = "各角色达成高度共识"
        elif consensus_level >= 0.6:
            consensus_summary = "各角色基本达成共识"
        elif consensus_level >= 0.4:
            consensus_summary = "各角色存在部分分歧"
        else:
            consensus_summary = "各角色存在重大分歧"

        consensus = {
            "consensus_level": consensus_level,
            "summary": consensus_summary,
            "role_positions": role_summaries,
            "total_rounds": len(session.rounds),
            "key_disagreements": last_round.key_disagreements
        }

        logger.info(f"   共识水平: {consensus_level:.2f}")
        logger.info(f"   共识总结: {consensus_summary}")

        return consensus

    async def _make_final_decision(self, session: DebateSession, consensus: Dict[str, Any]) -> Dict[str, Any]:
        """做出最终决策"""
        logger.info("⚖️ 生成最终决策...")

        # 如果有天权星参与，由天权星做最终决策
        if AgentRole.TIANQUAN in session.participants:
            try:
                tianquan_agent = await self._get_tianquan_agent()

                decision_request = {
                    "topic": session.topic,
                    "consensus": consensus,
                    "debate_history": session.rounds,
                    "task": "final_decision"
                }

                decision_result = await self._call_agent_analysis(tianquan_agent, decision_request)

                final_decision = {
                    "decision_maker": "天权星",
                    "decision": decision_result.get("position", ""),
                    "confidence": decision_result.get("confidence", 0.5),
                    "reasoning": decision_result.get("evidence", []),
                    "consensus_input": consensus,
                    "timestamp": datetime.now().isoformat()
                }

            except Exception as e:
                logger.error(f"天权星决策失败: {e}")
                final_decision = self._generate_default_decision(consensus)
        else:
            # 没有天权星时，基于共识生成决策
            final_decision = self._generate_default_decision(consensus)

        logger.info(f"   最终决策: {final_decision['decision'][:100]}...")

        return final_decision

    def _generate_default_decision(self, consensus: Dict[str, Any]) -> Dict[str, Any]:
        """生成默认决策"""
        consensus_level = consensus.get("consensus_level", 0.0)

        if consensus_level >= 0.8:
            decision = "基于高度共识，建议按照多数观点执行"
        elif consensus_level >= 0.6:
            decision = "基于基本共识，建议谨慎执行并持续监控"
        elif consensus_level >= 0.4:
            decision = "存在分歧，建议进一步分析后再做决定"
        else:
            decision = "分歧较大，建议暂缓决策并收集更多信息"

        return {
            "decision_maker": "系统自动决策",
            "decision": decision,
            "confidence": consensus_level,
            "reasoning": [f"共识水平: {consensus_level:.2f}", "基于辩论结果的综合判断"],
            "consensus_input": consensus,
            "timestamp": datetime.now().isoformat()
        }

    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """获取辩论会话状态"""
        if session_id not in self.active_sessions:
            return {"error": "会话不存在"}

        session = self.active_sessions[session_id]

        return {
            "session_id": session_id,
            "topic": session.topic,
            "stage": session.stage.value,
            "participants": [role.value for role in session.participants],
            "rounds_completed": len(session.rounds),
            "current_consensus": session.rounds[-1].consensus_level if session.rounds else 0.0,
            "is_completed": session.stage == DebateStage.COMPLETED,
            "final_decision": session.final_consensus
        }

# 全局智能体辩论系统实例
intelligent_agent_debate_system = IntelligentAgentDebateSystem()
