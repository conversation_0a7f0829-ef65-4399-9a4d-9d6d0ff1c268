#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析七个角色自动化系统状态
检查DeepSeek人设配置、传奇记忆系统、绩效系统、层级系统的集成情况
"""

import asyncio
import sys
import os
import json
from datetime import datetime

sys.path.append(os.getcwd())

async def deep_analyze_seven_roles():
    """深度分析七个角色自动化系统"""
    print('🔍 深度检查七个角色自动化系统状态')
    print('=' * 60)
    
    analysis_result = {
        'timestamp': datetime.now().isoformat(),
        'automation_systems': {},
        'deepseek_configs': {},
        'memory_integration': {},
        'performance_monitoring': {},
        'hierarchy_system': {},
        'summary': {}
    }
    
    # 1. 检查角色自动化系统
    print('\n🤖 检查角色自动化系统...')
    roles_automation = {
        '天枢星': 'roles.tianshu_star.services.tianshu_automation_system',
        '天璇星': 'roles.tianxuan_star.services.tianxuan_automation_system', 
        '天玑星': 'roles.tianji_star.services.tianji_automation_system',
        '天权星': 'roles.tianquan_star.core.tianquan_automation_system',
        '玉衡星': 'roles.yuheng_star.services.yuheng_automation_system',
        '开阳星': 'roles.kaiyang_star.services.kaiyang_automation_system',
        '瑶光星': 'roles.yaoguang_star.automation.quantitative_research_automation'
    }
    
    for role_name, module_path in roles_automation.items():
        try:
            print(f'  🔍 检查 {role_name}...')
            
            # 导入自动化系统
            module = __import__(module_path, fromlist=[''])
            
            # 查找自动化系统类或实例
            automation_system = None
            automation_class = None
            
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if 'automation' in attr_name.lower():
                    if hasattr(attr, 'is_active') or hasattr(attr, 'start_automation'):
                        automation_system = attr
                        break
                    elif hasattr(attr, '__init__') and 'automation' in attr_name.lower():
                        automation_class = attr
            
            status = {
                'module_loaded': True,
                'has_automation_system': automation_system is not None,
                'has_automation_class': automation_class is not None,
                'system_methods': [],
                'is_active': False
            }
            
            if automation_system:
                status['is_active'] = getattr(automation_system, 'is_active', False)
                status['system_methods'] = [method for method in dir(automation_system) 
                                          if not method.startswith('_') and callable(getattr(automation_system, method))]
                print(f'    ✅ 自动化系统实例存在，方法数: {len(status["system_methods"])}')
            elif automation_class:
                status['system_methods'] = [method for method in dir(automation_class) 
                                          if not method.startswith('_')]
                print(f'    ⚠️ 只有自动化类，需要实例化')
            else:
                print(f'    ❌ 未找到自动化系统')
                
            analysis_result['automation_systems'][role_name] = status
            
        except Exception as e:
            analysis_result['automation_systems'][role_name] = {
                'module_loaded': False,
                'error': str(e)
            }
            print(f'    ❌ 导入失败: {e}')
    
    # 2. 检查DeepSeek配置
    print(f'\n🧠 检查DeepSeek人设配置...')
    role_modules = ['tianshu_star', 'tianxuan_star', 'tianji_star', 'tianquan_star', 'yuheng_star', 'kaiyang_star', 'yaoguang_star']
    
    for role_name in role_modules:
        try:
            print(f'  🔍 检查 {role_name} DeepSeek配置...')
            config_module = __import__(f'roles.{role_name}.config.deepseek_config', fromlist=[''])
            
            config_status = {
                'config_exists': True,
                'has_config_function': hasattr(config_module, 'get_deepseek_config'),
                'has_role_setting': False,
                'has_professional_prompts': hasattr(config_module, 'PROFESSIONAL_PROMPTS'),
                'config_details': {}
            }
            
            # 检查角色设定
            role_setting_attrs = [f'{role_name.upper()}_ROLE_SETTING', 'get_role_setting', 'ROLE_SETTING']
            for attr in role_setting_attrs:
                if hasattr(config_module, attr):
                    config_status['has_role_setting'] = True
                    break
            
            # 获取配置详情
            if config_status['has_config_function']:
                try:
                    config = config_module.get_deepseek_config()
                    config_status['config_details'] = {
                        'temperature': config.get('temperature', 'unknown'),
                        'max_tokens': config.get('max_tokens', 'unknown'),
                        'model': config.get('model', 'unknown')
                    }
                except:
                    pass
            
            analysis_result['deepseek_configs'][role_name] = config_status
            print(f'    ✅ 配置完整: 设定={config_status["has_role_setting"]}, 函数={config_status["has_config_function"]}')
            
        except Exception as e:
            analysis_result['deepseek_configs'][role_name] = {
                'config_exists': False,
                'error': str(e)
            }
            print(f'    ❌ 配置缺失: {e}')
    
    # 3. 检查传奇记忆系统
    print(f'\n🧠 检查传奇记忆系统集成...')
    try:
        from core.domain.memory.legendary.interface import legendary_memory_interface
        
        memory_initialized = await legendary_memory_interface.initialize()
        memory_stats = legendary_memory_interface.get_memory_statistics()
        
        analysis_result['memory_integration'] = {
            'system_available': True,
            'initialized': memory_initialized,
            'statistics': memory_stats,
            'total_memories': memory_stats.get('total_memories', 0)
        }
        
        print(f'    ✅ 传奇记忆系统: 初始化={memory_initialized}, 记忆数量={memory_stats.get("total_memories", 0)}')
        
    except Exception as e:
        analysis_result['memory_integration'] = {
            'system_available': False,
            'error': str(e)
        }
        print(f'    ❌ 传奇记忆系统: {e}')
    
    # 4. 检查绩效系统
    print(f'\n📊 检查绩效监控系统...')
    try:
        from core.performance.star_performance_monitor import star_performance_monitor
        
        perf_status = star_performance_monitor.get_system_status()
        
        analysis_result['performance_monitoring'] = {
            'system_available': True,
            'status': perf_status,
            'is_initialized': getattr(star_performance_monitor, 'is_initialized', False)
        }
        
        print(f'    ✅ 绩效监控系统: {perf_status}')
        
    except Exception as e:
        analysis_result['performance_monitoring'] = {
            'system_available': False,
            'error': str(e)
        }
        print(f'    ❌ 绩效监控系统: {e}')
    
    # 5. 检查层级系统
    print(f'\n🏛️ 检查层级权限系统...')
    try:
        from core.enhanced_seven_stars_hierarchy import EnhancedSevenStarsHierarchy
        
        hierarchy = EnhancedSevenStarsHierarchy()
        
        analysis_result['hierarchy_system'] = {
            'system_available': True,
            'is_initialized': hierarchy.is_initialized,
            'agents_count': len(hierarchy.agents),
            'performance_metrics': bool(hierarchy.performance_metrics)
        }
        
        print(f'    ✅ 层级系统: 初始化={hierarchy.is_initialized}, 角色数量={len(hierarchy.agents)}')
        
    except Exception as e:
        analysis_result['hierarchy_system'] = {
            'system_available': False,
            'error': str(e)
        }
        print(f'    ❌ 层级系统: {e}')
    
    # 6. 生成总结
    print(f'\n📋 生成总结报告...')
    
    automation_working = sum(1 for status in analysis_result['automation_systems'].values() 
                           if status.get('has_automation_system', False))
    deepseek_working = sum(1 for status in analysis_result['deepseek_configs'].values() 
                         if status.get('config_exists', False))
    
    analysis_result['summary'] = {
        'automation_systems_working': f'{automation_working}/7',
        'deepseek_configs_working': f'{deepseek_working}/7',
        'memory_system_working': analysis_result['memory_integration'].get('system_available', False),
        'performance_system_working': analysis_result['performance_monitoring'].get('system_available', False),
        'hierarchy_system_working': analysis_result['hierarchy_system'].get('system_available', False),
        'overall_score': (automation_working + deepseek_working + 
                         int(analysis_result['memory_integration'].get('system_available', False)) +
                         int(analysis_result['performance_monitoring'].get('system_available', False)) +
                         int(analysis_result['hierarchy_system'].get('system_available', False))) / 17 * 100
    }
    
    print('=' * 60)
    print(f'🎯 总结报告:')
    print(f'  自动化系统: {automation_working}/7 个角色正常')
    print(f'  DeepSeek配置: {deepseek_working}/7 个角色正常')
    print(f'  传奇记忆系统: {"✅" if analysis_result["memory_integration"].get("system_available") else "❌"}')
    print(f'  绩效监控系统: {"✅" if analysis_result["performance_monitoring"].get("system_available") else "❌"}')
    print(f'  层级权限系统: {"✅" if analysis_result["hierarchy_system"].get("system_available") else "❌"}')
    print(f'  整体完成度: {analysis_result["summary"]["overall_score"]:.1f}%')
    
    # 保存报告
    report_file = f'deep_seven_roles_analysis_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2)
    
    print(f'\n📄 详细报告已保存到: {report_file}')
    
    return analysis_result

if __name__ == "__main__":
    result = asyncio.run(deep_analyze_seven_roles())
