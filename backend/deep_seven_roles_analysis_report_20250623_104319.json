{"timestamp": "2025-06-23T10:43:16.734415", "automation_systems": {"天枢星": {"module_loaded": true, "has_automation_system": true, "has_automation_class": false, "system_methods": ["execute_market_analysis", "get_automation_status", "start_automation", "stop_automation"], "is_active": false}, "天璇星": {"module_loaded": true, "has_automation_system": true, "has_automation_class": false, "system_methods": ["execute_technical_analysis", "get_automation_status", "start_automation"], "is_active": false}, "天玑星": {"module_loaded": true, "has_automation_system": true, "has_automation_class": false, "system_methods": ["execute_risk_analysis", "get_automation_status", "start_automation"], "is_active": false}, "天权星": {"module_loaded": true, "has_automation_system": true, "has_automation_class": false, "system_methods": ["execute_decision_automation", "get_automation_status", "start_automation", "stop_automation"], "is_active": false}, "玉衡星": {"module_loaded": true, "has_automation_system": true, "has_automation_class": false, "system_methods": ["execute_trading_automation", "get_automation_status", "start_automation"], "is_active": false}, "开阳星": {"module_loaded": true, "has_automation_system": false, "has_automation_class": true, "system_methods": ["execute_stock_selection_automation", "initialize", "is_initialized", "service_name", "version"], "is_active": false}, "瑶光星": {"module_loaded": true, "has_automation_system": true, "has_automation_class": false, "system_methods": ["auto_optimize_system", "check_system_status", "collect_performance_metrics", "continuous_monitoring", "daily_data_preparation", "daily_data_quality_check", "daily_factor_research", "daily_learning_summary", "daily_model_training", "daily_strategy_optimization", "generate_daily_learning_report", "get_all_available_stocks", "get_automation_status", "get_daily_research_stock_pool", "get_stock_data_for_research", "monitor_task_queue", "monthly_strategy_evolution", "optimize_trading_strategies", "rank_and_filter_selections", "run_local_model_training", "run_rd_agent_factor_evolution", "run_rd_agent_model_training", "run_strategy_backtest", "setup_automation_schedules", "start_automation", "stop_automation", "system_health_check", "trigger_memory_consolidation", "update_learning_records", "weekly_deep_research"], "is_active": false}}, "deepseek_configs": {"tianshu_star": {"config_exists": true, "has_config_function": true, "has_role_setting": true, "has_professional_prompts": true, "config_details": {"temperature": 0.3, "max_tokens": 2000, "model": "deepseek-chat"}}, "tianxuan_star": {"config_exists": true, "has_config_function": true, "has_role_setting": true, "has_professional_prompts": true, "config_details": {"temperature": 0.4, "max_tokens": 2000, "model": "deepseek-chat"}}, "tianji_star": {"config_exists": true, "has_config_function": true, "has_role_setting": true, "has_professional_prompts": true, "config_details": {"temperature": 0.2, "max_tokens": 2000, "model": "deepseek-chat"}}, "tianquan_star": {"config_exists": true, "has_config_function": true, "has_role_setting": true, "has_professional_prompts": true, "config_details": {"temperature": 0.6, "max_tokens": 2000, "model": "deepseek-chat"}}, "yuheng_star": {"config_exists": true, "has_config_function": true, "has_role_setting": true, "has_professional_prompts": true, "config_details": {"temperature": 0.2, "max_tokens": 2000, "model": "deepseek-chat"}}, "kaiyang_star": {"config_exists": true, "has_config_function": true, "has_role_setting": true, "has_professional_prompts": true, "config_details": {"temperature": 0.5, "max_tokens": 2000, "model": "deepseek-chat"}}, "yaoguang_star": {"config_exists": true, "has_config_function": true, "has_role_setting": true, "has_professional_prompts": true, "config_details": {"temperature": 0.7, "max_tokens": 2000, "model": "deepseek-chat"}}}, "memory_integration": {"system_available": false, "error": "'LegendaryMemoryInterface' object has no attribute 'get_memory_statistics'"}, "performance_monitoring": {"system_available": false, "error": "'StarPerformanceMonitor' object has no attribute 'get_system_status'"}, "hierarchy_system": {"system_available": true, "is_initialized": false, "agents_count": 7, "performance_metrics": false}, "summary": {"automation_systems_working": "6/7", "deepseek_configs_working": "7/7", "memory_system_working": false, "performance_system_working": false, "hierarchy_system_working": true, "overall_score": 82.35294117647058}}