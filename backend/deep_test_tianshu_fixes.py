#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度测试天枢星修复效果
确保所有Bug都已修复
"""

import asyncio
import requests
import json
import re
from pathlib import Path

async def test_crawl4ai_direct():
    """直接测试Crawl4AI功能"""
    print("🕷️ 直接测试Crawl4AI功能...")
    
    try:
        from crawl4ai import AsyncWebCrawler
        
        async with AsyncWebCrawler(verbose=False) as crawler:
            # 测试爬取新浪财经
            result = await crawler.arun(
                url="https://finance.sina.com.cn/",
                word_count_threshold=10,
                bypass_cache=True
            )
            
            if result.success:
                print(f"   ✅ Crawl4AI直接测试成功: {len(result.markdown)}字符")
                # 检查是否包含财经内容
                content = result.markdown.lower()
                financial_keywords = ["股票", "基金", "证券", "投资", "财经", "金融"]
                found_keywords = [kw for kw in financial_keywords if kw in content]
                print(f"   📰 财经关键词: {found_keywords}")
                return True
            else:
                print(f"   ❌ Crawl4AI直接测试失败: {result.error_message}")
                return False
                
    except Exception as e:
        print(f"   ❌ Crawl4AI直接测试异常: {e}")
        return False

def check_code_cleanup():
    """检查代码清理效果"""
    print("\n🧹 检查代码清理效果...")
    
    # 检查新闻收集服务
    news_service_file = Path("backend/roles/tianshu_star/services/news_collection_service.py")
    
    if news_service_file.exists():
        with open(news_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有示例数据
        issues = []
        
        # 检查示例新闻数据
        if "A股三大指数集体收涨" in content:
            issues.append("仍有示例新闻数据")
        
        if "_generate_sample_news(" in content:
            issues.append("仍有示例数据生成方法")
        
        if "sample_news = [" in content:
            issues.append("仍有示例新闻数组")

        # 检查示例标识
        sample_count = content.count("示例")
        if sample_count > 2:  # 允许少量注释中的"示例"
            issues.append(f"仍有{sample_count}个示例标识")
        
        print(f"   📄 news_collection_service.py: {len(issues)}个问题")
        for issue in issues:
            print(f"      ❌ {issue}")
        
        if not issues:
            print("   ✅ 代码清理完成")
        
        return len(issues) == 0
    
    return False

async def test_apis_comprehensive():
    """全面测试API"""
    print("\n🔌 全面测试API...")
    
    base_url = "http://127.0.0.1:8003"
    
    test_results = {}
    
    # 测试天枢星API
    print("   测试天枢星API...")
    try:
        response = requests.get(f"{base_url}/api/tianshu/news/latest?limit=5", timeout=10)
        if response.status_code == 200:
            data = response.json()
            response_text = str(data).lower()
            
            # 详细检查模拟标识
            mock_indicators = {
                "sample": response_text.count("sample"),
                "示例": response_text.count("示例"),
                "test": response_text.count("test"),
                "mock": response_text.count("mock"),
                "example": response_text.count("example"),

            }
            
            total_mock = sum(mock_indicators.values())
            
            test_results["tianshu_api"] = {
                "status": "成功",
                "mock_indicators": mock_indicators,
                "total_mock_count": total_mock,
                "is_clean": total_mock == 0,
                "news_count": len(data.get("data", {}).get("news", [])) if "data" in data else 0
            }
            
            print(f"      状态: 成功")
            print(f"      新闻数: {test_results['tianshu_api']['news_count']}")
            print(f"      模拟标识总数: {total_mock}")
            if total_mock > 0:
                print(f"      详细: {mock_indicators}")
        else:
            test_results["tianshu_api"] = {"status": "失败", "code": response.status_code}
            print(f"      状态: 失败 ({response.status_code})")
    except Exception as e:
        test_results["tianshu_api"] = {"status": "异常", "error": str(e)}
        print(f"      状态: 异常 ({e})")
    
    # 测试Intelligence API
    print("   测试Intelligence API...")
    try:
        response = requests.get(f"{base_url}/api/intelligence/news", timeout=10)
        if response.status_code == 200:
            data = response.json()
            response_text = str(data).lower()
            
            # 详细检查模拟标识
            mock_indicators = {
                "sample": response_text.count("sample"),
                "示例": response_text.count("示例"),
                "test": response_text.count("test"),
                "mock": response_text.count("mock"),
                "example": response_text.count("example"),

            }
            
            total_mock = sum(mock_indicators.values())
            
            test_results["intelligence_api"] = {
                "status": "成功",
                "mock_indicators": mock_indicators,
                "total_mock_count": total_mock,
                "is_clean": total_mock == 0,
                "news_count": len(data.get("data", {}).get("news", [])) if "data" in data else 0
            }
            
            print(f"      状态: 成功")
            print(f"      新闻数: {test_results['intelligence_api']['news_count']}")
            print(f"      模拟标识总数: {total_mock}")
            if total_mock > 0:
                print(f"      详细: {mock_indicators}")
        else:
            test_results["intelligence_api"] = {"status": "失败", "code": response.status_code}
            print(f"      状态: 失败 ({response.status_code})")
    except Exception as e:
        test_results["intelligence_api"] = {"status": "异常", "error": str(e)}
        print(f"      状态: 异常 ({e})")
    
    return test_results

def calculate_fix_score(crawl4ai_works, code_clean, api_results):
    """计算修复评分"""
    print("\n📊 计算修复评分...")
    
    scores = []
    
    # Crawl4AI功能评分 (30%)
    crawl4ai_score = 100 if crawl4ai_works else 0
    scores.append(("Crawl4AI功能", crawl4ai_score, 30))
    
    # 代码清理评分 (40%)
    code_score = 100 if code_clean else 0
    scores.append(("代码清理", code_score, 40))
    
    # API清洁度评分 (30%)
    api_clean_count = 0
    api_total_count = 0
    
    for api_name, result in api_results.items():
        if result.get("status") == "成功":
            api_total_count += 1
            if result.get("is_clean", False):
                api_clean_count += 1
    
    api_score = (api_clean_count / api_total_count * 100) if api_total_count > 0 else 0
    scores.append(("API清洁度", api_score, 30))
    
    # 计算总分
    total_score = sum(score * weight / 100 for _, score, weight in scores)
    
    print(f"   评分详情:")
    for name, score, weight in scores:
        print(f"      {name}: {score:.1f}% (权重{weight}%)")
    
    print(f"   总评分: {total_score:.1f}%")
    
    return total_score, scores

async def main():
    """主函数"""
    print("🚀 开始天枢星修复效果深度测试...")
    print("=" * 80)
    
    # 1. 直接测试Crawl4AI
    crawl4ai_works = await test_crawl4ai_direct()
    
    # 2. 检查代码清理
    code_clean = check_code_cleanup()
    
    # 3. 全面测试API
    api_results = await test_apis_comprehensive()
    
    # 4. 计算修复评分
    total_score, score_details = calculate_fix_score(crawl4ai_works, code_clean, api_results)
    
    print("\n" + "=" * 80)
    print("📊 天枢星修复效果总结")
    print("=" * 80)
    
    print(f"🕷️ Crawl4AI功能: {'✅正常' if crawl4ai_works else '❌异常'}")
    print(f"🧹 代码清理: {'✅完成' if code_clean else '❌未完成'}")
    
    print(f"🔌 API测试结果:")
    for api_name, result in api_results.items():
        status = result.get("status", "未知")
        is_clean = result.get("is_clean", False)
        clean_status = "✅无模拟数据" if is_clean else "❌有模拟数据"
        print(f"   {clean_status} {api_name}: {status}")
    
    print(f"\n🎯 总体修复评分: {total_score:.1f}%")
    
    if total_score >= 90:
        print("   ✅ 修复效果优秀，天枢星已达到生产标准")
    elif total_score >= 70:
        print("   ⚠️ 修复效果良好，但仍有改进空间")
    elif total_score >= 50:
        print("   🔧 修复效果一般，需要继续优化")
    else:
        print("   ❌ 修复效果较差，需要重新修复")
    
    # 保存测试报告
    report = {
        "timestamp": "2025-06-21 21:00:00",
        "crawl4ai_works": crawl4ai_works,
        "code_clean": code_clean,
        "api_results": api_results,
        "total_score": total_score,
        "score_details": score_details
    }
    
    with open("backend/tianshu_fix_test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细测试报告已保存: backend/tianshu_fix_test_report.json")
    
    return total_score

if __name__ == "__main__":
    score = asyncio.run(main())
    print(f"\n✅ 测试完成，总评分: {score:.1f}%")
