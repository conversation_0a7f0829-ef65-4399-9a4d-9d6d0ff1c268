{"fix_time": "2025-06-23T15:42:52.942611", "total_files_fixed": 11, "fixed_files": ["D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\services\\tianji_automation_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\detailed_system_test.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\automation\\quantitative_research_automation.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\kaiyang_automation_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\final_complete_roles_test.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\core\\tianquan_automation_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\fix_deepseek_none_issue.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\real_debate_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianshu_star\\services\\tianshu_automation_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\yuheng_automation_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianxuan_star\\services\\tianxuan_automation_system.py"], "issues_fixed": ["DeepSeek服务返回None导致的AttributeError", "result.get()调用的空指针异常", "response.get()调用的空指针异常", "角色DeepSeek调用的异常处理"], "status": "completed"}