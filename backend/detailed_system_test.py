#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细系统测试 - 找出具体的失败项目
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any, List
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DetailedSystemTest:
    """详细系统测试"""
    
    def __init__(self):
        self.test_results = {}
        self.failed_tests = []
        
    async def run_detailed_test(self):
        """运行详细测试"""
        print("🔍 开始详细系统测试 - 找出所有失败项目")
        print("=" * 80)
        
        # 测试每个具体功能
        await self.test_memory_integration()
        await self.test_performance_integration()
        await self.test_deepseek_integration()
        await self.test_hierarchy_integration()
        await self.test_yaoguang_data_collection()
        await self.test_role_automation_methods()
        
        # 生成详细报告
        await self.generate_detailed_report()
        
    async def test_memory_integration(self):
        """测试记忆系统集成"""
        print("\n🧠 详细测试记忆系统集成...")
        print("-" * 50)
        
        roles_config = {
            "tianshu_star": "roles.tianshu_star.services.tianshu_automation_system",
            "tianxuan_star": "roles.tianxuan_star.services.tianxuan_automation_system", 
            "tianji_star": "roles.tianji_star.services.tianji_automation_system",
            "tianquan_star": "roles.tianquan_star.core.tianquan_automation_system",
            "yuheng_star": "roles.yuheng_star.services.yuheng_automation_system",
            "kaiyang_star": "roles.kaiyang_star.services.kaiyang_automation_system",
            "yaoguang_star": "roles.yaoguang_star.automation.quantitative_research_automation"
        }
        
        for role_name, module_path in roles_config.items():
            print(f"  🔍 测试 {role_name} 记忆集成...")
            
            try:
                # 动态导入模块
                module_parts = module_path.split('.')
                module = __import__(module_path, fromlist=[module_parts[-1]])
                
                # 获取自动化系统实例
                automation_system = None
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if hasattr(attr, 'store_memory') and hasattr(attr, 'retrieve_memories'):
                        automation_system = attr
                        break
                
                if automation_system:
                    # 测试记忆存储 - 修复metadata格式
                    try:
                        store_result = await automation_system.store_memory(
                            f"测试记忆内容 - {role_name}",
                            "test",
                            "normal"
                        )
                        
                        if store_(result or {}).get("success"):
                            print(f"    ✅ {role_name} 记忆存储成功")
                            self.test_results[f"{role_name}_memory_store"] = True
                        else:
                            print(f"    ❌ {role_name} 记忆存储失败: {store_(result or {}).get('error')}")
                            self.test_results[f"{role_name}_memory_store"] = False
                            self.failed_tests.append(f"{role_name}_memory_store")
                    except Exception as e:
                        print(f"    ❌ {role_name} 记忆存储异常: {e}")
                        self.test_results[f"{role_name}_memory_store"] = False
                        self.failed_tests.append(f"{role_name}_memory_store")
                    
                    # 测试记忆检索
                    try:
                        retrieve_result = await automation_system.retrieve_memories("测试", 5)
                        
                        if isinstance(retrieve_result, list):
                            print(f"    ✅ {role_name} 记忆检索成功: {len(retrieve_result)}条")
                            self.test_results[f"{role_name}_memory_retrieve"] = True
                        else:
                            print(f"    ❌ {role_name} 记忆检索失败")
                            self.test_results[f"{role_name}_memory_retrieve"] = False
                            self.failed_tests.append(f"{role_name}_memory_retrieve")
                    except Exception as e:
                        print(f"    ❌ {role_name} 记忆检索异常: {e}")
                        self.test_results[f"{role_name}_memory_retrieve"] = False
                        self.failed_tests.append(f"{role_name}_memory_retrieve")
                        
                else:
                    print(f"    ❌ {role_name} 自动化系统未找到")
                    self.test_results[f"{role_name}_memory_store"] = False
                    self.test_results[f"{role_name}_memory_retrieve"] = False
                    self.failed_tests.extend([f"{role_name}_memory_store", f"{role_name}_memory_retrieve"])
                    
            except Exception as e:
                print(f"    ❌ {role_name} 模块导入失败: {e}")
                self.test_results[f"{role_name}_memory_store"] = False
                self.test_results[f"{role_name}_memory_retrieve"] = False
                self.failed_tests.extend([f"{role_name}_memory_store", f"{role_name}_memory_retrieve"])
    
    async def test_performance_integration(self):
        """测试绩效系统集成"""
        print("\n📊 详细测试绩效系统集成...")
        print("-" * 50)
        
        roles_config = {
            "tianshu_star": "roles.tianshu_star.services.tianshu_automation_system",
            "tianxuan_star": "roles.tianxuan_star.services.tianxuan_automation_system", 
            "tianji_star": "roles.tianji_star.services.tianji_automation_system",
            "tianquan_star": "roles.tianquan_star.core.tianquan_automation_system",
            "yuheng_star": "roles.yuheng_star.services.yuheng_automation_system",
            "kaiyang_star": "roles.kaiyang_star.services.kaiyang_automation_system",
            "yaoguang_star": "roles.yaoguang_star.automation.quantitative_research_automation"
        }
        
        for role_name, module_path in roles_config.items():
            print(f"  🔍 测试 {role_name} 绩效集成...")
            
            try:
                # 动态导入模块
                module_parts = module_path.split('.')
                module = __import__(module_path, fromlist=[module_parts[-1]])
                
                # 获取自动化系统实例
                automation_system = None
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if hasattr(attr, 'record_performance') and hasattr(attr, 'get_performance_stats'):
                        automation_system = attr
                        break
                
                if automation_system:
                    # 测试绩效记录
                    try:
                        record_result = await automation_system.record_performance(
                            "test_metric", 
                            0.85, 
                            {"test": True}
                        )
                        
                        if record_(result or {}).get("success"):
                            print(f"    ✅ {role_name} 绩效记录成功")
                            self.test_results[f"{role_name}_performance_record"] = True
                        else:
                            print(f"    ❌ {role_name} 绩效记录失败: {record_(result or {}).get('error')}")
                            self.test_results[f"{role_name}_performance_record"] = False
                            self.failed_tests.append(f"{role_name}_performance_record")
                    except Exception as e:
                        print(f"    ❌ {role_name} 绩效记录异常: {e}")
                        self.test_results[f"{role_name}_performance_record"] = False
                        self.failed_tests.append(f"{role_name}_performance_record")
                    
                    # 测试绩效统计 - 修复self参数问题
                    try:
                        # 创建实例来调用方法
                        instance = automation_system()
                        stats_result = instance.get_performance_stats()

                        if isinstance(stats_result, dict) and "error" not in stats_result:
                            print(f"    ✅ {role_name} 绩效统计成功")
                            self.test_results[f"{role_name}_performance_stats"] = True
                        else:
                            print(f"    ❌ {role_name} 绩效统计失败: {stats_(result or {}).get('error', 'Unknown error')}")
                            self.test_results[f"{role_name}_performance_stats"] = False
                            self.failed_tests.append(f"{role_name}_performance_stats")
                    except Exception as e:
                        print(f"    ❌ {role_name} 绩效统计异常: {e}")
                        self.test_results[f"{role_name}_performance_stats"] = False
                        self.failed_tests.append(f"{role_name}_performance_stats")
                        
                else:
                    print(f"    ❌ {role_name} 自动化系统未找到")
                    self.test_results[f"{role_name}_performance_record"] = False
                    self.test_results[f"{role_name}_performance_stats"] = False
                    self.failed_tests.extend([f"{role_name}_performance_record", f"{role_name}_performance_stats"])
                    
            except Exception as e:
                print(f"    ❌ {role_name} 模块导入失败: {e}")
                self.test_results[f"{role_name}_performance_record"] = False
                self.test_results[f"{role_name}_performance_stats"] = False
                self.failed_tests.extend([f"{role_name}_performance_record", f"{role_name}_performance_stats"])
    
    async def test_deepseek_integration(self):
        """测试DeepSeek集成"""
        print("\n🧠 详细测试DeepSeek集成...")
        print("-" * 50)
        
        roles_config = {
            "tianshu_star": "roles.tianshu_star.services.tianshu_automation_system",
            "tianxuan_star": "roles.tianxuan_star.services.tianxuan_automation_system", 
            "tianji_star": "roles.tianji_star.services.tianji_automation_system",
            "tianquan_star": "roles.tianquan_star.core.tianquan_automation_system",
            "yuheng_star": "roles.yuheng_star.services.yuheng_automation_system",
            "kaiyang_star": "roles.kaiyang_star.services.kaiyang_automation_system",
            "yaoguang_star": "roles.yaoguang_star.automation.quantitative_research_automation"
        }
        
        for role_name, module_path in roles_config.items():
            print(f"  🔍 测试 {role_name} DeepSeek集成...")
            
            try:
                # 动态导入模块
                module_parts = module_path.split('.')
                module = __import__(module_path, fromlist=[module_parts[-1]])
                
                # 获取自动化系统实例
                automation_system = None
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if hasattr(attr, '_call_role_deepseek'):
                        automation_system = attr
                        break
                
                if automation_system:
                    # 测试DeepSeek调用 - 修复参数格式
                    try:
                        deepseek_result = await automation_system._call_role_deepseek(
                            "测试分析请求",
                            "测试分析请求"
                        )
                        
                        if deepseek_(result or {}).get("success"):
                            print(f"    ✅ {role_name} DeepSeek调用成功")
                            self.test_results[f"{role_name}_deepseek_call"] = True
                        else:
                            print(f"    ❌ {role_name} DeepSeek调用失败: {deepseek_(result or {}).get('error')}")
                            self.test_results[f"{role_name}_deepseek_call"] = False
                            self.failed_tests.append(f"{role_name}_deepseek_call")
                    except Exception as e:
                        print(f"    ❌ {role_name} DeepSeek调用异常: {e}")
                        self.test_results[f"{role_name}_deepseek_call"] = False
                        self.failed_tests.append(f"{role_name}_deepseek_call")
                        
                else:
                    print(f"    ❌ {role_name} 自动化系统未找到")
                    self.test_results[f"{role_name}_deepseek_call"] = False
                    self.failed_tests.append(f"{role_name}_deepseek_call")
                    
            except Exception as e:
                print(f"    ❌ {role_name} 模块导入失败: {e}")
                self.test_results[f"{role_name}_deepseek_call"] = False
                self.failed_tests.append(f"{role_name}_deepseek_call")
    
    async def test_hierarchy_integration(self):
        """测试层级系统集成"""
        print("\n🏛️ 详细测试层级系统集成...")
        print("-" * 50)
        
        try:
            from enhanced_seven_stars_hierarchy import enhanced_seven_stars_hierarchy
            
            # 测试层级系统初始化
            if hasattr(enhanced_seven_stars_hierarchy, 'is_initialized'):
                print("    ✅ 层级系统初始化成功")
                self.test_results["hierarchy_init"] = True
            else:
                print("    ❌ 层级系统初始化失败")
                self.test_results["hierarchy_init"] = False
                self.failed_tests.append("hierarchy_init")
                
            # 测试权限检查
            if hasattr(enhanced_seven_stars_hierarchy, 'check_permission'):
                print("    ✅ 层级权限检查功能存在")
                self.test_results["hierarchy_permission"] = True
            else:
                print("    ❌ 层级权限检查功能缺失")
                self.test_results["hierarchy_permission"] = False
                self.failed_tests.append("hierarchy_permission")
                
        except Exception as e:
            print(f"    ❌ 层级系统测试失败: {e}")
            self.test_results["hierarchy_init"] = False
            self.test_results["hierarchy_permission"] = False
            self.failed_tests.extend(["hierarchy_init", "hierarchy_permission"])
    
    async def test_yaoguang_data_collection(self):
        """测试瑶光星数据收集"""
        print("\n🌟 详细测试瑶光星数据收集...")
        print("-" * 50)
        
        try:
            from roles.yaoguang_star.services.comprehensive_data_collection_service import comprehensive_data_collection_service
            
            # 测试数据收集统计
            stats = await comprehensive_data_collection_service.get_collection_stats()
            if stats and isinstance(stats, dict):
                print("    ✅ 数据收集统计成功")
                self.test_results["yaoguang_stats"] = True
            else:
                print("    ❌ 数据收集统计失败")
                self.test_results["yaoguang_stats"] = False
                self.failed_tests.append("yaoguang_stats")
            
            # 测试选股注册
            register_result = await comprehensive_data_collection_service.register_kaiyang_selection("000001", "测试选股")
            if register_result:
                print("    ✅ 选股注册成功")
                self.test_results["yaoguang_register"] = True
            else:
                print("    ❌ 选股注册失败")
                self.test_results["yaoguang_register"] = False
                self.failed_tests.append("yaoguang_register")
                
        except Exception as e:
            print(f"    ❌ 瑶光星数据收集测试失败: {e}")
            self.test_results["yaoguang_stats"] = False
            self.test_results["yaoguang_register"] = False
            self.failed_tests.extend(["yaoguang_stats", "yaoguang_register"])
    
    async def test_role_automation_methods(self):
        """测试角色自动化方法"""
        print("\n🤖 详细测试角色自动化方法...")
        print("-" * 50)
        
        roles_config = {
            "tianshu_star": ("roles.tianshu_star.services.tianshu_automation_system", "execute_market_analysis"),
            "tianxuan_star": ("roles.tianxuan_star.services.tianxuan_automation_system", "execute_technical_analysis"),
            "tianji_star": ("roles.tianji_star.services.tianji_automation_system", "execute_risk_analysis"),
            "tianquan_star": ("roles.tianquan_star.core.tianquan_automation_system", "execute_decision_automation"),
            "yuheng_star": ("roles.yuheng_star.services.yuheng_automation_system", "execute_trading_automation"),
            "kaiyang_star": ("roles.kaiyang_star.services.kaiyang_automation_system", "execute_stock_selection_automation"),
            "yaoguang_star": ("roles.yaoguang_star.automation.quantitative_research_automation", "start_automation")
        }
        
        for role_name, (module_path, method_name) in roles_config.items():
            print(f"  🔍 测试 {role_name} 自动化方法...")
            
            try:
                # 动态导入模块
                module_parts = module_path.split('.')
                module = __import__(module_path, fromlist=[module_parts[-1]])
                
                # 获取自动化系统实例
                automation_system = None
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if hasattr(attr, method_name):
                        automation_system = attr
                        break
                
                if automation_system:
                    print(f"    ✅ {role_name} 自动化方法存在")
                    self.test_results[f"{role_name}_automation_method"] = True
                else:
                    print(f"    ❌ {role_name} 自动化方法缺失")
                    self.test_results[f"{role_name}_automation_method"] = False
                    self.failed_tests.append(f"{role_name}_automation_method")
                    
            except Exception as e:
                print(f"    ❌ {role_name} 自动化方法测试失败: {e}")
                self.test_results[f"{role_name}_automation_method"] = False
                self.failed_tests.append(f"{role_name}_automation_method")
    
    async def generate_detailed_report(self):
        """生成详细报告"""
        print("\n📋 生成详细测试报告...")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = len(self.failed_tests)
        
        print(f"📊 详细测试统计:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过测试: {passed_tests}")
        print(f"  失败测试: {failed_tests}")
        print(f"  成功率: {passed_tests/total_tests*100:.1f}%")
        
        if self.failed_tests:
            print(f"\n❌ 失败的测试项目 ({len(self.failed_tests)}项):")
            for i, failed_test in enumerate(self.failed_tests, 1):
                print(f"  {i}. {failed_test}")
        else:
            print("\n🎉 所有测试都通过了!")
        
        # 保存详细报告
        report = {
            "test_results": self.test_results,
            "failed_tests": self.failed_tests,
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": passed_tests/total_tests*100 if total_tests > 0 else 0
            },
            "test_time": datetime.now().isoformat()
        }
        
        report_filename = f"detailed_system_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细报告已保存到: {report_filename}")

async def main():
    """主函数"""
    test_runner = DetailedSystemTest()
    await test_runner.run_detailed_test()

if __name__ == "__main__":
    asyncio.run(main())
