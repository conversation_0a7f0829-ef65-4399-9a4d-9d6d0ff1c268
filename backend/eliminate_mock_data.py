#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
        pass  # 专业版模式
确保系统100%使用真实数据
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

class MockDataEliminator:
    """模拟数据清除器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.mock_patterns = [
            # 模拟数据关键词
            r'generateDeterministicValue',
        pass  # 专业版模式
        pass  # 专业版模式
            r'mock_data',
            r'simulate.*data',
        pass  # 专业版模式
        pass  # 专业版模式
            r'模拟数据',
            r'fake_data',
            r'dummy_data',
            r'np\.random',
            r'random\.uniform',
            r'random\.normal',
            r'Math\.random',
        pass  # 专业版模式
        pass  # 专业版模式
        pass  # 专业版模式
            r'backup.*data',
            # 模拟服务
            r'MockService',
            r'FallbackService',
            r'SimulationService'
        ]
        
        self.files_to_check = []
        self.issues_found = []
        
    def scan_project(self):
        """扫描项目中的所有文件"""
        print("🔍 扫描项目中的模拟数据...")
        
        # 扫描后端文件
        backend_patterns = [
            "backend/**/*.py",
            "backend/**/*.json"
        ]
        
        # 扫描前端文件
        frontend_patterns = [
            "frontend/src/**/*.ts",
            "frontend/src/**/*.vue",
            "frontend/src/**/*.js"
        ]
        
        all_patterns = backend_patterns + frontend_patterns
        
        for pattern in all_patterns:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file():
                    self.files_to_check.append(file_path)
        
        print(f"   发现 {len(self.files_to_check)} 个文件需要检查")
        
    def check_file_for_mock_data(self, file_path: Path) -> List[Dict[str, Any]]:
        """检查单个文件中的模拟数据"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                for pattern in self.mock_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        issues.append({
                            "file": str(file_path),
                            "line": line_num,
                            "content": line.strip(),
                            "pattern": pattern,
                            "severity": self._get_severity(pattern)
                        })
        
        except Exception as e:
            print(f"   ⚠️ 无法检查文件 {file_path}: {e}")
        
        return issues
    
    def _get_severity(self, pattern: str) -> str:
        """获取问题严重程度"""
        critical_patterns = [
            'generateDeterministicValue',
            'np\.random',
            'Math\.random',
        pass  # 专业版模式
            'mock_data'
        ]
        
        for critical in critical_patterns:
            if re.search(critical, pattern, re.IGNORECASE):
                return "CRITICAL"
        
        return "WARNING"
    
    def analyze_issues(self):
        """分析发现的问题"""
        print("\n📊 分析模拟数据问题...")
        
        for file_path in self.files_to_check:
            file_issues = self.check_file_for_mock_data(file_path)
            self.issues_found.extend(file_issues)
        
        # 按严重程度分类
        critical_issues = [issue for issue in self.issues_found if issue["severity"] == "CRITICAL"]
        warning_issues = [issue for issue in self.issues_found if issue["severity"] == "WARNING"]
        
        print(f"   🚨 严重问题: {len(critical_issues)} 个")
        print(f"   ⚠️ 警告问题: {len(warning_issues)} 个")
        print(f"   📋 总问题数: {len(self.issues_found)} 个")
        
        return critical_issues, warning_issues
    
    def generate_replacement_plan(self) -> Dict[str, Any]:
        """生成替换计划"""
        print("\n📋 生成替换计划...")
        
        replacement_plan = {
            "timestamp": datetime.now().isoformat(),
            "total_issues": len(self.issues_found),
            "files_affected": len(set(issue["file"] for issue in self.issues_found)),
            "replacements": []
        }
        
        # 按文件分组问题
        files_with_issues = {}
        for issue in self.issues_found:
            file_path = issue["file"]
            if file_path not in files_with_issues:
                files_with_issues[file_path] = []
            files_with_issues[file_path].append(issue)
        
        # 为每个文件生成替换建议
        for file_path, issues in files_with_issues.items():
            file_plan = {
                "file": file_path,
                "issues_count": len(issues),
                "issues": issues,
                "replacement_strategy": self._get_replacement_strategy(file_path, issues)
            }
            replacement_plan["replacements"].append(file_plan)
        
        return replacement_plan
    
    def _get_replacement_strategy(self, file_path: str, issues: List[Dict]) -> str:
        """获取替换策略"""
        if "frontend" in file_path:
            if "deterministicDataProvider" in file_path:
                return "REMOVE_FILE - 完全删除确定性数据提供者，使用真实API"
            elif ".vue" in file_path:
                return "REPLACE_API_CALLS - 替换为真实API调用"
            elif ".ts" in file_path:
                return "UPDATE_CONFIG - 更新配置为真实数据源"
        
        elif "backend" in file_path:
        pass  # 专业版模式
        pass  # 专业版模式
            elif "mock" in file_path.lower():
                return "REPLACE_WITH_REAL - 替换为真实数据服务"
            else:
                return "UPDATE_LOGIC - 更新业务逻辑使用真实数据"
        
        return "MANUAL_REVIEW - 需要手动审查"
    
    def save_analysis_report(self, replacement_plan: Dict[str, Any]):
        """保存分析报告"""
        report_file = self.project_root / "backend" / "mock_data_elimination_report.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(replacement_plan, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 分析报告已保存: {report_file}")
        
        # 生成Markdown报告
        self._generate_markdown_report(replacement_plan)
    
    def _generate_markdown_report(self, replacement_plan: Dict[str, Any]):
        """生成Markdown报告"""
        report_file = self.project_root / "backend" / "mock_data_elimination_report.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 模拟数据清除报告\n\n")
            f.write(f"**生成时间**: {replacement_plan['timestamp']}\n")
            f.write(f"**总问题数**: {replacement_plan['total_issues']}\n")
            f.write(f"**影响文件数**: {replacement_plan['files_affected']}\n\n")
            
            f.write("## 🚨 严重问题文件\n\n")
            critical_files = [r for r in replacement_plan['replacements'] 
                            if any(issue['severity'] == 'CRITICAL' for issue in r['issues'])]
            
            for file_plan in critical_files:
                f.write(f"### {file_plan['file']}\n")
                f.write(f"**问题数**: {file_plan['issues_count']}\n")
                f.write(f"**替换策略**: {file_plan['replacement_strategy']}\n\n")
                
                for issue in file_plan['issues']:
                    if issue['severity'] == 'CRITICAL':
                        f.write(f"- 第{issue['line']}行: `{issue['content'][:100]}...`\n")
                f.write("\n")
            
            f.write("## ⚠️ 警告问题文件\n\n")
            warning_files = [r for r in replacement_plan['replacements'] 
                           if any(issue['severity'] == 'WARNING' for issue in r['issues'])]
            
            for file_plan in warning_files[:10]:  # 只显示前10个
                f.write(f"### {file_plan['file']}\n")
                f.write(f"**问题数**: {file_plan['issues_count']}\n")
                f.write(f"**替换策略**: {file_plan['replacement_strategy']}\n\n")
        
        print(f"📄 Markdown报告已保存: {report_file}")
    
    def run_analysis(self):
        """运行完整分析"""
        print("🚀 开始模拟数据清除分析...")
        print("=" * 60)
        
        # 1. 扫描项目
        self.scan_project()
        
        # 2. 分析问题
        critical_issues, warning_issues = self.analyze_issues()
        
        # 3. 生成替换计划
        replacement_plan = self.generate_replacement_plan()
        
        # 4. 保存报告
        self.save_analysis_report(replacement_plan)
        
        # 5. 显示摘要
        print("\n" + "=" * 60)
        print("📊 分析摘要")
        print("=" * 60)
        print(f"🚨 严重问题: {len(critical_issues)} 个")
        print(f"⚠️ 警告问题: {len(warning_issues)} 个")
        print(f"📁 影响文件: {replacement_plan['files_affected']} 个")
        
        if critical_issues:
            print("\n🚨 需要立即处理的严重问题:")
            for issue in critical_issues[:5]:  # 显示前5个
                print(f"   - {issue['file']}:{issue['line']} - {issue['pattern']}")
        
        print("\n💡 建议:")
        print("   1. 立即处理所有CRITICAL级别问题")
        print("   2. 删除frontend/src/utils/deterministicDataProvider.ts")
        pass  # 专业版模式
        print("   4. 强制使用真实API数据源")
        print("   5. 更新前端配置使用真实数据")
        
        return replacement_plan

def main():
    """主函数"""
    eliminator = MockDataEliminator()
    replacement_plan = eliminator.run_analysis()
    
    print(f"\n✅ 分析完成！发现 {replacement_plan['total_issues']} 个需要处理的问题")
    print("📄 详细报告已保存到 backend/mock_data_elimination_report.md")

if __name__ == "__main__":
    main()
