{"clean_time": "2025-06-23T15:34:13.439617", "total_files_cleaned": 142, "cleaned_files": ["D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\rd_agent_integration_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\clean_fallback_indicators.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\comprehensive_bug_hunter.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\deep_test_tianshu_fixes.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\eliminate_mock_data.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\final_complete_roles_test.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\final_real_data_verification.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\fix_all_errors_warnings_comprehensive.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\fix_all_issues_comprehensive.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\fix_specific_issues.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\honest_system_status_check.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\main.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\real_professional_automation_test.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\real_signal_components.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\test_class_integrity.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\test_complete_fixes.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\test_learning_with_real_data.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\test_legendary_memory_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\test_rd_agent_simple.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\tianshu_honest_check.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\api\\seven_stars_chat_debate.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\api\\__init___simple.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\api\\__init___temp.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\core\\ai_services.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\core\\enhanced_four_stars_debate.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\rd_agent_localized\\core_config.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\rd_agent_localized\\factor_evaluator.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\rd_agent_localized\\factor_generator.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\rd_agent_localized\\rd_loop_controller.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\rd_agent_localized\\__init__.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\seven_roles_integration.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\collect_all_stocks_eastmoney.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\monitor_failure_rate.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\simple_eastmoney_collector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\simple_stock_collector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\test_tianshu_deep.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\enhanced_factor_extractor.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\api\\roles\\commander.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\api\\roles\\intelligence.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\api\\roles\\yuheng_star_api.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\core\\domain\\memory\\legendary\\coordinator.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\core\\domain\\memory\\legendary\\interface.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\core\\domain\\memory\\simplified\\master.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\external\\TradingAgents-main\\tradingagents\\agents\\managers\\risk_manager.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\rd_agent_integration\\adapters\\akshare_adapter.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\rd_agent_integration\\core\\alpha158_performance_validator.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\rd_agent_integration\\core\\intelligent_factor_selector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\rd_agent_integration\\core\\skill_library_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\simple_tianquan_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\core\\market_scanner_engine.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\client_notification_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\crawler4ai_stock_collector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\enhanced_stock_screening_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\intelligent_portfolio_builder.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\news_analysis_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\rd_agent_portfolio_optimizer.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\real_market_scanning_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\real_opportunity_push_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\real_stock_screening_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\stock_selection_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\trading_calendar_crawler.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\legacy_roles\\kaiyang_star_legacy\\kaiyang_star_new\\core\\kaiyang_stock_detector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\engines\\professional_risk_engine.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\factor_extraction\\technical_factors.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\models\\standard_models.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\services\\enhanced_disc_finllm_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\services\\factor_extraction_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\services\\knowledge_base_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\services\\rd_agent_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\services\\risk_assessment_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\workflows\\enhanced_intelligence_workflow.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\workflows\\portfolio_analysis_workflow.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\workflows\\stress_test_workflow.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\components\\market_regime_detector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\services\\advanced_strategy_adjustment_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\services\\beidou_node_tree_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\services\\dynamic_resource_allocator.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\services\\plan_task_management_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\services\\rd_agent_system_optimizer.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\services\\strategic_decision_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\services\\workflow_engine_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianshu_star\\services\\crawl4ai_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianshu_star\\services\\disc_finllm_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianshu_star\\services\\event_identification_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianshu_star\\services\\impact_assessment_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianshu_star\\services\\news_collection_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianshu_star\\services\\tianshu_core_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianxuan_star\\services\\adaptive_optimization_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianxuan_star\\services\\disc_finllm_strategy_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianxuan_star\\services\\factor_research_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianxuan_star\\services\\learning_enhanced_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianxuan_star\\services\\strategy_trigger_research_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianxuan_star\\services\\technical_analysis_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianxuan_star\\workflows\\strategy_workflow_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\automation\\quantitative_research_automation.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\components\\alpha158_factors.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\components\\fundamental_factors.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\core\\alpha158_factor_calculator.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\core\\historical_data_manager.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\core\\unified_yaoguang_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\daily_stock_update_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\data_management_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\enhanced_data_source_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\individual_stock_learning_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\intelligent_stock_selection_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\learning_environment_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\tianquan_strategy_learning_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\advanced_execution_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\intelligent_execution_and_profit_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\rd_agent_deep_execution_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\rd_agent_execution_optimizer.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\rd_agent_intelligent_risk_model.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\real_data_integration_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\risk_assessment_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\risk_calculation_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\risk_manager_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\risk_model_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\separated_trading_statistics_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\trading_execution_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\virtual_trading_engine.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\virtual_trading_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\workflows\\workflow_manager.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\ai\\concept_drift_detector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\ai\\multi_objective_portfolio_optimizer.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\core\\enhanced_crawl4ai_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\core\\hybrid_intelligence_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\enhanced_news_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\enhanced_sina_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\multi_source_data_manager.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\unified_real_price_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\experimental\\akshare_learning_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\intelligence\\historical_learning_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\shared\\automation\\automation_engine.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\shared\\data_sources\\technical_indicators_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\shared\\infrastructure\\continuous_learning_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\shared\\infrastructure\\deepseek_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\shared\\infrastructure\\distribution_permission_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\shared\\infrastructure\\investment_node_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\shared\\infrastructure\\rd_agent_experiment_manager.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\shared\\infrastructure\\rd_agent_knowledge_manager.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\shared\\infrastructure\\rd_agent_performance_monitor.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\shared\\utils\\error_handler.py"], "status": "completed"}