{"timestamp": "2025-06-23T10:59:24.078122", "role_tests": {"tianshu_star": {"automation_loaded": true, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": true, "real_data_processing": false, "no_fallback_mode": true, "errors": [], "completion_score": 3, "completion_percentage": 50.0}, "tianxuan_star": {"automation_loaded": true, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": true, "real_data_processing": false, "no_fallback_mode": true, "errors": [], "completion_score": 3, "completion_percentage": 50.0}, "tianji_star": {"automation_loaded": true, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": true, "real_data_processing": false, "no_fallback_mode": true, "errors": [], "completion_score": 3, "completion_percentage": 50.0}, "tianquan_star": {"automation_loaded": true, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": true, "real_data_processing": false, "no_fallback_mode": false, "errors": [], "completion_score": 2, "completion_percentage": 33.33333333333333}, "yuheng_star": {"automation_loaded": true, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": true, "real_data_processing": false, "no_fallback_mode": true, "errors": [], "completion_score": 3, "completion_percentage": 50.0}, "kaiyang_star": {"automation_loaded": true, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": true, "real_data_processing": false, "no_fallback_mode": true, "errors": [], "completion_score": 3, "completion_percentage": 50.0}, "yaoguang_star": {"automation_loaded": true, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": false, "real_data_processing": false, "no_fallback_mode": false, "errors": ["核心方法调用异常: QuantitativeResearchAutomation.start_automation() takes 1 positional argument but 2 were given"], "completion_score": 1, "completion_percentage": 16.666666666666664}}, "integration_tests": {"legendary_memory": {"available": true, "initialized": true, "statistics": {"total_memories": 0, "role_stats": {}, "cache_size": 0, "is_initialized": true}, "test_passed": true}, "performance_monitor": {"available": true, "status": {"is_initialized": true, "performance_data_count": 0, "stars_monitored": [], "last_update": "2025-06-23T10:59:42.658557", "monitoring_active": true, "service_name": "StarPerformanceMonitor", "version": "1.0.0"}, "test_passed": true}, "deepseek_service": {"available": true, "connected": true, "test_passed": true}}, "performance_tests": {}, "summary": {"total_roles": 7, "excellent_roles": 0, "good_roles": 0, "average_completion_percentage": 42.9, "system_integration_tests_passed": "3/3", "overall_system_health": "needs_improvement", "test_timestamp": "2025-06-23T10:59:42.861021"}}