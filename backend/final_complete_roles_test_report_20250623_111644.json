{"timestamp": "2025-06-23T11:16:30.522129", "role_tests": {"tianshu_star": {"automation_loaded": true, "deepseek_integrated": true, "memory_integrated": true, "performance_integrated": true, "core_method_working": true, "real_data_processing": false, "no_fallback_mode": true, "errors": [], "completion_score": 6, "completion_percentage": 100.0}, "tianxuan_star": {"automation_loaded": false, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": false, "real_data_processing": false, "no_fallback_mode": true, "errors": ["测试失败: expected an indented block after 'except' statement on line 567 (tianxuan_automation_system.py, line 569)"]}, "tianji_star": {"automation_loaded": false, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": false, "real_data_processing": false, "no_fallback_mode": true, "errors": ["测试失败: expected an indented block after 'except' statement on line 532 (tianji_automation_system.py, line 534)"]}, "tianquan_star": {"automation_loaded": false, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": false, "real_data_processing": false, "no_fallback_mode": true, "errors": ["测试失败: expected an indented block after 'except' statement on line 424 (tianquan_automation_system.py, line 426)"]}, "yuheng_star": {"automation_loaded": false, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": false, "real_data_processing": false, "no_fallback_mode": true, "errors": ["测试失败: expected an indented block after 'except' statement on line 521 (yuheng_automation_system.py, line 523)"]}, "kaiyang_star": {"automation_loaded": false, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": false, "real_data_processing": false, "no_fallback_mode": true, "errors": ["测试失败: expected an indented block after 'except' statement on line 206 (kaiyang_automation_system.py, line 208)"]}, "yaoguang_star": {"automation_loaded": false, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": false, "real_data_processing": false, "no_fallback_mode": true, "errors": ["测试失败: expected an indented block after 'except' statement on line 1092 (quantitative_research_automation.py, line 1094)"]}}, "integration_tests": {"legendary_memory": {"available": true, "initialized": true, "statistics": {"total_memories": 1, "role_stats": {}, "cache_size": 1, "is_initialized": true}, "test_passed": true}, "performance_monitor": {"available": true, "status": {"is_initialized": true, "performance_data_count": 0, "stars_monitored": [], "last_update": "2025-06-23T11:16:44.451803", "monitoring_active": true, "service_name": "StarPerformanceMonitor", "version": "1.0.0"}, "test_passed": true}, "deepseek_service": {"available": true, "connected": true, "test_passed": true}}, "performance_tests": {}, "summary": {"total_roles": 7, "excellent_roles": 1, "good_roles": 0, "average_completion_percentage": 14.3, "system_integration_tests_passed": "3/3", "overall_system_health": "needs_improvement", "test_timestamp": "2025-06-23T11:16:44.635954"}}