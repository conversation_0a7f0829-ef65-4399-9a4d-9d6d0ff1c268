#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复所有角色的集成问题
"""

import os
import re

def fix_role_file(file_path: str, role_name: str) -> bool:
    """修复角色文件"""
    try:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复except语句后缺失的内容
        content = re.sub(
            r'(\s*except Exception as e:\s*)\n(\s*def|\s*#|\s*$)',
            r'\1\n            return {"success": False, "error": str(e)}\n\n\2',
            content
        )
        
        # 修复retrieve_memories方法的except
        content = re.sub(
            r'(\s*except Exception as e:\s*)\n(\s*async def record_performance|\s*def get_performance_stats)',
            r'\1\n            return []\n\n    \2',
            content
        )
        
        # 确保record_performance方法存在
        if 'async def record_performance' not in content and 'def get_performance_stats' in content:
            # 在get_performance_stats前添加record_performance方法
            record_performance_method = '''    async def record_performance(self, metric_name: str, value: float, context: dict = None) -> dict:
        """记录绩效到监控系统"""
        try:
            from core.performance.star_performance_monitor import star_performance_monitor
            
            result = await star_performance_monitor.record_performance(
                star_name="{role_name}",
                metric_type=metric_name,
                value=value,
                context=context or {{}}
            )
            
            return {{"success": result, "metric": metric_name, "value": value}}
            
        except Exception as e:
            return {{"success": False, "error": str(e)}}

    '''.format(role_name=role_name)
            
            content = content.replace('    def get_performance_stats', record_performance_method + 'def get_performance_stats')
        
        # 修复get_performance_stats方法的except
        content = re.sub(
            r'(\s*def get_performance_stats.*?except Exception as e:\s*)\n(\s*#|\s*$)',
            r'\1\n            return {"error": str(e)}\n\n\2',
            content,
            flags=re.DOTALL
        )
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"  ✅ {role_name} 修复成功")
        return True
        
    except Exception as e:
        print(f"  ❌ {role_name} 修复失败: {e}")
        return False

def main():
    """主函数"""
    print('🔧 最终修复所有角色的集成问题')
    print('=' * 60)
    
    # 角色配置
    roles_config = {
        'tianxuan_star': 'roles/tianxuan_star/services/tianxuan_automation_system.py',
        'tianji_star': 'roles/tianji_star/services/tianji_automation_system.py',
        'tianquan_star': 'roles/tianquan_star/core/tianquan_automation_system.py',
        'yuheng_star': 'roles/yuheng_star/services/yuheng_automation_system.py',
        'kaiyang_star': 'roles/kaiyang_star/services/kaiyang_automation_system.py',
        'yaoguang_star': 'roles/yaoguang_star/automation/quantitative_research_automation.py'
    }
    
    success_count = 0
    
    for role_name, file_path in roles_config.items():
        print(f'\n🔍 修复 {role_name}...')
        
        if fix_role_file(file_path, role_name):
            success_count += 1
    
    print(f'\n📋 修复完成:')
    print(f'  成功: {success_count}/{len(roles_config)}')
    print(f'  失败: {len(roles_config) - success_count}/{len(roles_config)}')

if __name__ == "__main__":
    main()
