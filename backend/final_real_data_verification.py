from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终真实数据验证脚本

"""

import asyncio
import aiohttp
import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

class FinalRealDataVerifier:
    """最终真实数据验证器"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8003"):
        self.base_url = base_url
        self.session = None
        self.verification_results = {}
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发起HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        try:
            async with self.session.request(method, url, **kwargs) as response:
                if response.content_type == 'application/json':
                    data = await response.json()
                else:
                    data = {"text": await response.text()}
                
                return {
                    "status_code": response.status,
                    "data": data,
                    "success": 200 <= response.status < 300
                }
        except Exception as e:
            return {
                "status_code": 0,
                "data": {"error": str(e)},
                "success": False
            }
    
    def verify_database_real_data(self) -> Dict[str, Any]:
        """验证数据库中的真实数据"""
        print("💾 验证数据库真实数据...")
        
        results = {}
        
        # 检查股票数据库
        stock_db_path = Path(get_database_path("stock_database"))
        if stock_db_path.exists():
            try:
                conn = sqlite3.connect(stock_db_path)
                cursor = conn.cursor()
                
                # 检查股票基础信息
                cursor.execute("SELECT COUNT(*) FROM stock_info")
                stock_count = cursor.fetchone()[0]

                # 检查日线数据
                cursor.execute("SELECT COUNT(*) FROM daily_data")
                daily_data_count = cursor.fetchone()[0]

                # 检查技术指标
                cursor.execute("SELECT COUNT(*) FROM technical_indicators")
                indicator_count = cursor.fetchone()[0]
                
                # 检查数据来源 - 使用正确的列名
                try:
                    cursor.execute("SELECT DISTINCT source FROM daily_data LIMIT 10")
                    data_sources = [row[0] for row in cursor.fetchall()]
                except:
                    try:
                        cursor.execute("SELECT DISTINCT data_source FROM daily_data LIMIT 10")
                        data_sources = [row[0] for row in cursor.fetchall()]
                    except:
                        data_sources = ["unknown"]

                # 检查最新数据时间 - 使用正确的列名
                try:
                    cursor.execute("SELECT MAX(trade_date) FROM daily_data")
                    latest_date = cursor.fetchone()[0]
                except:
                    try:
                        cursor.execute("SELECT MAX(date) FROM daily_data")
                        latest_date = cursor.fetchone()[0]
                    except:
                        latest_date = "unknown"
                
                results["stock_database"] = {
                    "status": "✅ 正常",
                    "stock_count": stock_count,
                    "daily_data_count": daily_data_count,
                    "indicator_count": indicator_count,
                    "data_sources": data_sources,
                    "latest_date": latest_date,
                    "real_data": "eastmoney" in str(data_sources).lower()
                }
                
                conn.close()
                
            except Exception as e:
                results["stock_database"] = {
                    "status": f"❌ 错误: {e}",
                    "real_data": False
                }
        else:
            results["stock_database"] = {
                "status": "❌ 数据库文件不存在",
                "real_data": False
            }
        
        # 检查其他数据库
        other_dbs = [
            ("传奇记忆数据库", "backend/data/legendary_memory.db"),
            ("新闻知识库", "backend/data/news_knowledge_base.db"),
            ("风险数据库", "backend/data/risk_database.db"),
            ("历史数据库", "backend/data/historical_data.db"),
            ("交易执行数据库", "backend/data/trading_execution.db"),
            ("天枢新闻分析库", "backend/data/tianshu_news_analysis.db"),
            ("天玑风险策略库", "backend/data/tianji_strategy_risk.db"),
            ("天权策略库", "backend/data/tianquan_strategies.db")
        ]
        
        for db_name, db_path in other_dbs:
            db_file = Path(db_path)
            if db_file.exists():
                try:
                    conn = sqlite3.connect(db_file)
                    cursor = conn.cursor()
                    
                    # 获取所有表
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    
                    # 统计总记录数
                    total_records = 0
                    for table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        total_records += count
                    
                    results[db_name] = {
                        "status": "✅ 正常",
                        "tables": len(tables),
                        "total_records": total_records,
                        "size_mb": round(db_file.stat().st_size / 1024 / 1024, 2)
                    }
                    
                    conn.close()
                    
                except Exception as e:
                    results[db_name] = {
                        "status": f"❌ 错误: {e}"
                    }
            else:
                results[db_name] = {
                    "status": "❌ 文件不存在"
                }
        
        return results
    
    async def verify_api_real_data(self) -> Dict[str, Any]:
        """验证API返回的真实数据"""
        print("🔌 验证API真实数据...")
        
        results = {}
        
        # 测试瑶光星实时数据
        print("   测试瑶光星实时数据...")
        realtime_result = await self.make_request("GET", "/api/yaoguang-star/data/realtime/000001")
        if realtime_result["success"]:
            data = realtime_result["data"]
            results["yaoguang_realtime"] = {
                "status": "✅ 正常",
                "has_price": "current_price" in str(data),
                "has_volume": "volume" in str(data),
                "data_source": data.get("data_source", "unknown"),
                "is_real_data": "eastmoney" in str(data).lower() or "real" in str(data).lower()
            }
        else:
            results["yaoguang_realtime"] = {
                "status": f"❌ 失败: {realtime_result['data']}",
                "is_real_data": False
            }
        
        # 测试玉衡星交易数据
        print("   测试玉衡星交易数据...")
        trader_result = await self.make_request("GET", "/api/trader/positions")
        if trader_result["success"]:
            data = trader_result["data"]
            results["yuheng_trading"] = {
                "status": "✅ 正常",
                "has_positions": "data" in data and len(data.get("data", [])) > 0,
                "data_source": data.get("data_source", "unknown"),
                "is_real_data": "mock" not in str(data).lower() and "simulate" not in str(data).lower()
            }
        else:
            results["yuheng_trading"] = {
                "status": f"❌ 失败: {trader_result['data']}",
                "is_real_data": False
            }
        
        # 测试天枢星新闻数据
        print("   测试天枢星新闻数据...")
        news_result = await self.make_request("GET", "/api/intelligence/news")
        if news_result["success"]:
            data = news_result["data"]
            results["tianshu_news"] = {
                "status": "✅ 正常",
                "has_news": "data" in data and len(data.get("data", [])) > 0,
                "data_source": data.get("data_source", "unknown"),
                "is_real_data": "crawl4ai" in str(data).lower() or "real" in str(data).lower()
            }
        else:
            results["tianshu_news"] = {
                "status": f"❌ 失败: {news_result['data']}",
                "is_real_data": False
            }
        
        # 测试系统统计数据
        print("   测试系统统计数据...")
        stats_result = await self.make_request("GET", "/api/system/statistics")
        if stats_result["success"]:
            data = stats_result["data"]
            results["system_statistics"] = {
                "status": "✅ 正常",
                "has_stats": "data" in data,

            }
        else:
            results["system_statistics"] = {
                "status": f"❌ 失败: {stats_result['data']}",
                "is_real_data": False
            }
        
        return results
    
    async def verify_no_mock_patterns(self) -> Dict[str, Any]:
        """验证系统中没有模拟数据模式"""
        print("🔍 验证无模拟数据模式...")
        
        results = {}
        
        # 检查关键API端点是否包含模拟数据标识
        test_endpoints = [
            "/api/system/status",
            "/api/yaoguang/memory",
            "/api/commander/collaboration",
            "/api/monitoring/dashboard"
        ]
        
        mock_keywords = [

            "fake", "dummy", "test_data", "sample_data"
        ]
        
        for endpoint in test_endpoints:
            result = await self.make_request("GET", endpoint)
            if result["success"]:
                response_text = str(result["data"]).lower()
                found_mock_patterns = [keyword for keyword in mock_keywords if keyword in response_text]
                
                results[endpoint] = {
                    "status": "✅ 正常" if not found_mock_patterns else f"⚠️ 发现模拟模式: {found_mock_patterns}",
                    "mock_patterns_found": found_mock_patterns,
                    "is_clean": len(found_mock_patterns) == 0
                }
            else:
                results[endpoint] = {
                    "status": f"❌ 无法访问: {result['data']}",
                    "is_clean": False
                }
        
        return results
    
    async def run_final_verification(self) -> Dict[str, Any]:
        """运行最终验证"""
        print("🚀 开始最终真实数据验证...")
        print("=" * 80)
        
        verification_results = {
            "timestamp": datetime.now().isoformat(),
            "database_verification": self.verify_database_real_data(),
            "api_verification": await self.verify_api_real_data(),
            "mock_pattern_verification": await self.verify_no_mock_patterns()
        }
        
        # 生成验证报告
        await self.generate_verification_report(verification_results)
        
        return verification_results
    
    async def generate_verification_report(self, results: Dict[str, Any]):
        """生成验证报告"""
        print("\n" + "=" * 80)
        print("📊 最终真实数据验证报告")
        print("=" * 80)
        
        # 数据库验证结果
        print("\n💾 数据库验证结果:")
        db_results = results["database_verification"]
        for db_name, db_result in db_results.items():
            status = db_result.get("status", "未知")
            print(f"   {status} {db_name}")
            if "stock_database" in db_name and db_result.get("real_data"):
                print(f"      - 股票数: {db_result.get('stock_count', 0)}")
                print(f"      - 日线数据: {db_result.get('daily_data_count', 0)}")
                print(f"      - 数据源: {db_result.get('data_sources', [])}")
        
        # API验证结果
        print("\n🔌 API验证结果:")
        api_results = results["api_verification"]
        real_data_count = 0
        total_api_count = len(api_results)
        
        for api_name, api_result in api_results.items():
            status = api_result.get("status", "未知")
            is_real = api_result.get("is_real_data", False)
            if is_real:
                real_data_count += 1
            print(f"   {status} {api_name} - {'真实数据' if is_real else '非真实数据'}")
        
        # 模拟模式验证结果
        print("\n🔍 模拟模式验证结果:")
        mock_results = results["mock_pattern_verification"]
        clean_count = 0
        total_endpoint_count = len(mock_results)
        
        for endpoint, mock_result in mock_results.items():
            status = mock_result.get("status", "未知")
            is_clean = mock_result.get("is_clean", False)
            if is_clean:
                clean_count += 1
            print(f"   {status} {endpoint}")
        
        # 总体评估
        print("\n🎯 总体评估:")
        api_real_rate = (real_data_count / total_api_count * 100) if total_api_count > 0 else 0
        clean_rate = (clean_count / total_endpoint_count * 100) if total_endpoint_count > 0 else 0
        
        print(f"   📊 API真实数据率: {api_real_rate:.1f}% ({real_data_count}/{total_api_count})")
        print(f"   🧹 端点清洁率: {clean_rate:.1f}% ({clean_count}/{total_endpoint_count})")
        
        overall_score = (api_real_rate + clean_rate) / 2
        print(f"   🏆 总体评分: {overall_score:.1f}%")
        
        if overall_score >= 90:
            print("   ✅ 系统已达到生产级真实数据标准")
        elif overall_score >= 70:
            print("   ⚠️ 系统基本符合真实数据要求，建议进一步优化")
        else:
            print("   ❌ 系统仍存在较多模拟数据，需要继续清理")
        
        # 保存详细报告
        report_file = f"backend/final_real_data_verification_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存: {report_file}")

async def main():
    """主函数"""
    async with FinalRealDataVerifier() as verifier:
        await verifier.run_final_verification()

if __name__ == "__main__":
    asyncio.run(main())
