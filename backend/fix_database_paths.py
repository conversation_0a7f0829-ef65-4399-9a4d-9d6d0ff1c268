from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复所有数据库路径问题
统一所有七星角色的数据库调用路径
"""

import os
import re
from pathlib import Path
from typing import Dict, List

class DatabasePathFixer:
    """数据库路径修复器"""
    
    def __init__(self):
        self.project_root = Path(".")
        
        # 正确的数据库路径映射
        self.correct_paths = {
            # 主要数据库
            "stock_database.db": get_database_path("stock_database"),
            "legendary_memory.db": "backend/data/legendary_memory.db", 
            "news_knowledge_base.db": "backend/data/news_knowledge_base.db",
            "risk_database.db": "backend/data/risk_database.db",
            "historical_data.db": "backend/data/historical_data.db",
            "trading_execution.db": "backend/data/trading_execution.db",
            "tianshu_news_analysis.db": "backend/data/tianshu_news_analysis.db",
            "tianji_strategy_risk.db": "backend/data/tianji_strategy_risk.db",
            "tianxuan_trigger_research.db": "backend/data/tianxuan_trigger_research.db",
            "yuheng_execution_optimization.db": "backend/data/yuheng_execution_optimization.db",
            "advanced_execution_system.db": "backend/data/advanced_execution_system.db",
            "separated_trading_statistics.db": "backend/data/separated_trading_statistics.db"
        }
        
        # 需要替换的错误路径模式
        self.path_replacements = {
            # 错误的backend/data路径
            r'"backend/data/stock_database\.db"': 'get_database_path("stock_database")',
            r'"backend/data/legendary_memory\.db"': '"backend/data/legendary_memory.db"',
            r'"backend/data/news_knowledge_base\.db"': '"backend/data/news_knowledge_base.db"',
            r'"backend/data/risk_database\.db"': '"backend/data/risk_database.db"',
            r'"backend/data/historical_data\.db"': '"backend/data/historical_data.db"',
            r'"backend/data/trading_execution\.db"': '"backend/data/trading_execution.db"',
            r'"backend/data/tianshu_news_analysis\.db"': '"backend/data/tianshu_news_analysis.db"',
            r'"backend/data/tianji_strategy_risk\.db"': '"backend/data/tianji_strategy_risk.db"',
            
            # 错误的data路径
            r'"data/tianquan_simple\.db"': '"backend/data/tianquan_simple.db"',
            r'"data/advanced_execution_system\.db"': '"backend/data/advanced_execution_system.db"',
            r'"data/alerts\.db"': '"backend/data/alerts.db"',
            r'"data/trading_execution\.db"': '"backend/data/trading_execution.db"',
            r'"data/risk_management\.db"': '"backend/data/risk_database.db"',
            
            # 错误的databases子目录路径
            r'"data/databases/[^"]*\.db"': '"backend/data/yaoguang_distribution.db"',
            
            # 错误的完整路径
            r'"backend/data/complete_a_stock_library/[^"]*\.db"': 'get_database_path("stock_database")',
            r'"backend/data/tianquan_strategies/[^"]*\.db"': '"backend/data/tianquan_strategies.db"',
            r'"backend/data/decision_making/[^"]*\.db"': '"backend/data/decision_making.db"',
        }
    
    def scan_and_fix_files(self):
        """扫描并修复所有Python文件中的数据库路径"""
        print("🔧 开始修复数据库路径...")
        
        # 扫描所有Python文件
        python_files = list(self.project_root.glob("backend/**/*.py"))
        
        fixed_files = []
        total_replacements = 0
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                file_replacements = 0
                
                # 应用所有路径替换
                for pattern, replacement in self.path_replacements.items():
                    matches = re.findall(pattern, content)
                    if matches:
                        content = re.sub(pattern, replacement, content)
                        file_replacements += len(matches)
                
                # 如果有修改，保存文件
                if content != original_content:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    rel_path = str(py_file.relative_to(self.project_root))
                    fixed_files.append(rel_path)
                    total_replacements += file_replacements
                    print(f"   ✅ 修复 {rel_path}: {file_replacements}处替换")
                
            except Exception as e:
                print(f"   ❌ 无法处理 {py_file}: {e}")
        
        print(f"\n📊 修复完成:")
        print(f"   修复文件数: {len(fixed_files)}")
        print(f"   总替换数: {total_replacements}")
        
        return fixed_files, total_replacements
    
    def create_missing_databases(self):
        """创建缺失的数据库文件"""
        print("\n🗄️ 创建缺失的数据库文件...")
        
        import sqlite3
        
        created_dbs = []
        
        # 需要创建的数据库及其表结构
        db_schemas = {
            "tianquan_strategies.db": [
                """CREATE TABLE IF NOT EXISTS strategies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    parameters TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )""",
                """CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id INTEGER,
                    date DATE,
                    return_rate REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    FOREIGN KEY (strategy_id) REFERENCES strategies (id)
                )"""
            ],
            "decision_making.db": [
                """CREATE TABLE IF NOT EXISTS decisions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    decision_type TEXT,
                    stock_code TEXT,
                    action TEXT,
                    confidence REAL,
                    reasoning TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )"""
            ],
            "alerts.db": [
                """CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    alert_type TEXT,
                    message TEXT,
                    severity TEXT,
                    stock_code TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved BOOLEAN DEFAULT FALSE
                )"""
            ],
            "yaoguang_distribution.db": [
                """CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE,
                    email TEXT,
                    level TEXT,
                    total_commission REAL DEFAULT 0,
                    monthly_commission REAL DEFAULT 0,
                    referral_count INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )""",
                """CREATE TABLE IF NOT EXISTS commissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    amount REAL,
                    type TEXT,
                    date DATE,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )"""
            ]
        }
        
        for db_name, schemas in db_schemas.items():
            db_path = Path(f"backend/data/{db_name}")
            
            if not db_path.exists():
                try:
                    # 确保目录存在
                    db_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 创建数据库和表
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    for schema in schemas:
                        cursor.execute(schema)
                    
                    conn.commit()
                    conn.close()
                    
                    created_dbs.append(db_name)
                    print(f"   ✅ 创建数据库: {db_name}")
                    
                except Exception as e:
                    print(f"   ❌ 创建数据库失败 {db_name}: {e}")
        
        print(f"\n📊 创建完成: {len(created_dbs)}个数据库")
        return created_dbs
    
    def verify_database_paths(self):
        """验证修复后的数据库路径"""
        print("\n🔍 验证数据库路径...")
        
        # 检查所有数据库文件是否存在
        existing_dbs = []
        missing_dbs = []
        
        for db_name, db_path in self.correct_paths.items():
            full_path = Path(db_path)
            if full_path.exists():
                existing_dbs.append(db_name)
                print(f"   ✅ {db_name}: 存在")
            else:
                missing_dbs.append(db_name)
                print(f"   ❌ {db_name}: 缺失")
        
        print(f"\n📊 验证结果:")
        print(f"   存在: {len(existing_dbs)}")
        print(f"   缺失: {len(missing_dbs)}")
        
        return existing_dbs, missing_dbs
    
    def generate_unified_config(self):
        """生成统一的数据库配置文件"""
        print("\n📄 生成统一数据库配置...")
        
        config_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据库配置
所有七星角色使用此配置文件获取正确的数据库路径
"""

from pathlib import Path

# 数据库基础路径
DATABASE_BASE_PATH = Path("backend/data")

# 统一数据库路径配置
DATABASE_PATHS = {
    # 主要数据库
    "stock_database": DATABASE_BASE_PATH / "stock_database.db",
    "legendary_memory": DATABASE_BASE_PATH / "legendary_memory.db",
    "news_knowledge": DATABASE_BASE_PATH / "news_knowledge_base.db",
    "risk_database": DATABASE_BASE_PATH / "risk_database.db",
    "historical_data": DATABASE_BASE_PATH / "historical_data.db",
    "trading_execution": DATABASE_BASE_PATH / "trading_execution.db",
    
    # 角色专用数据库
    "tianshu_news_analysis": DATABASE_BASE_PATH / "tianshu_news_analysis.db",
    "tianxuan_trigger_research": DATABASE_BASE_PATH / "tianxuan_trigger_research.db",
    "tianji_strategy_risk": DATABASE_BASE_PATH / "tianji_strategy_risk.db",
    "tianquan_strategies": DATABASE_BASE_PATH / "tianquan_strategies.db",
    "yuheng_execution": DATABASE_BASE_PATH / "yuheng_execution_optimization.db",
    "kaiyang_portfolio": DATABASE_BASE_PATH / "kaiyang_portfolio.db",
    "yaoguang_distribution": DATABASE_BASE_PATH / "yaoguang_distribution.db",
    
    # 系统数据库
    "advanced_execution": DATABASE_BASE_PATH / "advanced_execution_system.db",
    "separated_trading": DATABASE_BASE_PATH / "separated_trading_statistics.db",
    "alerts": DATABASE_BASE_PATH / "alerts.db",
    "decision_making": DATABASE_BASE_PATH / "decision_making.db"
}

def get_database_path(db_name: str) -> str:
    """获取数据库路径"""
    if db_name in DATABASE_PATHS:
        return str(DATABASE_PATHS[db_name])
    else:
        raise ValueError(f"未知的数据库名称: {db_name}")

def get_all_database_paths() -> dict:
    """获取所有数据库路径"""
    return {name: str(path) for name, path in DATABASE_PATHS.items()}
'''
        
        config_file = Path("backend/config/database_config.py")
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"   ✅ 配置文件已生成: {config_file}")
        return config_file
    
    def run_fix(self):
        """运行完整修复"""
        print("🚀 开始数据库路径统一修复...")
        print("=" * 60)
        
        # 1. 修复文件中的路径
        fixed_files, total_replacements = self.scan_and_fix_files()
        
        # 2. 创建缺失的数据库
        created_dbs = self.create_missing_databases()
        
        # 3. 验证路径
        existing_dbs, missing_dbs = self.verify_database_paths()
        
        # 4. 生成统一配置
        config_file = self.generate_unified_config()
        
        print("\n" + "=" * 60)
        print("📊 修复总结")
        print("=" * 60)
        print(f"✅ 修复文件: {len(fixed_files)}")
        print(f"✅ 路径替换: {total_replacements}")
        print(f"✅ 创建数据库: {len(created_dbs)}")
        print(f"✅ 现有数据库: {len(existing_dbs)}")
        print(f"⚠️ 缺失数据库: {len(missing_dbs)}")
        
        if missing_dbs:
            print(f"\n🔧 仍需处理的缺失数据库:")
            for db in missing_dbs:
                print(f"   - {db}")
        
        return {
            "fixed_files": fixed_files,
            "total_replacements": total_replacements,
            "created_dbs": created_dbs,
            "existing_dbs": existing_dbs,
            "missing_dbs": missing_dbs,
            "config_file": str(config_file)
        }

def main():
    """主函数"""
    fixer = DatabasePathFixer()
    results = fixer.run_fix()
    
    print(f"\n✅ 数据库路径修复完成！")
    print(f"📄 统一配置文件: {results['config_file']}")

if __name__ == "__main__":
    main()
