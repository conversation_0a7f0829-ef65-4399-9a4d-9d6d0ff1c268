#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复DeepSeek返回None导致的'NoneType' object has no attribute 'get'问题
"""

import os
import re
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_deepseek_none_issue():
    """修复DeepSeek None问题"""
    logger.info("🔧 修复DeepSeek None问题...")
    
    backend_dir = Path(__file__).parent
    fixed_files = []
    
    # 查找所有包含_call_role_deepseek方法的文件
    for py_file in backend_dir.rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '_call_role_deepseek' in content and '(result or {}).get(' in content:
                original_content = content
                
                # 修复DeepSeek调用的返回值处理
                # 模式1: result = await deepseek_service.chat_completion(...)
                #        return { "success": (result or {}).get("success", False), ... }
                pattern1 = r'(\s+)(result\s*=\s*await\s+deepseek_service\.chat_completion\([^)]+\))\s*\n(\s+)(return\s*\{[^}]*"success":\s*result\.get\("success",\s*False\)[^}]*\})'
                
                def replace_pattern1(match):
                    indent1 = match.group(1)
                    call_line = match.group(2)
                    indent2 = match.group(3)
                    return_line = match.group(4)
                    
                    return f'''{indent1}{call_line}
{indent1}
{indent1}# 确保result不为None
{indent1}if result is None:
{indent1}    result = {{"success": False, "error": "DeepSeek服务返回None"}}
{indent1}
{indent2}{return_line}'''
                
                content = re.sub(pattern1, replace_pattern1, content, flags=re.DOTALL)
                
                # 模式2: 直接的result.get()调用
                content = re.sub(
                    r'result\.get\(([^)]+)\)',
                    r'(result or {}).get(\1)',
                    content
                )
                
                # 模式3: if (result or {}).get("success"):
                content = re.sub(
                    r'if\s+result\.get\("success"\):',
                    r'if result and (result or {}).get("success"):',
                    content
                )
                
                # 模式4: response.get() 调用
                content = re.sub(
                    r'response\.get\(([^)]+)\)',
                    r'(response or {}).get(\1)',
                    content
                )
                
                if content != original_content:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    fixed_files.append(str(py_file))
                    logger.info(f"  ✅ 修复DeepSeek None问题: {py_file.name}")
                    
        except Exception as e:
            logger.error(f"  ❌ 修复失败 {py_file}: {e}")
    
    return fixed_files

def fix_specific_role_files():
    """修复特定角色文件中的DeepSeek调用"""
    logger.info("🔧 修复特定角色文件...")
    
    backend_dir = Path(__file__).parent
    fixed_files = []
    
    # 需要修复的特定文件
    role_files = [
        "roles/tianshu_star/services/tianshu_automation_system.py",
        "roles/tianxuan_star/services/tianxuan_automation_system.py", 
        "roles/tianji_star/services/tianji_automation_system.py",
        "roles/tianquan_star/core/tianquan_automation_system.py",
        "roles/yuheng_star/services/yuheng_automation_system.py",
        "roles/kaiyang_star/services/kaiyang_automation_system.py",
        "roles/yaoguang_star/automation/quantitative_research_automation.py"
    ]
    
    for file_path in role_files:
        full_path = backend_dir / file_path
        if full_path.exists():
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 查找_call_role_deepseek方法并修复
                if 'async def _call_role_deepseek' in content:
                    # 添加None检查的模板
                    none_check_template = '''
            # 确保DeepSeek服务返回值不为None
            if result is None:
                logger.warning("DeepSeek服务返回None，使用默认响应")
                result = {
                    "success": False,
                    "error": "DeepSeek服务返回None",
                    "response": "服务暂时不可用，请稍后重试"
                }
'''
                    
                    # 在result = await deepseek_service.chat_completion之后添加检查
                    pattern = r'(\s+)(result\s*=\s*await\s+deepseek_service\.chat_completion\([^)]+\))\s*\n'
                    replacement = r'\1\2\n' + none_check_template + '\n'
                    
                    content = re.sub(pattern, replacement, content)
                    
                    # 修复所有的result.get()调用
                    content = re.sub(
                        r'result\.get\("success",\s*False\)',
                        r'(result or {}).get("success", False)',
                        content
                    )
                    
                    content = re.sub(
                        r'result\.get\("response",\s*""\)',
                        r'(result or {}).get("response", "")',
                        content
                    )
                    
                    content = re.sub(
                        r'result\.get\("error",\s*"[^"]*"\)',
                        r'(result or {}).get("error", "未知错误")',
                        content
                    )
                
                if content != original_content:
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    fixed_files.append(str(full_path))
                    logger.info(f"  ✅ 修复角色文件: {full_path.name}")
                    
            except Exception as e:
                logger.error(f"  ❌ 修复角色文件失败 {full_path}: {e}")
    
    return fixed_files

def main():
    """主修复函数"""
    logger.info("🔧 开始修复DeepSeek None问题...")
    
    # 1. 通用修复
    fixed_files1 = fix_deepseek_none_issue()
    
    # 2. 特定角色文件修复
    fixed_files2 = fix_specific_role_files()
    
    all_fixed_files = fixed_files1 + fixed_files2
    
    logger.info(f"✅ DeepSeek None问题修复完成，共修复 {len(set(all_fixed_files))} 个文件")
    
    # 生成修复报告
    import json
    from datetime import datetime
    
    report = {
        "fix_time": datetime.now().isoformat(),
        "total_files_fixed": len(set(all_fixed_files)),
        "fixed_files": list(set(all_fixed_files)),
        "issues_fixed": [
            "DeepSeek服务返回None导致的AttributeError",
            "result.get()调用的空指针异常",
            "response.get()调用的空指针异常",
            "角色DeepSeek调用的异常处理"
        ],
        "status": "completed"
    }
    
    backend_dir = Path(__file__).parent
    with open(backend_dir / "deepseek_none_fix_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    logger.info("📄 修复报告已保存到: deepseek_none_fix_report.json")

if __name__ == "__main__":
    main()
