#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复所有角色的记忆集成问题
将store_memory方法修复为正确的add_memory调用
"""

import os
import re

def fix_memory_integration_in_file(file_path: str, role_name: str, role_chinese: str) -> bool:
    """修复文件中的记忆集成"""
    try:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否需要修复
        if 'legendary_memory_interface.store_memory' not in content:
            print(f"  ✅ {role_name} 无需修复记忆集成")
            return True
        
        # 修复store_memory调用
        fixed_content = content.replace(
            'legendary_memory_interface.store_memory(',
            'legendary_memory_interface.add_memory('
        )
        
        # 修复参数名
        fixed_content = fixed_content.replace(
            'role_source="' + role_name + '"',
            'role="' + role_chinese + '"'
        )
        
        # 添加必要的导入
        if 'from core.domain.memory.legendary.models import MessageType, MemoryPriority' not in fixed_content:
            # 在legendary_memory_interface导入后添加
            import_pattern = r'(from core\.domain\.memory\.legendary\.interface import legendary_memory_interface)'
            replacement = r'\1\n            from core.domain.memory.legendary.models import MessageType, MemoryPriority'
            fixed_content = re.sub(import_pattern, replacement, fixed_content)
        
        # 添加消息类型和优先级映射
        memory_mapping = '''
            # 映射消息类型
            message_type_mapping = {
                "analysis": MessageType.MARKET_ANALYSIS,
                "news": MessageType.NEWS_UPDATE,
                "general": MessageType.GENERAL,
                "system": MessageType.SYSTEM_NOTIFICATION,
                "risk": MessageType.RISK_ASSESSMENT,
                "strategy": MessageType.STRATEGY_PLANNING,
                "execution": MessageType.TRADING_EXECUTION
            }
            
            # 映射优先级
            priority_mapping = {
                "high": MemoryPriority.HIGH,
                "normal": MemoryPriority.NORMAL,
                "low": MemoryPriority.LOW
            }
'''
        
        # 替换store_memory方法的实现
        store_memory_pattern = r'(async def store_memory\(self, content: str, memory_type: str = "analysis", priority: str = "normal", metadata: dict = None\) -> dict:\s*"""存储记忆到传奇记忆系统"""\s*try:\s*from core\.domain\.memory\.legendary\.interface import legendary_memory_interface\s*from core\.domain\.memory\.legendary\.models import MessageType, MemoryPriority)(.*?)(result = await legendary_memory_interface\.add_memory\(\s*content=content,\s*role="[^"]*",\s*message_type=message_type_mapping\.get\(memory_type, MessageType\.GENERAL\),\s*priority=priority_mapping\.get\(priority, MemoryPriority\.NORMAL\),\s*metadata=metadata or \{\}\s*\))'
        
        if not re.search(store_memory_pattern, fixed_content, re.DOTALL):
            # 如果没有找到完整的模式，进行简单替换
            old_pattern = r'(result = await legendary_memory_interface\.add_memory\(\s*content=content,\s*role="[^"]*",\s*message_type=)[^,]*(,\s*priority=)[^,]*(,\s*metadata=metadata or \{\}\s*\))'
            new_replacement = r'\1message_type_mapping.get(memory_type, MessageType.GENERAL)\2priority_mapping.get(priority, MemoryPriority.NORMAL)\3'
            fixed_content = re.sub(old_pattern, new_replacement, fixed_content)
            
            # 添加映射代码
            mapping_insert_pattern = r'(from core\.domain\.memory\.legendary\.models import MessageType, MemoryPriority\s*)'
            fixed_content = re.sub(mapping_insert_pattern, r'\1' + memory_mapping, fixed_content)
        
        # 修复返回值
        fixed_content = fixed_content.replace(
            'return result',
            'return {"success": result.success, "memory_id": result.memory_id}'
        )
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"  ✅ {role_name} 记忆集成修复成功")
        return True
        
    except Exception as e:
        print(f"  ❌ {role_name} 记忆集成修复失败: {e}")
        return False

def main():
    """主函数"""
    print('🔧 修复所有角色的记忆集成问题')
    print('=' * 60)
    
    # 角色配置
    roles_config = {
        'tianxuan_star': {
            'file': 'roles/tianxuan_star/services/tianxuan_automation_system.py',
            'chinese': '天璇星'
        },
        'tianji_star': {
            'file': 'roles/tianji_star/services/tianji_automation_system.py',
            'chinese': '天玑星'
        },
        'tianquan_star': {
            'file': 'roles/tianquan_star/core/tianquan_automation_system.py',
            'chinese': '天权星'
        },
        'yuheng_star': {
            'file': 'roles/yuheng_star/services/yuheng_automation_system.py',
            'chinese': '玉衡星'
        },
        'kaiyang_star': {
            'file': 'roles/kaiyang_star/services/kaiyang_automation_system.py',
            'chinese': '开阳星'
        },
        'yaoguang_star': {
            'file': 'roles/yaoguang_star/automation/quantitative_research_automation.py',
            'chinese': '瑶光星'
        }
    }
    
    success_count = 0
    
    for role_name, config in roles_config.items():
        print(f'\n🔍 修复 {role_name} ({config["chinese"]})...')
        
        if fix_memory_integration_in_file(config['file'], role_name, config['chinese']):
            success_count += 1
    
    print(f'\n📋 修复完成:')
    print(f'  成功: {success_count}/{len(roles_config)}')
    print(f'  失败: {len(roles_config) - success_count}/{len(roles_config)}')

if __name__ == "__main__":
    main()
