#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复七个角色自动化系统集成问题
解决DeepSeek人设配置、传奇记忆系统、绩效系统、层级系统的集成问题
"""

import asyncio
import sys
import os
import json
from datetime import datetime

sys.path.append(os.getcwd())

async def fix_seven_roles_integration():
    """修复七个角色自动化系统集成"""
    print('🔧 修复七个角色自动化系统集成问题')
    print('=' * 60)
    
    fix_results = {
        'timestamp': datetime.now().isoformat(),
        'fixes_applied': [],
        'errors_encountered': [],
        'summary': {}
    }
    
    # 1. 修复传奇记忆系统接口
    print('\n🧠 修复传奇记忆系统接口...')
    try:
        from core.domain.memory.legendary.interface import legendary_memory_interface
        
        # 测试初始化
        memory_initialized = await legendary_memory_interface.initialize()
        
        # 测试新的统计方法
        if hasattr(legendary_memory_interface, 'get_memory_statistics'):
            memory_stats = legendary_memory_interface.get_memory_statistics()
            print(f'    ✅ 传奇记忆系统接口修复成功: {memory_stats}')
            fix_results['fixes_applied'].append('传奇记忆系统接口修复')
        else:
            print(f'    ❌ 传奇记忆系统接口修复失败: 方法不存在')
            fix_results['errors_encountered'].append('传奇记忆系统接口修复失败')
            
    except Exception as e:
        print(f'    ❌ 传奇记忆系统接口修复失败: {e}')
        fix_results['errors_encountered'].append(f'传奇记忆系统接口修复失败: {e}')
    
    # 2. 修复绩效监控系统接口
    print('\n📊 修复绩效监控系统接口...')
    try:
        from core.performance.star_performance_monitor import star_performance_monitor
        
        # 测试新的状态方法
        if hasattr(star_performance_monitor, 'get_system_status'):
            perf_status = star_performance_monitor.get_system_status()
            print(f'    ✅ 绩效监控系统接口修复成功: {perf_status}')
            fix_results['fixes_applied'].append('绩效监控系统接口修复')
        else:
            print(f'    ❌ 绩效监控系统接口修复失败: 方法不存在')
            fix_results['errors_encountered'].append('绩效监控系统接口修复失败')
            
    except Exception as e:
        print(f'    ❌ 绩效监控系统接口修复失败: {e}')
        fix_results['errors_encountered'].append(f'绩效监控系统接口修复失败: {e}')
    
    # 3. 初始化层级系统
    print('\n🏛️ 初始化层级权限系统...')
    try:
        from core.enhanced_seven_stars_hierarchy import EnhancedSevenStarsHierarchy
        
        hierarchy = EnhancedSevenStarsHierarchy()
        
        # 尝试初始化
        if not hierarchy.is_initialized:
            # 手动初始化
            await hierarchy._initialize_star_agents()
            await hierarchy._initialize_performance_monitoring()
            hierarchy.is_initialized = True
            
        print(f'    ✅ 层级系统初始化成功: 角色数={len(hierarchy.agents)}, 初始化={hierarchy.is_initialized}')
        fix_results['fixes_applied'].append('层级权限系统初始化')
        
    except Exception as e:
        print(f'    ❌ 层级系统初始化失败: {e}')
        fix_results['errors_encountered'].append(f'层级系统初始化失败: {e}')
    
    # 4. 验证角色自动化系统集成
    print('\n🤖 验证角色自动化系统集成...')
    
    roles_automation = {
        '天枢星': 'roles.tianshu_star.services.tianshu_automation_system',
        '天璇星': 'roles.tianxuan_star.services.tianxuan_automation_system', 
        '天玑星': 'roles.tianji_star.services.tianji_automation_system',
        '天权星': 'roles.tianquan_star.core.tianquan_automation_system',
        '玉衡星': 'roles.yuheng_star.services.yuheng_automation_system',
        '开阳星': 'roles.kaiyang_star.services.kaiyang_automation_system',
        '瑶光星': 'roles.yaoguang_star.automation.quantitative_research_automation'
    }
    
    working_automation = 0
    
    for role_name, module_path in roles_automation.items():
        try:
            print(f'  🔍 验证 {role_name} 自动化系统...')
            
            # 导入自动化系统
            module = __import__(module_path, fromlist=[''])
            
            # 查找自动化系统实例
            automation_system = None
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if 'automation' in attr_name.lower() and hasattr(attr, 'start_automation'):
                    automation_system = attr
                    break
            
            if automation_system:
                # 测试核心系统集成
                core_systems_integrated = await test_role_core_systems_integration(role_name, automation_system)
                
                if core_systems_integrated:
                    print(f'    ✅ {role_name} 自动化系统集成完整')
                    working_automation += 1
                    fix_results['fixes_applied'].append(f'{role_name}自动化系统验证通过')
                else:
                    print(f'    ⚠️ {role_name} 自动化系统部分集成')
                    fix_results['errors_encountered'].append(f'{role_name}自动化系统部分集成')
            else:
                print(f'    ❌ {role_name} 自动化系统未找到')
                fix_results['errors_encountered'].append(f'{role_name}自动化系统未找到')
                
        except Exception as e:
            print(f'    ❌ {role_name} 验证失败: {e}')
            fix_results['errors_encountered'].append(f'{role_name}验证失败: {e}')
    
    # 5. 测试DeepSeek人设配置集成
    print('\n🧠 测试DeepSeek人设配置集成...')
    
    deepseek_working = 0
    role_modules = ['tianshu_star', 'tianxuan_star', 'tianji_star', 'tianquan_star', 'yuheng_star', 'kaiyang_star', 'yaoguang_star']
    
    for role_name in role_modules:
        try:
            print(f'  🔍 测试 {role_name} DeepSeek集成...')
            
            # 导入配置
            config_module = __import__(f'roles.{role_name}.config.deepseek_config', fromlist=[''])
            
            # 测试配置调用
            if hasattr(config_module, 'get_deepseek_config'):
                config = config_module.get_deepseek_config()
                
                # 测试角色设定
                role_setting = None
                if hasattr(config_module, 'get_role_setting'):
                    role_setting = config_module.get_role_setting()
                elif hasattr(config_module, f'{role_name.upper()}_ROLE_SETTING'):
                    role_setting = getattr(config_module, f'{role_name.upper()}_ROLE_SETTING')
                
                if role_setting and len(role_setting) > 50:  # 确保有实际内容
                    print(f'    ✅ {role_name} DeepSeek配置完整')
                    deepseek_working += 1
                    fix_results['fixes_applied'].append(f'{role_name}DeepSeek配置验证通过')
                else:
                    print(f'    ⚠️ {role_name} DeepSeek配置不完整')
                    fix_results['errors_encountered'].append(f'{role_name}DeepSeek配置不完整')
            else:
                print(f'    ❌ {role_name} DeepSeek配置缺失')
                fix_results['errors_encountered'].append(f'{role_name}DeepSeek配置缺失')
                
        except Exception as e:
            print(f'    ❌ {role_name} DeepSeek测试失败: {e}')
            fix_results['errors_encountered'].append(f'{role_name}DeepSeek测试失败: {e}')
    
    # 6. 生成修复总结
    print('\n📋 生成修复总结...')
    
    fix_results['summary'] = {
        'automation_systems_working': f'{working_automation}/7',
        'deepseek_configs_working': f'{deepseek_working}/7',
        'fixes_applied_count': len(fix_results['fixes_applied']),
        'errors_encountered_count': len(fix_results['errors_encountered']),
        'overall_success_rate': (len(fix_results['fixes_applied']) / 
                               (len(fix_results['fixes_applied']) + len(fix_results['errors_encountered'])) * 100
                               if (len(fix_results['fixes_applied']) + len(fix_results['errors_encountered'])) > 0 else 0)
    }
    
    print('=' * 60)
    print(f'🎯 修复总结:')
    print(f'  自动化系统: {working_automation}/7 个角色正常')
    print(f'  DeepSeek配置: {deepseek_working}/7 个角色正常')
    print(f'  修复成功: {len(fix_results["fixes_applied"])} 项')
    print(f'  遇到错误: {len(fix_results["errors_encountered"])} 项')
    print(f'  成功率: {fix_results["summary"]["overall_success_rate"]:.1f}%')
    
    # 保存修复报告
    report_file = f'seven_roles_integration_fix_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(fix_results, f, ensure_ascii=False, indent=2)
    
    print(f'\n📄 修复报告已保存到: {report_file}')
    
    return fix_results

async def test_role_core_systems_integration(role_name: str, automation_system) -> bool:
    """测试角色核心系统集成"""
    try:
        integration_score = 0
        total_tests = 4
        
        # 1. 测试DeepSeek集成
        if hasattr(automation_system, '_call_role_deepseek') or hasattr(automation_system, 'deepseek_service'):
            integration_score += 1
        
        # 2. 测试传奇记忆集成
        if hasattr(automation_system, 'memory_system') or hasattr(automation_system, 'store_memory'):
            integration_score += 1
        
        # 3. 测试绩效监控集成
        if hasattr(automation_system, 'performance_monitor') or hasattr(automation_system, 'record_performance'):
            integration_score += 1
        
        # 4. 测试自动化方法
        automation_methods = ['start_automation', 'execute_automation', 'execute_market_analysis', 
                            'execute_risk_analysis', 'execute_trading_automation', 'execute_decision_automation']
        if any(hasattr(automation_system, method) for method in automation_methods):
            integration_score += 1
        
        return integration_score >= 3  # 至少3/4的集成才算成功
        
    except Exception as e:
        print(f'    ❌ {role_name} 核心系统集成测试失败: {e}')
        return False

if __name__ == "__main__":
    result = asyncio.run(fix_seven_roles_integration())
