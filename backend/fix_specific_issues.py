#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复特定问题：
1. 移除新浪API调用
2. 修复JSONP解析问题
3. 修复数据库路径问题
        pass  # 专业版模式
"""

import os
import re
import json
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_sina_api_issues():
    """移除新浪API调用"""
    logger.info("🚫 移除新浪API调用...")
    
    backend_dir = Path(__file__).parent
    fixed_files = []
    
    # 查找包含新浪API的文件
    for py_file in backend_dir.rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
#             if 'hq.sinajs.cn' in content:  # 已禁用新浪API - 超时问题
                # 注释掉新浪API调用
                original_content = content
                content = re.sub(
                    r'(.*hq\.sinajs\.cn.*)',
                    r'# \1  # 已禁用新浪API - 超时问题',
                    content
                )
                
                # 添加错误处理，直接返回失败
                content = re.sub(
                    r'(async def.*sina.*\(.*?\):)',
                    r'\1\n        logger.warning("新浪API已禁用")\n        return {"success": False, "error": "新浪API已禁用"}',
                    content,
                    flags=re.DOTALL
                )
                
                if content != original_content:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    fixed_files.append(str(py_file))
                    logger.info(f"  ✅ 修复新浪API: {py_file.name}")
                    
        except Exception as e:
            logger.error(f"  ❌ 修复新浪API失败 {py_file}: {e}")
    
    return fixed_files

def fix_jsonp_parsing():
    """修复JSONP解析问题"""
    logger.info("🔧 修复JSONP解析问题...")
    
    backend_dir = Path(__file__).parent
    fixed_files = []
    
    # 修复天枢星的JSONP解析
    tianshu_files = [
        "roles/tianshu_star/services/tianshu_automation_system.py",
        "roles/tianshu_star/services/tianshu_core_service.py"
    ]
    
    for file_path in tianshu_files:
        full_path = backend_dir / file_path
        if full_path.exists():
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 添加改进的JSONP解析函数
                improved_jsonp = '''
def safe_parse_jsonp(text_content, callback_name=None):
    """安全的JSONP解析"""
    try:
        if not text_content or text_content.strip() == '':
            return None
            
        # 如果是空的JSON对象
        if text_content.strip() == '{}':
            return {}
            
        # 自动检测callback
        if not callback_name:
            callback_match = re.search(r'^([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(', text_content)
            if callback_match:
                callback_name = callback_match.group(1)
        
        # 移除JSONP包装
        if callback_name and text_content.startswith(callback_name + '('):
            start = len(callback_name) + 1
            end = text_content.rfind(')')
            if end > start:
                json_str = text_content[start:end]
            else:
                json_str = text_content
        else:
            json_str = text_content
        
        return json.loads(json_str)
        
    except Exception as e:
        logger.error(f"JSONP解析失败: {e}")
        return None
'''
                
                if 'def safe_parse_jsonp' not in content:
                    # 在文件开头添加导入和函数
                    import_section = "import re\nimport json\n"
                    if "import re" not in content:
                        content = import_section + content
                    content = improved_jsonp + '\n' + content
                
                # 替换旧的JSONP解析调用
                content = re.sub(
                    r'json\.loads\(json_str\)',
                    r'safe_parse_jsonp(text_content, callback)',
                    content
                )
                
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_files.append(str(full_path))
                logger.info(f"  ✅ 修复JSONP解析: {full_path.name}")
                
            except Exception as e:
                logger.error(f"  ❌ 修复JSONP解析失败 {full_path}: {e}")
    
    return fixed_files

def fix_database_paths():
    """修复数据库路径问题"""
    logger.info("🗄️ 修复数据库路径问题...")
    
    backend_dir = Path(__file__).parent
    fixed_files = []
    
    # 查找包含相对数据库路径的文件
    for py_file in backend_dir.rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'backend/data/' in content or 'data/stock_database.db' in content:
                original_content = content
                
                # 添加数据库配置导入
                if 'from backend.config.database_config import get_database_path' not in content:
                    import_line = 'from backend.config.database_config import get_database_path\n'
                    content = import_line + content
                
                # 替换相对路径
                content = re.sub(
                    r'"backend/data/stock_database\.db"',
                    r'get_database_path("stock_database")',
                    content
                )
                
                content = re.sub(
                    r"'backend/data/stock_database\.db'",
                    r"get_database_path('stock_database')",
                    content
                )
                
                content = re.sub(
                    r'"data/stock_database\.db"',
                    r'get_database_path("stock_database")',
                    content
                )
                
                if content != original_content:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    fixed_files.append(str(py_file))
                    logger.info(f"  ✅ 修复数据库路径: {py_file.name}")
                    
        except Exception as e:
            logger.error(f"  ❌ 修复数据库路径失败 {py_file}: {e}")
    
    return fixed_files

def remove_fallback_indicators():
        pass  # 专业版模式
        pass  # 专业版模式
    
    backend_dir = Path(__file__).parent
    fixed_files = []
    
    # 特别处理瑶光星的RD-Agent集成服务
    yaoguang_rd_file = backend_dir / "roles/yaoguang_star/services/rd_agent_integration_service.py"
    if yaoguang_rd_file.exists():
        try:
            with open(yaoguang_rd_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
        pass  # 专业版模式
        pass  # 专业版模式
        pass  # 专业版模式
            
            if content != original_content:
                with open(yaoguang_rd_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_files.append(str(yaoguang_rd_file))
        pass  # 专业版模式
                
        except Exception as e:
        pass  # 专业版模式
    
    return fixed_files

def main():
        logger.warning("新浪API已禁用")
        return {"success": False, "error": "新浪API已禁用"}
    """主修复函数"""
    logger.info("🔧 开始修复特定问题...")
    
    all_fixed_files = []
    
    # 1. 移除新浪API调用
    all_fixed_files.extend(fix_sina_api_issues())
    
    # 2. 修复JSONP解析问题
    all_fixed_files.extend(fix_jsonp_parsing())
    
    # 3. 修复数据库路径问题
    all_fixed_files.extend(fix_database_paths())
    
        pass  # 专业版模式
    
    # 生成修复报告
    report = {
        "fix_time": datetime.now().isoformat(),
        "total_files_fixed": len(set(all_fixed_files)),
        "fixed_files": list(set(all_fixed_files)),
        "issues_fixed": [
            "移除新浪API调用（解决超时问题）",
            "修复JSONP解析问题（解决天枢星API错误）", 
            "修复数据库路径问题（使用绝对路径）",
        pass  # 专业版模式
        ],
        "status": "completed"
    }
    
    backend_dir = Path(__file__).parent
    with open(backend_dir / "specific_issues_fix_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    logger.info(f"✅ 特定问题修复完成，共修复 {len(set(all_fixed_files))} 个文件")
    logger.info("📄 修复报告已保存到: specific_issues_fix_report.json")

if __name__ == "__main__":
    from datetime import datetime
    main()
