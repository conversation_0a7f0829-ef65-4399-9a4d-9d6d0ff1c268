#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复清理脚本造成的语法错误
"""

import os
import re
import ast
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        return True, None
    except SyntaxError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)

def fix_syntax_errors():
    """修复语法错误"""
    logger.info("🔧 修复语法错误...")
    
    backend_dir = Path(__file__).parent
    fixed_files = []
    
    # 查找所有Python文件
    for py_file in backend_dir.rglob("*.py"):
        if py_file.name == "fix_syntax_errors.py":
            continue
            
        is_valid, error = check_syntax(py_file)
        if not is_valid:
            logger.info(f"  🔧 修复语法错误: {py_file.name} - {error}")
            
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 修复常见的语法错误
                
                # 1. 修复孤立的 pass 语句
                content = re.sub(
                    r'^\s*pass\s*#\s*专业版模式\s*$',
                    '',
                    content,
                    flags=re.MULTILINE
                )
                
                # 2. 修复缺少的 except 块
                content = re.sub(
                    r'(\s+try:\s*\n.*?\n)\s*pass\s*#\s*专业版模式',
                    r'\1    except Exception as e:\n        logger.error(f"操作失败: {e}")',
                    content,
                    flags=re.DOTALL
                )
                
                # 3. 修复缺少的代码块
                content = re.sub(
                    r'(\s+if.*?:)\s*\n\s*pass\s*#\s*专业版模式',
                    r'\1\n        pass',
                    content
                )
                
                content = re.sub(
                    r'(\s+elif.*?:)\s*\n\s*pass\s*#\s*专业版模式',
                    r'\1\n        pass',
                    content
                )
                
                content = re.sub(
                    r'(\s+else:)\s*\n\s*pass\s*#\s*专业版模式',
                    r'\1\n        pass',
                    content
                )
                
                content = re.sub(
                    r'(\s+except.*?:)\s*\n\s*pass\s*#\s*专业版模式',
                    r'\1\n        pass',
                    content
                )
                
                content = re.sub(
                    r'(\s+finally:)\s*\n\s*pass\s*#\s*专业版模式',
                    r'\1\n        pass',
                    content
                )
                
                # 4. 修复缺少的函数体
                content = re.sub(
                    r'(\s+def.*?\):)\s*\n\s*pass\s*#\s*专业版模式',
                    r'\1\n        """专业版实现"""\n        pass',
                    content
                )
                
                # 5. 修复缺少的类方法体
                content = re.sub(
                    r'(\s+async def.*?\):)\s*\n\s*pass\s*#\s*专业版模式',
                    r'\1\n        """专业版异步实现"""\n        pass',
                    content
                )
                
                # 6. 移除多余的空行
                content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
                
                # 7. 修复缩进问题
                lines = content.split('\n')
                fixed_lines = []
                
                for i, line in enumerate(lines):
                    # 如果是空的专业版模式注释行，跳过
                    if line.strip() == 'pass  # 专业版模式' or line.strip() == '# 专业版模式':
                        continue
                    
                    # 如果前一行是控制结构，确保有正确的缩进
                    if i > 0:
                        prev_line = lines[i-1].strip()
                        if prev_line.endswith(':') and line.strip() == '':
                            # 添加适当的pass语句
                            indent = len(lines[i-1]) - len(lines[i-1].lstrip()) + 4
                            fixed_lines.append(' ' * indent + 'pass')
                            continue
                    
                    fixed_lines.append(line)
                
                content = '\n'.join(fixed_lines)
                
                # 再次检查语法
                try:
                    ast.parse(content)
                    # 语法正确，保存文件
                    if content != original_content:
                        with open(py_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        fixed_files.append(str(py_file))
                        logger.info(f"    ✅ 修复成功: {py_file.name}")
                except SyntaxError as e:
                    logger.warning(f"    ⚠️ 仍有语法错误: {py_file.name} - {e}")
                    # 如果仍有错误，尝试更激进的修复
                    try:
                        # 移除所有专业版模式相关的行
                        lines = original_content.split('\n')
                        clean_lines = []
                        
                        for line in lines:
                            if '专业版模式' not in line and 'pass  #' not in line:
                                clean_lines.append(line)
                        
                        content = '\n'.join(clean_lines)
                        ast.parse(content)
                        
                        with open(py_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        fixed_files.append(str(py_file))
                        logger.info(f"    ✅ 激进修复成功: {py_file.name}")
                        
                    except:
                        logger.error(f"    ❌ 无法修复: {py_file.name}")
                        
            except Exception as e:
                logger.error(f"    ❌ 修复失败 {py_file}: {e}")
    
    return fixed_files

def main():
    """主修复函数"""
    logger.info("🔧 开始修复语法错误...")
    
    fixed_files = fix_syntax_errors()
    
    logger.info(f"✅ 语法错误修复完成，共修复 {len(fixed_files)} 个文件")

if __name__ == "__main__":
    main()
