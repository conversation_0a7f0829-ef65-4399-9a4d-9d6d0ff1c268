from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制启动真实新闻收集，确保天枢星使用真实数据
"""

import asyncio
import requests
import json
from datetime import datetime

async def force_collect_real_news():
    """强制收集真实新闻"""
    print("🔍 强制启动真实新闻收集...")
    
    base_url = "http://127.0.0.1:8003"
    
    # 1. 启动市场新闻收集
    print("1. 启动市场新闻收集...")
    try:
        response = requests.post(f"{base_url}/api/tianshu/news/collect/start", 
                               json={"symbols": [], "limit": 20}, 
                               timeout=30)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   收集结果: {data.get('message', 'Unknown')}")
            if 'data' in data:
                print(f"   新闻数量: {data['data'].get('total_news', 0)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   请求失败: {e}")
    
    # 2. 启动股票新闻收集
    print("\n2. 启动股票新闻收集...")
    hot_stocks = ["000001", "600519", "000002"]
    
    for stock in hot_stocks:
        try:
            response = requests.post(f"{base_url}/api/tianshu/news/collect/start", 
                                   json={"symbols": [stock], "limit": 10}, 
                                   timeout=30)
            print(f"   股票{stock} - 状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   股票{stock} - 结果: {data.get('message', 'Unknown')}")
        except Exception as e:
            print(f"   股票{stock} - 失败: {e}")
    
    # 3. 等待收集完成
    print("\n3. 等待收集完成...")
    await asyncio.sleep(5)
    
    # 4. 检查收集结果
    print("\n4. 检查收集结果...")
    try:
        response = requests.get(f"{base_url}/api/tianshu/news/latest?limit=10", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应成功: {data.get('success', False)}")
            if 'data' in data and 'news' in data['data']:
                news_list = data['data']['news']
                print(f"   新闻数量: {len(news_list)}")
                if news_list:
                    first_news = news_list[0]
                    print(f"   第一条新闻: {first_news.get('title', 'No title')[:50]}...")
                    print(f"   数据源: {first_news.get('source', 'Unknown')}")
                    print(f"   发布时间: {first_news.get('publish_time', 'Unknown')}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   检查失败: {e}")
    
    # 5. 再次测试intelligence/news API
    print("\n5. 再次测试intelligence/news API...")
    try:
        response = requests.get(f"{base_url}/api/intelligence/news", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应成功: {data.get('success', False)}")
            if 'data' in data:
                news_data = data['data']
                print(f"   新闻数据类型: {type(news_data)}")
                if isinstance(news_data, dict) and 'news' in news_data:
                    news_list = news_data['news']
                    print(f"   新闻数量: {len(news_list)}")
                    print(f"   数据源: {news_data.get('data_source', 'Unknown')}")
                    
                    # 检查是否包含真实数据标识
                    response_text = str(data).lower()
                    real_indicators = ["crawl4ai", "eastmoney", "sina", "163", "real", "actual", "tianshu_star_real"]
                    mock_indicators = ["mock", "simulate", "fake", "test", "sample"]
                    
                    found_real = [indicator for indicator in real_indicators if indicator in response_text]
                    found_mock = [indicator for indicator in mock_indicators if indicator in response_text]
                    
                    print(f"   真实数据标识: {found_real}")
                    print(f"   模拟数据标识: {found_mock}")
                    print(f"   判断为真实数据: {len(found_real) > 0 and len(found_mock) == 0}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   测试失败: {e}")

def check_news_database():
    """检查新闻数据库"""
    print("\n💾 检查新闻数据库...")
    
    import sqlite3
    from pathlib import Path
    
    # 检查天枢星新闻分析数据库
    db_path = Path("backend/data/tianshu_news_analysis.db")
    if db_path.exists():
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"天枢星数据库表: {tables}")
            
            # 检查新闻分析表
            if 'news_analysis' in tables:
                cursor.execute("SELECT COUNT(*) FROM news_analysis")
                count = cursor.fetchone()[0]
                print(f"新闻分析记录数: {count}")
                
                if count > 0:
                    cursor.execute("SELECT * FROM news_analysis ORDER BY rowid DESC LIMIT 3")
                    recent = cursor.fetchall()
                    print(f"最新记录: {recent}")
            
            conn.close()
            
        except Exception as e:
            print(f"检查天枢星数据库失败: {e}")
    else:
        print("天枢星数据库文件不存在")
    
    # 检查新闻知识库
    news_db_path = Path("backend/data/news_knowledge_base.db")
    if news_db_path.exists():
        try:
            conn = sqlite3.connect(news_db_path)
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"新闻知识库表: {tables}")
            
            # 检查新闻项目表
            if 'news_items' in tables:
                cursor.execute("SELECT COUNT(*) FROM news_items")
                count = cursor.fetchone()[0]
                print(f"新闻项目记录数: {count}")
                
                if count > 0:
                    cursor.execute("SELECT title, source, created_at FROM news_items ORDER BY created_at DESC LIMIT 3")
                    recent = cursor.fetchall()
                    print(f"最新新闻: {recent}")
            
            conn.close()
            
        except Exception as e:
            print(f"检查新闻知识库失败: {e}")
    else:
        print("新闻知识库文件不存在")

async def main():
    """主函数"""
    print("🚀 开始强制真实新闻收集...")
    print("=" * 60)
    
    # 检查数据库状态
    check_news_database()
    
    # 强制收集真实新闻
    await force_collect_real_news()
    
    print("\n" + "=" * 60)
    print("✅ 强制新闻收集完成")

if __name__ == "__main__":
    asyncio.run(main())
