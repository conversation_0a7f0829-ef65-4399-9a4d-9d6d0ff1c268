#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诚实的系统状态检查 - 不美化，只说真话
"""

import requests
import json
import sqlite3
from pathlib import Path
from datetime import datetime

def check_api_status():
    """检查API真实状态"""
    print("🔍 检查API真实状态...")
    
    base_url = "http://127.0.0.1:8003"
    
    # 测试关键API
    apis_to_test = [
        ("/api/intelligence/news", "Intelligence新闻API"),
        ("/api/tianshu/news/latest", "天枢星新闻API"),
        ("/api/yaoguang/data/realtime/000001", "瑶光星实时数据"),
        ("/api/trader/positions", "玉衡星交易数据"),
        ("/api/system/statistics", "系统统计")
    ]
    
    results = {}
    
    for endpoint, name in apis_to_test:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                response_text = str(data).lower()
                
                # 检查真实性指标
                real_indicators = ["crawl4ai", "eastmoney", "real", "tianshu_star_real"]

                found_real = [ind for ind in real_indicators if ind in response_text]
                found_mock = [ind for ind in mock_indicators if ind in response_text]
                
                results[name] = {
                    "status": "成功",
                    "status_code": 200,
                    "has_data": "data" in data,
                    "real_indicators": found_real,
                    "mock_indicators": found_mock,
                    "is_real": len(found_real) > 0 and len(found_mock) == 0,
                    "data_size": len(str(data))
                }
            else:
                results[name] = {
                    "status": "失败",
                    "status_code": response.status_code,
                    "error": response.text[:200]
                }
                
        except Exception as e:
            results[name] = {
                "status": "异常",
                "error": str(e)
            }
    
    return results

def check_database_status():
    """检查数据库真实状态"""
    print("💾 检查数据库真实状态...")
    
    data_dir = Path("backend/data")
    db_status = {}
    
    if not data_dir.exists():
        return {"error": "数据目录不存在"}
    
    db_files = list(data_dir.glob("*.db"))
    
    for db_file in db_files:
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 统计记录数
            total_records = 0
            table_info = {}
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    total_records += count
                    table_info[table] = count
                except:
                    table_info[table] = "错误"
            
            size_mb = round(db_file.stat().st_size / 1024 / 1024, 2)
            
            db_status[db_file.name] = {
                "存在": True,
                "大小MB": size_mb,
                "表数量": len(tables),
                "总记录数": total_records,
                "表详情": table_info
            }
            
            conn.close()
            
        except Exception as e:
            db_status[db_file.name] = {
                "存在": True,
                "错误": str(e)
            }
    
    return db_status

def check_crawl4ai_status():
    """检查Crawl4AI真实状态"""
    print("🕷️ 检查Crawl4AI真实状态...")
    
    try:
        import crawl4ai
        crawl4ai_available = True
        crawl4ai_version = crawl4ai.__version__
    except ImportError:
        crawl4ai_available = False
        crawl4ai_version = "未安装"
    
    # 检查天枢星新闻收集服务
    news_service_file = Path("backend/roles/tianshu_star/services/news_collection_service.py")
    
    crawl4ai_usage = {
        "依赖可用": crawl4ai_available,
        "版本": crawl4ai_version
    }
    
    if news_service_file.exists():
        try:
            with open(news_service_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键实现
            crawl4ai_usage.update({
                "有AsyncWebCrawler导入": "from crawl4ai import AsyncWebCrawler" in content,
                "有arun调用": "await crawler.arun(" in content or "await self.crawler.arun(" in content,

                "有示例数据": "sample_news" in content or "示例" in content,
                "文件大小": len(content)
            })
            
        except Exception as e:
            crawl4ai_usage["文件读取错误"] = str(e)
    else:
        crawl4ai_usage["新闻服务文件"] = "不存在"
    
    return crawl4ai_usage

def generate_honest_report():
    """生成诚实报告"""
    print("📋 生成诚实系统状态报告...")
    print("=" * 60)
    
    # 检查各个组件
    api_status = check_api_status()
    db_status = check_database_status()
    crawl4ai_status = check_crawl4ai_status()
    
    # 计算真实评分
    total_apis = len(api_status)
    real_apis = sum(1 for api in api_status.values() if api.get("is_real", False))
    api_real_rate = (real_apis / total_apis * 100) if total_apis > 0 else 0
    
    working_dbs = sum(1 for db in db_status.values() if db.get("存在", False) and "错误" not in db)
    total_dbs = len(db_status)
    db_health_rate = (working_dbs / total_dbs * 100) if total_dbs > 0 else 0
    
    overall_score = (api_real_rate + db_health_rate) / 2
    
    print("\n📊 诚实评估结果")
    print("=" * 60)
    print(f"API真实数据率: {api_real_rate:.1f}% ({real_apis}/{total_apis})")
    print(f"数据库健康率: {db_health_rate:.1f}% ({working_dbs}/{total_dbs})")
    print(f"总体评分: {overall_score:.1f}%")
    
    print("\n🔌 API详细状态:")
    for api_name, status in api_status.items():
        real_status = "✅真实" if status.get("is_real", False) else "❌非真实"
        print(f"   {real_status} {api_name}: {status.get('status', '未知')}")
        if status.get("mock_indicators"):
            print(f"      发现模拟标识: {status['mock_indicators']}")
        if status.get("real_indicators"):
            print(f"      发现真实标识: {status['real_indicators']}")
    
    print("\n💾 数据库详细状态:")
    for db_name, status in db_status.items():
        if status.get("存在", False) and "错误" not in status:
            print(f"   ✅ {db_name}: {status.get('总记录数', 0)}条记录, {status.get('大小MB', 0)}MB")
        else:
            print(f"   ❌ {db_name}: {status.get('错误', '未知错误')}")
    
    print("\n🕷️ Crawl4AI状态:")
    for key, value in crawl4ai_status.items():
        status_icon = "✅" if value else "❌"
        if isinstance(value, bool):
            print(f"   {status_icon} {key}: {value}")
        else:
            print(f"   📋 {key}: {value}")
    
    # 诚实结论
    print("\n🎯 诚实结论:")
    if overall_score >= 90:
        print("   ✅ 系统状态优秀，基本达到生产要求")
    elif overall_score >= 70:
        print("   ⚠️ 系统状态良好，但仍有改进空间")
    elif overall_score >= 50:
        print("   🔧 系统状态一般，需要继续优化")
    else:
        print("   ❌ 系统状态较差，需要大量修复工作")
    
    print("\n📋 主要问题:")
    problems = []
    
    # API问题
    for api_name, status in api_status.items():
        if not status.get("is_real", False):
            problems.append(f"   - {api_name}仍包含模拟数据")
    
    # 数据库问题
    for db_name, status in db_status.items():
        if "错误" in status:
            problems.append(f"   - {db_name}存在错误")
    
    # Crawl4AI问题

    if crawl4ai_status.get("有示例数据", False):
        problems.append("   - 天枢星仍有示例数据")
    
    if problems:
        for problem in problems:
            print(problem)
    else:
        print("   🎉 未发现重大问题")
    
    # 保存详细报告
    report = {
        "timestamp": datetime.now().isoformat(),
        "overall_score": overall_score,
        "api_status": api_status,
        "database_status": db_status,
        "crawl4ai_status": crawl4ai_status,
        "problems": problems
    }
    
    with open("backend/honest_system_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存: backend/honest_system_report.json")
    
    return report

if __name__ == "__main__":
    print("🚀 开始诚实系统状态检查...")
    report = generate_honest_report()
    print(f"\n✅ 检查完成，总体评分: {report['overall_score']:.1f}%")
