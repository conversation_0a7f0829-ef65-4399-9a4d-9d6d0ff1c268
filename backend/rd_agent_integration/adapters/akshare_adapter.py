#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD-Agent AkShare数据适配器
将AkShare数据源适配为RD-Agent可用的格式
"""

import asyncio
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import numpy as np

# 导入现有的AkShare服务
try:
    from shared.data_sources.akshare_service import AkShareService
    _AKSHARE_SERVICE_AVAILABLE = True
except ImportError:
    _AKSHARE_SERVICE_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class RDAgentDataFormat:
    """RD-Agent标准数据格式"""
    symbol: str
    timestamp: datetime
    features: Dict[str, float]
    target: Optional[float] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class RDAgentAkShareAdapter:
    """RD-Agent AkShare数据适配器"""
    
    def __init__(self):
        self.service_name = "RDAgentAkShareAdapter"
        self.version = "0.3.0"
        
        # 初始化AkShare服务
        if _AKSHARE_SERVICE_AVAILABLE:
            self.akshare_service = AkShareService()
            self.available = True
        else:
            self.akshare_service = None
            self.available = False
            logger.warning("AkShare服务不可用")
        
        # 数据缓存
        self.data_cache: Dict[str, pd.DataFrame] = {}
        
        # 特征映射配置
        self.feature_mapping = {
            # 价格特征
            "open": "open_price",
            "high": "high_price", 
            "low": "low_price",
            "close": "close_price",
            "volume": "volume",
            "amount": "turnover",
            
            # 技术指标特征
            "ma5": "ma_5",
            "ma10": "ma_10",
            "ma20": "ma_20",
            "ma60": "ma_60",
            
            # 基本面特征
            "pe": "pe_ratio",
            "pb": "pb_ratio",
            "ps": "ps_ratio",
            "pcf": "pcf_ratio"
        }
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "cache_hits": 0,
            "data_points_processed": 0
        }
        
        logger.info(f"{self.service_name} v{self.version} 初始化完成")
    
    async def get_stock_data_for_rd_agent(self, 
                                        symbol: str,
                                        start_date: str,
                                        end_date: str,
                                        features: List[str] = None) -> List[RDAgentDataFormat]:
        """获取股票数据并转换为RD-Agent格式"""
        
        if not self.available:
            logger.error("AkShare服务不可用")
            return []
        
        try:
            self.stats["total_requests"] += 1
            
            # 检查缓存
            cache_key = f"{symbol}_{start_date}_{end_date}"
            if cache_key in self.data_cache:
                self.stats["cache_hits"] += 1
                raw_data = self.data_cache[cache_key]
            else:
                # 从AkShare获取数据
                raw_data = await self._fetch_akshare_data(symbol, start_date, end_date)
                if raw_data is not None and not raw_data.empty:
                    self.data_cache[cache_key] = raw_data
            
            if raw_data is None or raw_data.empty:
                self.stats["failed_requests"] += 1
                return []
            
            # 转换为RD-Agent格式
            rd_agent_data = await self._convert_to_rd_agent_format(raw_data, symbol, features)
            
            self.stats["successful_requests"] += 1
            self.stats["data_points_processed"] += len(rd_agent_data)
            
            logger.info(f"  获取RD-Agent格式数据: {symbol}, {len(rd_agent_data)}条记录")
            return rd_agent_data
            
        except Exception as e:
            self.stats["failed_requests"] += 1
            logger.error(f"  获取RD-Agent数据失败: {symbol} - {e}")
            return []
    
    async def _fetch_akshare_data(self, symbol: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """从AkShare获取原始数据"""
        
        try:
            # 调用现有的AkShare服务
            result = await self.akshare_service.get_stock_history(
                stock_code=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            if result.get("success") and result.get("data") is not None:
                return result["data"]
            else:
                logger.warning(f"AkShare数据获取失败: {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"AkShare数据获取异常: {symbol} - {e}")
            return None
    
    async def _convert_to_rd_agent_format(self, 
                                        raw_data: pd.DataFrame, 
                                        symbol: str,
                                        features: List[str] = None) -> List[RDAgentDataFormat]:
        """将原始数据转换为RD-Agent格式"""
        
        rd_agent_data = []
        
        try:
            # 确保有日期列
            if 'trade_date' not in raw_data.columns:
                logger.error("数据缺少trade_date列")
                return []
            
            # 计算技术指标
            enhanced_data = await self._calculate_technical_indicators(raw_data)
            
            # 逐行转换
            for idx, row in enhanced_data.iterrows():
                try:
                    # 提取基础特征
                    base_features = self._extract_base_features(row)
                    
                    # 计算衍生特征
                    derived_features = await self._calculate_derived_features(enhanced_data, idx)
                    
                    # 合并特征
                    all_features = {**base_features, **derived_features}
                    
                    # 筛选指定特征
                    if features:
                        filtered_features = {k: v for k, v in all_features.items() if k in features}
                    else:
                        filtered_features = all_features
                    
                    # 计算目标值（下一日收益率）
                    target = await self._calculate_target(enhanced_data, idx)
                    
                    # 创建RD-Agent数据格式
                    rd_data = RDAgentDataFormat(
                        symbol=symbol,
                        timestamp=pd.to_datetime(row['trade_date']),
                        features=filtered_features,
                        target=target,
                        metadata={
                            "data_source": "akshare",
                            "original_index": idx,
                            "feature_count": len(filtered_features)
                        }
                    )
                    
                    rd_agent_data.append(rd_data)
                    
                except Exception as e:
                    logger.warning(f"转换单行数据失败: {idx} - {e}")
                    continue
            
            return rd_agent_data
            
        except Exception as e:
            logger.error(f"数据格式转换失败: {e}")
            return []
    
    def _extract_base_features(self, row: pd.Series) -> Dict[str, float]:
        """提取基础特征"""
        
        features = {}
        
        # 价格特征
        for akshare_col, rd_col in self.feature_mapping.items():
            if akshare_col in row.index and pd.notna(row[akshare_col]):
                features[rd_col] = float(row[akshare_col])
        
        # 计算价格相关特征
        if all(col in row.index for col in ['open_price', 'high_price', 'low_price', 'close_price']):
            # 价格范围
            features['price_range'] = (row['high_price'] - row['low_price']) / row['close_price']
            
            # 开盘缺口
            if 'prev_close' in row.index and pd.notna(row['prev_close']):
                features['gap'] = (row['open_price'] - row['prev_close']) / row['prev_close']
            
            # 实体大小
            features['body_size'] = abs(row['close_price'] - row['open_price']) / row['close_price']
            
            # 上影线和下影线
            features['upper_shadow'] = (row['high_price'] - max(row['open_price'], row['close_price'])) / row['close_price']
            features['lower_shadow'] = (min(row['open_price'], row['close_price']) - row['low_price']) / row['close_price']
        
        return features
    
    async def _calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        
        enhanced_data = data.copy()
        
        try:
            # 确保数据按日期排序
            enhanced_data = enhanced_data.sort_values('trade_date')
            
            # 移动平均线
            if 'close_price' in enhanced_data.columns:
                enhanced_data['ma_5'] = enhanced_data['close_price'].rolling(window=5).mean()
                enhanced_data['ma_10'] = enhanced_data['close_price'].rolling(window=10).mean()
                enhanced_data['ma_20'] = enhanced_data['close_price'].rolling(window=20).mean()
                enhanced_data['ma_60'] = enhanced_data['close_price'].rolling(window=60).mean()
                
                # 价格相对位置
                enhanced_data['price_vs_ma20'] = enhanced_data['close_price'] / enhanced_data['ma_20'] - 1
                enhanced_data['price_vs_ma60'] = enhanced_data['close_price'] / enhanced_data['ma_60'] - 1
                
                # 收益率
                enhanced_data['return_1d'] = enhanced_data['close_price'].pct_change()
                enhanced_data['return_5d'] = enhanced_data['close_price'].pct_change(5)
                enhanced_data['return_20d'] = enhanced_data['close_price'].pct_change(20)
                
                # 波动率
                enhanced_data['volatility_5d'] = enhanced_data['return_1d'].rolling(window=5).std()
                enhanced_data['volatility_20d'] = enhanced_data['return_1d'].rolling(window=20).std()
                
                # 前一日收盘价
                enhanced_data['prev_close'] = enhanced_data['close_price'].shift(1)
            
            # 成交量指标
            if 'volume' in enhanced_data.columns:
                enhanced_data['volume_ma_5'] = enhanced_data['volume'].rolling(window=5).mean()
                enhanced_data['volume_ma_20'] = enhanced_data['volume'].rolling(window=20).mean()
                enhanced_data['volume_ratio'] = enhanced_data['volume'] / enhanced_data['volume_ma_20']
            
            return enhanced_data
            
        except Exception as e:
            logger.error(f"技术指标计算失败: {e}")
            return data
    
    async def _calculate_derived_features(self, data: pd.DataFrame, current_idx: int) -> Dict[str, float]:
        """计算衍生特征"""
        
        features = {}
        
        try:
            # 确保索引有效
            if current_idx < 0 or current_idx >= len(data):
                return features
            
            current_row = data.iloc[current_idx]
            
            # 动量特征
            if 'return_1d' in data.columns and current_idx >= 5:
                recent_returns = data['return_1d'].iloc[current_idx-4:current_idx+1]
                features['momentum_5d'] = recent_returns.mean() if not recent_returns.empty else 0.0
                features['momentum_consistency'] = (recent_returns > 0).sum() / len(recent_returns) if not recent_returns.empty else 0.5
            
            # 相对强度
            if all(col in current_row.index for col in ['close_price', 'ma_20', 'ma_60']):
                if pd.notna(current_row['ma_20']) and current_row['ma_20'] > 0:
                    features['rsi_vs_ma20'] = (current_row['close_price'] / current_row['ma_20'] - 1) * 100
                
                if pd.notna(current_row['ma_60']) and current_row['ma_60'] > 0:
                    features['rsi_vs_ma60'] = (current_row['close_price'] / current_row['ma_60'] - 1) * 100
            
            # 成交量特征
            if all(col in current_row.index for col in ['volume', 'volume_ma_20']):
                if pd.notna(current_row['volume_ma_20']) and current_row['volume_ma_20'] > 0:
                    features['volume_surge'] = current_row['volume'] / current_row['volume_ma_20']
            
            # 波动率特征
            if 'volatility_20d' in current_row.index and pd.notna(current_row['volatility_20d']):
                pass
            return features
            
        except Exception as e:
            logger.warning(f"衍生特征计算失败: {e}")
            return {}
    
    async def _calculate_target(self, data: pd.DataFrame, current_idx: int) -> Optional[float]:
        """计算目标值（下一日收益率）"""
        
        try:
            # 确保有下一日数据
            if current_idx + 1 >= len(data):
                return None
            
            current_close = data.iloc[current_idx]['close_price']
            next_close = data.iloc[current_idx + 1]['close_price']
            
            if pd.notna(current_close) and pd.notna(next_close) and current_close > 0:
                return (next_close - current_close) / current_close
            
            return None
            
        except Exception as e:
            logger.warning(f"目标值计算失败: {e}")
            return None
    
    async def get_multiple_stocks_data(self, 
                                     symbols: List[str],
                                     start_date: str,
                                     end_date: str,
                                     features: List[str] = None) -> Dict[str, List[RDAgentDataFormat]]:
        """批量获取多只股票的RD-Agent格式数据"""
        
        results = {}
        
        for symbol in symbols:
            try:
                data = await self.get_stock_data_for_rd_agent(symbol, start_date, end_date, features)
                results[symbol] = data
                
                # 添加延迟避免API限制
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"获取股票数据失败: {symbol} - {e}")
                results[symbol] = []
        
        return results
    
    def get_available_features(self) -> List[str]:
        """获取可用特征列表"""
        
        base_features = list(self.feature_mapping.values())
        
        derived_features = [
            'price_range', 'gap', 'body_size', 'upper_shadow', 'lower_shadow',
            'price_vs_ma20', 'price_vs_ma60', 'return_1d', 'return_5d', 'return_20d',
            'volatility_5d', 'volatility_20d', 'volume_ratio', 'momentum_5d',
            'momentum_consistency', 'rsi_vs_ma20', 'rsi_vs_ma60', 'volume_surge',
            'volatility_percentile'
        ]
        
        return base_features + derived_features
    
    def get_adapter_stats(self) -> Dict[str, Any]:
        """获取适配器统计信息"""
        
        return {
            "service_name": self.service_name,
            "version": self.version,
            "available": self.available,
            "akshare_service_available": _AKSHARE_SERVICE_AVAILABLE,
            "cache_size": len(self.data_cache),
            "available_features": len(self.get_available_features()),
            "performance_stats": self.stats,
            "timestamp": datetime.now().isoformat()
        }
