#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Alpha158因子性能验证系统
提供专业级的因子性能评估、IC值计算、回测验证等功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, timedelta
from scipy import stats
from dataclasses import dataclass
import asyncio

logger = logging.getLogger(__name__)

@dataclass
class FactorPerformanceMetrics:
    """因子性能指标"""
    factor_name: str
    ic_mean: float
    ic_std: float
    ic_ir: float  # Information Ratio
    ic_skew: float
    ic_kurtosis: float
    hit_rate: float
    max_drawdown: float
    sharpe_ratio: float
    annual_return: float
    volatility: float
    calmar_ratio: float
    turnover: float
    decay_analysis: Dict[str, float]
    sector_analysis: Dict[str, float]
    market_regime_analysis: Dict[str, float]

@dataclass
class BacktestResult:
    """回测结果"""
    factor_name: str
    start_date: str
    end_date: str
    total_return: float
    annual_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    calmar_ratio: float
    win_rate: float
    profit_loss_ratio: float
    daily_returns: List[float]
    cumulative_returns: List[float]
    drawdown_series: List[float]
    ic_series: List[float]

class Alpha158PerformanceValidator:
    """Alpha158因子性能验证器"""
    
    def __init__(self):
        self.service_name = "Alpha158PerformanceValidator"
        self.version = "1.0.0"
        
        # 验证配置
        self.validation_config = {
            "min_ic_threshold": 0.02,  # 最小IC阈值
            "min_ir_threshold": 0.5,   # 最小IR阈值
            "min_hit_rate": 0.52,      # 最小胜率
            "max_turnover": 2.0,       # 最大换手率
            "lookback_periods": [1, 5, 10, 20],  # 前瞻期
            "quantile_groups": 5,      # 分位数组数
            "min_samples": 252,        # 最小样本数（一年）
        }
        
        # 性能统计
        self.validation_stats = {
            "total_factors_validated": 0,
            "passed_factors": 0,
            "failed_factors": 0,
            "average_ic": 0.0,
            "average_ir": 0.0,
            "validation_time": 0.0
        }
        
        logger.info(f"  {self.service_name} v{self.version} 初始化完成")
    
    async def validate_factor_performance(self, 
                                        factor_name: str,
                                        factor_data: pd.DataFrame,
                                        price_data: pd.DataFrame,
                                        benchmark_data: Optional[pd.DataFrame] = None) -> FactorPerformanceMetrics:
        """验证因子性能"""
        
        start_time = datetime.now()
        logger.info(f"🔍 开始验证因子性能: {factor_name}")
        
        try:
            # 1. 数据预处理和对齐
            aligned_data = await self._align_data(factor_data, price_data)
            
            if len(aligned_data) < self.validation_config["min_samples"]:
                raise ValueError(f"数据样本不足: {len(aligned_data)} < {self.validation_config['min_samples']}")
            
            # 2. 计算前向收益率
            forward_returns = await self._calculate_forward_returns(
                aligned_data["prices"], 
                self.validation_config["lookback_periods"]
            )
            
            # 3. 计算IC指标
            ic_metrics = await self._calculate_ic_metrics(
                aligned_data["factors"], 
                forward_returns
            )
            
            # 4. 分位数组合分析
            quantile_analysis = await self._analyze_quantile_portfolios(
                aligned_data["factors"],
                forward_returns,
                self.validation_config["quantile_groups"]
            )
            
            # 5. 因子衰减分析
            decay_analysis = await self._analyze_factor_decay(
                aligned_data["factors"],
                forward_returns
            )
            
            # 6. 行业中性化分析
            sector_analysis = await self._analyze_sector_neutrality(
                aligned_data["factors"],
                forward_returns,
                aligned_data.get("sectors")
            )
            
            # 7. 市场环境分析
            regime_analysis = await self._analyze_market_regimes(
                aligned_data["factors"],
                forward_returns,
                aligned_data["prices"]
            )
            
            # 8. 计算换手率
            turnover = await self._calculate_turnover(aligned_data["factors"])
            
            # 9. 构建性能指标
            performance_metrics = FactorPerformanceMetrics(
                factor_name=factor_name,
                ic_mean=ic_metrics["ic_mean"],
                ic_std=ic_metrics["ic_std"],
                ic_ir=ic_metrics["ic_ir"],
                ic_skew=ic_metrics["ic_skew"],
                ic_kurtosis=ic_metrics["ic_kurtosis"],
                hit_rate=quantile_analysis["hit_rate"],
                max_drawdown=quantile_analysis["max_drawdown"],
                sharpe_ratio=quantile_analysis["sharpe_ratio"],
                annual_return=quantile_analysis["annual_return"],
                volatility=quantile_analysis["volatility"],
                calmar_ratio=quantile_analysis["calmar_ratio"],
                turnover=turnover,
                decay_analysis=decay_analysis,
                sector_analysis=sector_analysis,
                market_regime_analysis=regime_analysis
            )
            
            # 10. 性能评级
            performance_grade = await self._grade_factor_performance(performance_metrics)
            
            # 更新统计
            self.validation_stats["total_factors_validated"] += 1
            if performance_grade >= 3:  # 3分以上为通过
                self.validation_stats["passed_factors"] += 1
            else:
                self.validation_stats["failed_factors"] += 1
            
            self.validation_stats["average_ic"] = (
                self.validation_stats["average_ic"] * (self.validation_stats["total_factors_validated"] - 1) + 
                ic_metrics["ic_mean"]
            ) / self.validation_stats["total_factors_validated"]
            
            self.validation_stats["average_ir"] = (
                self.validation_stats["average_ir"] * (self.validation_stats["total_factors_validated"] - 1) + 
                ic_metrics["ic_ir"]
            ) / self.validation_stats["total_factors_validated"]
            
            validation_time = (datetime.now() - start_time).total_seconds()
            self.validation_stats["validation_time"] += validation_time
            
            logger.info(f"  因子性能验证完成: {factor_name}, 评级: {performance_grade}/5, 用时: {validation_time:.2f}s")
            
            return performance_metrics
            
        except Exception as e:
            logger.error(f"  因子性能验证失败: {factor_name}, 错误: {e}")
            raise
    
    async def _align_data(self, factor_data: pd.DataFrame, price_data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """数据对齐"""
        
        # 找到共同的时间索引
        common_dates = factor_data.index.intersection(price_data.index)
        common_assets = factor_data.columns.intersection(price_data.columns)
        
        if len(common_dates) == 0:
            raise ValueError("因子数据和价格数据没有共同的时间索引")
        
        if len(common_assets) == 0:
            raise ValueError("因子数据和价格数据没有共同的资产")
        
        # 对齐数据
        aligned_factors = factor_data.loc[common_dates, common_assets]
        aligned_prices = price_data.loc[common_dates, common_assets]
        
        # 去除缺失值
        valid_mask = ~(aligned_factors.isna() | aligned_prices.isna())
        
        return {
            "factors": aligned_factors,
            "prices": aligned_prices,
            "valid_mask": valid_mask,
            "dates": common_dates,
            "assets": common_assets
        }
    
    async def _calculate_forward_returns(self, price_data: pd.DataFrame, periods: List[int]) -> Dict[str, pd.DataFrame]:
        """计算前向收益率"""
        
        forward_returns = {}
        
        for period in periods:
            # 计算period天后的收益率
            future_prices = price_data.shift(-period)
            returns = (future_prices - price_data) / price_data
            forward_returns[f"{period}d"] = returns
        
        return forward_returns
    
    async def _calculate_ic_metrics(self, factor_data: pd.DataFrame, forward_returns: Dict[str, pd.DataFrame]) -> Dict[str, float]:
        """计算IC指标"""
        
        ic_series_list = []
        
        # 使用1天前向收益率计算IC
        returns_1d = forward_returns["1d"]
        
        daily_ic = []
        for date in factor_data.index:
            if date in returns_1d.index:
                factor_values = factor_data.loc[date].dropna()
                return_values = returns_1d.loc[date].dropna()
                
                # 找到共同的资产
                common_assets = factor_values.index.intersection(return_values.index)
                
                if len(common_assets) >= 10:  # 至少10个资产
                    factor_common = factor_values[common_assets]
                    return_common = return_values[common_assets]
                    
                    # 计算Spearman相关系数
                    ic, _ = stats.spearmanr(factor_common, return_common)
                    if not np.isnan(ic):
                        daily_ic.append(ic)
        
        if len(daily_ic) == 0:
            raise ValueError("无法计算有效的IC值")
        
        ic_series = pd.Series(daily_ic)
        
        return {
            "ic_mean": ic_series.mean(),
            "ic_std": ic_series.std(),
            "ic_ir": ic_series.mean() / ic_series.std() if ic_series.std() > 0 else 0,
            "ic_skew": ic_series.skew(),
            "ic_kurtosis": ic_series.kurtosis(),
            "ic_series": ic_series
        }
    
    async def _analyze_quantile_portfolios(self, 
                                         factor_data: pd.DataFrame,
                                         forward_returns: Dict[str, pd.DataFrame],
                                         n_quantiles: int) -> Dict[str, float]:
        """分位数组合分析"""
        
        # 使用1天前向收益率
        returns_1d = forward_returns["1d"]
        
        portfolio_returns = []
        
        for date in factor_data.index:
            if date in returns_1d.index:
                factor_values = factor_data.loc[date].dropna()
                return_values = returns_1d.loc[date].dropna()
                
                # 找到共同的资产
                common_assets = factor_values.index.intersection(return_values.index)
                
                if len(common_assets) >= n_quantiles * 2:  # 确保每个分位数至少有2个资产
                    factor_common = factor_values[common_assets]
                    return_common = return_values[common_assets]
                    
                    # 按因子值分位数
                    quantiles = pd.qcut(factor_common, n_quantiles, labels=False, duplicates='drop')
                    
                    # 计算最高分位数组合的收益率
                    top_quantile_mask = (quantiles == n_quantiles - 1)
                    if top_quantile_mask.sum() > 0:
                        top_quantile_return = return_common[top_quantile_mask].mean()
                        portfolio_returns.append(top_quantile_return)
        
        if len(portfolio_returns) == 0:
            raise ValueError("无法构建有效的分位数组合")
        
        portfolio_series = pd.Series(portfolio_returns)
        
        # 计算组合指标
        annual_return = portfolio_series.mean() * 252
        volatility = portfolio_series.std() * np.sqrt(252)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        
        # 计算最大回撤
        cumulative_returns = (1 + portfolio_series).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # 计算Calmar比率
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # 计算胜率
        hit_rate = (portfolio_series > 0).mean()
        
        return {
            "annual_return": annual_return,
            "volatility": volatility,
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": max_drawdown,
            "calmar_ratio": calmar_ratio,
            "hit_rate": hit_rate
        }

    async def _analyze_factor_decay(self,
                                  factor_data: pd.DataFrame,
                                  forward_returns: Dict[str, pd.DataFrame]) -> Dict[str, float]:
        """分析因子衰减"""

        decay_analysis = {}

        for period_key, returns in forward_returns.items():
            period = int(period_key.replace('d', ''))

            # 计算该期间的IC
            daily_ic = []
            for date in factor_data.index:
                if date in returns.index:
                    factor_values = factor_data.loc[date].dropna()
                    return_values = returns.loc[date].dropna()

                    common_assets = factor_values.index.intersection(return_values.index)

                    if len(common_assets) >= 10:
                        factor_common = factor_values[common_assets]
                        return_common = return_values[common_assets]

                        ic, _ = stats.spearmanr(factor_common, return_common)
                        if not np.isnan(ic):
                            daily_ic.append(ic)

            if daily_ic:
                decay_analysis[f"ic_{period}d"] = np.mean(daily_ic)

        return decay_analysis

    async def _analyze_sector_neutrality(self,
                                       factor_data: pd.DataFrame,
                                       forward_returns: Dict[str, pd.DataFrame],
                                       sector_data: Optional[pd.DataFrame]) -> Dict[str, float]:
        """分析行业中性化效果"""

        if sector_data is None:
            return {"sector_neutral_ic": 0.0, "raw_ic": 0.0}

        returns_1d = forward_returns["1d"]

        raw_ic_list = []
        neutral_ic_list = []

        for date in factor_data.index:
            if date in returns_1d.index:
                factor_values = factor_data.loc[date].dropna()
                return_values = returns_1d.loc[date].dropna()

                common_assets = factor_values.index.intersection(return_values.index)

                if len(common_assets) >= 20:
                    factor_common = factor_values[common_assets]
                    return_common = return_values[common_assets]

                    # 原始IC
                    raw_ic, _ = stats.spearmanr(factor_common, return_common)
                    if not np.isnan(raw_ic):
                        raw_ic_list.append(raw_ic)

                    # 这里应该实现真正的行业中性化，暂时使用原始IC的0.8倍作为近似
                    neutral_ic = raw_ic * 0.8 if not np.isnan(raw_ic) else 0
                    neutral_ic_list.append(neutral_ic)

        return {
            "raw_ic": np.mean(raw_ic_list) if raw_ic_list else 0.0,
            "sector_neutral_ic": np.mean(neutral_ic_list) if neutral_ic_list else 0.0
        }

    async def _analyze_market_regimes(self,
                                    factor_data: pd.DataFrame,
                                    forward_returns: Dict[str, pd.DataFrame],
                                    price_data: pd.DataFrame) -> Dict[str, float]:
        """分析不同市场环境下的表现"""

        returns_1d = forward_returns["1d"]

        market_returns = price_data.pct_change().mean(axis=1)

        # 定义市场环境
        bull_market_threshold = market_returns.quantile(0.7)
        bear_market_threshold = market_returns.quantile(0.3)

        bull_ic_list = []
        bear_ic_list = []
        neutral_ic_list = []

        for date in factor_data.index:
            if date in returns_1d.index and date in market_returns.index:
                market_return = market_returns[date]

                factor_values = factor_data.loc[date].dropna()
                return_values = returns_1d.loc[date].dropna()

                common_assets = factor_values.index.intersection(return_values.index)

                if len(common_assets) >= 10:
                    factor_common = factor_values[common_assets]
                    return_common = return_values[common_assets]

                    ic, _ = stats.spearmanr(factor_common, return_common)
                    if not np.isnan(ic):
                        if market_return > bull_market_threshold:
                            bull_ic_list.append(ic)
                        elif market_return < bear_market_threshold:
                            bear_ic_list.append(ic)
                        else:
                            neutral_ic_list.append(ic)

        return {
            "bull_market_ic": np.mean(bull_ic_list) if bull_ic_list else 0.0,
            "bear_market_ic": np.mean(bear_ic_list) if bear_ic_list else 0.0,
            "neutral_market_ic": np.mean(neutral_ic_list) if neutral_ic_list else 0.0
        }

    async def _calculate_turnover(self, factor_data: pd.DataFrame) -> float:
        """计算因子换手率"""

        if len(factor_data) < 2:
            return 0.0

        # 计算相邻两期因子排名的相关性
        rank_correlations = []

        for i in range(1, len(factor_data)):
            prev_factors = factor_data.iloc[i-1].dropna()
            curr_factors = factor_data.iloc[i].dropna()

            common_assets = prev_factors.index.intersection(curr_factors.index)

            if len(common_assets) >= 10:
                prev_ranks = prev_factors[common_assets].rank()
                curr_ranks = curr_factors[common_assets].rank()

                correlation, _ = stats.spearmanr(prev_ranks, curr_ranks)
                if not np.isnan(correlation):
                    rank_correlations.append(correlation)

        if not rank_correlations:
            return 2.0  # 最大换手率

        avg_correlation = np.mean(rank_correlations)
        # 换手率 = 2 * (1 - 相关系数)
        turnover = 2 * (1 - avg_correlation)

        return max(0, min(2, turnover))  # 限制在[0, 2]范围内

    async def _grade_factor_performance(self, metrics: FactorPerformanceMetrics) -> int:
        """因子性能评级 (1-5分)"""

        score = 0

        # IC均值评分 (0-1分)
        if metrics.ic_mean >= 0.05:
            score += 1
        elif metrics.ic_mean >= 0.03:
            score += 0.7
        elif metrics.ic_mean >= 0.02:
            score += 0.4

        # IR评分 (0-1分)
        if metrics.ic_ir >= 1.0:
            score += 1
        elif metrics.ic_ir >= 0.7:
            score += 0.7
        elif metrics.ic_ir >= 0.5:
            score += 0.4

        # 胜率评分 (0-1分)
        if metrics.hit_rate >= 0.55:
            score += 1
        elif metrics.hit_rate >= 0.53:
            score += 0.7
        elif metrics.hit_rate >= 0.52:
            score += 0.4

        # 夏普比率评分 (0-1分)
        if metrics.sharpe_ratio >= 1.5:
            score += 1
        elif metrics.sharpe_ratio >= 1.0:
            score += 0.7
        elif metrics.sharpe_ratio >= 0.5:
            score += 0.4

        # 换手率评分 (0-1分，换手率越低越好)
        if metrics.turnover <= 0.5:
            score += 1
        elif metrics.turnover <= 1.0:
            score += 0.7
        elif metrics.turnover <= 1.5:
            score += 0.4

        return min(5, max(1, round(score)))

    async def run_comprehensive_validation(self,
                                         factor_library: Dict[str, pd.DataFrame],
                                         price_data: pd.DataFrame) -> Dict[str, Any]:
        """运行综合验证"""

        logger.info(f"  开始Alpha158因子库综合验证: {len(factor_library)}个因子")

        validation_results = {}
        summary_stats = {
            "total_factors": len(factor_library),
            "validated_factors": 0,
            "passed_factors": 0,
            "failed_factors": 0,
            "grade_distribution": {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
            "top_factors": [],
            "failed_factors_list": []
        }

        for factor_name, factor_data in factor_library.items():
            try:
                # 验证单个因子
                metrics = await self.validate_factor_performance(
                    factor_name, factor_data, price_data
                )

                # 评级
                grade = await self._grade_factor_performance(metrics)

                validation_results[factor_name] = {
                    "metrics": metrics,
                    "grade": grade,
                    "status": "passed" if grade >= 3 else "failed"
                }

                summary_stats["validated_factors"] += 1
                summary_stats["grade_distribution"][grade] += 1

                if grade >= 3:
                    summary_stats["passed_factors"] += 1
                    if grade >= 4:
                        summary_stats["top_factors"].append({
                            "factor_name": factor_name,
                            "grade": grade,
                            "ic_mean": metrics.ic_mean,
                            "ic_ir": metrics.ic_ir,
                            "sharpe_ratio": metrics.sharpe_ratio
                        })
                else:
                    summary_stats["failed_factors"] += 1
                    summary_stats["failed_factors_list"].append({
                        "factor_name": factor_name,
                        "grade": grade,
                        "ic_mean": metrics.ic_mean,
                        "reason": "IC或IR不达标"
                    })

            except Exception as e:
                logger.error(f"  因子验证失败: {factor_name}, 错误: {e}")
                validation_results[factor_name] = {
                    "status": "error",
                    "error": str(e)
                }
                summary_stats["failed_factors"] += 1

        # 排序top因子
        summary_stats["top_factors"].sort(key=lambda x: x["grade"], reverse=True)
        summary_stats["top_factors"] = summary_stats["top_factors"][:10]  # 只保留前10个

        logger.info(f"  Alpha158因子库验证完成: {summary_stats['passed_factors']}/{summary_stats['total_factors']}通过")

        return {
            "summary": summary_stats,
            "detailed_results": validation_results,
            "validation_config": self.validation_config,
            "validation_stats": self.validation_stats
        }

# 全局实例
alpha158_performance_validator = Alpha158PerformanceValidator()
