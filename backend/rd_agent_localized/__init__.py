#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地化RD-Agent模块
        pass  # 专业版模式
"""

from .core_config import (
    LocalizedRDAgentSettings,
    FactorBasePropSetting,
    QuantBasePropSetting,
    LocalizedFactorConfig,
    LocalizedExperimentConfig,
    LOCALIZED_RD_AGENT_SETTINGS,
    FACTOR_PROP_SETTING,
    QUANT_PROP_SETTING,
    DEFAULT_FACTOR_CONFIGS,
    DEFAULT_EXPERIMENT_CONFIG
)

from .factor_generator import (
    LocalizedFactorHypothesisGen,
    localized_factor_hypothesis_gen
)

from .factor_evaluator import (
    LocalizedFactorEvaluator,
    localized_factor_evaluator
)

from .rd_loop_controller import (
    LocalizedRDLoop,
    localized_rd_loop_controller
)

__version__ = "1.0.0"
__author__ = "Localized RD-Agent Team"

__all__ = [
    # 配置类
    'LocalizedRDAgentSettings',
    'FactorBasePropSetting',
    'QuantBasePropSetting',
    'LocalizedFactorConfig',
    'LocalizedExperimentConfig',
    
    # 配置实例
    'LOCALIZED_RD_AGENT_SETTINGS',
    'FACTOR_PROP_SETTING',
    'QUANT_PROP_SETTING',
    'DEFAULT_FACTOR_CONFIGS',
    'DEFAULT_EXPERIMENT_CONFIG',
    
    # 核心类
    'LocalizedFactorHypothesisGen',
    'LocalizedFactorEvaluator',
    'LocalizedRDLoop',
    
    # 全局实例
    'localized_factor_hypothesis_gen',
    'localized_factor_evaluator',
    'localized_rd_loop_controller'
]
