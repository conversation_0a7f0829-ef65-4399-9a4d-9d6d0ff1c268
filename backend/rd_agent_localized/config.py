from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地化RD-Agent配置文件
提供完整的本地化RD-Agent配置参数
"""

import os
from typing import Dict, Any, List

# 基础配置
BASE_CONFIG = {
    "service_name": "本地化RD-Agent",
    "version": "1.0.0",
    "debug_mode": False,
    "log_level": "INFO"
}

# 因子生成配置
FACTOR_GENERATION_CONFIG = {
    "max_factors_per_iteration": 10,
    "factor_complexity_levels": [1, 2, 3, 4, 5],
    "default_complexity": 3,
    "mutation_rate": 0.3,
    "crossover_rate": 0.7,
    "selection_pressure": 0.8,
    "diversity_threshold": 0.1
}

# 因子评估配置
FACTOR_EVALUATION_CONFIG = {
    "evaluation_metrics": ["ic", "ir", "sharpe", "max_drawdown"],
    "ic_threshold": 0.03,
    "ir_threshold": 0.5,
    "sharpe_threshold": 1.0,
    "max_drawdown_threshold": 0.2,
    "evaluation_period": "1Y",
    "rebalance_frequency": "1M"
}

# RD循环配置
RD_LOOP_CONFIG = {
    "max_iterations": 100,
    "convergence_threshold": 0.001,
    "early_stopping_patience": 10,
    "population_size": 50,
    "elite_ratio": 0.2,
    "tournament_size": 5
}

# 数据配置
DATA_CONFIG = {
    "data_source": "local_database",
    "database_path": get_database_path("stock_database"),
    "stock_universe": "A股全市场",
    "start_date": "2020-01-01",
    "end_date": "2024-12-31",
    "frequency": "daily",
    "fields": ["open", "high", "low", "close", "volume", "amount"]
}

# Alpha158因子配置
ALPHA158_CONFIG = {
    "enabled": True,
    "factor_count": 159,
    "categories": {
        "价格因子": {"start": 0, "count": 20},
        "成交量因子": {"start": 20, "count": 20},
        "技术指标因子": {"start": 40, "count": 40},
        "波动率因子": {"start": 80, "count": 20},
        "动量因子": {"start": 100, "count": 20},
        "基本面因子": {"start": 120, "count": 20},
        "市场微观结构因子": {"start": 140, "count": 19}
    },
    "calculation_window": 30,
    "update_frequency": "daily"
}

# 实验配置
EXPERIMENT_CONFIG = {
    "experiment_name": "本地化RD-Agent实验",
    "experiment_id": "local_rd_agent_exp_001",
    "output_dir": "backend/rd_agent_localized/experiments",
    "save_intermediate_results": True,
    "checkpoint_frequency": 10,
    "max_experiment_time": 3600  # 1小时
}

# 性能配置
PERFORMANCE_CONFIG = {
    "parallel_processing": True,
    "max_workers": 4,
    "memory_limit": "4GB",
    "cache_enabled": True,
    "cache_size": 1000,
    "batch_size": 100
}

# 默认实验配置
DEFAULT_EXPERIMENT_CONFIG = {
    **BASE_CONFIG,
    **FACTOR_GENERATION_CONFIG,
    **FACTOR_EVALUATION_CONFIG,
    **RD_LOOP_CONFIG,
    **DATA_CONFIG,
    **ALPHA158_CONFIG,
    **EXPERIMENT_CONFIG,
    **PERFORMANCE_CONFIG
}

# 角色特定配置
ROLE_SPECIFIC_CONFIG = {
    "天璇星": {
        "focus_areas": ["技术指标因子", "价格因子"],
        "factor_allocation": 40,
        "specialization": "technical_analysis"
    },
    "瑶光星": {
        "focus_areas": ["成交量因子", "市场微观结构因子"],
        "factor_allocation": 39,
        "specialization": "volume_analysis"
    },
    "天权星": {
        "focus_areas": ["基本面因子"],
        "factor_allocation": 20,
        "specialization": "fundamental_analysis"
    },
    "天枢星": {
        "focus_areas": ["动量因子"],
        "factor_allocation": 20,
        "specialization": "momentum_analysis"
    },
    "天玑星": {
        "focus_areas": ["波动率因子"],
        "factor_allocation": 20,
        "specialization": "risk_analysis"
    },
    "玉衡星": {
        "focus_areas": ["市场微观结构因子"],
        "factor_allocation": 20,
        "specialization": "execution_analysis"
    },
    "开阳星": {
        "focus_areas": ["价格因子"],
        "factor_allocation": 20,
        "specialization": "stock_selection"
    }
}

# 模型配置
MODEL_CONFIG = {
    "default_model": "lightgbm",
    "model_params": {
        "lightgbm": {
            "objective": "regression",
            "metric": "rmse",
            "boosting_type": "gbdt",
            "num_leaves": 31,
            "learning_rate": 0.05,
            "feature_fraction": 0.9,
            "bagging_fraction": 0.8,
            "bagging_freq": 5,
            "verbose": 0
        },
        "xgboost": {
            "objective": "reg:squarederror",
            "eval_metric": "rmse",
            "max_depth": 6,
            "learning_rate": 0.1,
            "n_estimators": 100,
            "subsample": 0.8,
            "colsample_bytree": 0.8
        }
    },
    "cross_validation": {
        "n_splits": 5,
        "test_size": 0.2,
        "random_state": 42
    }
}

# 输出配置
OUTPUT_CONFIG = {
    "save_factors": True,
    "save_models": True,
    "save_predictions": True,
    "save_evaluations": True,
    "output_format": "pickle",
    "compression": True
}

def get_config(config_name: str = "default") -> Dict[str, Any]:
    """获取配置"""
    if config_name == "default":
        return DEFAULT_EXPERIMENT_CONFIG.copy()
    elif config_name == "factor_generation":
        return FACTOR_GENERATION_CONFIG.copy()
    elif config_name == "factor_evaluation":
        return FACTOR_EVALUATION_CONFIG.copy()
    elif config_name == "rd_loop":
        return RD_LOOP_CONFIG.copy()
    elif config_name == "alpha158":
        return ALPHA158_CONFIG.copy()
    elif config_name == "model":
        return MODEL_CONFIG.copy()
    else:
        return DEFAULT_EXPERIMENT_CONFIG.copy()

def get_role_config(role: str) -> Dict[str, Any]:
    """获取角色特定配置"""
    base_config = DEFAULT_EXPERIMENT_CONFIG.copy()
    role_config = ROLE_SPECIFIC_CONFIG.get(role, {})
    
    # 合并配置
    base_config.update(role_config)
    return base_config

def validate_config(config: Dict[str, Any]) -> bool:
    """验证配置有效性"""
    required_keys = [
        "service_name", "version", "max_factors_per_iteration",
        "evaluation_metrics", "max_iterations", "data_source"
    ]
    
    for key in required_keys:
        if key not in config:
            return False
    
    return True

def create_experiment_dir(experiment_id: str) -> str:
    """创建实验目录"""
    experiment_dir = os.path.join(EXPERIMENT_CONFIG["output_dir"], experiment_id)
    os.makedirs(experiment_dir, exist_ok=True)
    return experiment_dir

# 导出主要配置
__all__ = [
    "DEFAULT_EXPERIMENT_CONFIG",
    "FACTOR_GENERATION_CONFIG", 
    "FACTOR_EVALUATION_CONFIG",
    "RD_LOOP_CONFIG",
    "ALPHA158_CONFIG",
    "ROLE_SPECIFIC_CONFIG",
    "MODEL_CONFIG",
    "get_config",
    "get_role_config",
    "validate_config",
    "create_experiment_dir"
]
