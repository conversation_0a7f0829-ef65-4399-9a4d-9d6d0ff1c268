#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD-Agent本地化核心配置
        pass  # 专业版模式
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path
import os

@dataclass
class LocalizedRDAgentSettings:
    """本地化RD-Agent设置"""
    
    # 基础设置
    project_name: str = "localized_rd_agent"
    version: str = "1.0.0"
    
    # LLM设置 (使用DeepSeek)
    llm_provider: str = "deepseek"
    llm_model: str = "deepseek-chat"
    llm_api_key: str = "***********************************"
    llm_base_url: str = "https://api.deepseek.com/v1/"
    llm_temperature: float = 0.3
    llm_max_tokens: int = 4096
    
    # 因子研发设置
    factor_evolving_n: int = 10
    factor_max_iterations: int = 5
    factor_target_ic: float = 0.08
    factor_min_ic: float = 0.05
    
    # 遗传算法设置
    genetic_population_size: int = 50
    genetic_generations: int = 20
    genetic_mutation_rate: float = 0.1
    genetic_crossover_rate: float = 0.8
    genetic_elite_ratio: float = 0.2
    
    # 数据设置
    data_source: str = "jqdata"
    data_start_date: str = "2020-01-01"
    data_end_date: str = "2024-12-31"
    data_frequency: str = "daily"
    
    # 实验设置
    experiment_workspace: str = "workspace"
    experiment_max_parallel: int = 4
    experiment_timeout: int = 300
    
    # 评估设置
    evaluation_method: str = "ic_ir"
    evaluation_period: int = 252
    evaluation_min_periods: int = 60
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "project_name": self.project_name,
            "version": self.version,
            "llm_provider": self.llm_provider,
            "llm_model": self.llm_model,
            "llm_api_key": self.llm_api_key,
            "llm_base_url": self.llm_base_url,
            "llm_temperature": self.llm_temperature,
            "llm_max_tokens": self.llm_max_tokens,
            "factor_evolving_n": self.factor_evolving_n,
            "factor_max_iterations": self.factor_max_iterations,
            "factor_target_ic": self.factor_target_ic,
            "factor_min_ic": self.factor_min_ic,
            "genetic_population_size": self.genetic_population_size,
            "genetic_generations": self.genetic_generations,
            "genetic_mutation_rate": self.genetic_mutation_rate,
            "genetic_crossover_rate": self.genetic_crossover_rate,
            "genetic_elite_ratio": self.genetic_elite_ratio,
            "data_source": self.data_source,
            "data_start_date": self.data_start_date,
            "data_end_date": self.data_end_date,
            "data_frequency": self.data_frequency,
            "experiment_workspace": self.experiment_workspace,
            "experiment_max_parallel": self.experiment_max_parallel,
            "experiment_timeout": self.experiment_timeout,
            "evaluation_method": self.evaluation_method,
            "evaluation_period": self.evaluation_period,
            "evaluation_min_periods": self.evaluation_min_periods
        }

@dataclass
class FactorBasePropSetting:
    """因子基础属性设置 (从RD-Agent复制)"""
    
    # 场景设置
    scen: str = "localized_rd_agent.factor_experiment.LocalizedFactorScenario"
    
    # 假设生成
    hypothesis_gen: str = "localized_rd_agent.factor_proposal.LocalizedFactorHypothesisGen"
    
    # 假设到实验转换
    hypothesis2experiment: str = "localized_rd_agent.factor_proposal.LocalizedFactorHypothesis2Experiment"
    
    # 编码器
    coder: str = "localized_rd_agent.factor_coder.LocalizedFactorCoder"
    
    # 运行器
    runner: str = "localized_rd_agent.factor_runner.LocalizedFactorRunner"
    
    # 总结器
    summarizer: str = "localized_rd_agent.factor_feedback.LocalizedFactorFeedback"
    
    # 进化次数
    evolving_n: int = 10

@dataclass
class QuantBasePropSetting:
    """量化基础属性设置 (从RD-Agent复制)"""
    
    # 场景设置
    scen: str = "localized_rd_agent.quant_experiment.LocalizedQuantScenario"
    
    # 量化假设生成
    quant_hypothesis_gen: str = "localized_rd_agent.quant_proposal.LocalizedQuantHypothesisGen"
    
    # 模型假设到实验转换
    model_hypothesis2experiment: str = "localized_rd_agent.model_proposal.LocalizedModelHypothesis2Experiment"
    
    # 模型编码器
    model_coder: str = "localized_rd_agent.model_coder.LocalizedModelCoder"
    
    # 模型运行器
    model_runner: str = "localized_rd_agent.model_runner.LocalizedModelRunner"
    
    # 模型总结器
    model_summarizer: str = "localized_rd_agent.model_feedback.LocalizedModelFeedback"
    
    # 因子假设到实验转换
    factor_hypothesis2experiment: str = "localized_rd_agent.factor_proposal.LocalizedFactorHypothesis2Experiment"
    
    # 因子编码器
    factor_coder: str = "localized_rd_agent.factor_coder.LocalizedFactorCoder"
    
    # 因子运行器
    factor_runner: str = "localized_rd_agent.factor_runner.LocalizedFactorRunner"
    
    # 因子总结器
    factor_summarizer: str = "localized_rd_agent.factor_feedback.LocalizedFactorFeedback"
    
    # 进化次数
    evolving_n: int = 10
    
    # 动作选择策略
    action_selection: str = "bandit"

@dataclass
class LocalizedFactorConfig:
    """本地化因子配置"""
    
    factor_id: str
    factor_name: str
    factor_expression: str
    factor_description: str
    factor_category: str = "technical"
    expected_ic: float = 0.05
    expected_ir: float = 0.1
    complexity: str = "medium"
    generation_method: str = "localized_rd_agent"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "factor_id": self.factor_id,
            "factor_name": self.factor_name,
            "factor_expression": self.factor_expression,
            "factor_description": self.factor_description,
            "factor_category": self.factor_category,
            "expected_ic": self.expected_ic,
            "expected_ir": self.expected_ir,
            "complexity": self.complexity,
            "generation_method": self.generation_method
        }

@dataclass
class LocalizedExperimentConfig:
    """本地化实验配置"""
    
    experiment_id: str
    experiment_name: str
    experiment_type: str = "factor_research"
    target_ic: float = 0.08
    max_iterations: int = 5
    population_size: int = 50
    
    # 数据配置
    data_start_date: str = "2024-03-08"
    data_end_date: str = "2024-03-15"
    stock_pool: List[str] = field(default_factory=lambda: ["000001", "000002", "600000", "600036", "000858"])
    
    # 评估配置
    evaluation_metrics: List[str] = field(default_factory=lambda: ["ic", "ir", "sharpe", "max_drawdown"])
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "experiment_id": self.experiment_id,
            "experiment_name": self.experiment_name,
            "experiment_type": self.experiment_type,
            "target_ic": self.target_ic,
            "max_iterations": self.max_iterations,
            "population_size": self.population_size,
            "data_start_date": self.data_start_date,
            "data_end_date": self.data_end_date,
            "stock_pool": self.stock_pool,
            "evaluation_metrics": self.evaluation_metrics
        }

# 全局配置实例
LOCALIZED_RD_AGENT_SETTINGS = LocalizedRDAgentSettings()
FACTOR_PROP_SETTING = FactorBasePropSetting()
QUANT_PROP_SETTING = QuantBasePropSetting()

# 默认因子配置
DEFAULT_FACTOR_CONFIGS = [
    LocalizedFactorConfig(
        factor_id="localized_momentum_001",
        factor_name="Localized_Momentum_Alpha",
        factor_expression="rank(ts_delta(close, 20) * volume / ts_mean(volume, 20))",
        factor_description="本地化动量Alpha因子",
        factor_category="momentum",
        expected_ic=0.10,
        expected_ir=0.16
    ),
    LocalizedFactorConfig(
        factor_id="localized_reversal_001",
        factor_name="Localized_Reversal_Beta",
        factor_expression="rank(correlation(close, volume, 15) * (close / ts_mean(close, 20) - 1))",
        factor_description="本地化反转Beta因子",
        factor_category="mean_reversion",
        expected_ic=0.08,
        expected_ir=0.13
    ),
    LocalizedFactorConfig(
        factor_id="localized_volatility_001",
        factor_name="Localized_Volatility_Gamma",
        factor_expression="rank(ts_std(returns, 20) * rank(volume / ts_mean(volume, 10)))",
        factor_description="本地化波动率Gamma因子",
        factor_category="volatility",
        expected_ic=0.07,
        expected_ir=0.12
    )
]

# 默认实验配置
DEFAULT_EXPERIMENT_CONFIG = LocalizedExperimentConfig(
    experiment_id="localized_exp_001",
    experiment_name="本地化RD-Agent因子研发实验",
    experiment_type="factor_research",
    target_ic=0.08,
    max_iterations=5,
    population_size=50
)

__all__ = [
    'LocalizedRDAgentSettings',
    'FactorBasePropSetting', 
    'QuantBasePropSetting',
    'LocalizedFactorConfig',
    'LocalizedExperimentConfig',
    'LOCALIZED_RD_AGENT_SETTINGS',
    'FACTOR_PROP_SETTING',
    'QUANT_PROP_SETTING',
    'DEFAULT_FACTOR_CONFIGS',
    'DEFAULT_EXPERIMENT_CONFIG'
]
