#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地化RD-Agent因子评估器
        pass  # 专业版模式
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json
from scipy import stats

from .core_config import (
    LOCALIZED_RD_AGENT_SETTINGS,
    LocalizedFactorConfig,
    LocalizedExperimentConfig
)

logger = logging.getLogger(__name__)

class LocalizedFactorEvaluator:
    """本地化因子评估器 (从RD-Agent复制)"""
    
    def __init__(self, settings=None):
        self.settings = settings or LOCALIZED_RD_AGENT_SETTINGS
        self.evaluation_history = []
        
        logger.info("本地化因子评估器初始化完成")
    
    async def evaluate_factors(self, factors: List[LocalizedFactorConfig], 
                             stock_data: Dict[str, pd.DataFrame],
                             experiment_config: LocalizedExperimentConfig) -> Dict[str, Any]:
        """评估因子 (模拟RD-Agent的评估过程)"""
        logger.info(f"开始评估 {len(factors)} 个因子...")
        
        evaluation_results = {
            "experiment_id": experiment_config.experiment_id,
            "timestamp": datetime.now().isoformat(),
            "total_factors": len(factors),
            "evaluated_factors": 0,
            "factor_results": {},
            "summary_statistics": {},
            "ranking": []
        }
        
        factor_scores = {}
        
        for i, factor in enumerate(factors):
            try:
                logger.debug(f"评估因子 {i+1}/{len(factors)}: {factor.factor_name}")
                
                # 计算因子值
                factor_values = await self._calculate_factor_values(factor, stock_data)
                
                if factor_values is not None and len(factor_values) > 0:
                    # 计算评估指标
                    metrics = await self._calculate_evaluation_metrics(factor_values, stock_data)
                    
                    # 计算综合得分
                    composite_score = await self._calculate_composite_score(metrics, factor)
                    
                    factor_result = {
                        "factor_config": factor.to_dict(),
                        "factor_values": factor_values,
                        "evaluation_metrics": metrics,
                        "composite_score": composite_score,
                        "evaluation_status": "success"
                    }
                    
                    evaluation_results["factor_results"][factor.factor_id] = factor_result
                    factor_scores[factor.factor_id] = composite_score
                    evaluation_results["evaluated_factors"] += 1
                    
                else:
                    logger.warning(f"因子 {factor.factor_name} 计算失败")
                    evaluation_results["factor_results"][factor.factor_id] = {
                        "factor_config": factor.to_dict(),
                        "evaluation_status": "failed",
                        "error": "因子值计算失败"
                    }
                    
            except Exception as e:
                logger.error(f"因子评估失败 {factor.factor_name}: {e}")
                evaluation_results["factor_results"][factor.factor_id] = {
                    "factor_config": factor.to_dict(),
                    "evaluation_status": "error",
                    "error": str(e)
                }
        
        # 计算汇总统计
        evaluation_results["summary_statistics"] = await self._calculate_summary_statistics(
            evaluation_results["factor_results"]
        )
        
        # 生成排名
        evaluation_results["ranking"] = await self._generate_factor_ranking(factor_scores)
        
        logger.info(f"因子评估完成: {evaluation_results['evaluated_factors']}/{evaluation_results['total_factors']} 成功")
        
        # 记录评估历史
        self.evaluation_history.append(evaluation_results)
        
        return evaluation_results
    
    async def _calculate_factor_values(self, factor: LocalizedFactorConfig, 
                                     stock_data: Dict[str, pd.DataFrame]) -> Optional[Dict[str, float]]:
        """计算因子值"""
        try:
            factor_values = {}
            
            for stock_code, data in stock_data.items():
                try:
                    # 基于因子表达式计算因子值
                    factor_value = await self._evaluate_factor_expression(
                        factor.factor_expression, data, factor.factor_category
                    )
                    
                    if not pd.isna(factor_value) and not np.isinf(factor_value):
                        factor_values[stock_code] = float(factor_value)
                    else:
                        factor_values[stock_code] = 0.0
                        
                except Exception as e:
                    logger.warning(f"股票 {stock_code} 因子值计算失败: {e}")
                    factor_values[stock_code] = 0.0
            
            return factor_values if factor_values else None
            
        except Exception as e:
            logger.error(f"因子值计算失败: {e}")
            return None
    
    async def _evaluate_factor_expression(self, expression: str, data: pd.DataFrame, category: str) -> float:
        """评估因子表达式"""
        try:
            # 基于因子类别和表达式进行计算
            if category == "momentum":
                return await self._calculate_momentum_factor(expression, data)
            elif category == "mean_reversion":
                return await self._calculate_mean_reversion_factor(expression, data)
            elif category == "volatility":
                return await self._calculate_volatility_factor(expression, data)
            elif category == "cross_sectional":
                return await self._calculate_cross_sectional_factor(expression, data)
            elif category == "ml_enhanced":
                return await self._calculate_ml_enhanced_factor(expression, data)
            else:
                # 默认计算
                return await self._calculate_default_factor(expression, data)
                
        except Exception as e:
            logger.error(f"因子表达式评估失败: {e}")
            return 0.0
    
    async def _calculate_momentum_factor(self, expression: str, data: pd.DataFrame) -> float:
        """计算动量因子"""
        try:
            close = data['close']
            volume = data.get('volume', pd.Series(index=close.index, data=1))
            
            if "ts_delta" in expression and "volume" in expression:
                # ts_delta(close, period) * volume / ts_mean(volume, vol_period)
                price_delta = close.diff(20) if len(close) >= 20 else close.diff()
                volume_ratio = volume / volume.rolling(20).mean() if len(volume) >= 20 else volume / volume.mean()
                
                factor_value = (price_delta * volume_ratio).iloc[-1]
                
            elif "ts_delta" in expression and "ts_std" in expression:
                # ts_delta(close, period) / ts_std(close, std_period)
                price_delta = close.diff(20) if len(close) >= 20 else close.diff()
                price_std = close.rolling(20).std() if len(close) >= 20 else close.std()
                
                factor_value = (price_delta / price_std).iloc[-1] if price_std.iloc[-1] != 0 else 0.0
                
            else:
                # 默认动量计算
                factor_value = close.pct_change(20).iloc[-1] if len(close) >= 20 else 0.0
            
            return factor_value if not pd.isna(factor_value) else 0.0
            
        except Exception as e:
            logger.error(f"动量因子计算失败: {e}")
            return 0.0
    
    async def _calculate_mean_reversion_factor(self, expression: str, data: pd.DataFrame) -> float:
        """计算均值回归因子"""
        try:
            close = data['close']
            volume = data.get('volume', pd.Series(index=close.index, data=1))
            
            if "correlation" in expression:
                # correlation(close, volume, corr_period) * (close / ts_mean(close, period) - 1)
                correlation = close.rolling(15).corr(volume).iloc[-1] if len(close) >= 15 else 0.0
                mean_ratio = (close.iloc[-1] / close.rolling(20).mean().iloc[-1] - 1) if len(close) >= 20 else 0.0
                
                factor_value = correlation * mean_ratio
                
            elif "ts_mean" in expression and "ts_std" in expression:
                # -(close / ts_mean(close, period) - 1) / ts_std(close, std_period)
                mean_ratio = (close.iloc[-1] / close.rolling(20).mean().iloc[-1] - 1) if len(close) >= 20 else 0.0
                price_std = close.rolling(20).std().iloc[-1] if len(close) >= 20 else 1.0
                
                factor_value = -mean_ratio / price_std if price_std != 0 else 0.0
                
            else:
                # 默认均值回归计算
                factor_value = -(close.pct_change(5).iloc[-1]) if len(close) >= 5 else 0.0
            
            return factor_value if not pd.isna(factor_value) else 0.0
            
        except Exception as e:
            logger.error(f"均值回归因子计算失败: {e}")
            return 0.0
    
    async def _calculate_volatility_factor(self, expression: str, data: pd.DataFrame) -> float:
        """计算波动率因子"""
        try:
            close = data['close']
            volume = data.get('volume', pd.Series(index=close.index, data=1))
            returns = close.pct_change()
            
            if "ts_std" in expression and "volume" in expression:
                # ts_std(returns, period) * rank(volume / ts_mean(volume, vol_period))
                returns_std = returns.rolling(20).std().iloc[-1] if len(returns) >= 20 else returns.std()
                volume_rank = (volume / volume.rolling(20).mean()).rank().iloc[-1] / len(data) if len(volume) >= 20 else 0.5
                
                factor_value = returns_std * volume_rank
                
            elif "ts_max" in expression and "ts_min" in expression:
                # ts_max(high, period) / ts_min(low, period) - 1
                high = data.get('high', close)
                low = data.get('low', close)
                
                max_high = high.rolling(20).max().iloc[-1] if len(high) >= 20 else high.max()
                min_low = low.rolling(20).min().iloc[-1] if len(low) >= 20 else low.min()
                
                factor_value = (max_high / min_low - 1) if min_low != 0 else 0.0
                
            else:
                # 默认波动率计算
                factor_value = returns.rolling(20).std().iloc[-1] if len(returns) >= 20 else returns.std()
            
            return factor_value if not pd.isna(factor_value) else 0.0
            
        except Exception as e:
            logger.error(f"波动率因子计算失败: {e}")
            return 0.0
    
    async def _calculate_cross_sectional_factor(self, expression: str, data: pd.DataFrame) -> float:
        """计算截面因子"""
        try:
            close = data['close']
            volume = data.get('volume', pd.Series(index=close.index, data=1))
            
            if "rank" in expression and "-" in expression:
                # rank(close / ts_mean(close, period)) - rank(volume / ts_mean(volume, vol_period))
                close_ratio = close.iloc[-1] / close.rolling(20).mean().iloc[-1] if len(close) >= 20 else 1.0
                volume_ratio = volume.iloc[-1] / volume.rolling(20).mean().iloc[-1] if len(volume) >= 20 else 1.0
                
                factor_value = close_ratio - volume_ratio
                
            elif "ts_rank" in expression:
                # ts_rank(close, rank_period) * ts_rank(volume, vol_rank_period)
                close_rank = close.rolling(20).rank().iloc[-1] / 20 if len(close) >= 20 else 0.5
                volume_rank = volume.rolling(20).rank().iloc[-1] / 20 if len(volume) >= 20 else 0.5
                
                factor_value = close_rank * volume_rank
                
            else:
                # 默认截面计算
                factor_value = (close.iloc[-1] / close.mean()) - (volume.iloc[-1] / volume.mean())
            
            return factor_value if not pd.isna(factor_value) else 0.0
            
        except Exception as e:
            logger.error(f"截面因子计算失败: {e}")
            return 0.0
    
    async def _calculate_ml_enhanced_factor(self, expression: str, data: pd.DataFrame) -> float:
        """计算机器学习增强因子"""
        try:
            close = data['close']
            volume = data.get('volume', pd.Series(index=close.index, data=1))
            returns = close.pct_change()
            
            if "ts_rank" in expression and "*" in expression:
                # ts_rank(close, period) * ts_rank(volume, vol_period) * ts_rank(returns, ret_period)
                close_rank = close.rolling(20).rank().iloc[-1] / 20 if len(close) >= 20 else 0.5
                volume_rank = volume.rolling(20).rank().iloc[-1] / 20 if len(volume) >= 20 else 0.5
                returns_rank = returns.rolling(10).rank().iloc[-1] / 10 if len(returns) >= 10 else 0.5
                
                factor_value = close_rank * volume_rank * returns_rank
                
            elif "correlation" in expression:
                # correlation(close, volume, corr_period) * correlation(returns, volume, ret_corr_period)
                close_vol_corr = close.rolling(15).corr(volume).iloc[-1] if len(close) >= 15 else 0.0
                ret_vol_corr = returns.rolling(10).corr(volume).iloc[-1] if len(returns) >= 10 else 0.0
                
                factor_value = close_vol_corr * ret_vol_corr
                
            else:
                # 默认ML增强计算
                close_norm = (close.iloc[-1] - close.mean()) / close.std() if close.std() != 0 else 0.0
                volume_norm = (volume.iloc[-1] - volume.mean()) / volume.std() if volume.std() != 0 else 0.0
                returns_norm = (returns.iloc[-1] - returns.mean()) / returns.std() if returns.std() != 0 else 0.0
                
                factor_value = close_norm * volume_norm * returns_norm
            
            return factor_value if not pd.isna(factor_value) else 0.0
            
        except Exception as e:
            logger.error(f"ML增强因子计算失败: {e}")
            return 0.0
    
    async def _calculate_default_factor(self, expression: str, data: pd.DataFrame) -> float:
        """计算默认因子"""
        try:
            close = data['close']
            # 简单的动量因子
            factor_value = close.pct_change(10).iloc[-1] if len(close) >= 10 else 0.0
            return factor_value if not pd.isna(factor_value) else 0.0
        except Exception as e:
            logger.error(f"默认因子计算失败: {e}")
            return 0.0
    
    async def _calculate_evaluation_metrics(self, factor_values: Dict[str, float], 
                                          stock_data: Dict[str, pd.DataFrame]) -> Dict[str, float]:
        """计算评估指标"""
        try:
            # 计算收益率
            returns = {}
            for stock_code, data in stock_data.items():
                if len(data) >= 2:
                    stock_return = (data['close'].iloc[-1] / data['close'].iloc[0] - 1) * 100
                    returns[stock_code] = stock_return
                else:
                    returns[stock_code] = 0.0
            
            # 计算IC (信息系数)
            factor_vals = [factor_values.get(stock, 0) for stock in returns.keys()]
            return_vals = [returns.get(stock, 0) for stock in returns.keys()]
            
            if len(factor_vals) > 1 and len(return_vals) > 1:
                ic, ic_p_value = stats.pearsonr(factor_vals, return_vals)
                ic = ic if not pd.isna(ic) else 0.0
            else:
                ic = 0.0
                ic_p_value = 1.0
            
            # 计算IR (信息比率)
            ir = abs(ic) / (0.1 + np.std(factor_vals)) if np.std(factor_vals) > 0 else 0.0
            
            # 计算其他指标
            factor_mean = np.mean(list(factor_values.values()))
            factor_std = np.std(list(factor_values.values()))
            factor_skew = stats.skew(list(factor_values.values())) if len(factor_values) > 2 else 0.0
            factor_kurt = stats.kurtosis(list(factor_values.values())) if len(factor_values) > 2 else 0.0
            
            return {
                "ic": ic,
                "ic_p_value": ic_p_value,
                "ir": ir,
                "factor_mean": factor_mean,
                "factor_std": factor_std,
                "factor_skewness": factor_skew,
                "factor_kurtosis": factor_kurt,
                "coverage": len(factor_values) / len(stock_data) if stock_data else 0.0
            }
            
        except Exception as e:
            logger.error(f"评估指标计算失败: {e}")
            return {
                "ic": 0.0,
                "ic_p_value": 1.0,
                "ir": 0.0,
                "factor_mean": 0.0,
                "factor_std": 0.0,
                "factor_skewness": 0.0,
                "factor_kurtosis": 0.0,
                "coverage": 0.0
            }
    
    async def _calculate_composite_score(self, metrics: Dict[str, float], 
                                       factor: LocalizedFactorConfig) -> float:
        """计算综合得分"""
        try:
            # 权重配置
            weights = {
                "ic": 0.4,
                "ir": 0.3,
                "coverage": 0.2,
                "complexity": 0.1
            }
            
            # IC得分 (绝对值)
            ic_score = min(abs(metrics["ic"]) / 0.1, 1.0)
            
            # IR得分
            ir_score = min(metrics["ir"] / 0.2, 1.0)
            
            # 覆盖率得分
            coverage_score = metrics["coverage"]
            
            # 复杂度得分
            complexity_scores = {"low": 0.3, "medium": 0.6, "high": 0.8, "very_high": 1.0}
            complexity_score = complexity_scores.get(factor.complexity, 0.5)
            
            # 综合得分
            composite_score = (
                weights["ic"] * ic_score +
                weights["ir"] * ir_score +
                weights["coverage"] * coverage_score +
                weights["complexity"] * complexity_score
            )
            
            return max(0.0, min(1.0, composite_score))
            
        except Exception as e:
            logger.error(f"综合得分计算失败: {e}")
            return 0.0
    
    async def _calculate_summary_statistics(self, factor_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算汇总统计"""
        try:
            successful_results = [
                result for result in factor_results.values() 
                if result.get("evaluation_status") == "success"
            ]
            
            if not successful_results:
                return {"error": "没有成功的评估结果"}
            
            # 提取指标
            ics = [result["evaluation_metrics"]["ic"] for result in successful_results]
            irs = [result["evaluation_metrics"]["ir"] for result in successful_results]
            scores = [result["composite_score"] for result in successful_results]
            
            return {
                "total_evaluated": len(successful_results),
                "ic_statistics": {
                    "mean": np.mean(ics),
                    "std": np.std(ics),
                    "min": np.min(ics),
                    "max": np.max(ics),
                    "median": np.median(ics)
                },
                "ir_statistics": {
                    "mean": np.mean(irs),
                    "std": np.std(irs),
                    "min": np.min(irs),
                    "max": np.max(irs),
                    "median": np.median(irs)
                },
                "score_statistics": {
                    "mean": np.mean(scores),
                    "std": np.std(scores),
                    "min": np.min(scores),
                    "max": np.max(scores),
                    "median": np.median(scores)
                }
            }
            
        except Exception as e:
            logger.error(f"汇总统计计算失败: {e}")
            return {"error": str(e)}
    
    async def _generate_factor_ranking(self, factor_scores: Dict[str, float]) -> List[Dict[str, Any]]:
        """生成因子排名"""
        try:
            sorted_factors = sorted(factor_scores.items(), key=lambda x: x[1], reverse=True)
            
            ranking = []
            for rank, (factor_id, score) in enumerate(sorted_factors, 1):
                ranking.append({
                    "rank": rank,
                    "factor_id": factor_id,
                    "composite_score": score
                })
            
            return ranking
            
        except Exception as e:
            logger.error(f"因子排名生成失败: {e}")
            return []
    
    def get_evaluation_history(self) -> List[Dict]:
        """获取评估历史"""
        return self.evaluation_history
    
    def save_evaluation_history(self, filepath: str):
        """保存评估历史"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.evaluation_history, f, indent=2, ensure_ascii=False)
            logger.info(f"评估历史已保存: {filepath}")
        except Exception as e:
            logger.error(f"保存评估历史失败: {e}")

# 全局实例
localized_factor_evaluator = LocalizedFactorEvaluator()

__all__ = ['LocalizedFactorEvaluator', 'localized_factor_evaluator']
