#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地化RD-Agent因子生成器
        pass  # 专业版模式
"""

import asyncio
import logging
import random
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json

from .core_config import (
    LOCALIZED_RD_AGENT_SETTINGS,
    LocalizedFactorConfig,
    LocalizedExperimentConfig,
    DEFAULT_FACTOR_CONFIGS
)

logger = logging.getLogger(__name__)

class LocalizedFactorHypothesisGen:
    """本地化因子假设生成器 (从RD-Agent复制)"""
    
    def __init__(self, settings=None):
        self.settings = settings or LOCALIZED_RD_AGENT_SETTINGS
        self.factor_templates = self._load_factor_templates()
        self.generation_history = []
        
        logger.info("本地化因子假设生成器初始化完成")
    
    def _load_factor_templates(self) -> List[Dict]:
        """加载因子模板 (从RD-Agent的prompts.yaml复制)"""
        return [
            {
                "category": "momentum",
                "templates": [
                    "rank(ts_delta(close, {period}) * volume / ts_mean(volume, {vol_period}))",
                    "rank(ts_delta(close, {period}) / ts_std(close, {std_period}))",
                    "rank((close / ts_mean(close, {period}) - 1) * volume)",
                    "rank(ts_sum(returns, {period}) * ts_mean(volume, {vol_period}))"
                ],
                "params": {
                    "period": [5, 10, 20, 30],
                    "vol_period": [10, 20, 30],
                    "std_period": [10, 20, 30]
                }
            },
            {
                "category": "mean_reversion",
                "templates": [
                    "rank(correlation(close, volume, {corr_period}) * (close / ts_mean(close, {period}) - 1))",
                    "rank(-(close / ts_mean(close, {period}) - 1) / ts_std(close, {std_period}))",
                    "rank(-ts_delta(close, {period}) * ts_std(volume, {vol_period}))",
                    "rank((ts_mean(close, {short_period}) / ts_mean(close, {long_period}) - 1) * (-1))"
                ],
                "params": {
                    "corr_period": [10, 15, 20],
                    "period": [20, 30, 60],
                    "std_period": [10, 20, 30],
                    "vol_period": [10, 20, 30],
                    "short_period": [5, 10],
                    "long_period": [20, 30, 60]
                }
            },
            {
                "category": "volatility",
                "templates": [
                    "rank(ts_std(returns, {period}) * rank(volume / ts_mean(volume, {vol_period})))",
                    "rank(ts_std(close, {period}) / ts_mean(close, {mean_period}))",
                    "rank(ts_max(high, {period}) / ts_min(low, {period}) - 1)",
                    "rank(ts_std(returns, {period}) * ts_delta(volume, {vol_delta_period}))"
                ],
                "params": {
                    "period": [10, 20, 30],
                    "vol_period": [10, 20, 30],
                    "mean_period": [20, 30, 60],
                    "vol_delta_period": [5, 10, 20]
                }
            },
            {
                "category": "cross_sectional",
                "templates": [
                    "rank(close / ts_mean(close, {period})) - rank(volume / ts_mean(volume, {vol_period}))",
                    "rank(ts_rank(close, {rank_period}) * ts_rank(volume, {vol_rank_period}))",
                    "rank(correlation(close, volume, {corr_period}) * ts_delta(close, {delta_period}))",
                    "rank((close - ts_min(low, {period})) / (ts_max(high, {period}) - ts_min(low, {period})))"
                ],
                "params": {
                    "period": [20, 30, 60],
                    "vol_period": [20, 30, 60],
                    "rank_period": [10, 20, 30],
                    "vol_rank_period": [10, 20, 30],
                    "corr_period": [10, 15, 20],
                    "delta_period": [5, 10, 20]
                }
            },
            {
                "category": "ml_enhanced",
                "templates": [
                    "rank(ts_rank(close, {period}) * ts_rank(volume, {vol_period}) * ts_rank(returns, {ret_period}))",
                    "rank(correlation(close, volume, {corr_period}) * correlation(returns, volume, {ret_corr_period}))",
                    "rank(ts_delta(close, {period}) * ts_delta(volume, {vol_period}) * ts_std(returns, {std_period}))",
                    "rank((close / ts_mean(close, {period})) * (volume / ts_mean(volume, {vol_period})) * (returns / ts_std(returns, {std_period})))"
                ],
                "params": {
                    "period": [10, 20, 30],
                    "vol_period": [10, 20, 30],
                    "ret_period": [5, 10, 20],
                    "corr_period": [10, 15, 20],
                    "ret_corr_period": [5, 10, 15],
                    "std_period": [10, 20, 30]
                }
            }
        ]
    
    async def generate_factor_hypothesis(self, experiment_config: LocalizedExperimentConfig) -> List[LocalizedFactorConfig]:
        """生成因子假设 (模拟RD-Agent的LLM生成过程)"""
        logger.info("开始生成因子假设...")
        
        generated_factors = []
        target_count = min(experiment_config.get("population_size", 10), 20)  # 限制生成数量
        
        for i in range(target_count):
            try:
                # 随机选择因子类别
                category_template = random.choice(self.factor_templates)
                category = category_template["category"]
                
                # 随机选择模板
                template = random.choice(category_template["templates"])
                
                # 随机选择参数
                params = {}
                for param_name, param_values in category_template["params"].items():
                    if f"{{{param_name}}}" in template:
                        params[param_name] = random.choice(param_values)
                
                # 生成因子表达式
                factor_expression = template.format(**params)
                
                # 生成因子配置
                factor_config = LocalizedFactorConfig(
                    factor_id=f"localized_gen_{category}_{i:03d}",
                    factor_name=f"Localized_Gen_{category.title()}_{i:03d}",
                    factor_expression=factor_expression,
                    factor_description=f"本地化生成的{category}因子",
                    factor_category=category,
                    expected_ic=self._estimate_factor_ic(category, template),
                    expected_ir=self._estimate_factor_ir(category, template),
                    complexity=self._estimate_factor_complexity(template),
                    generation_method="localized_rd_agent_hypothesis_gen"
                )
                
                generated_factors.append(factor_config)
                
                logger.debug(f"生成因子 {i+1}/{target_count}: {factor_config.factor_name}")
                
            except Exception as e:
                logger.warning(f"因子生成失败 {i+1}: {e}")
                continue
        
        logger.info(f"因子假设生成完成: {len(generated_factors)}个因子")
        
        # 记录生成历史
        self.generation_history.append({
            "timestamp": datetime.now().isoformat(),
            "experiment_id": experiment_config.get("experiment_id", f"exp_{int(time.time())}"),
            "generated_count": len(generated_factors),
            "factors": [f.to_dict() for f in generated_factors]
        })
        
        return generated_factors
    
    def _estimate_factor_ic(self, category: str, template: str) -> float:
        """估计因子IC值"""
        base_ic = {
            "momentum": 0.08,
            "mean_reversion": 0.06,
            "volatility": 0.05,
            "cross_sectional": 0.07,
            "ml_enhanced": 0.10
        }.get(category, 0.05)
        
        # 基于模板复杂度调整
        complexity_bonus = 0.02 if "correlation" in template else 0.0
        complexity_bonus += 0.01 if "ts_rank" in template else 0.0
        complexity_bonus += 0.015 if "ts_std" in template else 0.0
        
        # 添加随机扰动
        noise = random.uniform(-0.01, 0.01)

        return max(0.03, min(0.15, base_ic + complexity_bonus + noise))
    
    def _estimate_factor_ir(self, category: str, template: str) -> float:
        """估计因子IR值"""
        base_ir = {
            "momentum": 0.12,
            "mean_reversion": 0.10,
            "volatility": 0.08,
            "cross_sectional": 0.11,
            "ml_enhanced": 0.15
        }.get(category, 0.08)
        
        # 基于模板复杂度调整
        complexity_bonus = 0.02 if "correlation" in template else 0.0
        complexity_bonus += 0.015 if "ts_rank" in template else 0.0
        
        # 添加随机扰动
        noise = random.uniform(-0.01, 0.01)

        return max(0.05, min(0.25, base_ir + complexity_bonus + noise))
    
    def _estimate_factor_complexity(self, template: str) -> str:
        """估计因子复杂度"""
        complexity_score = 0
        
        # 计算复杂度得分
        if "correlation" in template:
            complexity_score += 2
        if "ts_rank" in template:
            complexity_score += 2
        if "ts_std" in template:
            complexity_score += 1
        if "ts_delta" in template:
            complexity_score += 1
        if template.count("*") >= 2:
            complexity_score += 1
        if template.count("rank") >= 2:
            complexity_score += 1
        
        # 分类复杂度
        if complexity_score >= 5:
            return "very_high"
        elif complexity_score >= 3:
            return "high"
        elif complexity_score >= 1:
            return "medium"
        else:
            return "low"
    
    async def evolve_factors(self, parent_factors: List[LocalizedFactorConfig], 
                           experiment_config: LocalizedExperimentConfig) -> List[LocalizedFactorConfig]:
        """进化因子 (模拟RD-Agent的遗传算法)"""
        logger.info("开始因子进化...")
        
        evolved_factors = []
        
        # 选择优秀的父代因子
        elite_count = int(len(parent_factors) * self.settings.genetic_elite_ratio)
        elite_factors = sorted(parent_factors, key=lambda x: x.expected_ic, reverse=True)[:elite_count]
        
        # 保留精英
        evolved_factors.extend(elite_factors)
        
        # 生成新的因子
        target_count = experiment_config.get("population_size", 10) - len(elite_factors)
        
        for i in range(target_count):
            try:
                if random.random() < 0.7:  # 70%概率交叉
                    # 交叉操作
                    parent1, parent2 = random.sample(parent_factors, 2)
                    child_factor = await self._crossover_factors(parent1, parent2, i)
                else:
                    # 变异操作
                    parent = random.choice(parent_factors)
                    child_factor = await self._mutate_factor(parent, i)
                
                if child_factor:
                    evolved_factors.append(child_factor)
                    
            except Exception as e:
                logger.warning(f"因子进化失败 {i+1}: {e}")
                continue
        
        logger.info(f"因子进化完成: {len(evolved_factors)}个因子")
        return evolved_factors
    
    async def _crossover_factors(self, parent1: LocalizedFactorConfig, 
                               parent2: LocalizedFactorConfig, index: int) -> Optional[LocalizedFactorConfig]:
        """因子交叉操作"""
        try:
            new_category = random.choice([parent1.factor_category, parent2.factor_category])
            
            # 从对应类别的模板中选择
            category_templates = [t for t in self.factor_templates if t["category"] == new_category]
            if not category_templates:
                return None
            
            category_template = category_templates[0]
            template = random.choice(category_template["templates"])
            
            # 生成参数
            params = {}
            for param_name, param_values in category_template["params"].items():
                if f"{{{param_name}}}" in template:
                    params[param_name] = random.choice(param_values)
            
            factor_expression = template.format(**params)
            
            # 混合父代的IC和IR
            expected_ic = (parent1.expected_ic + parent2.expected_ic) / 2
            expected_ir = (parent1.expected_ir + parent2.expected_ir) / 2
            
            # 添加变异
            if random.random() < 0.3:  # 30%概率变异
                expected_ic += random.uniform(-0.005, 0.005)
                expected_ir += random.uniform(-0.005, 0.005)
            
            return LocalizedFactorConfig(
                factor_id=f"localized_cross_{new_category}_{index:03d}",
                factor_name=f"Localized_Cross_{new_category.title()}_{index:03d}",
                factor_expression=factor_expression,
                factor_description=f"交叉生成的{new_category}因子",
                factor_category=new_category,
                expected_ic=max(0.03, min(0.15, expected_ic)),
                expected_ir=max(0.05, min(0.25, expected_ir)),
                complexity=self._estimate_factor_complexity(template),
                generation_method="localized_rd_agent_crossover"
            )
            
        except Exception as e:
            logger.error(f"因子交叉失败: {e}")
            return None
    
    async def _mutate_factor(self, parent: LocalizedFactorConfig, index: int) -> Optional[LocalizedFactorConfig]:
        """因子变异操作"""
        try:
            # 获取父代的类别模板
            category_templates = [t for t in self.factor_templates if t["category"] == parent.factor_category]
            if not category_templates:
                return None
            
            category_template = category_templates[0]
            template = random.choice(category_template["templates"])
            
            # 生成新参数
            params = {}
            for param_name, param_values in category_template["params"].items():
                if f"{{{param_name}}}" in template:
                    params[param_name] = random.choice(param_values)
            
            factor_expression = template.format(**params)
            
            # 变异IC和IR
            expected_ic = parent.expected_ic + random.uniform(-0.01, 0.01)
            expected_ir = parent.expected_ir + random.uniform(-0.01, 0.01)
            
            return LocalizedFactorConfig(
                factor_id=f"localized_mut_{parent.factor_category}_{index:03d}",
                factor_name=f"Localized_Mut_{parent.factor_category.title()}_{index:03d}",
                factor_expression=factor_expression,
                factor_description=f"变异生成的{parent.factor_category}因子",
                factor_category=parent.factor_category,
                expected_ic=max(0.03, min(0.15, expected_ic)),
                expected_ir=max(0.05, min(0.25, expected_ir)),
                complexity=self._estimate_factor_complexity(template),
                generation_method="localized_rd_agent_mutation"
            )
            
        except Exception as e:
            logger.error(f"因子变异失败: {e}")
            return None
    
    def get_generation_history(self) -> List[Dict]:
        """获取生成历史"""
        return self.generation_history
    
    def save_generation_history(self, filepath: str):
        """保存生成历史"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.generation_history, f, indent=2, ensure_ascii=False)
            logger.info(f"生成历史已保存: {filepath}")
        except Exception as e:
            logger.error(f"保存生成历史失败: {e}")

# 全局实例
localized_factor_hypothesis_gen = LocalizedFactorHypothesisGen()

__all__ = ['LocalizedFactorHypothesisGen', 'localized_factor_hypothesis_gen']
