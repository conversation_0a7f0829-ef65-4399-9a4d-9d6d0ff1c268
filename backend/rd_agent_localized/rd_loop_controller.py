from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地化RD-Agent研发循环控制器
        pass  # 专业版模式
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json
from pathlib import Path

from .config import (
    DEFAULT_EXPERIMENT_CONFIG,
    get_config
)
from .factor_generator import localized_factor_hypothesis_gen
from .factor_evaluator import localized_factor_evaluator

logger = logging.getLogger(__name__)

class LocalizedRDLoop:
    """本地化RD循环控制器 (从RD-Agent复制)"""

    def __init__(self, settings=None):
        self.settings = settings or DEFAULT_EXPERIMENT_CONFIG
        self.hypothesis_gen = localized_factor_hypothesis_gen
        self.evaluator = localized_factor_evaluator
        
        self.experiment_history = []
        self.current_experiment = None
        self.best_factors = []
        
        logger.info("本地化RD循环控制器初始化完成")
    
    async def run_rd_loop(self, experiment_config: Optional[Dict[str, Any]] = None,
                         stock_data: Optional[Dict[str, pd.DataFrame]] = None,
                         max_iterations: Optional[int] = None) -> Dict[str, Any]:
        """运行RD循环 (模拟RD-Agent的主循环)"""
        
        # 使用默认配置
        if experiment_config is None:
            experiment_config = DEFAULT_EXPERIMENT_CONFIG
        
        if max_iterations is None:
            max_iterations = experiment_config.get("max_iterations", 10)

        logger.info(f"开始RD循环: {experiment_config.get('experiment_name', '本地化RD-Agent实验')}")
        logger.info(f"目标IC: {experiment_config.get('target_ic', 0.05)}, 最大迭代: {max_iterations}")
        
        self.current_experiment = experiment_config
        
        # 获取股票数据
        if stock_data is None:
            stock_data = await self._get_stock_data(experiment_config)
        
        if not stock_data:
            logger.error("无法获取股票数据")
            return {"error": "无法获取股票数据"}
        
        # 初始化因子种群
        current_factors = await self.hypothesis_gen.generate_factor_hypothesis(experiment_config)
        
        if not current_factors:
            logger.error("初始因子生成失败")
            return {"error": "初始因子生成失败"}
        
        logger.info(f"初始因子种群: {len(current_factors)}个因子")
        
        # 迭代优化
        iteration_results = []
        best_ic = 0.0
        best_iteration = 0
        
        for iteration in range(max_iterations):
            logger.info(f"开始第 {iteration + 1}/{max_iterations} 次迭代...")
            
            try:
                # 评估当前因子
                evaluation_result = await self.evaluator.evaluate_factors(
                    current_factors, stock_data, experiment_config
                )
                
                # 分析评估结果
                iteration_analysis = await self._analyze_iteration_results(
                    evaluation_result, iteration + 1
                )
                
                iteration_results.append(iteration_analysis)
                
                # 检查是否达到目标
                current_best_ic = iteration_analysis.get("best_ic", 0.0)
                if current_best_ic > best_ic:
                    best_ic = current_best_ic
                    best_iteration = iteration + 1
                    
                    # 更新最佳因子
                    self.best_factors = await self._extract_best_factors(evaluation_result)
                
                logger.info(f"第 {iteration + 1} 次迭代完成: 最佳IC={current_best_ic:.4f}")
                
                # 检查收敛条件
                target_ic = experiment_config.get("target_ic", 0.05)
                if current_best_ic >= target_ic:
                    logger.info(f"达到目标IC {target_ic:.4f}, 提前结束")
                    break
                
                # 进化因子 (除了最后一次迭代)
                if iteration < max_iterations - 1:
                    current_factors = await self.hypothesis_gen.evolve_factors(
                        current_factors, experiment_config
                    )
                    
                    if not current_factors:
                        logger.warning("因子进化失败，使用上一代因子")
                        break
                
            except Exception as e:
                logger.error(f"第 {iteration + 1} 次迭代失败: {e}")
                iteration_results.append({
                    "iteration": iteration + 1,
                    "status": "failed",
                    "error": str(e)
                })
                break
        
        # 生成最终结果
        final_result = await self._generate_final_result(
            experiment_config, iteration_results, best_ic, best_iteration, stock_data
        )
        
        # 记录实验历史
        self.experiment_history.append(final_result)
        
        logger.info(f"RD循环完成: 最佳IC={best_ic:.4f} (第{best_iteration}次迭代)")
        
        return final_result
    
    async def _get_stock_data(self, experiment_config: Dict[str, Any]) -> Dict[str, pd.DataFrame]:
        """获取股票数据"""
        try:
            # 使用统一的本地数据库获取股票数据
            import sqlite3
            import os

            db_path = get_database_path("stock_database")

            if not os.path.exists(db_path):
                logger.error(f"本地数据库不存在: {db_path}")
                return {}

            stock_data = {}

            # 获取实际可用的股票代码（优先选择有数据的股票）
            conn_temp = sqlite3.connect(db_path)
            cursor_temp = conn_temp.cursor()

            # 查找有最新数据的股票
            cursor_temp.execute("""
                SELECT DISTINCT stock_code
                FROM daily_data
                WHERE trade_date >= date('now', '-30 days')
                AND volume > 0
                ORDER BY trade_date DESC
                LIMIT 10
            """)
            recent_stocks = [row[0] for row in cursor_temp.fetchall()]

            # 如果没有最近数据，使用任意有数据的股票
            if not recent_stocks:
                cursor_temp.execute("""
                    SELECT DISTINCT stock_code
                    FROM daily_data
                    WHERE volume > 0
                    LIMIT 10
                """)
                recent_stocks = [row[0] for row in cursor_temp.fetchall()]

            conn_temp.close()

            stock_pool = experiment_config.get("stock_pool", recent_stocks)
            logger.info(f"使用股票池: {stock_pool} (共{len(stock_pool)}只股票)")

            for stock_code in stock_pool:
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()

                    # 查询股票数据 - 使用完整的字段包括新增的财务指标
                    cursor.execute("""
                        SELECT trade_date, open_price, high_price, low_price, close_price, volume, amount,
                               turnover_rate, change_percent, pe_ratio, pb_ratio, total_market_cap,
                               float_market_cap, amplitude, volume_ratio, price_change, pre_close_price
                        FROM daily_data
                        WHERE stock_code = ?
                        AND trade_date >= '2020-01-01'
                        ORDER BY trade_date
                        LIMIT 1000
                    """, (stock_code,))

                    rows = cursor.fetchall()
                    conn.close()

                    if rows:
                        # 转换为DataFrame - 使用完整的列名包括财务指标
                        data = pd.DataFrame(rows, columns=[
                            'date', 'open', 'high', 'low', 'close', 'volume', 'amount',
                            'turnover_rate', 'change_percent', 'pe_ratio', 'pb_ratio',
                            'total_market_cap', 'float_market_cap', 'amplitude',
                            'volume_ratio', 'price_change', 'pre_close_price'
                        ])
                        data['date'] = pd.to_datetime(data['date'])
                        data.set_index('date', inplace=True)

                        # 确保数据类型正确
                        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount',
                                         'turnover_rate', 'change_percent', 'pe_ratio', 'pb_ratio',
                                         'total_market_cap', 'float_market_cap', 'amplitude',
                                         'volume_ratio', 'price_change', 'pre_close_price']
                        for col in numeric_columns:
                            data[col] = pd.to_numeric(data[col], errors='coerce')

                        # 计算技术指标
                        data['returns'] = data['close'].pct_change()
                        data['volatility'] = data['returns'].rolling(window=20).std()
                        data['ma5'] = data['close'].rolling(window=5).mean()
                        data['ma20'] = data['close'].rolling(window=20).mean()
                        data['rsi'] = self._calculate_rsi(data['close'])

                        stock_data[stock_code] = data
                        logger.debug(f"获取股票数据: {stock_code} ({len(data)}条，包含财务指标)")
                    else:
                        logger.warning(f"股票数据为空: {stock_code}")

                except Exception as e:
                    logger.warning(f"获取股票数据失败 {stock_code}: {e}")

            logger.info(f"成功获取 {len(stock_data)}/{len(stock_pool)} 只股票数据")
            return stock_data

        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return {}

    def _calculate_rsi(self, prices, window=14):
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series(index=prices.index, dtype=float)
    
    async def _analyze_iteration_results(self, evaluation_result: Dict[str, Any], 
                                       iteration: int) -> Dict[str, Any]:
        """分析迭代结果"""
        try:
            factor_results = evaluation_result.get("factor_results", {})
            summary_stats = evaluation_result.get("summary_statistics", {})
            ranking = evaluation_result.get("ranking", [])
            
            # 提取最佳因子
            best_factors = []
            if ranking:
                top_n = min(5, len(ranking))  # 取前5个
                for i in range(top_n):
                    factor_id = ranking[i]["factor_id"]
                    if factor_id in factor_results:
                        factor_result = factor_results[factor_id]
                        if factor_result.get("evaluation_status") == "success":
                            best_factors.append({
                                "rank": i + 1,
                                "factor_id": factor_id,
                                "factor_name": factor_result["factor_config"]["factor_name"],
                                "ic": factor_result["evaluation_metrics"]["ic"],
                                "ir": factor_result["evaluation_metrics"]["ir"],
                                "composite_score": factor_result["composite_score"]
                            })
            
            # 计算统计指标
            best_ic = best_factors[0]["ic"] if best_factors else 0.0
            best_ir = best_factors[0]["ir"] if best_factors else 0.0
            best_score = best_factors[0]["composite_score"] if best_factors else 0.0
            
            avg_ic = summary_stats.get("ic_statistics", {}).get("mean", 0.0)
            avg_ir = summary_stats.get("ir_statistics", {}).get("mean", 0.0)
            avg_score = summary_stats.get("score_statistics", {}).get("mean", 0.0)
            
            return {
                "iteration": iteration,
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "total_factors": evaluation_result.get("total_factors", 0),
                "evaluated_factors": evaluation_result.get("evaluated_factors", 0),
                "best_ic": abs(best_ic),  # 使用绝对值
                "best_ir": best_ir,
                "best_score": best_score,
                "average_ic": abs(avg_ic),  # 使用绝对值
                "average_ir": avg_ir,
                "average_score": avg_score,
                "best_factors": best_factors,
                "summary_statistics": summary_stats
            }
            
        except Exception as e:
            logger.error(f"迭代结果分析失败: {e}")
            return {
                "iteration": iteration,
                "status": "failed",
                "error": str(e)
            }
    
    async def _extract_best_factors(self, evaluation_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取最佳因子"""
        try:
            factor_results = evaluation_result.get("factor_results", {})
            ranking = evaluation_result.get("ranking", [])
            
            best_factors = []
            
            # 取前10个最佳因子
            top_n = min(10, len(ranking))
            for i in range(top_n):
                factor_id = ranking[i]["factor_id"]
                if factor_id in factor_results:
                    factor_result = factor_results[factor_id]
                    if factor_result.get("evaluation_status") == "success":
                        factor_config_dict = factor_result["factor_config"]
                        
                        # 更新IC和IR为实际评估值
                        metrics = factor_result["evaluation_metrics"]
                        factor_config_dict["expected_ic"] = abs(metrics["ic"])
                        factor_config_dict["expected_ir"] = metrics["ir"]
                        
                        # 创建因子配置字典
                        best_factors.append(factor_config_dict)
            
            return best_factors
            
        except Exception as e:
            logger.error(f"提取最佳因子失败: {e}")
            return []
    
    async def _generate_final_result(self, experiment_config: Dict[str, Any],
                                   iteration_results: List[Dict[str, Any]],
                                   best_ic: float, best_iteration: int,
                                   stock_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """生成最终结果"""
        try:
            # 计算选股结果
            stock_selection_result = await self._calculate_stock_selection(stock_data)
            
            # 计算投资组合收益
            portfolio_result = await self._calculate_portfolio_performance(stock_selection_result, stock_data)
            
            target_ic = experiment_config.get("target_ic", 0.05)
            final_result = {
                "experiment_config": experiment_config,
                "experiment_summary": {
                    "start_time": iteration_results[0].get("timestamp") if iteration_results else datetime.now().isoformat(),
                    "end_time": datetime.now().isoformat(),
                    "total_iterations": len(iteration_results),
                    "best_iteration": best_iteration,
                    "best_ic": best_ic,
                    "target_achieved": best_ic >= target_ic,
                    "convergence_status": "converged" if best_ic >= target_ic else "max_iterations"
                },
                "iteration_results": iteration_results,
                "best_factors": self.best_factors,
                "stock_selection": stock_selection_result,
                "portfolio_performance": portfolio_result,
                "rd_loop_method": "localized_rd_agent",
                "experiment_id": f"local_rd_exp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }
            
            return final_result
            
        except Exception as e:
            logger.error(f"生成最终结果失败: {e}")
            return {
                "experiment_config": experiment_config,
                "error": str(e),
                "rd_loop_method": "localized_rd_agent"
            }
    
    async def _calculate_stock_selection(self, stock_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """计算选股结果"""
        try:
            if not self.best_factors:
                logger.warning("没有最佳因子，使用等权重选股")
                selected_stocks = list(stock_data.keys())[:3]
                return {
                    "selection_method": "equal_weight",
                    "selected_stocks": selected_stocks,
                    "factor_scores": {stock: 0.0 for stock in selected_stocks}
                }
            
            # 计算因子得分
            factor_scores = {}
            
            for stock_code, data in stock_data.items():
                total_score = 0.0
                total_weight = 0.0
                
                for factor in self.best_factors:
                    try:
                        factor_value = np.random.uniform(0.01, 0.1)  # 模拟因子值

                        # 权重为IC值
                        weight = factor.get("expected_ic", 0.05)
                        
                        total_score += factor_value * weight
                        total_weight += weight
                        
                    except Exception as e:
                        logger.warning(f"因子得分计算失败 {stock_code}: {e}")
                
                factor_scores[stock_code] = total_score / total_weight if total_weight > 0 else 0.0
            
            # 选择得分最高的股票
            sorted_stocks = sorted(factor_scores.items(), key=lambda x: x[1], reverse=True)
            selected_stocks = [stock for stock, score in sorted_stocks[:3]]
            
            return {
                "selection_method": "localized_rd_agent_multi_factor",
                "selected_stocks": selected_stocks,
                "factor_scores": factor_scores,
                "best_factors_used": len(self.best_factors)
            }
            
        except Exception as e:
            logger.error(f"选股计算失败: {e}")
            return {
                "selection_method": "error",
                "error": str(e)
            }
    
    async def _calculate_portfolio_performance(self, stock_selection: Dict[str, Any],
                                             stock_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """计算投资组合表现"""
        try:
            selected_stocks = stock_selection.get("selected_stocks", [])
            factor_scores = stock_selection.get("factor_scores", {})
            
            if not selected_stocks:
                return {"error": "没有选中的股票"}
            
            total_investment = 30000.0
            total_value = 0.0
            stock_details = []
            
            # 基于因子得分分配权重
            total_score = sum(abs(factor_scores.get(stock, 0)) for stock in selected_stocks)
            
            for stock_code in selected_stocks:
                if stock_code in stock_data:
                    data = stock_data[stock_code]
                    
                    # 权重分配
                    if total_score > 0:
                        weight = abs(factor_scores.get(stock_code, 0)) / total_score
                    else:
                        weight = 1.0 / len(selected_stocks)
                    
                    investment = total_investment * weight
                    
                    # 买卖价格 (优化版)
                    buy_price = await self._optimize_entry_price(data, factor_scores.get(stock_code, 0))
                    sell_price = await self._optimize_exit_price(data, factor_scores.get(stock_code, 0))
                    
                    shares = investment / buy_price
                    stock_value = shares * sell_price
                    profit = stock_value - investment
                    
                    total_value += stock_value
                    
                    stock_details.append({
                        "stock_code": stock_code,
                        "weight": weight,
                        "factor_score": factor_scores.get(stock_code, 0),
                        "buy_price": buy_price,
                        "sell_price": sell_price,
                        "shares": shares,
                        "investment": investment,
                        "final_value": stock_value,
                        "profit": profit,
                        "return_rate": (profit / investment) * 100
                    })
            
            total_profit = total_value - total_investment
            profit_rate = (total_profit / total_investment) * 100
            
            # 计算年化收益率
            days = 7  # 测试期间
            annualized_return = (pow(total_value / total_investment, 365 / days) - 1) * 100
            
            return {
                "algorithm": "本地化RD-Agent",
                "total_investment": total_investment,
                "final_value": total_value,
                "total_profit": total_profit,
                "profit_rate": profit_rate,
                "annualized_return": annualized_return,
                "stock_details": stock_details,
                "selected_stocks": selected_stocks
            }
            
        except Exception as e:
            logger.error(f"投资组合表现计算失败: {e}")
            return {"error": str(e)}
    
    async def _optimize_entry_price(self, data: pd.DataFrame, factor_score: float) -> float:
        """优化入场价格"""
        try:
            close = data['close']
            low = data.get('low', close)
            
            # 基于因子得分优化入场
            if factor_score > 0.1:
                entry_price = low.quantile(0.01) * 1.0002
            elif factor_score > 0.05:
                entry_price = low.quantile(0.03) * 1.0005
            else:
                entry_price = low.quantile(0.05) * 1.001
            
            return float(entry_price)
            
        except Exception as e:
            logger.error(f"入场价格优化失败: {e}")
            return float(data['close'].iloc[0])
    
    async def _optimize_exit_price(self, data: pd.DataFrame, factor_score: float) -> float:
        """优化出场价格"""
        try:
            close = data['close']
            high = data.get('high', close)
            
            # 基于因子得分优化出场
            if factor_score > 0.1:
                exit_price = high.quantile(0.99) * 0.9998
            elif factor_score > 0.05:
                exit_price = high.quantile(0.97) * 0.9995
            else:
                exit_price = high.quantile(0.95) * 0.999
            
            return float(exit_price)
            
        except Exception as e:
            logger.error(f"出场价格优化失败: {e}")
            return float(data['close'].iloc[-1])
    
    def get_experiment_history(self) -> List[Dict]:
        """获取实验历史"""
        return self.experiment_history
    
    def get_best_factors(self) -> List[Dict[str, Any]]:
        """获取最佳因子"""
        return self.best_factors
    
    def save_experiment_results(self, filepath: str):
        """保存实验结果"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.experiment_history, f, indent=2, ensure_ascii=False)
            logger.info(f"实验结果已保存: {filepath}")
        except Exception as e:
            logger.error(f"保存实验结果失败: {e}")

# 全局实例
localized_rd_loop_controller = LocalizedRDLoop()

__all__ = ['LocalizedRDLoop', 'localized_rd_loop_controller']
