#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实信号组件模块
提供真实的市场信号和技术指标计算
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class RealSignalComponents:
    """真实信号组件"""
    
    def __init__(self):
        self.component_name = "RealSignalComponents"
        self.version = "1.0.0"
        
    def calculate_ma_signals(self, prices: List[float], periods: List[int] = [5, 10, 20]) -> Dict[str, float]:
        """计算移动平均信号"""
        try:
            if len(prices) < max(periods):
                return {f"ma_{period}": 0.0 for period in periods}
            
            signals = {}
            for period in periods:
                ma_value = np.mean(prices[-period:])
                signals[f"ma_{period}"] = ma_value
                
            return signals
        except Exception as e:
            logger.error(f"移动平均计算失败: {e}")
            return {f"ma_{period}": 0.0 for period in periods}
    
    def calculate_rsi_signal(self, prices: List[float], period: int = 14) -> float:
        """计算RSI信号"""
        try:
            if len(prices) < period + 1:
                return 50.0
            
            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])
            
            if avg_loss == 0:
                return 100.0
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
        except Exception as e:
            logger.error(f"RSI计算失败: {e}")
            return 50.0
    
    def calculate_macd_signal(self, prices: List[float], fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, float]:
        """计算MACD信号"""
        try:
            if len(prices) < slow:
                return {"macd": 0.0, "signal": 0.0, "histogram": 0.0}
            
            # 计算EMA
            def ema(data, period):
                alpha = 2 / (period + 1)
                ema_values = [data[0]]
                for price in data[1:]:
                    ema_values.append(alpha * price + (1 - alpha) * ema_values[-1])
                return ema_values
            
            fast_ema = ema(prices, fast)
            slow_ema = ema(prices, slow)
            
            macd_line = fast_ema[-1] - slow_ema[-1]

            histogram = macd_line - signal_line
            
            return {
                "macd": macd_line,
                "signal": signal_line,
                "histogram": histogram
            }
        except Exception as e:
            logger.error(f"MACD计算失败: {e}")
            return {"macd": 0.0, "signal": 0.0, "histogram": 0.0}
    
    def calculate_bollinger_bands(self, prices: List[float], period: int = 20, std_dev: float = 2.0) -> Dict[str, float]:
        """计算布林带信号"""
        try:
            if len(prices) < period:
                current_price = prices[-1] if prices else 0.0
                return {
                    "upper_band": current_price * 1.02,
                    "middle_band": current_price,
                    "lower_band": current_price * 0.98,
                    "bandwidth": 0.04
                }
            
            recent_prices = prices[-period:]
            middle_band = np.mean(recent_prices)
            std = np.std(recent_prices)
            
            upper_band = middle_band + (std_dev * std)
            lower_band = middle_band - (std_dev * std)
            bandwidth = (upper_band - lower_band) / middle_band
            
            return {
                "upper_band": upper_band,
                "middle_band": middle_band,
                "lower_band": lower_band,
                "bandwidth": bandwidth
            }
        except Exception as e:
            logger.error(f"布林带计算失败: {e}")
            current_price = prices[-1] if prices else 0.0
            return {
                "upper_band": current_price * 1.02,
                "middle_band": current_price,
                "lower_band": current_price * 0.98,
                "bandwidth": 0.04
            }
    
    def calculate_volume_signals(self, volumes: List[float], prices: List[float]) -> Dict[str, float]:
        """计算成交量信号"""
        try:
            if len(volumes) < 5 or len(prices) < 5:
                return {
                    "volume_ratio": 1.0,
                    "price_volume_trend": 0.0,
                    "volume_ma_ratio": 1.0
                }
            
            # 成交量比率
            recent_volume = np.mean(volumes[-5:])
            historical_volume = np.mean(volumes[-20:-5]) if len(volumes) >= 20 else recent_volume
            volume_ratio = recent_volume / historical_volume if historical_volume > 0 else 1.0
            
            # 价量趋势
            price_changes = np.diff(prices[-10:])
            volume_changes = np.diff(volumes[-10:])
            
            if len(price_changes) > 0 and len(volume_changes) > 0:
                correlation = np.corrcoef(price_changes, volume_changes)[0, 1]
                price_volume_trend = correlation if not np.isnan(correlation) else 0.0
            else:
                price_volume_trend = 0.0
            
            # 成交量均线比率
            volume_ma = np.mean(volumes[-10:])
            current_volume = volumes[-1]
            volume_ma_ratio = current_volume / volume_ma if volume_ma > 0 else 1.0
            
            return {
                "volume_ratio": volume_ratio,
                "price_volume_trend": price_volume_trend,
                "volume_ma_ratio": volume_ma_ratio
            }
        except Exception as e:
            logger.error(f"成交量信号计算失败: {e}")
            return {
                "volume_ratio": 1.0,
                "price_volume_trend": 0.0,
                "volume_ma_ratio": 1.0
            }
    
    def calculate_momentum_signals(self, prices: List[float]) -> Dict[str, float]:
        """计算动量信号"""
        try:
            if len(prices) < 10:
                return {
                    "momentum_5": 0.0,
                    "momentum_10": 0.0,
                    "rate_of_change": 0.0
                }
            
            current_price = prices[-1]
            
            # 5日动量
            momentum_5 = (current_price - prices[-6]) / prices[-6] if len(prices) >= 6 else 0.0
            
            # 10日动量
            momentum_10 = (current_price - prices[-11]) / prices[-11] if len(prices) >= 11 else 0.0
            
            # 变化率
            rate_of_change = (current_price - prices[-5]) / prices[-5] if len(prices) >= 5 else 0.0
            
            return {
                "momentum_5": momentum_5,
                "momentum_10": momentum_10,
                "rate_of_change": rate_of_change
            }
        except Exception as e:
            logger.error(f"动量信号计算失败: {e}")
            return {
                "momentum_5": 0.0,
                "momentum_10": 0.0,
                "rate_of_change": 0.0
            }
    
    def calculate_comprehensive_signal(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """计算综合信号"""
        try:
            prices = market_data.get("prices", [])
            volumes = market_data.get("volumes", [])
            
            if not prices:
                return {"comprehensive_score": 0.5, "signal_strength": 0.0}
            
            # 计算各种信号
            ma_signals = self.calculate_ma_signals(prices)
            rsi = self.calculate_rsi_signal(prices)
            macd = self.calculate_macd_signal(prices)
            bollinger = self.calculate_bollinger_bands(prices)
            
            if volumes:
                volume_signals = self.calculate_volume_signals(volumes, prices)
            else:
                volume_signals = {"volume_ratio": 1.0, "price_volume_trend": 0.0}
            
            momentum = self.calculate_momentum_signals(prices)
            
            # 综合评分计算
            score_components = []
            
            # RSI评分 (0-1)
            if 30 <= rsi <= 70:
                rsi_score = 0.7
            elif rsi > 70:
                rsi_score = 0.3
            else:
                rsi_score = 0.8
            score_components.append(rsi_score * 0.2)
            
            # MACD评分
            macd_score = 0.6 if macd["histogram"] > 0 else 0.4
            score_components.append(macd_score * 0.2)
            
            # 成交量评分
            volume_score = min(1.0, volume_signals["volume_ratio"] / 2.0)
            score_components.append(volume_score * 0.2)
            
            # 动量评分
            momentum_score = 0.5 + momentum["momentum_5"] * 2
            momentum_score = max(0.0, min(1.0, momentum_score))
            score_components.append(momentum_score * 0.2)
            
            # 趋势评分
            current_price = prices[-1]
            ma_20 = ma_signals.get("ma_20", current_price)
            trend_score = 0.7 if current_price > ma_20 else 0.3
            score_components.append(trend_score * 0.2)
            
            comprehensive_score = sum(score_components)
            signal_strength = abs(comprehensive_score - 0.5) * 2
            
            return {
                "comprehensive_score": comprehensive_score,
                "signal_strength": signal_strength,
                "rsi": rsi,
                "macd_histogram": macd["histogram"],
                "volume_ratio": volume_signals["volume_ratio"],
                "momentum_5": momentum["momentum_5"]
            }
            
        except Exception as e:
            logger.error(f"综合信号计算失败: {e}")
            return {"comprehensive_score": 0.5, "signal_strength": 0.0}

# 创建全局实例
real_signal_components = RealSignalComponents()

__all__ = ["RealSignalComponents", "real_signal_components"]
