from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户服务系统 - 开阳星-股票经理
实现智能客户服务、满意度管理、服务质量监控、客户关系维护
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pydantic import BaseModel, Field
from enum import Enum
import sqlite3
import os
from pathlib import Path
import json
import uuid

logger = logging.getLogger(__name__)

class ServiceType(Enum):
    """服务类型"""
    CONSULTATION = "consultation"           # 咨询服务
    ANALYSIS_REQUEST = "analysis_request"   # 分析请求
    COMPLAINT = "complaint"                 # 投诉处理
    FEEDBACK = "feedback"                   # 反馈收集
    FOLLOW_UP = "follow_up"                 # 跟进服务

class ServiceStatus(Enum):
    """服务状态"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ESCALATED = "escalated"
    CLOSED = "closed"

class SatisfactionLevel(Enum):
    """满意度等级"""
    VERY_SATISFIED = "very_satisfied"       # 非常满意
    SATISFIED = "satisfied"                 # 满意
    NEUTRAL = "neutral"                     # 一般
    DISSATISFIED = "dissatisfied"           # 不满意
    VERY_DISSATISFIED = "very_dissatisfied" # 非常不满意

class ClientServiceRecord(BaseModel):
    """客户服务记录"""
    service_id: str = Field(..., description="服务ID")
    client_id: str = Field(..., description="客户ID")
    service_type: ServiceType = Field(..., description="服务类型")
    
    # 服务内容
    request_content: str = Field(..., description="请求内容")
    response_content: str = Field("", description="响应内容")
    
    # 状态管理
    status: ServiceStatus = Field(ServiceStatus.PENDING, description="服务状态")
    priority: int = Field(3, description="优先级", ge=1, le=5)
    
    # 质量指标
    response_time: float = Field(0.0, description="响应时间(秒)")
    resolution_time: float = Field(0.0, description="解决时间(秒)")
    satisfaction_score: Optional[float] = Field(None, description="满意度评分", ge=1.0, le=5.0)
    satisfaction_level: Optional[SatisfactionLevel] = Field(None, description="满意度等级")
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    responded_at: Optional[datetime] = Field(None, description="响应时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    # 服务质量
    service_quality: float = Field(0.0, description="服务质量评分")
    follow_up_required: bool = Field(False, description="是否需要跟进")
    
    class Config:
        arbitrary_types_allowed = True

class ClientProfile(BaseModel):
    """客户档案"""
    client_id: str = Field(..., description="客户ID")
    client_name: str = Field(..., description="客户姓名")
    
    # 客户特征
    risk_preference: str = Field("moderate", description="风险偏好")
    investment_experience: str = Field("intermediate", description="投资经验")
    preferred_communication: str = Field("text", description="偏好沟通方式")
    
    # 服务历史
    total_services: int = Field(0, description="总服务次数")
    avg_satisfaction: float = Field(0.0, description="平均满意度")
    last_service_date: Optional[datetime] = Field(None, description="最后服务时间")
    
    # 客户价值
    client_value_score: float = Field(0.0, description="客户价值评分")
    loyalty_level: str = Field("new", description="忠诚度等级")
    
    class Config:
        arbitrary_types_allowed = True

class ClientServiceSystem:
    """客户服务系统"""
    
    def __init__(self):
        self.active_services = {}
        self.client_profiles = {}
        self.service_metrics = {
            "total_services": 0,
            "avg_response_time": 0.0,
            "avg_satisfaction": 0.0,
            "resolution_rate": 0.0
        }
        
        # 初始化数据库
        self.db_path = "backend/data/yaoguang_distribution.db"
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self._init_database()
        
        logger.info("客户服务系统初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建服务记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS service_records (
                service_id TEXT PRIMARY KEY,
                client_id TEXT,
                service_type TEXT,
                request_content TEXT,
                response_content TEXT,
                status TEXT,
                priority INTEGER,
                response_time REAL,
                resolution_time REAL,
                satisfaction_score REAL,
                satisfaction_level TEXT,
                service_quality REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建客户档案表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_profiles (
                client_id TEXT PRIMARY KEY,
                client_name TEXT,
                risk_preference TEXT,
                investment_experience TEXT,
                preferred_communication TEXT,
                total_services INTEGER,
                avg_satisfaction REAL,
                client_value_score REAL,
                loyalty_level TEXT,
                last_service_date TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建满意度调查表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS satisfaction_surveys (
                survey_id TEXT PRIMARY KEY,
                service_id TEXT,
                client_id TEXT,
                satisfaction_score REAL,
                satisfaction_level TEXT,
                feedback_text TEXT,
                improvement_suggestions TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    async def handle_client_request(
        self,
        client_id: str,
        request_content: str,
        service_type: ServiceType = ServiceType.CONSULTATION,
        priority: int = 3
    ) -> Dict[str, Any]:
        """处理客户请求"""
        
        try:
            logger.info(f"处理客户请求: {client_id}")
            
            # 创建服务记录
            service_record = ClientServiceRecord(
                service_id=str(uuid.uuid4()),
                client_id=client_id,
                service_type=service_type,
                request_content=request_content,
                priority=priority
            )
            
            # 更新客户档案
            await self._update_client_profile(client_id)
            
            # 智能响应生成
            response_result = await self._generate_intelligent_response(service_record)
            
            # 更新服务记录
            service_record.response_content = response_result["response_content"]
            service_record.status = ServiceStatus.COMPLETED
            service_record.responded_at = datetime.now()
            service_record.completed_at = datetime.now()
            service_record.response_time = response_result["response_time"]
            service_record.resolution_time = response_result["response_time"]
            service_record.service_quality = response_result["quality_score"]
            
            # 保存服务记录
            await self._save_service_record(service_record)
            
            # 预测满意度
            satisfaction_prediction = await self._predict_client_satisfaction(service_record)
            
            # 更新服务指标
            await self._update_service_metrics()
            
            result = {
                "service_id": service_record.service_id,
                "response_content": service_record.response_content,
                "service_quality": service_record.service_quality,
                "response_time": service_record.response_time,
                "satisfaction_prediction": satisfaction_prediction,
                "follow_up_required": service_record.follow_up_required,
                "status": service_record.status.value
            }
            
            logger.info(f"客户请求处理完成: {service_record.service_id}")
            return result
            
        except Exception as e:
            logger.error(f"客户请求处理失败: {e}")
            raise
    
    async def _generate_intelligent_response(self, service_record: ClientServiceRecord) -> Dict[str, Any]:
        """生成智能响应"""
        
        start_time = datetime.now()
        
        # 获取客户档案
        client_profile = await self._get_client_profile(service_record.client_id)
        
        # 基于服务类型生成响应
        if service_record.service_type == ServiceType.CONSULTATION:
            response = await self._generate_consultation_response(service_record, client_profile)
        elif service_record.service_type == ServiceType.ANALYSIS_REQUEST:
            response = await self._generate_analysis_response(service_record, client_profile)
        elif service_record.service_type == ServiceType.COMPLAINT:
            response = await self._generate_complaint_response(service_record, client_profile)
        elif service_record.service_type == ServiceType.FEEDBACK:
            response = await self._generate_feedback_response(service_record, client_profile)
        else:
            response = await self._generate_generic_response(service_record, client_profile)
        
        # 计算响应时间
        response_time = max(0.001, (datetime.now() - start_time).total_seconds())
        
        # 评估响应质量
        quality_score = await self._evaluate_response_quality(response, service_record)
        
        return {
            "response_content": response,
            "response_time": response_time,
            "quality_score": quality_score
        }
    
    async def _generate_consultation_response(
        self,
        service_record: ClientServiceRecord,
        client_profile: Optional[ClientProfile]
    ) -> str:
        """生成咨询响应"""
        
        request = service_record.request_content.lower()
        
        # 个性化响应
        if client_profile:
            greeting = f"尊敬的{client_profile.client_name}，"
            risk_context = f"基于您{client_profile.risk_preference}的风险偏好，"
        else:
            greeting = "尊敬的客户，"
            risk_context = ""
        
        # 智能内容匹配
        if any(keyword in request for keyword in ["市场", "行情", "大盘"]):
            response = f"""
{greeting}感谢您的咨询。

关于当前市场情况，我们的AI分析团队持续监控市场动态：

📈 **市场概况**
- 当前市场整体呈现稳中有升的态势
- 主要指数表现相对稳健
- 成交量维持在合理水平

💡 **我们的观点**
{risk_context}我们建议关注以下几个方面：
1. 关注政策面的变化和影响
2. 重点关注业绩确定性较高的优质公司
3. 适当分散投资，控制风险

如需了解具体个股分析，请告诉我们股票代码，我们将为您提供详细的分析报告。
            """.strip()
            
        elif any(keyword in request for keyword in ["推荐", "买什么", "投资建议"]):
            response = f"""
{greeting}关于投资建议，我们的AI团队会基于多维度分析为您提供专业建议。

  **我们的投资方法**
1. **情报收集**: 全面收集市场信息和公司基本面数据
2. **策略分析**: 运用量化模型进行技术和基本面分析
3. **风险评估**: 严格的风险控制和合规检查
4. **决策整合**: 综合各方面因素制定投资建议

{risk_context}我们当前重点关注的方向包括：
- 新能源和清洁技术
- 人工智能和科技创新
- 消费升级相关行业
- 具有稳定分红的价值股

请告诉我们您的具体需求，我们将为您提供个性化的投资建议。
            """.strip()
            
        else:
            response = f"""
{greeting}感谢您的咨询。

我们是专业的AI投资顾问团队，可以为您提供：

 **专业服务**
- 个股深度分析报告
- 市场行情解读
- 投资策略建议
- 风险评估和管理
- 投资组合优化

  **AI团队优势**
- 7×24小时市场监控
- 多维度数据分析
- 客观理性的投资建议
- 严格的风险控制

请告诉我们您的具体问题，我们将竭诚为您服务。
            """.strip()
        
        return response
    
    async def _generate_analysis_response(
        self,
        service_record: ClientServiceRecord,
        client_profile: Optional[ClientProfile]
    ) -> str:
        """生成分析请求响应"""
        
        response = f"""
感谢您的分析请求，我们已经收到您的需求。

  **分析流程**
我们的AI团队正在为您进行多维度分析：
1. 情报官正在收集最新市场数据和新闻
2. 策略架构师正在进行技术和基本面分析
3. 风控总监正在评估投资风险
4. 决策指挥官正在整合分析结果

 **预计完成时间**: 15-30分钟

 **分析内容将包括**:
- 技术面分析评分
- 基本面分析评分
- 风险等级评估
- 投资建议和目标价格
- "我们会怎么做"的具体方案

我们将在分析完成后第一时间为您提供详细报告。
        """.strip()
        
        return response
    
    async def _generate_complaint_response(
        self,
        service_record: ClientServiceRecord,
        client_profile: Optional[ClientProfile]
    ) -> str:
        """生成投诉处理响应"""
        
        response = f"""
非常抱歉给您带来了不便，我们高度重视您的反馈。

🙏 **诚挚道歉**
我们对服务中的不足深表歉意，您的意见对我们改进服务质量非常重要。

🔍 **处理流程**
1. 我们已经记录了您的投诉内容
2. 相关负责人将在24小时内联系您
3. 我们会深入调查问题原因
4. 制定改进措施并及时反馈

📞 **联系方式**
如需紧急处理，请通过以下方式联系我们：
- 在线客服：随时为您服务
- 投诉热线：400-XXX-XXXX

我们承诺会认真对待每一个客户的意见，持续改进我们的服务质量。
        """.strip()
        
        return response
    
    async def _generate_feedback_response(
        self,
        service_record: ClientServiceRecord,
        client_profile: Optional[ClientProfile]
    ) -> str:
        """生成反馈收集响应"""
        
        response = f"""
感谢您宝贵的反馈意见！

🌟 **感谢您的参与**
您的反馈是我们持续改进服务的重要动力。

📝 **反馈处理**
- 我们已经记录了您的建议
- 产品团队将认真评估您的意见
- 有价值的建议将纳入我们的改进计划

🎁 **感谢回馈**
作为对您参与的感谢，我们将为您提供：
- 优先享受新功能体验
- 专属客户服务支持
- 定期投资策略分享

我们会持续优化服务，为您提供更好的投资体验。
        """.strip()
        
        return response
    
    async def _generate_generic_response(
        self,
        service_record: ClientServiceRecord,
        client_profile: Optional[ClientProfile]
    ) -> str:
        """生成通用响应"""
        
        response = f"""
感谢您联系我们的AI投资顾问团队。

  **我们的服务**
- 专业的投资分析和建议
- 实时市场监控和解读
- 个性化投资策略制定
- 全面的风险管理服务

📞 **如何获得帮助**
请告诉我们您的具体需求：
- 个股分析：提供股票代码
- 市场咨询：描述您关心的问题
- 投资建议：告诉我们您的投资目标

我们将竭诚为您提供专业的投资服务。
        """.strip()
        
        return response
    
    async def _evaluate_response_quality(
        self,
        response: str,
        service_record: ClientServiceRecord
    ) -> float:
        """评估响应质量"""
        
        quality_factors = []
        
        # 响应长度评分
        response_length = len(response)
        if response_length > 200:
            quality_factors.append(0.9)
        elif response_length > 100:
            quality_factors.append(0.8)
        else:
            quality_factors.append(0.6)
        
        # 个性化程度评分
        if "尊敬的" in response:
            quality_factors.append(0.9)
        else:
            quality_factors.append(0.7)
        
        # 专业性评分
        professional_keywords = ["分析", "建议", "风险", "投资", "策略"]
        professional_score = sum(1 for keyword in professional_keywords if keyword in response) / len(professional_keywords)
        quality_factors.append(professional_score)
        
        # 结构化程度评分
        if "**" in response and "📈" in response:
            quality_factors.append(0.9)
        else:
            quality_factors.append(0.7)
        
        # 综合质量评分
        overall_quality = np.mean(quality_factors)
        
        return min(1.0, max(0.1, overall_quality))
    
    async def _get_client_profile(self, client_id: str) -> Optional[ClientProfile]:
        """获取客户档案"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM client_profiles WHERE client_id = ?
            ''', (client_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                columns = [
                    "client_id", "client_name", "risk_preference", "investment_experience",
                    "preferred_communication", "total_services", "avg_satisfaction",
                    "client_value_score", "loyalty_level", "last_service_date", "created_at"
                ]
                
                profile_data = dict(zip(columns, row))
                return ClientProfile(**profile_data)
            
            return None
            
        except Exception as e:
            logger.error(f"获取客户档案失败: {e}")
            return None
    
    async def _update_client_profile(self, client_id: str):
        """更新客户档案"""
        
        try:
            # 如果客户不存在，创建新档案
            existing_profile = await self._get_client_profile(client_id)
            
            if not existing_profile:
                new_profile = ClientProfile(
                    client_id=client_id,
                    client_name=f"客户{client_id[-4:]}",
                    total_services=1,
                    last_service_date=datetime.now()
                )
                
                await self._save_client_profile(new_profile)
            else:
                # 更新现有档案
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE client_profiles 
                    SET total_services = total_services + 1,
                        last_service_date = ?
                    WHERE client_id = ?
                ''', (datetime.now().isoformat(), client_id))
                
                conn.commit()
                conn.close()
                
        except Exception as e:
            logger.error(f"更新客户档案失败: {e}")
    
    async def _save_client_profile(self, profile: ClientProfile):
        """保存客户档案"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO client_profiles 
                (client_id, client_name, risk_preference, investment_experience,
                 preferred_communication, total_services, avg_satisfaction,
                 client_value_score, loyalty_level, last_service_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                profile.client_id,
                profile.client_name,
                profile.risk_preference,
                profile.investment_experience,
                profile.preferred_communication,
                profile.total_services,
                profile.avg_satisfaction,
                profile.client_value_score,
                profile.loyalty_level,
                profile.last_service_date.isoformat() if profile.last_service_date else None
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"保存客户档案失败: {e}")
    
    async def _save_service_record(self, record: ClientServiceRecord):
        """保存服务记录"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO service_records 
                (service_id, client_id, service_type, request_content, response_content,
                 status, priority, response_time, resolution_time, satisfaction_score,
                 satisfaction_level, service_quality)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record.service_id,
                record.client_id,
                record.service_type.value,
                record.request_content,
                record.response_content,
                record.status.value,
                record.priority,
                record.response_time,
                record.resolution_time,
                record.satisfaction_score,
                record.satisfaction_level.value if record.satisfaction_level else None,
                record.service_quality
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"保存服务记录失败: {e}")
    
    async def _predict_client_satisfaction(self, record: ClientServiceRecord) -> Dict[str, Any]:
        """预测客户满意度"""
        
        # 基于服务质量和响应时间预测满意度
        quality_score = record.service_quality
        response_time = record.response_time
        
        # 响应时间评分 (越快越好)
        time_score = max(0.1, 1.0 - response_time / 10.0)  # 10秒内满分
        
        # 综合满意度预测
        predicted_satisfaction = (quality_score * 0.7 + time_score * 0.3) * 5.0  # 转换为5分制
        
        # 满意度等级
        if predicted_satisfaction >= 4.5:
            satisfaction_level = SatisfactionLevel.VERY_SATISFIED
        elif predicted_satisfaction >= 3.5:
            satisfaction_level = SatisfactionLevel.SATISFIED
        elif predicted_satisfaction >= 2.5:
            satisfaction_level = SatisfactionLevel.NEUTRAL
        elif predicted_satisfaction >= 1.5:
            satisfaction_level = SatisfactionLevel.DISSATISFIED
        else:
            satisfaction_level = SatisfactionLevel.VERY_DISSATISFIED
        
        return {
            "predicted_score": predicted_satisfaction,
            "predicted_level": satisfaction_level.value,
            "confidence": 0.8,
            "factors": {
                "service_quality": quality_score,
                "response_time_score": time_score
            }
        }
    
    async def _update_service_metrics(self):
        """更新服务指标"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 计算总服务数
            cursor.execute("SELECT COUNT(*) FROM service_records")
            total_services = cursor.fetchone()[0]
            
            # 计算平均响应时间
            cursor.execute("SELECT AVG(response_time) FROM service_records WHERE response_time > 0")
            avg_response_time = cursor.fetchone()[0] or 0.0
            
            # 计算平均满意度
            cursor.execute("SELECT AVG(satisfaction_score) FROM service_records WHERE satisfaction_score IS NOT NULL")
            avg_satisfaction = cursor.fetchone()[0] or 0.0
            
            # 计算解决率
            cursor.execute("SELECT COUNT(*) FROM service_records WHERE status = 'completed'")
            completed_services = cursor.fetchone()[0]
            resolution_rate = completed_services / total_services if total_services > 0 else 0.0
            
            conn.close()
            
            # 更新指标
            self.service_metrics.update({
                "total_services": total_services,
                "avg_response_time": avg_response_time,
                "avg_satisfaction": avg_satisfaction,
                "resolution_rate": resolution_rate
            })
            
        except Exception as e:
            logger.error(f"更新服务指标失败: {e}")
    
    async def get_service_metrics(self) -> Dict[str, Any]:
        """获取服务指标"""
        
        await self._update_service_metrics()
        
        return {
            "service_metrics": self.service_metrics,
            "performance_rating": self._calculate_performance_rating(),
            "improvement_suggestions": self._generate_improvement_suggestions()
        }
    
    def _calculate_performance_rating(self) -> str:
        """计算性能评级"""
        
        metrics = self.service_metrics
        
        # 综合评分
        response_score = max(0, 1 - metrics["avg_response_time"] / 5.0)  # 5秒内满分
        satisfaction_score = metrics["avg_satisfaction"] / 5.0
        resolution_score = metrics["resolution_rate"]
        
        overall_score = (response_score + satisfaction_score + resolution_score) / 3
        
        if overall_score >= 0.9:
            return "优秀"
        elif overall_score >= 0.8:
            return "良好"
        elif overall_score >= 0.7:
            return "一般"
        else:
            return "需改进"
    
    def _generate_improvement_suggestions(self) -> List[str]:
        """生成改进建议"""
        
        suggestions = []
        metrics = self.service_metrics
        
        if metrics["avg_response_time"] > 3.0:
            suggestions.append("优化响应速度，目标控制在3秒内")
        
        if metrics["avg_satisfaction"] < 4.0:
            suggestions.append("提升服务质量，增强客户满意度")
        
        if metrics["resolution_rate"] < 0.9:
            suggestions.append("提高问题解决率，加强服务流程")
        
        if not suggestions:
            suggestions.append("继续保持优质服务水平")
        
        return suggestions
