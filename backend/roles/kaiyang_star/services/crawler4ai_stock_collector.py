#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Crawler4AI的高效股票数据采集系统
充分利用Crawler4AI的并发能力和反爬虫特性
"""

import asyncio
import logging
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
import json
import aiohttp
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# 尝试导入crawler4ai
try:
                from crawl4ai import AsyncWebCrawler, BrowserConfig
                CRAWLER4AI_AVAILABLE = True
except ImportError:
                CRAWLER4AI_AVAILABLE = False

@dataclass
class StockDataPoint:
                """股票数据点"""
                stock_code: str
                trade_date: str
                open_price: float
                high_price: float
                low_price: float
                close_price: float
                volume: int
                amount: float = 0.0
                change_percent: float = 0.0

class A股交易日历:
                """A股交易日历 - 排除节假日和周末"""

                def __init__(self):
                                # 2024-2025年节假日（需要定期更新）
                                self.holidays = {
                                                # 2024年节假日
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), 
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 春节
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 清明节
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 劳动节
                                                datetime.now().strftime("%Y-%m-%d"),  # 端午节
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 中秋节
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), 
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 国庆节

                                                # 2025年节假日
                                                datetime.now().strftime("%Y-%m-%d"),  # 元旦
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 春节
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 清明节
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 劳动节
                                                datetime.now().strftime("%Y-%m-%d"),  # 端午节
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),
                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 国庆节
                                }

                def is_trading_day(self, date_str: str) -> bool:
                                """判断是否为交易日"""
                                try:
                                                date_obj = datetime.strptime(date_str, '%Y-%m-%d')

                                                # 周末不是交易日
                                                if date_obj.weekday() >= 5:  # 5=周六, 6=周日
                                                                return False

                                                # 节假日不是交易日
                                                if date_str in self.holidays:
                                                                return False

                                                return True
                                except:
                                                return False

                def get_trading_days(self, start_date: str, end_date: str) -> List[str]:
                                """获取指定时间范围内的所有交易日"""
                                trading_days = []

                                start = datetime.strptime(start_date, '%Y-%m-%d')
                                end = datetime.strptime(end_date, '%Y-%m-%d')

                                current = start
                                while current <= end:
                                                date_str = current.strftime('%Y-%m-%d')
                                                if self.is_trading_day(date_str):
                                                                trading_days.append(date_str)
                                                current += timedelta(days=1)

                                return trading_days

class StockListManager:
                """股票列表管理器 - 动态获取A股股票列表"""

                def __init__(self):
                                self.stock_cache = {}
                                self.cache_expiry = {}

                async def get_all_a_stocks(self) -> List[str]:
                                """获取所有A股股票代码"""
                                cache_key = "all_a_stocks"

                                # 检查缓存
                                if (cache_key in self.stock_cache and 
                                                cache_key in self.cache_expiry and
                                                datetime.now() < self.cache_expiry[cache_key]):
                                                return self.stock_cache[cache_key]

                                # 从多个源获取股票列表
                                stock_codes = set()

                                # 方法1: 从东方财富获取
                                try:
                                                eastmoney_stocks = await self._get_stocks_from_eastmoney()
                                                stock_codes.update(eastmoney_stocks)
                                except Exception as e:
                                                logger.warning(f"从东方财富获取股票列表失败: {e}")

                                # 方法2: 从新浪财经获取
                                try:
                                                sina_stocks = await self._get_stocks_from_sina()
                                                stock_codes.update(sina_stocks)
                                except Exception as e:
                                                logger.warning(f"从新浪财经获取股票列表失败: {e}")

                                # 方法3: 使用预定义列表作为备选
                                if not stock_codes:
                                                stock_codes = self._get_predefined_stocks()

                                # 过滤和排序
                                filtered_stocks = self._filter_valid_stocks(list(stock_codes))

                                # 缓存结果（24小时）
                                self.stock_cache[cache_key] = filtered_stocks
                                self.cache_expiry[cache_key] = datetime.now() + timedelta(hours=24)

                                logger.info(f"获取A股股票列表: {len(filtered_stocks)}只")
                                return filtered_stocks

                async def _get_stocks_from_eastmoney(self) -> List[str]:
                                """从东方财富获取股票列表"""
                                url = "http://80.push2.eastmoney.com/api/qt/clist/get"
                                params = {
                                                "pn": "1",
                                                "pz": "5000",
                                                "po": "1",
                                                "np": "1",
                                                "ut": "bd1d9ddb04089700cf9c27f6f7426281",
                                                "fltt": "2",
                                                "invt": "2",
                                                "fid": "f3",
                                                "fs": "m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23",
                                                "fields": "f12"
                                }

                                async with aiohttp.ClientSession() as session:
                                                async with session.get(url, params=params) as response:
                                                                if response.status == 200:
                                                                                data = await response.json()
                                                                                stocks = []
                                                                                if data.get("data") and data["data"].get("diff"):
                                                                                                for item in data["data"]["diff"]:
                                                                                                                if item.get("f12"):
                                                                                                                                stocks.append(item["f12"])
                                                                                return stocks
                                return []

                async def _get_stocks_from_sina(self) -> List[str]:
                                """从新浪财经获取股票列表"""
                                # 这里可以实现从新浪获取股票列表的逻辑
                                # 返回真实数据