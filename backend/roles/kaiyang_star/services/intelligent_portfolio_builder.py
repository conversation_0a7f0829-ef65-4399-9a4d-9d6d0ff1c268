from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能投资组合构建服务 - 开阳星
基于现代投资组合理论和AI算法构建最优投资组合
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import random
import math

logger = logging.getLogger(__name__)

class IntelligentPortfolioBuilder:
    """智能投资组合构建服务"""

    def __init__(self):
        self.service_name = "IntelligentPortfolioBuilder"
        self.version = "1.0.0"
        self.screening_service = None
        self.risk_service = None

        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")

    async def _get_screening_service(self):
        """获取股票筛选服务"""
        if self.screening_service is None:
            try:
                from backend.roles.kaiyang_star.services.enhanced_stock_screening_service import enhanced_stock_screening_service
                self.screening_service = enhanced_stock_screening_service
            except Exception as e:
                logger.warning(f"筛选服务不可用: {e}")
        return self.screening_service

    async def _get_risk_service(self):
        """获取风险服务"""
        if self.risk_service is None:
            try:
                from backend.services.risk.tianji_risk_integration import tianji_risk_integration_service
                self.risk_service = tianji_risk_integration_service
            except Exception as e:
                logger.warning(f"风险服务不可用: {e}")
        return self.risk_service

    async def build_portfolio(self, portfolio_config: Dict[str, Any]) -> Dict[str, Any]:
        """构建智能投资组合"""
        try:
            start_time = datetime.now()
            logger.info(f"🏗️ 开始构建智能投资组合，配置: {portfolio_config}")

            # 1. 解析组合配置
            config = self._parse_portfolio_config(portfolio_config)

            # 2. 获取候选股票池
            candidate_stocks = await self._get_candidate_stocks(config)
            if not candidate_stocks:
                return self._get_empty_portfolio_result("无可用候选股票")

            # 3. 风险评估和筛选
            risk_assessed_stocks = await self._assess_portfolio_risks(candidate_stocks, config)

            # 4. 行业分散化
            diversified_stocks = await self._apply_sector_diversification(risk_assessed_stocks, config)

            # 5. 权重优化
            optimized_portfolio = await self._optimize_portfolio_weights(diversified_stocks, config)

            # 6. 组合验证和调整
            final_portfolio = await self._validate_and_adjust_portfolio(optimized_portfolio, config)

            # 7. 生成组合报告
            portfolio_report = await self._generate_portfolio_report(final_portfolio, config)

            # 计算耗时
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            return {
                "success": True,
                "portfolio": final_portfolio,
                "report": portfolio_report,
                "config": config,
                "build_time": duration,
                "timestamp": end_time.isoformat()
            }

        except Exception as e:
            logger.error(f"❌ 智能投资组合构建失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "portfolio": {},
                "report": {}
            }

    def _parse_portfolio_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """解析投资组合配置"""
        return {
            "total_amount": config.get("total_amount", 1000000),  # 总投资金额
            "max_stocks": config.get("max_stocks", 20),  # 最大持股数量
            "min_stocks": config.get("min_stocks", 5),   # 最小持股数量
            "max_single_weight": config.get("max_single_weight", 0.15),  # 单股最大权重
            "min_single_weight": config.get("min_single_weight", 0.02),  # 单股最小权重
            "risk_tolerance": config.get("risk_tolerance", "medium"),  # 风险承受能力
            "investment_style": config.get("investment_style", "balanced"),  # 投资风格
            "sector_limits": config.get("sector_limits", {}),  # 行业限制
            "exclude_stocks": config.get("exclude_stocks", []),  # 排除股票
            "rebalance_frequency": config.get("rebalance_frequency", "monthly"),  # 再平衡频率
            "target_return": config.get("target_return", 0.15),  # 目标收益率
            "max_volatility": config.get("max_volatility", 0.20)  # 最大波动率
        }

    async def _get_candidate_stocks(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取候选股票池"""
        try:
            # 优先从数据库获取有真实数据的股票
            db_candidates = await self._get_qualified_stocks_from_db(config)

            if len(db_candidates) >= config["min_stocks"]:
                logger.info(f"📊 从数据库获得足够的候选股票: {len(db_candidates)} 只")
                return db_candidates[:config["max_stocks"] * 2]  # 限制候选数量

            # 如果数据库候选股票不足，尝试使用筛选服务
            logger.info(f"🔍 数据库候选股票不足({len(db_candidates)}只)，尝试使用筛选服务...")
            screening_service = await self._get_screening_service()
            if screening_service:
                # 根据投资风格设置筛选条件
                screening_criteria = self._get_screening_criteria_by_style(config)
                # 放宽筛选条件
                screening_criteria["enable_technical_screening"] = False
                screening_criteria["enable_risk_screening"] = False

                # 执行股票筛选
                screening_result = await screening_service.intelligent_screening(screening_criteria)

                if screening_result.get("success"):
                    screening_candidates = screening_result["data"]["qualified_stocks"]
                    # 合并候选股票
                    all_candidates = db_candidates + screening_candidates
                    # 去重
                    seen_codes = set()
                    unique_candidates = []
                    for stock in all_candidates:
                        code = stock.get("code") or stock.get("stock_code")
                        if code and code not in seen_codes:
                            seen_codes.add(code)
                            unique_candidates.append(stock)

                    logger.info(f"📊 合并后获得 {len(unique_candidates)} 只候选股票")
                    return unique_candidates

            # 最后使用备用候选股票
            logger.warning("⚠️ 所有方法失败，使用备用候选股票")

        except Exception as e:
            logger.error(f"获取候选股票失败: {e}")

    async def _get_qualified_stocks_from_db(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从数据库获取有真实数据的合格股票"""
        try:
            import sqlite3
            conn = sqlite3.connect(get_database_path("stock_database"))
            cursor = conn.cursor()

            # 获取有东方财富真实数据的股票
            cursor.execute("""
                SELECT DISTINCT s.stock_code, s.stock_name, s.exchange, s.industry,
                       d.close_price, d.volume, d.change_percent
                FROM stock_info s
                JOIN daily_data d ON s.stock_code = d.stock_code
                WHERE d.data_source LIKE 'eastmoney%'
                  AND d.close_price > 0
                  AND d.volume > 1000000
                GROUP BY s.stock_code
                HAVING COUNT(d.trade_date) >= 5
                ORDER BY d.close_price * d.volume DESC
                LIMIT 100
            """)

            rows = cursor.fetchall()
            conn.close()

            candidates = []
            for row in rows:
                stock_code, stock_name, exchange, industry, close_price, volume, change_percent = row

                # 估算基本指标

                pe_ratio = 15 + (int(stock_code[-3:]) % 20)  # 估算PE
                pb_ratio = 1.0 + (int(stock_code[-2:]) % 10) * 0.5  # 估算PB

                candidate = {
                    "code": stock_code,
                    "stock_code": stock_code,
                    "name": stock_name,
                    "stock_name": stock_name,
                    "exchange": exchange,
                    "industry": industry or "其他",
                    "current_price": close_price,
                    "volume": volume,
                    "change_percent": change_percent or 0.0,
                    "market_cap": market_cap,
                    "pe_ratio": pe_ratio,
                    "pb_ratio": pb_ratio,
                    "comprehensive_score": min(95, 60 + abs(change_percent or 0) * 2),
                    "recommendation": "推荐",
                    "data_source": "database_real"
                }

                candidates.append(candidate)

            logger.info(f"📊 从数据库获取到 {len(candidates)} 只有真实数据的股票")
            return candidates

        except Exception as e:
            logger.error(f"从数据库获取候选股票失败: {e}")
            return []

    def _get_screening_criteria_by_style(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """根据投资风格获取筛选条件"""
        style = config["investment_style"]

        if style == "growth":
            # 成长型投资
            return {
                "pe_ratio_max": 50,
                "market_cap_min": 1000000000,
                "change_percent_min": -5,
                "enable_technical_screening": True,
                "above_ma20": True,
                "rsi_min": 30,
                "rsi_max": 80
            }
        elif style == "value":
            # 价值型投资
            return {
                "pe_ratio_max": 20,
                "pb_ratio_max": 3,
                "market_cap_min": 5000000000,
                "volume_min": 10000000,
                "enable_risk_screening": True,
                "max_risk_level": "中等风险"
            }
        elif style == "dividend":
            # 股息型投资
            return {
                "pe_ratio_max": 25,
                "pb_ratio_max": 4,
                "market_cap_min": 10000000000,
                "industries": ["银行", "公用事业", "房地产"],
                "enable_risk_screening": True,
                "max_risk_score": 60
            }
        else:
            # 平衡型投资
            return {
                "pe_ratio_max": 30,
                "pb_ratio_max": 5,
                "market_cap_min": 1000000000,
                "volume_min": 5000000,
                "enable_technical_screening": True,
                "enable_risk_screening": True,
                "max_risk_level": "中高风险"
            }

    async def _assess_portfolio_risks(self, stocks: List[Dict[str, Any]], config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """评估投资组合风险"""
        try:
            risk_service = await self._get_risk_service()

            risk_assessed_stocks = []
            for stock in stocks:
                try:
                    # 获取个股风险评估
                    if risk_service:
                        risk_analysis = await risk_service.get_stock_risk_analysis(stock["code"])
                        stock["portfolio_risk"] = self._calculate_portfolio_risk_score(risk_analysis)
                    else:
                        stock["portfolio_risk"] = 50.0  # 默认中性风险

                    # 根据风险承受能力筛选
                    if self._meets_risk_tolerance(stock, config):
                        risk_assessed_stocks.append(stock)

                except Exception as e:
                    logger.debug(f"风险评估失败 {stock['code']}: {e}")
                    continue

            logger.info(f"🛡️ 风险评估完成: {len(risk_assessed_stocks)}/{len(stocks)} 只股票通过")
            return risk_assessed_stocks

        except Exception as e:
            logger.error(f"投资组合风险评估失败: {e}")
            return stocks  # 返回原始股票列表

    async def _apply_sector_diversification(self, stocks: List[Dict[str, Any]], config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用行业分散化"""
        try:
            # 按行业分组
            sector_groups = {}
            for stock in stocks:
                sector = stock.get("industry", "其他")
                if sector not in sector_groups:
                    sector_groups[sector] = []
                sector_groups[sector].append(stock)

            # 行业分散化选择
            diversified_stocks = []
            max_stocks = config["max_stocks"]

            # 计算每个行业的最大股票数
            sector_limits = config.get("sector_limits", {})

            for sector, sector_stocks in sector_groups.items():
                # 行业限制
                sector_limit = sector_limits.get(sector, max(1, max_stocks // len(sector_groups)))

                # 按综合评分排序，选择最优股票
                sector_stocks.sort(key=lambda x: x.get("comprehensive_score", 0), reverse=True)
                selected_count = min(len(sector_stocks), sector_limit)

                diversified_stocks.extend(sector_stocks[:selected_count])

            # 如果股票数量不足，补充高评分股票
            if len(diversified_stocks) < config["min_stocks"]:
                remaining_stocks = [s for s in stocks if s not in diversified_stocks]
                remaining_stocks.sort(key=lambda x: x.get("comprehensive_score", 0), reverse=True)

                need_count = config["min_stocks"] - len(diversified_stocks)
                diversified_stocks.extend(remaining_stocks[:need_count])

            # 如果股票数量过多，保留最优股票
            if len(diversified_stocks) > max_stocks:
                diversified_stocks.sort(key=lambda x: x.get("comprehensive_score", 0), reverse=True)
                diversified_stocks = diversified_stocks[:max_stocks]

            logger.info(f"🎯 行业分散化完成: 选择 {len(diversified_stocks)} 只股票，覆盖 {len(sector_groups)} 个行业")
            return diversified_stocks

        except Exception as e:
            logger.error(f"行业分散化失败: {e}")
            return stocks[:config["max_stocks"]]

    async def _optimize_portfolio_weights(self, stocks: List[Dict[str, Any]], config: Dict[str, Any]) -> Dict[str, Any]:
        """优化投资组合权重"""
        try:
            if not stocks:
                return {}

            # 使用改进的权重分配算法
            weights = self._calculate_optimal_weights(stocks, config)

            # 构建投资组合
            portfolio = {
                "holdings": [],
                "total_weight": 0.0,
                "total_amount": config["total_amount"],
                "expected_return": 0.0,
                "estimated_risk": 0.0
            }

            for i, stock in enumerate(stocks):
                weight = weights[i]
                amount = config["total_amount"] * weight

                holding = {
                    "stock_code": stock["code"],
                    "stock_name": stock["name"],
                    "industry": stock.get("industry", "未分类"),
                    "weight": weight,
                    "amount": amount,
                    "shares": int(amount / stock.get("current_price", 1)),
                    "comprehensive_score": stock.get("comprehensive_score", 0),
                    "risk_score": stock.get("portfolio_risk", 50),
                    "expected_return": self._estimate_expected_return(stock),
                    "current_price": stock.get("current_price", 0)
                }

                portfolio["holdings"].append(holding)
                portfolio["total_weight"] += weight
                portfolio["expected_return"] += holding["expected_return"] * weight

            # 计算组合风险
            portfolio["estimated_risk"] = self._calculate_portfolio_risk(portfolio["holdings"])

            logger.info(f"⚖️ 权重优化完成: {len(portfolio['holdings'])} 只股票，总权重: {portfolio['total_weight']:.2%}")
            return portfolio

        except Exception as e:
            logger.error(f"权重优化失败: {e}")
            return {}

    def _calculate_optimal_weights(self, stocks: List[Dict[str, Any]], config: Dict[str, Any]) -> List[float]:
        """计算最优权重"""
        n = len(stocks)
        if n == 0:
            return []

        # 基于综合评分的权重分配
        scores = [stock.get("comprehensive_score", 50) for stock in stocks]
        risk_scores = [stock.get("portfolio_risk", 50) for stock in stocks]

        # 风险调整后的评分
        adjusted_scores = []
        for i in range(n):
            # 评分越高越好，风险越低越好
            adjusted_score = scores[i] * (100 - risk_scores[i]) / 100
            adjusted_scores.append(max(1, adjusted_score))  # 确保最小权重

        # 归一化权重
        total_score = sum(adjusted_scores)
        base_weights = [score / total_score for score in adjusted_scores]

        # 应用权重约束
        min_weight = config["min_single_weight"]
        max_weight = config["max_single_weight"]

        # 调整权重以满足约束
        adjusted_weights = []
        for weight in base_weights:
            adjusted_weight = max(min_weight, min(max_weight, weight))
            adjusted_weights.append(adjusted_weight)

        # 重新归一化
        total_weight = sum(adjusted_weights)
        final_weights = [w / total_weight for w in adjusted_weights]

        return final_weights

    def _estimate_expected_return(self, stock: Dict[str, Any]) -> float:
        """估算预期收益率"""
        # 基于综合评分估算预期收益
        score = stock.get("comprehensive_score", 50)

        # 将评分转换为预期年化收益率
        if score >= 90:
            return random.uniform(0.20, 0.30)  # 20%-30%
        elif score >= 80:
            return random.uniform(0.15, 0.25)  # 15%-25%
        elif score >= 70:
            return random.uniform(0.10, 0.20)  # 10%-20%
        elif score >= 60:
            return random.uniform(0.05, 0.15)  # 5%-15%
        else:
            return random.uniform(0.00, 0.10)  # 0%-10%

    def _calculate_portfolio_risk(self, holdings: List[Dict[str, Any]]) -> float:
        """计算投资组合风险"""
        if not holdings:
            return 0.0

        # 加权平均风险
        total_risk = 0.0
        for holding in holdings:
            weight = holding["weight"]
            risk = holding["risk_score"] / 100.0  # 转换为0-1范围
            total_risk += weight * risk

        diversification_factor = max(0.7, 1 - len(holdings) * 0.05)

        return total_risk * diversification_factor

    def _meets_risk_tolerance(self, stock: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """检查是否符合风险承受能力"""
        risk_tolerance = config["risk_tolerance"]
        stock_risk = stock.get("portfolio_risk", 50)

        if risk_tolerance == "conservative":
            return stock_risk <= 40
        elif risk_tolerance == "moderate":
            return stock_risk <= 60
        elif risk_tolerance == "aggressive":
            return stock_risk <= 80
        else:  # medium
            return stock_risk <= 70

    def _calculate_portfolio_risk_score(self, risk_analysis: Dict[str, Any]) -> float:
        """计算投资组合风险评分"""
        risk_score = risk_analysis.get("risk_score", 50.0)
        risk_level = risk_analysis.get("risk_level", "中等风险")

        # 根据风险等级调整评分
        level_adjustments = {
            "低风险": -10,
            "中等风险": 0,
            "中高风险": 10,
            "高风险": 20,
            "极高风险": 30
        }

        adjustment = level_adjustments.get(risk_level, 0)
        return min(100, max(0, risk_score + adjustment))

    def _get_fallback_candidates(self) -> List[Dict[str, Any]]:
        """获取备用候选股票"""
        return [
            {
                "code": "000001", "name": "平安银行", "industry": "银行",
                "comprehensive_score": 75, "current_price": 12.5,
                "market_cap": 2500000000, "pe_ratio": 5.2, "pb_ratio": 0.8
            },
            {
                "code": "600036", "name": "招商银行", "industry": "银行",
                "comprehensive_score": 85, "current_price": 40.2,
                "market_cap": 12000000000, "pe_ratio": 6.8, "pb_ratio": 1.1
            },
            {
                "code": "000858", "name": "五粮液", "industry": "食品饮料",
                "comprehensive_score": 80, "current_price": 165.3,
                "market_cap": 8500000000, "pe_ratio": 22.3, "pb_ratio": 5.8
            },
            {
                "code": "002415", "name": "海康威视", "industry": "电子",
                "comprehensive_score": 78, "current_price": 35.6,
                "market_cap": 3300000000, "pe_ratio": 18.5, "pb_ratio": 3.2
            },
            {
                "code": "300750", "name": "宁德时代", "industry": "电气设备",
                "comprehensive_score": 82, "current_price": 220.8,
                "market_cap": 9700000000, "pe_ratio": 35.2, "pb_ratio": 8.1
            }
        ]

    async def _validate_and_adjust_portfolio(self, portfolio: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """验证和调整投资组合"""
        try:
            if not portfolio or not portfolio.get("holdings"):
                return portfolio

            # 验证权重总和
            total_weight = sum(holding["weight"] for holding in portfolio["holdings"])
            if abs(total_weight - 1.0) > 0.01:
                # 重新归一化权重
                for holding in portfolio["holdings"]:
                    holding["weight"] = holding["weight"] / total_weight
                    holding["amount"] = config["total_amount"] * holding["weight"]
                    holding["shares"] = int(holding["amount"] / holding["current_price"])

                portfolio["total_weight"] = 1.0

            # 验证风险约束
            if portfolio["estimated_risk"] > config["max_volatility"]:
                portfolio = await self._reduce_portfolio_risk(portfolio, config)

            # 验证收益目标
            if portfolio["expected_return"] < config["target_return"] * 0.8:
                portfolio = await self._enhance_portfolio_return(portfolio, config)

            logger.info(f"✅ 投资组合验证完成")
            return portfolio

        except Exception as e:
            logger.error(f"投资组合验证失败: {e}")
            return portfolio

    async def _reduce_portfolio_risk(self, portfolio: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """降低投资组合风险"""
        try:
            holdings = portfolio["holdings"]

            # 降低高风险股票权重
            for holding in holdings:
                if holding["risk_score"] > 70:
                    holding["weight"] *= 0.8  # 降低20%权重

            # 重新归一化
            total_weight = sum(h["weight"] for h in holdings)
            for holding in holdings:
                holding["weight"] = holding["weight"] / total_weight
                holding["amount"] = config["total_amount"] * holding["weight"]
                holding["shares"] = int(holding["amount"] / holding["current_price"])

            # 重新计算组合指标
            portfolio["estimated_risk"] = self._calculate_portfolio_risk(holdings)
            portfolio["expected_return"] = sum(h["expected_return"] * h["weight"] for h in holdings)

            return portfolio

        except Exception as e:
            logger.error(f"降低投资组合风险失败: {e}")
            return portfolio

    async def _enhance_portfolio_return(self, portfolio: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """提升投资组合收益"""
        try:
            holdings = portfolio["holdings"]

            # 提高高评分股票权重
            for holding in holdings:
                if holding["comprehensive_score"] > 80:
                    holding["weight"] *= 1.1  # 提高10%权重

            # 重新归一化
            total_weight = sum(h["weight"] for h in holdings)
            for holding in holdings:
                holding["weight"] = holding["weight"] / total_weight
                holding["amount"] = config["total_amount"] * holding["weight"]
                holding["shares"] = int(holding["amount"] / holding["current_price"])

            # 重新计算组合指标
            portfolio["estimated_risk"] = self._calculate_portfolio_risk(holdings)
            portfolio["expected_return"] = sum(h["expected_return"] * h["weight"] for h in holdings)

            return portfolio

        except Exception as e:
            logger.error(f"提升投资组合收益失败: {e}")
            return portfolio

    async def _generate_portfolio_report(self, portfolio: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """生成投资组合报告"""
        try:
            if not portfolio or not portfolio.get("holdings"):
                return {"error": "无有效投资组合"}

            holdings = portfolio["holdings"]

            # 基本统计
            total_stocks = len(holdings)
            total_amount = portfolio["total_amount"]
            expected_return = portfolio["expected_return"]
            estimated_risk = portfolio["estimated_risk"]

            # 行业分布
            sector_distribution = {}
            for holding in holdings:
                sector = holding["industry"]
                if sector not in sector_distribution:
                    sector_distribution[sector] = {"weight": 0, "amount": 0, "count": 0}

                sector_distribution[sector]["weight"] += holding["weight"]
                sector_distribution[sector]["amount"] += holding["amount"]
                sector_distribution[sector]["count"] += 1

            # 风险分析
            risk_distribution = {"低风险": 0, "中等风险": 0, "高风险": 0}
            for holding in holdings:
                risk_score = holding["risk_score"]
                if risk_score <= 40:
                    risk_distribution["低风险"] += holding["weight"]
                elif risk_score <= 70:
                    risk_distribution["中等风险"] += holding["weight"]
                else:
                    risk_distribution["高风险"] += holding["weight"]

            # 权重分析
            max_weight = max(h["weight"] for h in holdings)
            min_weight = min(h["weight"] for h in holdings)
            avg_weight = 1.0 / total_stocks

            # 收益风险比
            sharpe_ratio = expected_return / estimated_risk if estimated_risk > 0 else 0

            # 投资建议
            recommendations = self._generate_investment_recommendations(portfolio, config)

            report = {
                "portfolio_summary": {
                    "total_stocks": total_stocks,
                    "total_amount": total_amount,
                    "expected_annual_return": f"{expected_return:.2%}",
                    "estimated_risk": f"{estimated_risk:.2%}",
                    "sharpe_ratio": round(sharpe_ratio, 2),
                    "investment_style": config["investment_style"]
                },
                "sector_distribution": {
                    sector: {
                        "weight": f"{data['weight']:.2%}",
                        "amount": f"¥{data['amount']:,.0f}",
                        "stock_count": data["count"]
                    }
                    for sector, data in sector_distribution.items()
                },
                "risk_analysis": {
                    "risk_distribution": {
                        level: f"{weight:.2%}" for level, weight in risk_distribution.items()
                    },
                    "risk_level": self._determine_portfolio_risk_level(estimated_risk),
                    "risk_factors": self._identify_risk_factors(holdings)
                },
                "weight_analysis": {
                    "max_single_weight": f"{max_weight:.2%}",
                    "min_single_weight": f"{min_weight:.2%}",
                    "average_weight": f"{avg_weight:.2%}",
                    "weight_concentration": "分散" if max_weight < 0.2 else "集中"
                },
                "top_holdings": [
                    {
                        "stock_name": h["stock_name"],
                        "stock_code": h["stock_code"],
                        "weight": f"{h['weight']:.2%}",
                        "amount": f"¥{h['amount']:,.0f}",
                        "expected_return": f"{h['expected_return']:.2%}"
                    }
                    for h in sorted(holdings, key=lambda x: x["weight"], reverse=True)[:5]
                ],
                "recommendations": recommendations,
                "rebalancing": {
                    "frequency": config["rebalance_frequency"],
                    "next_review_date": self._calculate_next_review_date(config),
                    "triggers": ["权重偏离超过5%", "市场环境重大变化", "个股基本面恶化"]
                },
                "performance_targets": {
                    "target_annual_return": f"{config['target_return']:.2%}",
                    "max_acceptable_risk": f"{config['max_volatility']:.2%}",
                    "achievement_probability": self._estimate_achievement_probability(portfolio, config)
                }
            }

            return report

        except Exception as e:
            logger.error(f"生成投资组合报告失败: {e}")
            return {"error": str(e)}

    def _determine_portfolio_risk_level(self, risk: float) -> str:
        """确定投资组合风险等级"""
        if risk <= 0.1:
            return "低风险"
        elif risk <= 0.2:
            return "中等风险"
        elif risk <= 0.3:
            return "中高风险"
        else:
            return "高风险"

    def _identify_risk_factors(self, holdings: List[Dict[str, Any]]) -> List[str]:
        """识别风险因子"""
        risk_factors = []

        # 集中度风险
        max_weight = max(h["weight"] for h in holdings)
        if max_weight > 0.2:
            risk_factors.append("个股集中度风险")

        # 行业集中度风险
        sector_weights = {}
        for holding in holdings:
            sector = holding["industry"]
            sector_weights[sector] = sector_weights.get(sector, 0) + holding["weight"]

        max_sector_weight = max(sector_weights.values())
        if max_sector_weight > 0.4:
            risk_factors.append("行业集中度风险")

        # 高风险股票比例
        high_risk_weight = sum(h["weight"] for h in holdings if h["risk_score"] > 70)
        if high_risk_weight > 0.3:
            risk_factors.append("高风险股票占比过高")

        if not risk_factors:
            risk_factors.append("风险分散良好")

        return risk_factors

    def _generate_investment_recommendations(self, portfolio: Dict[str, Any], config: Dict[str, Any]) -> List[str]:
        """生成投资建议"""
        recommendations = []

        holdings = portfolio["holdings"]
        expected_return = portfolio["expected_return"]
        estimated_risk = portfolio["estimated_risk"]

        # 收益建议
        if expected_return < config["target_return"]:
            recommendations.append("考虑增加成长性更强的股票以提升收益预期")

        # 风险建议
        if estimated_risk > config["max_volatility"]:
            recommendations.append("建议降低高风险股票权重以控制组合风险")

        # 分散化建议
        sector_count = len(set(h["industry"] for h in holdings))
        if sector_count < 3:
            recommendations.append("建议增加行业分散化以降低系统性风险")

        # 权重建议
        max_weight = max(h["weight"] for h in holdings)
        if max_weight > 0.25:
            recommendations.append("建议降低最大持仓权重以提高分散化程度")

        # 再平衡建议
        recommendations.append(f"建议{config['rebalance_frequency']}进行组合再平衡")

        if not recommendations:
            recommendations.append("当前投资组合配置合理，建议保持")

        return recommendations

    def _calculate_next_review_date(self, config: Dict[str, Any]) -> str:
        """计算下次审查日期"""
        frequency = config["rebalance_frequency"]
        now = datetime.now()

        if frequency == "weekly":
            next_date = now + timedelta(weeks=1)
        elif frequency == "monthly":
            next_date = now + timedelta(days=30)
        elif frequency == "quarterly":
            next_date = now + timedelta(days=90)
        else:
            next_date = now + timedelta(days=30)

        return next_date.strftime("%Y-%m-%d")

    def _estimate_achievement_probability(self, portfolio: Dict[str, Any], config: Dict[str, Any]) -> str:
        """估算目标达成概率"""
        expected_return = portfolio["expected_return"]
        target_return = config["target_return"]
        estimated_risk = portfolio["estimated_risk"]

        if expected_return >= target_return:
            if estimated_risk <= config["max_volatility"]:
                return "高 (>70%)"
            else:
                return "中等 (50-70%)"
        else:
            return "较低 (<50%)"

    def _get_empty_portfolio_result(self, reason: str) -> Dict[str, Any]:
        """获取空投资组合结果"""
        return {
            "success": False,
            "error": reason,
            "portfolio": {},
            "report": {"error": reason}
        }

# 全局服务实例
intelligent_portfolio_builder = IntelligentPortfolioBuilder()