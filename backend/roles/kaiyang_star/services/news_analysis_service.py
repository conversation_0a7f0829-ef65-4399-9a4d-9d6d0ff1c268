#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开阳星新闻分析服务
直接从天枢星获取新闻内容，进行信息面质量评估
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

class SentimentAnalyzer:
    """情感分析器"""

    def analyze(self, content: str) -> Dict[str, Any]:
        """分析文本情感"""
        # 简单的情感分析逻辑
        positive_words = ["利好", "上涨", "增长", "盈利", "突破", "买入"]
        negative_words = ["利空", "下跌", "亏损", "风险", "卖出", "暴跌"]

        positive_count = sum(1 for word in positive_words if word in content)
        negative_count = sum(1 for word in negative_words if word in content)

        if positive_count > negative_count:
            sentiment = "positive"
            confidence = min(0.9, 0.5 + (positive_count - negative_count) * 0.1)
        elif negative_count > positive_count:
            sentiment = "negative"
            confidence = min(0.9, 0.5 + (negative_count - positive_count) * 0.1)
        else:
            sentiment = "neutral"
            confidence = 0.5

        return {
            "sentiment": sentiment,
            "confidence": confidence,
            "positive": positive_count / max(1, positive_count + negative_count),
            "negative": negative_count / max(1, positive_count + negative_count),
            "neutral": 1 - (positive_count + negative_count) / max(1, positive_count + negative_count + 1)
        }

class ImpactPredictor:
    """影响预测器"""

    def predict(self, stock_code: str, news_list: List[Dict], sentiment_results: List[Dict]) -> Dict[str, Any]:
        """预测新闻影响"""
        if not sentiment_results:
            return {
                "short_term": 0.0,
                "medium_term": 0.0,
                "long_term": 0.0,
                "confidence": 0.5,
                "direction": "neutral",
                "factors": []
            }

        # 计算平均情感影响
        avg_sentiment = sum(s.get("confidence", 0.5) for s in sentiment_results) / len(sentiment_results)
        positive_ratio = sum(1 for s in sentiment_results if s.get("sentiment") == "positive") / len(sentiment_results)

        # 简单的影响预测
        if positive_ratio > 0.6:
            direction = "positive"
            impact_magnitude = avg_sentiment * positive_ratio
        elif positive_ratio < 0.4:
            direction = "negative"
            impact_magnitude = avg_sentiment * (1 - positive_ratio)
        else:
            direction = "neutral"
            impact_magnitude = 0.05

        return {
            "short_term": impact_magnitude * 0.8,
            "medium_term": impact_magnitude * 0.5,
            "long_term": impact_magnitude * 0.2,
            "confidence": avg_sentiment,
            "direction": direction,
            "factors": ["市场情绪", "新闻数量", "情感强度"]
        }

class NewsQualityAssessor:
    """新闻质量评估器"""

    def assess(self, news: Dict) -> Dict[str, Any]:
        """评估新闻质量"""
        title = news.get("title", "")
        content = news.get("content", "")
        source = news.get("source", "")

        # 简单的质量评估
        credibility = 0.8 if "官方" in source or "权威" in source else 0.6
        relevance = 0.8 if any(word in title for word in ["股票", "财经", "投资"]) else 0.5
        timeliness = 0.9  # 假设都是最新新闻
        completeness = min(1.0, len(content) / 500)  # 内容完整性

        score = (credibility + relevance + timeliness + completeness) / 4

        return {
            "score": score,
            "credibility": credibility,
            "relevance": relevance,
            "timeliness": timeliness,
            "completeness": completeness
        }

class CatalystIdentifier:
    """催化剂识别器"""

    def identify(self, news_list: List[Dict], sentiment_results: List[Dict]) -> Dict[str, Any]:
        """识别关键催化剂"""
        catalysts = []

        # 基于新闻标题识别催化剂
        catalyst_keywords = {
            "业绩预期": ["业绩", "财报", "盈利", "营收"],
            "政策利好": ["政策", "支持", "补贴", "减税"],
            "行业趋势": ["行业", "趋势", "发展", "前景"],
            "技术突破": ["技术", "创新", "突破", "专利"],
            "合作并购": ["合作", "并购", "收购", "战略"]
        }

        for news in news_list:
            title = news.get("title", "")
            for catalyst_type, keywords in catalyst_keywords.items():
                if any(keyword in title for keyword in keywords):
                    catalysts.append(catalyst_type)

        # 去重并限制数量
        unique_catalysts = list(set(catalysts))

        return {
            "catalysts": unique_catalysts[:5]  # 最多5个催化剂
        }

@dataclass
class NewsImpactResult:
    """新闻影响分析结果"""
    stock_code: str
    analysis_time: datetime
    news_count: int
    sentiment_summary: Dict
    impact_prediction: Dict
    news_quality_score: float
    risk_level: str
    opportunity_level: str
    key_catalysts: List[str]
    risk_factors: List[str]

@dataclass
class NewsQualityAssessment:
    """新闻质量评估"""
    credibility_score: float
    timeliness_score: float
    relevance_score: float
    impact_potential: float
    overall_quality: float

class NewsAnalysisService:
    """开阳星新闻分析服务"""
    
    def __init__(self):
        # 天枢星新闻服务连接
        self.tianshu_news_service = None  # 延迟初始化
        
        # 分析组件
        self.sentiment_analyzer = SentimentAnalyzer()
        self.impact_predictor = ImpactPredictor()
        self.quality_assessor = NewsQualityAssessor()
        self.catalyst_identifier = CatalystIdentifier()
        
        logger.info("开阳星新闻分析服务初始化完成")
    
    async def analyze_stock_news_impact(self, stock_code: str, analysis_config: Dict = None) -> NewsImpactResult:
        """分析股票新闻影响"""
        
        try:
            # 1. 从天枢星获取新闻数据
            news_data = await self._get_news_from_tianshu(stock_code, analysis_config)
            
            if not news_data or not news_data.get("news_list"):
                return self._create_empty_result(stock_code)
            
            # 2. 新闻情感分析
            sentiment_results = await self._analyze_news_sentiment(news_data["news_list"])
            
            # 3. 新闻质量评估
            quality_assessments = await self._assess_news_quality(news_data["news_list"])
            
            # 4. 影响预测
            impact_prediction = await self._predict_news_impact(
                stock_code, news_data["news_list"], sentiment_results
            )
            
            # 5. 催化剂识别
            key_catalysts = await self._identify_key_catalysts(news_data["news_list"], sentiment_results)
            
            # 6. 风险因素识别
            risk_factors = await self._identify_risk_factors(news_data["news_list"], sentiment_results)
            
            # 7. 综合评分
            overall_quality_score = self._calculate_overall_quality_score(quality_assessments)
            
            # 8. 风险和机会等级评定
            risk_level = self._determine_risk_level(sentiment_results, impact_prediction)
            opportunity_level = self._determine_opportunity_level(sentiment_results, impact_prediction)
            
            # 9. 生成分析结果
            result = NewsImpactResult(
                stock_code=stock_code,
                analysis_time=datetime.now(),
                news_count=len(news_data["news_list"]),
                sentiment_summary=self._summarize_sentiment(sentiment_results),
                impact_prediction=impact_prediction,
                news_quality_score=overall_quality_score,
                risk_level=risk_level,
                opportunity_level=opportunity_level,
                key_catalysts=key_catalysts,
                risk_factors=risk_factors
            )
            
            logger.info(f"股票 {stock_code} 新闻影响分析完成，质量评分: {overall_quality_score:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"分析股票 {stock_code} 新闻影响失败: {e}")
            return self._create_empty_result(stock_code)
    
    async def batch_analyze_news_impact(self, stock_codes: List[str]) -> Dict[str, NewsImpactResult]:
        """批量分析新闻影响"""
        
        results = {}
        
        # 并行分析
        tasks = [self.analyze_stock_news_impact(code) for code in stock_codes]
        analysis_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for stock_code, result in zip(stock_codes, analysis_results):
            if isinstance(result, Exception):
                logger.warning(f"分析股票 {stock_code} 新闻失败: {result}")
                results[stock_code] = self._create_empty_result(stock_code)
            else:
                results[stock_code] = result
        
        return results
    
    async def get_market_news_summary(self) -> Dict[str, Any]:
        """获取市场新闻摘要"""
        
        try:
            # 从天枢星获取市场整体新闻
            market_news = await self._get_market_news_from_tianshu()
            
            if not market_news:
                return {"status": "no_data", "summary": {}}
            
            # 分析市场整体情绪
            market_sentiment = await self._analyze_market_sentiment(market_news)
            
            # 识别热点主题
            hot_topics = await self._identify_hot_topics(market_news)
            
            # 政策影响分析
            policy_impact = await self._analyze_policy_impact(market_news)
            
            return {
                "status": "success",
                "summary": {
                    "total_news": len(market_news),
                    "market_sentiment": market_sentiment,
                    "hot_topics": hot_topics,
                    "policy_impact": policy_impact,
                    "analysis_time": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"获取市场新闻摘要失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def _get_news_from_tianshu(self, stock_code: str, config: Dict = None) -> Dict:
        """从天枢星获取新闻数据 - 使用天枢星专业新闻收集服务"""

        try:
            # 使用天枢星的专业新闻收集服务
            from backend.roles.tianshu_star.services.news_collection_service import news_collection_service

            # 配置参数
            days = config.get("days", 7) if config else 7
            news_types = config.get("news_types", ["公告", "新闻", "研报"]) if config else ["公告", "新闻", "研报"]

            logger.info(f"🌟 调用天枢星专业新闻收集服务获取股票{stock_code}新闻")

            # 获取股票相关新闻
            stock_news_result = await news_collection_service.collect_stock_news(stock_code, limit=15)

            # 获取市场新闻作为补充
            market_news_result = await news_collection_service.collect_market_news(limit=10)

            # 合并新闻数据
            all_news = []

            # 处理股票新闻
            if stock_news_result.get("success") and stock_news_result.get("news_data"):
                for i, news_item in enumerate(stock_news_result["news_data"]):
                    all_news.append({
                        "id": f"tianshu_stock_news_{i}",
                        "title": news_item.get("title", ""),
                        "content": news_item.get("content", ""),
                        "source": news_item.get("source", "天枢星新闻收集"),
                        "publish_time": news_item.get("publish_time", datetime.now().isoformat()),
                        "news_type": "股票新闻",
                        "importance": "高",
                        "url": news_item.get("url", ""),
                        "author": news_item.get("author", ""),
                        "tags": news_item.get("tags", []),
                        "sentiment": news_item.get("sentiment", "neutral")
                    })

            # 处理市场新闻
            if market_news_result.get("success") and market_news_result.get("market_news"):
                for i, news_item in enumerate(market_news_result["market_news"]):
                    all_news.append({
                        "id": f"tianshu_market_news_{i}",
                        "title": news_item.get("title", ""),
                        "content": news_item.get("content", ""),
                        "source": news_item.get("source", "天枢星新闻收集"),
                        "publish_time": news_item.get("publish_time", datetime.now().isoformat()),
                        "news_type": "市场新闻",
                        "importance": "中等",
                        "url": news_item.get("url", ""),
                        "author": news_item.get("author", ""),
                        "tags": news_item.get("tags", []),
                        "sentiment": news_item.get("sentiment", "neutral")
                    })

            logger.info(f" 天枢星专业服务成功获取{len(all_news)}条新闻数据")

            return {
                "status": "success",
                "news_list": all_news,
                "data_source": "tianshu_star_professional_service",
                "total_count": len(all_news),
                "stock_related_count": len(stock_news_result.get("news_data", [])),
                "market_news_count": len(market_news_result.get("market_news", [])),
                "service_info": {
                    "stock_news_service": stock_news_result.get("data_source", ""),
                    "market_news_service": market_news_result.get("data_source", ""),
                    "collection_time": datetime.now().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"从天枢星专业新闻服务获取数据失败: {e}")
            # 返回错误信息，不使用虚拟数据
            return {
                "status": "error",
                "news_list": [],
                "error": str(e),
                "data_source": "tianshu_star_professional_service",
                "message": "天枢星专业新闻服务暂时不可用"
            }

    async def _get_market_news_from_tianshu(self) -> List[Dict]:
        """从天枢星获取市场新闻 - 使用天枢星专业新闻收集服务"""

        try:
            # 使用天枢星的专业新闻收集服务
            from backend.roles.tianshu_star.services.news_collection_service import news_collection_service

            logger.info("🌟 调用天枢星专业新闻收集服务获取市场新闻")

            # 获取市场新闻
            market_news_result = await news_collection_service.collect_market_news(limit=20)

            market_news = []

            if market_news_result.get("success") and market_news_result.get("market_news"):
                for i, news_item in enumerate(market_news_result["market_news"]):
                    # 判断重要性
                    title = news_item.get("title", "")
                    importance = "高" if any(kw in title for kw in ["重大", "突破", "暴涨", "暴跌", "政策", "央行"]) else "中等"

                    market_news_item = {
                        "id": f"tianshu_market_{i}_{hash(title) % 10000}",
                        "title": title,
                        "content": news_item.get("content", ""),
                        "source": news_item.get("source", "天枢星市场新闻"),
                        "publish_time": news_item.get("publish_time", datetime.now().isoformat()),
                        "news_type": "市场",
                        "importance": importance,
                        "url": news_item.get("url", ""),
                        "author": news_item.get("author", ""),
                        "tags": news_item.get("tags", ["市场", "A股"]),
                        "sentiment": news_item.get("sentiment", "neutral")
                    }
                    market_news.append(market_news_item)

                logger.info(f" 天枢星专业服务成功获取{len(market_news)}条市场新闻")
                return market_news
            else:
                logger.warning("天枢星市场新闻服务返回空数据")
                return []

        except Exception as e:
            logger.error(f"从天枢星专业新闻服务获取市场新闻失败: {e}")
            # 返回空列表，不使用虚拟数据
            return []

    async def _analyze_news_sentiment(self, news_list: List[Dict]) -> List[Dict]:
        """分析新闻情感"""

        sentiment_results = []

        for news in news_list:
            try:
                # 基于真实数据的情感分析
                sentiment_score = self.sentiment_analyzer.analyze(news.get("content", ""))

                sentiment_results.append({
                    "news_id": news.get("id"),
                    "sentiment": sentiment_score.get("sentiment", "neutral"),
                    "confidence": sentiment_score.get("confidence", 0.5),
                    "positive_score": sentiment_score.get("positive", 0.3),
                    "negative_score": sentiment_score.get("negative", 0.3),
                    "neutral_score": sentiment_score.get("neutral", 0.4)
                })

            except Exception as e:
                logger.warning(f"分析新闻情感失败: {e}")
                sentiment_results.append({
                    "news_id": news.get("id"),
                    "sentiment": "neutral",
                    "confidence": 0.5,
                    "positive_score": 0.3,
                    "negative_score": 0.3,
                    "neutral_score": 0.4
                })

        return sentiment_results

    async def _assess_news_quality(self, news_list: List[Dict]) -> List[Dict]:
        """评估新闻质量"""

        quality_assessments = []

        for news in news_list:
            try:
                # 基于真实数据的质量评估
                quality_score = self.quality_assessor.assess(news)

                quality_assessments.append({
                    "news_id": news.get("id"),
                    "quality_score": quality_score.get("score", 0.6),
                    "credibility": quality_score.get("credibility", 0.7),
                    "relevance": quality_score.get("relevance", 0.6),
                    "timeliness": quality_score.get("timeliness", 0.8),
                    "completeness": quality_score.get("completeness", 0.5)
                })

            except Exception as e:
                logger.warning(f"评估新闻质量失败: {e}")
                quality_assessments.append({
                    "news_id": news.get("id"),
                    "quality_score": 0.6,
                    "credibility": 0.7,
                    "relevance": 0.6,
                    "timeliness": 0.8,
                    "completeness": 0.5
                })

        return quality_assessments

    async def _predict_news_impact(self, stock_code: str, news_list: List[Dict], sentiment_results: List[Dict]) -> Dict:
        """预测新闻影响"""

        try:
            # 基于真实数据的影响预测
            impact_prediction = self.impact_predictor.predict(stock_code, news_list, sentiment_results)

            return {
                "short_term_impact": impact_prediction.get("short_term", 0.1),
                "medium_term_impact": impact_prediction.get("medium_term", 0.05),
                "long_term_impact": impact_prediction.get("long_term", 0.02),
                "confidence": impact_prediction.get("confidence", 0.6),
                "impact_direction": impact_prediction.get("direction", "neutral"),
                "key_factors": impact_prediction.get("factors", ["市场情绪", "基本面"])
            }

        except Exception as e:
            logger.warning(f"预测新闻影响失败: {e}")
            return {
                "short_term_impact": 0.1,
                "medium_term_impact": 0.05,
                "long_term_impact": 0.02,
                "confidence": 0.6,
                "impact_direction": "neutral",
                "key_factors": ["市场情绪", "基本面"]
            }

    async def _identify_key_catalysts(self, news_list: List[Dict], sentiment_results: List[Dict]) -> List[str]:
        """识别关键催化剂"""

        try:
            # 基于真实数据的催化剂识别
            catalysts = self.catalyst_identifier.identify(news_list, sentiment_results)

            return catalysts.get("catalysts", ["业绩预期", "政策利好", "行业趋势"])

        except Exception as e:
            logger.warning(f"识别关键催化剂失败: {e}")
            return ["业绩预期", "政策利好", "行业趋势"]

    async def _identify_risk_factors(self, news_list: List[Dict], sentiment_results: List[Dict]) -> List[str]:
        """识别风险因素"""

        try:
            # 基于真实数据的风险因素识别
            risk_factors = []

            for news, sentiment in zip(news_list, sentiment_results):
                if sentiment.get("sentiment") == "negative" and sentiment.get("confidence", 0) > 0.7:
                    risk_factors.append(f"负面新闻: {news.get('title', '')[:20]}...")

            if not risk_factors:
                risk_factors = ["市场波动", "流动性风险"]

            return risk_factors[:5]  # 限制数量

        except Exception as e:
            logger.warning(f"识别风险因素失败: {e}")
            return ["市场波动", "流动性风险"]

    def _calculate_overall_quality_score(self, quality_assessments: List[Dict]) -> float:
        """计算整体质量评分"""

        if not quality_assessments:
            return 0.5

        total_score = sum(assessment.get("quality_score", 0.5) for assessment in quality_assessments)
        return total_score / len(quality_assessments)

    def _determine_risk_level(self, sentiment_results: List[Dict], impact_prediction: Dict) -> str:
        """确定风险等级"""

        try:
            negative_count = sum(1 for s in sentiment_results if s.get("sentiment") == "negative")
            negative_ratio = negative_count / len(sentiment_results) if sentiment_results else 0

            impact_magnitude = abs(impact_prediction.get("short_term_impact", 0))

            if negative_ratio > 0.6 or impact_magnitude > 0.15:
                return "高"
            elif negative_ratio > 0.3 or impact_magnitude > 0.08:
                return "中"
            else:
                return "低"

        except Exception as e:
            logger.warning(f"确定风险等级失败: {e}")
            return "中"

    def _determine_opportunity_level(self, sentiment_results: List[Dict], impact_prediction: Dict) -> str:
        """确定机会等级"""

        try:
            positive_count = sum(1 for s in sentiment_results if s.get("sentiment") == "positive")
            positive_ratio = positive_count / len(sentiment_results) if sentiment_results else 0

            impact_magnitude = impact_prediction.get("short_term_impact", 0)

            if positive_ratio > 0.6 and impact_magnitude > 0.1:
                return "高"
            elif positive_ratio > 0.3 and impact_magnitude > 0.05:
                return "中"
            else:
                return "低"

        except Exception as e:
            logger.warning(f"确定机会等级失败: {e}")
            return "中"

    def _summarize_sentiment(self, sentiment_results: List[Dict]) -> Dict:
        """汇总情感分析结果"""

        if not sentiment_results:
            return {
                "overall_sentiment": "neutral",
                "positive_ratio": 0.3,
                "negative_ratio": 0.3,
                "neutral_ratio": 0.4,
                "confidence": 0.5
            }

        positive_count = sum(1 for s in sentiment_results if s.get("sentiment") == "positive")
        negative_count = sum(1 for s in sentiment_results if s.get("sentiment") == "negative")
        neutral_count = len(sentiment_results) - positive_count - negative_count

        total = len(sentiment_results)
        positive_ratio = positive_count / total
        negative_ratio = negative_count / total
        neutral_ratio = neutral_count / total

        # 确定整体情感
        if positive_ratio > negative_ratio and positive_ratio > neutral_ratio:
            overall_sentiment = "positive"
        elif negative_ratio > positive_ratio and negative_ratio > neutral_ratio:
            overall_sentiment = "negative"
        else:
            overall_sentiment = "neutral"

        # 计算平均置信度
        avg_confidence = sum(s.get("confidence", 0.5) for s in sentiment_results) / total

        return {
            "overall_sentiment": overall_sentiment,
            "positive_ratio": positive_ratio,
            "negative_ratio": negative_ratio,
            "neutral_ratio": neutral_ratio,
            "confidence": avg_confidence
        }

    async def _analyze_market_sentiment(self, market_news: List[Dict]) -> Dict:
        """分析市场整体情绪"""

        try:
            sentiment_results = await self._analyze_news_sentiment(market_news)
            return self._summarize_sentiment(sentiment_results)

        except Exception as e:
            logger.warning(f"分析市场情绪失败: {e}")
            return {
                "overall_sentiment": "neutral",
                "positive_ratio": 0.3,
                "negative_ratio": 0.3,
                "neutral_ratio": 0.4,
                "confidence": 0.5
            }

    async def _identify_hot_topics(self, market_news: List[Dict]) -> List[str]:
        """识别热点主题"""

        try:
            # 基于真实数据的热点主题识别
            topics = {}

            for news in market_news:
                title = news.get("title", "")
                content = news.get("content", "")

                # 简单的关键词提取
                keywords = ["新能源", "芯片", "医药", "房地产", "金融", "消费", "科技", "制造业"]
                for keyword in keywords:
                    if keyword in title or keyword in content:
                        topics[keyword] = topics.get(keyword, 0) + 1

            # 按频次排序
            hot_topics = sorted(topics.items(), key=lambda x: x[1], reverse=True)
            return [topic[0] for topic in hot_topics[:5]]

        except Exception as e:
            logger.warning(f"识别热点主题失败: {e}")
            return ["科技", "消费", "金融"]

    async def _analyze_policy_impact(self, market_news: List[Dict]) -> Dict:
        """分析政策影响"""

        try:
            policy_news = [news for news in market_news if "政策" in news.get("title", "") or "政策" in news.get("content", "")]

            if not policy_news:
                return {
                    "has_policy_impact": False,
                    "impact_level": "无",
                    "policy_areas": []
                }

            # 分析政策影响程度
            impact_level = "中等"
            if len(policy_news) > 3:
                impact_level = "重大"
            elif len(policy_news) == 1:
                impact_level = "轻微"

            # 识别政策领域
            policy_areas = []
            for news in policy_news:
                title = news.get("title", "")
                if "货币" in title or "利率" in title:
                    policy_areas.append("货币政策")
                elif "财政" in title or "税收" in title:
                    policy_areas.append("财政政策")
                elif "监管" in title:
                    policy_areas.append("监管政策")
                else:
                    policy_areas.append("其他政策")

            return {
                "has_policy_impact": True,
                "impact_level": impact_level,
                "policy_areas": list(set(policy_areas)),
                "policy_news_count": len(policy_news)
            }

        except Exception as e:
            logger.warning(f"分析政策影响失败: {e}")
            return {
                "has_policy_impact": False,
                "impact_level": "无",
                "policy_areas": []
            }

    def _create_empty_result(self, stock_code: str) -> NewsImpactResult:
        """创建空的分析结果"""

        return NewsImpactResult(
            stock_code=stock_code,
            analysis_time=datetime.now(),
            news_count=0,
            sentiment_summary={
                "overall_sentiment": "neutral",
                "positive_ratio": 0.3,
                "negative_ratio": 0.3,
                "neutral_ratio": 0.4,
                "confidence": 0.5
            },
            impact_prediction={
                "short_term_impact": 0.0,
                "medium_term_impact": 0.0,
                "long_term_impact": 0.0,
                "confidence": 0.5,
                "impact_direction": "neutral",
                "key_factors": []
            },
            news_quality_score=0.5,
            risk_level="低",
            opportunity_level="低",
            key_catalysts=[],
            risk_factors=[]
        )