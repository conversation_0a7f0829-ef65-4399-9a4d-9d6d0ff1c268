from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实机会推送服务 - 开阳星
实现真实的投资机会推送到天权星
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class RealOpportunityPushService:
    """真实机会推送服务"""
    
    def __init__(self):
        self.service_name = "RealOpportunityPushService"
        self.version = "1.0.0"
        self.push_history = []
        self.tianquan_service = None
        
        logger.info(f"真实机会推送服务 v{self.version} 初始化完成")
    
    async def _get_tianquan_service(self):
        """获取天权星服务"""
        if self.tianquan_service is None:
            try:
                # 尝试导入天权星策略管理服务
                from backend.roles.tianquan_star.services.strategy_management_service import strategy_management_service
                self.tianquan_service = strategy_management_service
                logger.info(" 连接天权星服务成功")
            except Exception as e:
                logger.warning(f"⚠️ 连接天权星服务失败: {e}")
                # 创建一个简单的备用服务
        pass  # 专业版模式

        return self.tianquan_service

    def _create_fallback_tianquan_service(self):
        """创建备用天权星服务"""
        class FallbackTianquanService:
            async def create_strategy(self, strategy_data):
                """创建策略的备用实现"""
                try:
                    # 简单的策略记录
                    strategy_id = f"strategy_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                    # 记录到本地文件
                    import os
                    import json

                    record_dir = "backend/data/tianquan_strategies"
                    os.makedirs(record_dir, exist_ok=True)

                    strategy_file = os.path.join(record_dir, f"{strategy_id}.json")
                    with open(strategy_file, 'w', encoding='utf-8') as f:
                        json.dump(strategy_data, f, ensure_ascii=False, indent=2)

                    logger.info(f"策略已记录到本地: {strategy_file}")

                    return {
                        "success": True,
                        "strategy_id": strategy_id,
                        "message": "策略已创建并记录到本地"
                    }

                except Exception as e:
                    return {
                        "success": False,
                        "error": str(e)
                    }

        return FallbackTianquanService()
    
    async def push_opportunities_to_tianquan(self, opportunities: List[Dict[str, Any]], push_config: Dict = None) -> Dict[str, Any]:
        """推送投资机会到天权星"""
        try:
            logger.info(f"开始推送 {len(opportunities)} 个投资机会到天权星")
            
            if not opportunities:
                return {
                    "success": True,
                    "total_opportunities": 0,
                    "pushed_count": 0,
                    "failed_count": 0,
                    "success_rate": 0.0,
                    "push_results": []
                }
            
            # 验证和处理机会数据
            validated_opportunities = []
            for opp in opportunities:
                validated_opp = await self._validate_opportunity(opp)
                if validated_opp:
                    validated_opportunities.append(validated_opp)
            
            # 推送到天权星
            push_results = []
            successful_pushes = 0
            
            tianquan_service = await self._get_tianquan_service()
            
            for opportunity in validated_opportunities:
                try:
                    # 转换为天权星策略格式
                    strategy_data = await self._convert_to_strategy_format(opportunity)
                    
                    # 推送到天权星
                    if tianquan_service:
                        push_result = await self._push_to_tianquan_service(tianquan_service, strategy_data)
                    else:
                        # 如果天权星服务不可用，记录到本地
                        push_result = await self._record_opportunity_locally(strategy_data)
                    
                    if push_result.get("success"):
                        successful_pushes += 1
                        push_results.append({
                            "stock_code": opportunity["stock_code"],
                            "status": "success",
                            "strategy_id": push_result.get("strategy_id"),
                            "message": "推送成功"
                        })
                    else:
                        push_results.append({
                            "stock_code": opportunity["stock_code"],
                            "status": "failed",
                            "error": push_result.get("error", "未知错误")
                        })
                
                except Exception as e:
                    logger.warning(f"推送机会 {opportunity.get('stock_code', 'unknown')} 失败: {e}")
                    push_results.append({
                        "stock_code": opportunity.get("stock_code", "unknown"),
                        "status": "failed",
                        "error": str(e)
                    })
            
            # 计算成功率
            success_rate = successful_pushes / len(validated_opportunities) if validated_opportunities else 0.0
            
            # 记录推送历史
            push_record = {
                "timestamp": datetime.now().isoformat(),
                "total_opportunities": len(opportunities),
                "validated_opportunities": len(validated_opportunities),
                "successful_pushes": successful_pushes,
                "success_rate": success_rate,
                "push_results": push_results
            }
            
            self.push_history.append(push_record)
            
            # 保持历史记录不超过100条
            if len(self.push_history) > 100:
                self.push_history = self.push_history[-100:]
            
            return {
                "success": True,
                "total_opportunities": len(opportunities),
                "validated_opportunities": len(validated_opportunities),
                "pushed_count": successful_pushes,
                "failed_count": len(validated_opportunities) - successful_pushes,
                "success_rate": success_rate,
                "push_results": push_results,
                "push_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"推送机会到天权星失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "total_opportunities": len(opportunities) if opportunities else 0,
                "pushed_count": 0,
                "failed_count": 0,
                "success_rate": 0.0
            }
    
    async def _validate_opportunity(self, opportunity: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """验证投资机会数据"""
        try:
            required_fields = ["stock_code", "stock_name", "opportunity_type", "confidence"]
            
            # 检查必需字段
            for field in required_fields:
                if field not in opportunity:
                    logger.warning(f"机会数据缺少必需字段: {field}")
                    return None
            
            # 验证数据类型和范围
            if not isinstance(opportunity["confidence"], (int, float)) or not (0 <= opportunity["confidence"] <= 1):
                logger.warning(f"置信度数据无效: {opportunity['confidence']}")
                return None
            
            # 补充默认值
            validated_opportunity = {
                "stock_code": opportunity["stock_code"],
                "stock_name": opportunity["stock_name"],
                "opportunity_type": opportunity["opportunity_type"],
                "confidence": float(opportunity["confidence"]),
                "expected_return": float(opportunity.get("expected_return", 0.1)),
                "risk_level": opportunity.get("risk_level", "中等"),
                "time_horizon": opportunity.get("time_horizon", "短期"),
                "analysis_reason": opportunity.get("analysis_reason", "AI智能分析推荐"),
                "created_time": datetime.now().isoformat()
            }
            
            return validated_opportunity
            
        except Exception as e:
            logger.warning(f"验证机会数据失败: {e}")
            return None
    
    async def _convert_to_strategy_format(self, opportunity: Dict[str, Any]) -> Dict[str, Any]:
        """转换为天权星策略格式"""
        try:
            strategy_data = {
                "strategy_name": f"开阳星推荐-{opportunity['stock_name']}",
                "strategy_type": "股票投资",
                "target_stocks": [opportunity["stock_code"]],
                "investment_logic": {
                    "opportunity_type": opportunity["opportunity_type"],
                    "confidence_level": opportunity["confidence"],
                    "expected_return": opportunity["expected_return"],
                    "risk_assessment": opportunity["risk_level"],
                    "analysis_reason": opportunity["analysis_reason"]
                },
                "risk_control": {
                    "max_position": min(0.1, opportunity["confidence"] * 0.15),  # 最大仓位
                    "stop_loss": -0.05,  # 止损5%
                    "take_profit": opportunity["expected_return"] * 0.8  # 止盈目标
                },
                "time_horizon": opportunity["time_horizon"],
                "priority": "high" if opportunity["confidence"] >= 0.8 else "medium",
                "source": "开阳星智能推荐",
                "created_by": "kaiyang_star",
                "created_time": opportunity["created_time"]
            }
            
            return strategy_data
            
        except Exception as e:
            logger.error(f"转换策略格式失败: {e}")
            return {}
    
    async def _push_to_tianquan_service(self, tianquan_service, strategy_data: Dict[str, Any]) -> Dict[str, Any]:
        """推送到天权星服务"""
        try:
            # 调用天权星的策略创建接口
            result = await tianquan_service.create_strategy(strategy_data)
            
            if result.get("success"):
                return {
                    "success": True,
                    "strategy_id": result.get("strategy_id"),
                    "message": "成功推送到天权星"
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "天权星策略创建失败")
                }
                
        except Exception as e:
            logger.warning(f"推送到天权星服务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _record_opportunity_locally(self, strategy_data: Dict[str, Any]) -> Dict[str, Any]:
        """本地记录投资机会（天权星服务不可用时）"""
        try:
            import os
            
            # 创建本地记录目录
            record_dir = "backend/data/opportunity_records"
            os.makedirs(record_dir, exist_ok=True)
            
            # 生成记录文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            stock_code = strategy_data.get("target_stocks", ["unknown"])[0]
            filename = f"opportunity_{stock_code}_{timestamp}.json"
            filepath = os.path.join(record_dir, filename)
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(strategy_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"投资机会已本地记录: {filepath}")
            
            return {
                "success": True,
                "strategy_id": f"local_{timestamp}",
                "message": f"已本地记录到 {filename}"
            }
            
        except Exception as e:
            logger.error(f"本地记录失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_push_history(self, limit: int = 20) -> Dict[str, Any]:
        """获取推送历史"""
        try:
            recent_history = self.push_history[-limit:] if self.push_history else []
            
            # 计算统计信息
            if self.push_history:
                total_pushes = len(self.push_history)
                total_opportunities = sum(record["total_opportunities"] for record in self.push_history)
                total_successful = sum(record["successful_pushes"] for record in self.push_history)
                avg_success_rate = sum(record["success_rate"] for record in self.push_history) / total_pushes
            else:
                total_pushes = 0
                total_opportunities = 0
                total_successful = 0
                avg_success_rate = 0.0
            
            return {
                "success": True,
                "recent_history": recent_history,
                "statistics": {
                    "total_push_sessions": total_pushes,
                    "total_opportunities_processed": total_opportunities,
                    "total_successful_pushes": total_successful,
                    "average_success_rate": avg_success_rate,
                    "last_push_time": self.push_history[-1]["timestamp"] if self.push_history else None
                }
            }
            
        except Exception as e:
            logger.error(f"获取推送历史失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_push_quality_assessment(self) -> Dict[str, Any]:
        """获取推送质量评估"""
        try:
            if not self.push_history:
                return {
                    "success": True,
                    "quality_score": 0.0,
                    "quality_level": "无数据",
                    "assessment": "暂无推送历史数据"
                }
            
            # 计算质量指标
            recent_records = self.push_history[-10:]  # 最近10次推送
            
            avg_success_rate = sum(record["success_rate"] for record in recent_records) / len(recent_records)
            avg_opportunities_per_push = sum(record["total_opportunities"] for record in recent_records) / len(recent_records)
            
            # 质量评分 (0-100)
            quality_score = (
                avg_success_rate * 60 +  # 成功率权重60%
                min(avg_opportunities_per_push / 10, 1) * 30 +  # 机会数量权重30%
                (1 if len(recent_records) >= 5 else len(recent_records) / 5) * 10  # 活跃度权重10%
            )
            
            # 质量等级
            if quality_score >= 80:
                quality_level = "优秀"
            elif quality_score >= 60:
                quality_level = "良好"
            elif quality_score >= 40:
                quality_level = "一般"
            else:
                quality_level = "需改进"
            
            return {
                "success": True,
                "quality_score": quality_score,
                "quality_level": quality_level,
                "metrics": {
                    "average_success_rate": avg_success_rate,
                    "average_opportunities_per_push": avg_opportunities_per_push,
                    "push_frequency": len(recent_records)
                },
                "assessment": f"推送质量{quality_level}，平均成功率{avg_success_rate:.1%}"
            }
            
        except Exception as e:
            logger.error(f"推送质量评估失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# 全局服务实例
real_opportunity_push_service = RealOpportunityPushService()
