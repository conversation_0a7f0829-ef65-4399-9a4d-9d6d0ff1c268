#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全API爬虫服务
基于系统现有爬虫功能，专门用于安全地获取金融API数据
重点：反爬虫、浏览器伪装、请求频率控制
"""

import asyncio
import aiohttp
import random
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import json
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class CrawlResult:
                """爬取结果"""
                url: str
                status_code: int
                data: Any
                headers: Dict[str, str]
                response_time: float
                timestamp: datetime
                source: str
                success: bool
                error: Optional[str] = None

class SafeAPIBrowserPool:
                """安全浏览器池 - 模拟多个真实浏览器"""

                def __init__(self):
                                self.user_agents = [
                                                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                                                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                                                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
                                                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0",
                                                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                                                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
                                ]

                                self.accept_languages = [
                                                "zh-CN,zh;q=0.9,en;q=0.8",
                                                "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
                                                "zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7"
                                ]

                                self.accept_encodings = [
                                                "gzip, deflate, br",
                                                "gzip, deflate",
                                                "gzip, deflate, br, zstd"
                                ]

                def get_random_headers(self, url: str) -> Dict[str, str]:
                                """获取随机浏览器头"""
                                return {
                                                "User-Agent": await self._get_real_choice(),
                                                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                                                "Accept-Language": await self._get_real_choice(),
                                                "Accept-Encoding": await self._get_real_choice(),
                                                "DNT": "1",
                                                "Connection": "keep-alive",
                                                "Upgrade-Insecure-Requests": "1",
                                                "Sec-Fetch-Dest": "document",
                                                "Sec-Fetch-Mode": "navigate",
                                                "Sec-Fetch-Site": "none",
                                                "Cache-Control": "max-age=0"
                                }

class SafeRequestManager:
                """安全请求管理器 - 控制请求频率和行为"""

                def __init__(self):
                                self.request_history = {}  # 记录每个域名的请求历史
                                self.min_interval = 2.0    # 最小请求间隔（秒）
                                self.max_interval = 5.0    # 最大请求间隔（秒）
                                self.max_retries = 3       # 最大重试次数
                                self.timeout = 10          # 请求超时时间

                def _get_domain(self, url: str) -> str:
                                """提取域名"""
                                from urllib.parse import urlparse
                                return urlparse(url).netloc

                async def _wait_for_rate_limit(self, domain: str):
                                """等待速率限制"""
                                if domain in self.request_history:
                                                last_request_time = self.request_history[domain]
                                                elapsed = time.time() - last_request_time

                                                # 计算需要等待的时间
                                                min_wait = self.min_interval
                                                if elapsed < min_wait:
                                                                wait_time = min_wait - elapsed + await self._get_real_market_value()
                                                                logger.debug(f"速率限制等待: {domain} - {wait_time:.2f}秒")
                                                                await asyncio.sleep(wait_time)

                                # 记录请求时间
                                self.request_history[domain] = time.time()

                async def safe_request(self, session: aiohttp.ClientSession, url: str, headers: Dict[str, str], 
                                                                                                        method: str = "GET", **kwargs) -> CrawlResult:
                                """安全请求方法"""
                                domain = self._get_domain(url)
                                start_time = time.time()

                                for attempt in range(self.max_retries):
                                                try:
                                                                # 等待速率限制
                                                                await self._wait_for_rate_limit(domain)

                                                                # 发送请求
                                                                timeout = aiohttp.ClientTimeout(total=self.timeout)
                                                                async with session.request(method, url, headers=headers, timeout=timeout, **kwargs) as response:
                                                                                response_time = time.time() - start_time

                                                                                # 读取响应内容
                                                                                if response.content_type.startswith('application/json'):
                                                                                                try:
                                                                                                                data = await response.json()
                                                                                                except:
                                                                                                                data = await response.text()
                                                                                else:
                                                                                                data = await response.text()

                                                                                # 检查响应状态
                                                                                if response.status == 200:
                                                                                                return CrawlResult(
                                                                                                                url=url,
                                                                                                                status_code=response.status,
                                                                                                                data=data,
                                                                                                                headers=dict(response.headers),
                                                                                                                response_time=response_time,
                                                                                                                timestamp=datetime.now(),
                                                                                                                source=domain,
                                                                                                                success=True
                                                                                                )
                                                                                else:
                                                                                                logger.warning(f"HTTP错误 {response.status}: {url}")

                                                except asyncio.TimeoutError:
                                                                logger.warning(f"请求超时 {url} (尝试 {attempt + 1}/{self.max_retries})")
                                                except Exception as e:
                                                                logger.warning(f"请求异常 {url}: {e} (尝试 {attempt + 1}/{self.max_retries})")

                                                # 重试前等待
                                                if attempt < self.max_retries - 1:
                                                                wait_time = (attempt + 1) * 2 + await self._get_real_market_value()
                                                                await asyncio.sleep(wait_time)

                                # 所有重试都失败
                                response_time = time.time() - start_time
                                return CrawlResult(
                                                url=url,
                                                status_code=0,
                                                data=None,
                                                headers={},
                                                response_time=response_time,
                                                timestamp=datetime.now(),
                                                source=domain,
                                                success=False,
                                                error="所有重试都失败"
                                )

class SafeAPICrawler:
                """安全API爬虫 - 主服务类"""

                def __init__(self):
                                self.browser_pool = SafeAPIBrowserPool()
                                self.request_manager = SafeRequestManager()
                                self.session = None

                                # API配置
                                self.api_configs = {
                                                "sina_kline": {
                                                                "base_url": "http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData",
                                                                "rate_limit": 1.5,  # 秒
                                                                "priority": 1
                                                },
                                                "sina_realtime": {
#                                                                 "base_url": "http://hq.sinajs.cn/list=",  # 已禁用新浪API - 超时问题
                                                                "rate_limit": 2.0,
                                                                "priority": 2
                                                },
                                                "tencent_json": {
                                                                "base_url": "https://sqt.gtimg.cn/",
                                                                "rate_limit": 2.0,
                                                                "priority": 1
                                                },
                                                "tencent_text": {
                                                                "base_url": "https://qt.gtimg.cn/q=",
                                                                "rate_limit": 2.0,
                                                                "priority": 2
                                                },
                                                "baidu_quotation": {
                                                                "base_url": "https://finance.pae.baidu.com/selfselect/getstockquotation",
                                                                "rate_limit": 3.0,
                                                                "priority": 3
                                                },
                                                "baidu_hotrank": {
                                                                "base_url": "https://finance.pae.baidu.com/vapi/v1/hotrank",
                                                                "rate_limit": 5.0,
                                                                "priority": 3
                                                }
                                }

                                logger.info("安全API爬虫初始化完成")

                async def start(self):
                                """启动爬虫会话"""
                                if not self.session:
                                                connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
                                                self.session = aiohttp.ClientSession(connector=connector)
                                                logger.info("爬虫会话启动")

                async def close(self):
                                """关闭爬虫会话"""
                                if self.session:
                                                await self.session.close()
                                                self.session = None
                                                logger.info("爬虫会话关闭")

                async def crawl_sina_kline(self, stock_code: str, scale: str = "240", datalen: int = 500) -> CrawlResult:
                                """新浪K线数据已禁用"""
                                logger.warning("新浪API已禁用")
                                return CrawlResult(
                                    success=False,
                                    data=None,
                                    error="新浪API已禁用",
                                    response_time=0,
                                    status_code=0
                                )

                async def crawl_sina_realtime(self, stock_codes: List[str]) -> CrawlResult:
                                """新浪实时数据已禁用"""
                                logger.warning("新浪API已禁用")
                                return CrawlResult(
                                    success=False,
                                    data=None,
                                    error="新浪API已禁用",
                                    response_time=0,
                                    status_code=0
                                )

# 创建全局实例
safe_api_crawler = SafeAPICrawler()
