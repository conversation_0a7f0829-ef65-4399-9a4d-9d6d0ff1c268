from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开阳星股票选择服务
负责股票筛选和投资组合构建
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class StockCandidate:
    """股票候选"""
    stock_code: str
    stock_name: str
    selection_score: float
    selection_reasons: List[str]
    risk_level: str
    expected_return: float
    market_cap: Optional[float] = None
    pe_ratio: Optional[float] = None
    pb_ratio: Optional[float] = None

class StockSelectionService:
    """股票选择服务"""
    
    def __init__(self):
        self.service_name = "开阳星股票选择服务"
        self.version = "Professional v2.0"
        
        # 选股策略配置
        self.selection_strategies = {
            "value": {"weight": 0.3, "enabled": True},      # 价值投资
            "growth": {"weight": 0.25, "enabled": True},    # 成长投资
            "momentum": {"weight": 0.2, "enabled": True},   # 动量投资
            "quality": {"weight": 0.25, "enabled": True}    # 质量投资
        }
        
        # 筛选标准
        self.selection_criteria = {
            "min_market_cap": 10000000000,  # 最小市值100亿
            "max_pe_ratio": 50,             # 最大PE比率
            "min_liquidity": 1000000,       # 最小日成交量
            "max_risk_level": "high"        # 最大风险等级
        }
        
        # 学习系统
        self.learning_insights = []  # 存储从瑶光星接收的学习洞察
        self.learning_history = []   # 学习历史记录
        self.strategy_improvements = {}  # 策略改进记录

        # 直接发送天权星历史
        self.direct_tianquan_history = []

        # 自我反思历史
        self.self_reflection_history = []

        logger.info(f" {self.service_name} v{self.version} 初始化完成")
    
    async def select_stocks(self, selection_context: Dict[str, Any]) -> Dict[str, Any]:
        """选择股票"""
        try:
            selection_type = selection_context.get("selection_type", "comprehensive")
            target_count = selection_context.get("target_count", 10)
            market_context = selection_context.get("market_context", {})
            
            # 获取股票池
            stock_pool = await self._get_stock_pool(selection_context)
            logger.info(f"获取到股票池: {len(stock_pool)} 只股票")

            if not stock_pool:
                return {
                    "success": False,
                    "error": "无法获取股票池",
                    "timestamp": datetime.now().isoformat()
                }
            
            # 应用筛选策略
            candidates = []
            for stock_info in stock_pool:
                candidate = await self._evaluate_stock_candidate(
                    stock_info, market_context
                )
                if candidate:
                    candidates.append(candidate)
            
            # 排序和筛选
            selected_stocks = self._rank_and_select_stocks(
                candidates, target_count, selection_type
            )
            
            # 构建投资组合建议
            portfolio_suggestion = self._build_portfolio_suggestion(
                selected_stocks, selection_context
            )
            
            return {
                "success": True,
                "selection_result": {
                    "selected_stocks": [self._candidate_to_dict(stock) for stock in selected_stocks],
                    "total_candidates": len(candidates),
                    "selection_count": len(selected_stocks),
                    "selection_type": selection_type,
                    "selection_time": datetime.now().isoformat()
                },
                "portfolio_suggestion": portfolio_suggestion,
                "market_analysis": self._analyze_market_conditions(market_context),
                "selector": "开阳星股票选择服务"
            }
            
        except Exception as e:
            logger.error(f"股票选择失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _get_stock_pool(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取股票池 - 从真实股票数据库"""
        try:
            import sqlite3
            import os

            # 使用统一的数据库路径
            db_paths = [
                get_database_path("stock_database")
            ]

            stock_pool = []

            for db_path in db_paths:
                if os.path.exists(db_path):
                    logger.info(f"📊 使用股票数据库: {db_path}")

                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()

                    # 查询股票基本信息
                    try:
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                        tables = [row[0] for row in cursor.fetchall()]
                        logger.info(f"数据库表: {tables}")

                        # 尝试不同的表名
                        if 'stock_info' in tables:
                            logger.info(f"使用 stock_info 表查询股票")
                            cursor.execute("SELECT stock_code, stock_name, exchange, industry FROM stock_info LIMIT 100")
                        elif 'stock_basic_info' in tables:
                            logger.info(f"使用 stock_basic_info 表查询股票")
                            cursor.execute("SELECT stock_code, stock_name, market, industry FROM stock_basic_info LIMIT 100")
                        elif 'stocks' in tables:
                            logger.info(f"使用 stocks 表查询股票")
                            cursor.execute("SELECT code, name, market, industry FROM stocks LIMIT 100")
                        elif 'a_stock_list' in tables:
                            logger.info(f"使用 a_stock_list 表查询股票")
                            cursor.execute("SELECT stock_code, stock_name, exchange, industry FROM a_stock_list LIMIT 100")
                        else:
                            # 使用第一个表
                            if tables:
                                table_name = tables[0]
                                logger.info(f"使用第一个表 {table_name} 查询股票")
                                cursor.execute(f"SELECT * FROM {table_name} LIMIT 100")
                            else:
                                logger.warning(f"数据库 {db_path} 没有可用的表")
                                continue

                        rows = cursor.fetchall()
                        logger.info(f"从数据库查询到 {len(rows)} 条股票记录")

                        for row in rows:
                            try:
                                if len(row) >= 2:
                                    stock_code = str(row[0]) if row[0] is not None else ""
                                    stock_name = str(row[1]) if row[1] is not None else "未知股票"
                                    market = str(row[2]) if len(row) > 2 and row[2] is not None else "未知"
                                    industry = str(row[3]) if len(row) > 3 and row[3] is not None else "未分类"

                                    # 跳过无效的股票代码
                                    if not stock_code or len(stock_code) < 6:
                                        continue

                                    # 转换股票代码格式
                                    if isinstance(stock_code, str) and '.' not in stock_code:
                                        if stock_code.startswith(('000', '002', '300')):
                                            stock_code = f"{stock_code}.XSHE"
                                        elif stock_code.startswith(('600', '601', '603', '605', '688')):
                                            stock_code = f"{stock_code}.XSHG"
                                    elif not isinstance(stock_code, str):
                                        stock_code = str(stock_code)
                                        if '.' not in stock_code:
                                            if stock_code.startswith(('000', '002', '300')):
                                                stock_code = f"{stock_code}.XSHE"
                                            elif stock_code.startswith(('600', '601', '603', '605', '688')):
                                                stock_code = f"{stock_code}.XSHG"

                                    stock_pool.append({
                                        "code": stock_code,
                                        "name": stock_name,
                                        "market": market,
                                        "industry": industry,
                                        "market_cap": 50000000000,  # 默认500亿市值
                                        "pe_ratio": 15.0,
                                        "pb_ratio": 2.0,
                                        "current_price": 20.0,
                                        "volume": 10000000,
                                        "change_percent": 0.0
                                    })
                            except Exception as e:
                                logger.warning(f"处理股票记录失败: {e}")
                                continue

                        conn.close()

                        if stock_pool:
                            logger.info(f"✅ 从数据库获取到 {len(stock_pool)} 只股票")
                            return stock_pool

                    except Exception as e:
                        logger.warning(f"查询数据库失败: {e}")
                        conn.close()
                        continue

            # 如果所有数据库都失败，使用备用股票列表
            logger.warning("所有数据库查询失败，使用备用股票列表")
            test_stocks = [
                {"code": "000001.XSHE", "name": "平安银行", "market_cap": 200000000000},
                {"code": "000002.XSHE", "name": "万科A", "market_cap": 150000000000},
                {"code": "600036.XSHG", "name": "招商银行", "market_cap": 800000000000},
                {"code": "600519.XSHG", "name": "贵州茅台", "market_cap": 2000000000000},
                {"code": "000858.XSHE", "name": "五粮液", "market_cap": 500000000000}
            ]
            logger.info(f"使用备用股票列表: {len(test_stocks)} 只股票")
            return test_stocks

        except Exception as e:
            logger.error(f"获取股票池失败: {e}")
            # 返回备用股票列表
            test_stocks = [
                {"code": "000001.XSHE", "name": "平安银行", "market_cap": 200000000000},
                {"code": "000002.XSHE", "name": "万科A", "market_cap": 150000000000},
                {"code": "600036.XSHG", "name": "招商银行", "market_cap": 800000000000},
                {"code": "600519.XSHG", "name": "贵州茅台", "market_cap": 2000000000000},
                {"code": "000858.XSHE", "name": "五粮液", "market_cap": 500000000000}
            ]

            logger.info(f"使用备用股票列表: {len(test_stocks)} 只股票")
            return test_stocks

    async def _evaluate_stock_candidate(self, stock_info: Dict[str, Any], 
                                       market_context: Dict[str, Any]) -> Optional[StockCandidate]:
        """评估股票候选"""
        try:
            stock_code = stock_info["code"]
            stock_name = stock_info["name"]
            
            # 基础筛选
            if not self._passes_basic_screening(stock_info):
                return None
            
            # 计算各策略评分
            value_score = self._calculate_value_score(stock_info)
            growth_score = self._calculate_growth_score(stock_info)
            momentum_score = self._calculate_momentum_score(stock_info)
            quality_score = self._calculate_quality_score(stock_info)
            
            # 计算综合评分
            selection_score = (
                value_score * self.selection_strategies["value"]["weight"] +
                growth_score * self.selection_strategies["growth"]["weight"] +
                momentum_score * self.selection_strategies["momentum"]["weight"] +
                quality_score * self.selection_strategies["quality"]["weight"]
            )
            
            # 生成选择理由
            selection_reasons = self._generate_selection_reasons(
                value_score, growth_score, momentum_score, quality_score
            )
            
            # 评估风险等级
            risk_level = self._assess_stock_risk(stock_info)
            
            # 预期收益
            expected_return = self._estimate_expected_return(
                selection_score, market_context
            )
            
            # 提取财务指标
            fundamentals = stock_info.get("fundamentals", {})
            pe_ratio = fundamentals.get("pe_ratio")
            pb_ratio = fundamentals.get("pb_ratio")
            market_cap = stock_info.get("market_cap")
            
            return StockCandidate(
                stock_code=stock_code,
                stock_name=stock_name,
                selection_score=selection_score,
                selection_reasons=selection_reasons,
                risk_level=risk_level,
                expected_return=expected_return,
                market_cap=market_cap,
                pe_ratio=pe_ratio,
                pb_ratio=pb_ratio
            )
            
        except Exception as e:
            logger.warning(f"评估股票候选失败: {e}")
            return None
    
    def _passes_basic_screening(self, stock_info: Dict[str, Any]) -> bool:
        """基础筛选"""
        try:
            # 市值筛选
            market_cap = stock_info.get("market_cap", 0)
            if market_cap < self.selection_criteria["min_market_cap"]:
                return False
            
            # PE比率筛选
            fundamentals = stock_info.get("fundamentals", {})
            pe_ratio = fundamentals.get("pe_ratio", 0)
            if pe_ratio > self.selection_criteria["max_pe_ratio"] and pe_ratio > 0:
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"基础筛选失败: {e}")
            return False
    
    def _calculate_value_score(self, stock_info: Dict[str, Any]) -> float:
        """计算价值投资评分"""
        try:
            fundamentals = stock_info.get("fundamentals", {})
            
            pe_ratio = fundamentals.get("pe_ratio", 20)
            pb_ratio = fundamentals.get("pb_ratio", 2)
            
            # PE评分（越低越好）
            pe_score = max(0, min(1, (30 - pe_ratio) / 30)) if pe_ratio > 0 else 0.5
            
            # PB评分（越低越好）
            pb_score = max(0, min(1, (5 - pb_ratio) / 5)) if pb_ratio > 0 else 0.5
            
            # 综合价值评分
            value_score = (pe_score + pb_score) / 2
            
            return value_score
            
        except Exception as e:
            logger.warning(f"价值评分计算失败: {e}")
            return 0.5
    
    def _calculate_growth_score(self, stock_info: Dict[str, Any]) -> float:
        """计算成长投资评分"""
        try:
            # 实际应该基于收入增长率、利润增长率等
            
            market_cap = stock_info.get("market_cap", 0)
            
            # 基于市值的成长潜力评估
            if market_cap < 50000000000:  # 500亿以下
                growth_score = 0.8
            elif market_cap < 200000000000:  # 2000亿以下
                growth_score = 0.6
            else:
                growth_score = 0.4
            
            return growth_score
            
        except Exception as e:
            logger.warning(f"成长评分计算失败: {e}")
            return 0.5
    
    def _calculate_momentum_score(self, stock_info: Dict[str, Any]) -> float:
        """计算动量投资评分"""
        try:
            # 实际应该基于价格趋势、成交量等
            
            # 这里返回随机评分，实际应该基于技术分析
            import random
            momentum_score = 0.3 + random.random() * 0.4  # 0.3-0.7之间
            
            return momentum_score
            
        except Exception as e:
            logger.warning(f"动量评分计算失败: {e}")
            return 0.5
    
    def _calculate_quality_score(self, stock_info: Dict[str, Any]) -> float:
        """计算质量投资评分"""
        try:
            # 实际应该基于ROE、ROA、负债率等
            
            market_cap = stock_info.get("market_cap", 0)
            
            # 大市值公司通常质量更高
            if market_cap > 1000000000000:  # 万亿以上
                quality_score = 0.9
            elif market_cap > 500000000000:  # 5000亿以上
                quality_score = 0.8
            elif market_cap > 100000000000:  # 1000亿以上
                quality_score = 0.7
            else:
                quality_score = 0.6
            
            return quality_score
            
        except Exception as e:
            logger.warning(f"质量评分计算失败: {e}")
            return 0.5
    
    def _generate_selection_reasons(self, value_score: float, growth_score: float,
                                  momentum_score: float, quality_score: float) -> List[str]:
        """生成选择理由"""
        reasons = []
        
        if value_score > 0.7:
            reasons.append("估值合理，具备价值投资潜力")
        if growth_score > 0.7:
            reasons.append("成长性良好，发展前景广阔")
        if momentum_score > 0.7:
            reasons.append("技术面强势，动量良好")
        if quality_score > 0.8:
            reasons.append("公司质地优秀，基本面扎实")
        
        if not reasons:
            reasons.append("综合评分良好，值得关注")
        
        return reasons[:3]  # 最多返回3个理由
    
    def _assess_stock_risk(self, stock_info: Dict[str, Any]) -> str:
        """评估股票风险"""
        try:
            market_cap = stock_info.get("market_cap", 0)
            
            if market_cap > 1000000000000:  # 万亿以上
                return "低风险"
            elif market_cap > 100000000000:  # 千亿以上
                return "中等风险"
            else:
                return "高风险"
                
        except Exception as e:
            logger.warning(f"风险评估失败: {e}")
            return "中等风险"
    
    def _estimate_expected_return(self, selection_score: float, market_context: Dict[str, Any]) -> float:
        """估算预期收益"""
        try:
            # 基于选择评分的预期收益
            base_return = selection_score * 0.2  # 最高20%
            
            # 考虑市场环境
            market_sentiment = market_context.get("sentiment", 0.5)
            market_adjustment = (market_sentiment - 0.5) * 0.1
            
            expected_return = base_return + market_adjustment
            
            return max(0.0, min(0.3, expected_return))  # 限制在0-30%之间
            
        except Exception as e:
            logger.warning(f"预期收益估算失败: {e}")
            return 0.1
    
    def _rank_and_select_stocks(self, candidates: List[StockCandidate], 
                               target_count: int, selection_type: str) -> List[StockCandidate]:
        """排序和选择股票"""
        try:
            # 按评分排序
            sorted_candidates = sorted(candidates, key=lambda x: x.selection_score, reverse=True)
            
            # 根据选择类型进行筛选
            if selection_type == "conservative":
                # 保守型：优先选择低风险股票
                filtered_candidates = [c for c in sorted_candidates if c.risk_level in ["低风险", "中等风险"]]
            elif selection_type == "aggressive":
                # 激进型：优先选择高收益潜力股票
                filtered_candidates = sorted(sorted_candidates, key=lambda x: x.expected_return, reverse=True)
            else:
                # 综合型：平衡风险和收益
                filtered_candidates = sorted_candidates
            
            # 选择前N只股票
            selected = filtered_candidates[:target_count]
            
            return selected
            
        except Exception as e:
            logger.warning(f"股票排序选择失败: {e}")
            return candidates[:target_count]
    
    def _build_portfolio_suggestion(self, selected_stocks: List[StockCandidate], 
                                   context: Dict[str, Any]) -> Dict[str, Any]:
        """构建投资组合建议"""
        try:
            total_stocks = len(selected_stocks)
            
            if total_stocks == 0:
                return {"error": "没有选中的股票"}
            
            # 计算权重分配
            portfolio_weights = []
            total_score = sum(stock.selection_score for stock in selected_stocks)
            
            for stock in selected_stocks:
                weight = stock.selection_score / total_score if total_score > 0 else 1.0 / total_stocks
                portfolio_weights.append({
                    "stock_code": stock.stock_code,
                    "stock_name": stock.stock_name,
                    "weight": round(weight, 3),
                    "risk_level": stock.risk_level,
                    "expected_return": round(stock.expected_return, 3)
                })
            
            # 计算组合预期收益和风险
            portfolio_return = sum(w["weight"] * w["expected_return"] for w in portfolio_weights)
            
            # 风险分散度
            risk_levels = [stock.risk_level for stock in selected_stocks]
            diversification_score = len(set(risk_levels)) / len(risk_levels) if risk_levels else 0
            
            return {
                "portfolio_weights": portfolio_weights,
                "portfolio_metrics": {
                    "expected_return": round(portfolio_return, 3),
                    "diversification_score": round(diversification_score, 3),
                    "total_stocks": total_stocks
                },
                "rebalancing_suggestion": "建议每季度重新评估组合配置"
            }
            
        except Exception as e:
            logger.warning(f"投资组合构建失败: {e}")
            return {"error": str(e)}
    
    def _analyze_market_conditions(self, market_context: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场条件"""
        try:
            return {
                "market_sentiment": market_context.get("sentiment", 0.5),
                "market_trend": market_context.get("trend", "neutral"),
                "volatility_level": market_context.get("volatility", "medium"),
                "recommendation": "根据当前市场条件，建议采用均衡配置策略"
            }
            
        except Exception as e:
            logger.warning(f"市场条件分析失败: {e}")
            return {"error": str(e)}
    
    def _candidate_to_dict(self, candidate: StockCandidate) -> Dict[str, Any]:
        """将候选股票转换为字典"""
        return {
            "stock_code": candidate.stock_code,
            "stock_name": candidate.stock_name,
            "selection_score": round(candidate.selection_score, 3),
            "selection_reasons": candidate.selection_reasons,
            "risk_level": candidate.risk_level,
            "expected_return": round(candidate.expected_return, 3),
            "market_cap": candidate.market_cap,
            "pe_ratio": candidate.pe_ratio,
            "pb_ratio": candidate.pb_ratio
        }

    async def receive_learning_insights(self, learning_data: Dict[str, Any]) -> Dict[str, Any]:
        """接收来自瑶光星的学习洞察"""
        try:
            source = learning_data.get("source", "未知来源")
            insights = learning_data.get("insights", [])

            logger.info(f"📚 接收到来自 {source} 的 {len(insights)} 个学习洞察")

            # 处理每个洞察
            processed_insights = []
            for insight in insights:
                processed_insight = await self._process_learning_insight(insight)
                if processed_insight:
                    processed_insights.append(processed_insight)

            # 保存学习洞察
            learning_record = {
                "timestamp": datetime.now().isoformat(),
                "source": source,
                "original_insights_count": len(insights),
                "processed_insights_count": len(processed_insights),
                "insights": processed_insights
            }

            self.learning_insights.extend(processed_insights)
            self.learning_history.append(learning_record)

            # 限制历史记录数量
            if len(self.learning_history) > 50:
                self.learning_history = self.learning_history[-50:]

            if len(self.learning_insights) > 200:
                self.learning_insights = self.learning_insights[-200:]

            # 应用学习洞察到选股策略
            strategy_updates = await self._apply_learning_insights(processed_insights)

            logger.info(f"✅ 成功处理 {len(processed_insights)} 个学习洞察，应用了 {len(strategy_updates)} 个策略更新")

            return {
                "success": True,
                "message": f"成功接收并处理 {len(processed_insights)} 个学习洞察",
                "processed_insights": len(processed_insights),
                "strategy_updates": len(strategy_updates),
                "total_insights": len(self.learning_insights),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"接收学习洞察失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _process_learning_insight(self, insight: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理单个学习洞察"""
        try:
            pattern_name = insight.get("pattern_name", "")
            success_rate = insight.get("success_rate", 0)
            confidence_score = insight.get("confidence_score", 0)
            key_indicators = insight.get("key_indicators", [])

            # 只处理高质量的洞察
            if success_rate < 0.6 or confidence_score < 0.5:
                logger.debug(f"跳过低质量洞察: {pattern_name} (成功率: {success_rate}, 置信度: {confidence_score})")
                return None

            # 转换为开阳星内部格式
            processed_insight = {
                "insight_id": f"yaoguang_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.learning_insights)}",
                "pattern_name": pattern_name,
                "success_rate": success_rate,
                "confidence_score": confidence_score,
                "key_indicators": key_indicators,
                "market_conditions": insight.get("market_conditions", []),
                "risk_level": insight.get("risk_level", "中等"),
                "learning_source": "瑶光星量化研究",
                "received_time": datetime.now().isoformat(),
                "applied": False
            }

            logger.debug(f"✅ 处理洞察: {pattern_name} (成功率: {success_rate:.2f}, 置信度: {confidence_score:.2f})")

            return processed_insight

        except Exception as e:
            logger.error(f"处理学习洞察失败: {e}")
            return None

    async def _apply_learning_insights(self, insights: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """应用学习洞察到选股策略"""
        try:
            strategy_updates = []

            for insight in insights:
                pattern_name = insight.get("pattern_name", "")
                success_rate = insight.get("success_rate", 0)
                key_indicators = insight.get("key_indicators", [])

                # 根据洞察类型应用不同的策略更新
                if "行业" in pattern_name:
                    update = await self._apply_industry_insight(insight)
                elif "技术" in pattern_name:
                    update = await self._apply_technical_insight(insight)
                elif "风险收益" in pattern_name:
                    update = await self._apply_risk_return_insight(insight)
                else:
                    update = await self._apply_general_insight(insight)

                if update:
                    strategy_updates.append(update)
                    insight["applied"] = True

                    # 记录策略改进
                    improvement_id = f"improvement_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    self.strategy_improvements[improvement_id] = {
                        "insight_id": insight.get("insight_id"),
                        "pattern_name": pattern_name,
                        "improvement_type": update.get("type"),
                        "improvement_details": update.get("details"),
                        "expected_impact": update.get("expected_impact"),
                        "applied_time": datetime.now().isoformat()
                    }

            return strategy_updates

        except Exception as e:
            logger.error(f"应用学习洞察失败: {e}")
            return []

    async def _apply_technical_insight(self, insight: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """应用技术洞察"""
        try:
            key_indicators = insight.get("key_indicators", [])
            success_rate = insight.get("success_rate", 0)

            # 调整技术分析权重
            if "上涨趋势" in key_indicators:
                momentum_weight_adjustment = min(success_rate * 0.05, 0.03)

                # 更新动量策略权重
                original_weight = self.selection_strategies["momentum"]["weight"]
                new_weight = min(original_weight + momentum_weight_adjustment, 0.4)  # 最大40%
                self.selection_strategies["momentum"]["weight"] = new_weight

                logger.info(f"📈 基于瑶光星技术洞察，调整动量策略权重: {original_weight:.2%} -> {new_weight:.2%}")

                return {
                    "type": "momentum_strategy_adjustment",
                    "details": {
                        "original_weight": original_weight,
                        "new_weight": new_weight,
                        "adjustment": momentum_weight_adjustment,
                        "reason": f"瑶光星发现上涨趋势模式成功率达 {success_rate:.2%}"
                    },
                    "expected_impact": "提升趋势向上股票的选择权重"
                }

        except Exception as e:
            logger.error(f"应用技术洞察失败: {e}")

        return None

    async def _apply_industry_insight(self, insight: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """应用行业洞察"""
        try:
            key_indicators = insight.get("key_indicators", [])
            success_rate = insight.get("success_rate", 0)

            # 提取行业信息
            industry = None
            for indicator in key_indicators:
                if "行业" in indicator:
                    industry = indicator.replace("行业", "")
                    break

            if industry:
                logger.info(f"📈 基于瑶光星洞察，关注 {industry} 行业 (成功率: {success_rate:.2%})")

                return {
                    "type": "industry_focus_adjustment",
                    "details": {
                        "industry": industry,
                        "success_rate": success_rate,
                        "reason": f"瑶光星学习发现该行业成功率达 {success_rate:.2%}"
                    },
                    "expected_impact": "提升该行业股票的关注度"
                }

        except Exception as e:
            logger.error(f"应用行业洞察失败: {e}")

        return None

    async def _apply_risk_return_insight(self, insight: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """应用风险收益洞察"""
        try:
            key_indicators = insight.get("key_indicators", [])
            success_rate = insight.get("success_rate", 0)

            if "高收益" in key_indicators and "低回撤" in key_indicators:
                # 调整质量策略权重
                quality_weight_adjustment = min(success_rate * 0.05, 0.03)

                original_weight = self.selection_strategies["quality"]["weight"]
                new_weight = min(original_weight + quality_weight_adjustment, 0.4)
                self.selection_strategies["quality"]["weight"] = new_weight

                logger.info(f"📈 基于瑶光星风险收益洞察，调整质量策略权重: {original_weight:.2%} -> {new_weight:.2%}")

                return {
                    "type": "quality_strategy_adjustment",
                    "details": {
                        "original_weight": original_weight,
                        "new_weight": new_weight,
                        "adjustment": quality_weight_adjustment,
                        "reason": f"瑶光星发现高收益低风险模式成功率达 {success_rate:.2%}"
                    },
                    "expected_impact": "提升高质量股票的选择权重"
                }

        except Exception as e:
            logger.error(f"应用风险收益洞察失败: {e}")

        return None

    async def _apply_general_insight(self, insight: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """应用通用洞察"""
        try:
            pattern_name = insight.get("pattern_name", "")
            success_rate = insight.get("success_rate", 0)

            # 通用策略调整
            if success_rate > 0.8:  # 高成功率洞察
                logger.info(f"📈 应用高成功率通用洞察: {pattern_name} (成功率: {success_rate:.2%})")

                return {
                    "type": "general_strategy_enhancement",
                    "details": {
                        "pattern_name": pattern_name,
                        "success_rate": success_rate,
                        "enhancement": "提升相关指标权重"
                    },
                    "expected_impact": "整体提升选股质量"
                }

        except Exception as e:
            logger.error(f"应用通用洞察失败: {e}")

        return None

    def get_learning_status(self) -> Dict[str, Any]:
        """获取学习状态"""
        return {
            "total_insights": len(self.learning_insights),
            "applied_insights": len([i for i in self.learning_insights if i.get("applied", False)]),
            "learning_sessions": len(self.learning_history),
            "strategy_improvements": len(self.strategy_improvements),
            "current_strategy_weights": self.selection_strategies.copy(),
            "last_learning_time": self.learning_history[-1].get("timestamp") if self.learning_history else None
        }

    async def select_learning_stocks(self, count: int = 1) -> Dict[str, Any]:
        """为学习系统选择股票 - 瑶光星学习专用"""
        try:
            logger.info(f"🎓 开阳星为学习系统选择 {count} 只股票")

            # 学习模式的选股配置
            learning_context = {
                "selection_type": "learning_mode",
                "target_count": count,
                "market_context": {
                    "sentiment": 0.6,  # 中性偏乐观
                    "volatility": "normal",
                    "learning_purpose": True
                },
                "learning_purpose": True,
                "requester": "瑶光星学习系统"
            }

            # 调用标准选股方法
            selection_result = await self.select_stocks(learning_context)
            return selection_result

        except Exception as e:
            logger.error(f"学习选股失败: {e}")
            return {
                "success": False,
                "error": str(e),

            }

    async def select_portfolio_stocks(self, count: int = 10) -> Dict[str, Any]:
        """为投资组合选择股票 - 实盘交易专用"""
        try:
            logger.info(f"💼 开阳星为投资组合选择 {count} 只股票")

            # 投资组合模式的选股配置
            portfolio_context = {
                "selection_type": "portfolio_mode",
                "target_count": count,
                "market_context": {
                    "sentiment": 0.7,  # 偏乐观
                    "volatility": "normal",
                    "portfolio_purpose": True
                },
                "portfolio_purpose": True,
                "requester": "投资组合管理系统"
            }

            # 调用标准选股方法
            selection_result = await self.select_stocks(portfolio_context)
            return selection_result

        except Exception as e:
            logger.error(f"投资组合选股失败: {e}")
            return {
                "success": False,
                "error": str(e),

            }

    async def select_stocks_for_learning(self) -> Dict[str, Any]:
        """为学习系统选择股票 - 瑶光星学习专用（兼容性方法）"""
        return await self.select_learning_stocks(count=1)

    async def record_learning_experience(self, learning_experience: Dict[str, Any]) -> bool:
        """记录学习经验"""
        try:
            stock_code = learning_experience.get("stock_code")
            logger.info(f"📝 开阳星记录学习经验: {stock_code}")

            # 添加到学习历史
            self.learning_history.append({
                "stock_code": stock_code,
                "learning_date": learning_experience.get("learning_date"),
                "practice_performance": learning_experience.get("practice_performance", {}),
                "research_performance": learning_experience.get("research_performance", {}),
                "overall_success": learning_experience.get("overall_success", False),
                "key_insights": learning_experience.get("key_insights", {}),
                "recorded_at": datetime.now().isoformat()
            })

            # 更新学习洞察
            if learning_experience.get("overall_success"):
                success_insight = {
                    "type": "learning_success",
                    "stock_code": stock_code,
                    "insight": f"成功学习{stock_code}，积累了宝贵经验",
                    "confidence": 0.8,
                    "source": "学习系统反馈",
                    "timestamp": datetime.now().isoformat(),
                    "applied": False
                }
                self.learning_insights.append(success_insight)

            # 提取改进建议
            optimization_results = learning_experience.get("optimization_results", {})
            if optimization_results.get("applied_optimizations"):
                for optimization in optimization_results["applied_optimizations"]:
                    improvement_insight = {
                        "type": "strategy_improvement",
                        "improvement": optimization,
                        "source": f"学习{stock_code}的反思",
                        "confidence": 0.7,
                        "timestamp": datetime.now().isoformat(),
                        "applied": False
                    }
                    self.strategy_improvements.append(improvement_insight)

            logger.info(f"✅ 开阳星学习经验记录完成: {stock_code}")
            return True

        except Exception as e:
            logger.error(f"开阳星记录学习经验失败: {e}")
            return False

    def get_learned_stocks(self) -> List[str]:
        """获取已学习的股票列表"""
        try:
            learned_stocks = []
            for history in self.learning_history:
                stock_code = history.get("stock_code")
                if stock_code and stock_code not in learned_stocks:
                    learned_stocks.append(stock_code)
            return learned_stocks
        except Exception as e:
            logger.error(f"获取已学习股票列表失败: {e}")
            return []

    async def direct_to_tianquan(self, selected_stocks: List[Dict[str, Any]],
                               selection_context: Dict[str, Any]) -> Dict[str, Any]:
        """直接将选股结果发送给天权星（新的统一数据流转）"""
        try:
            logger.info("📤 开阳星直接将选股结果发送给天权星")

            # 调用天权星战略决策服务
            from roles.tianquan_star.services.strategic_decision_service import StrategicDecisionService

            strategic_service = StrategicDecisionService()

            # 构建发送给天权星的数据包
            tianquan_data_packet = {
                "source": "开阳星选股服务",
                "selection_time": datetime.now().isoformat(),
                "selection_context": selection_context,
                "selected_stocks": selected_stocks,
                "total_candidates": selection_context.get("total_candidates", 0),
                "selection_criteria": selection_context.get("selection_criteria", {}),
                "market_context": selection_context.get("market_context", {}),
                "requester": selection_context.get("requester", "unknown")
            }

            # 直接发送给天权星
            tianquan_response = await strategic_service.receive_kaiyang_selection(tianquan_data_packet)

            if tianquan_response.get("success"):
                logger.info("✅ 开阳星选股结果已成功发送给天权星")

                # 记录直接发送历史
                self.direct_tianquan_history.append({
                    "send_time": datetime.now().isoformat(),
                    "stocks_count": len(selected_stocks),
                    "selection_type": selection_context.get("selection_type", "unknown"),
                    "tianquan_response": tianquan_response,
                    "success": True
                })

                return {
                    "success": True,
                    "message": "选股结果已直接发送给天权星",
                    "tianquan_response": tianquan_response,
                    "data_flow": "开阳星 → 天权星 (直接)"
                }
            else:
                logger.warning(f"⚠️ 天权星接收选股结果失败: {tianquan_response.get('error', 'Unknown error')}")
                return {
                    "success": False,
                    "error": tianquan_response.get('error', 'Unknown error'),
                    "data_flow": "开阳星 → 天权星 (失败)"
                }

        except Exception as e:
            logger.error(f"开阳星直接发送给天权星失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "data_flow": "开阳星 → 天权星 (异常)"
            }

    async def conduct_self_reflection(self, reflection_data: Dict[str, Any]) -> Dict[str, Any]:
        """进行自我反思（开阳星闭环内反思）"""
        try:
            logger.info("🤔 开阳星进行自我反思")

            # 调用自我反思服务
            from roles.kaiyang_star.services.self_reflection_service import kaiyang_self_reflection_service

            reflection_type = reflection_data.get("reflection_type", "unknown")

            if reflection_type == "live_trading":
                # 实盘交易反思
                reflection_result = await kaiyang_self_reflection_service.conduct_live_trading_reflection(
                    reflection_data.get("trading_session", {})
                )
            elif reflection_type == "learning":
                # 学习模式反思
                reflection_result = await kaiyang_self_reflection_service.conduct_learning_reflection(
                    reflection_data.get("learning_session", {})
                )
            elif reflection_type == "comparative":
                # 对比反思
                reflection_result = await kaiyang_self_reflection_service.conduct_comparative_reflection(
                    reflection_data.get("practice_results", {}),
                    reflection_data.get("research_results", {})
                )
            else:
                logger.warning(f"⚠️ 未知的反思类型: {reflection_type}")
                return {
                    "success": False,
                    "error": f"未知的反思类型: {reflection_type}"
                }

            if reflection_result.get("success", True):
                logger.info("✅ 开阳星自我反思完成")

                # 记录反思历史
                self.self_reflection_history.append({
                    "reflection_time": datetime.now().isoformat(),
                    "reflection_type": reflection_type,
                    "reflection_result": reflection_result,
                    "success": True
                })

                return {
                    "success": True,
                    "message": "开阳星自我反思完成",
                    "reflection_result": reflection_result,
                    "reflection_type": reflection_type
                }
            else:
                logger.error(f"开阳星自我反思失败: {reflection_result.get('error', 'Unknown error')}")
                return {
                    "success": False,
                    "error": reflection_result.get('error', 'Unknown error'),
                    "reflection_type": reflection_type
                }

        except Exception as e:
            logger.error(f"开阳星自我反思异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "reflection_type": reflection_data.get("reflection_type", "unknown")
            }

# 全局股票选择服务实例
stock_selection_service = StockSelectionService()

__all__ = ["stock_selection_service", "StockSelectionService", "StockCandidate"]
