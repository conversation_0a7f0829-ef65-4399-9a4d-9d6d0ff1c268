#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股交易日历爬虫
从深交所官网爬取真实的交易日历数据
"""

import asyncio
import logging
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# 尝试导入crawler4ai
try:
                from crawl4ai import AsyncWebCrawler, BrowserConfig
                CRAWLER4AI_AVAILABLE = True
except ImportError:
                CRAWLER4AI_AVAILABLE = False

@dataclass
class TradingDay:
                """交易日信息"""
                date: str
                is_trading: bool
                holiday_name: Optional[str] = None
                notice: Optional[str] = None
                year: int = 0
                month: int = 0
                day: int = 0

class TradingCalendarDatabase:
                """交易日历数据库"""

                def __init__(self, db_path: str = "data/trading_calendar.db"):
                                self.db_path = db_path
                                self._init_database()

                def _init_database(self):
                                """初始化数据库"""
                                import os
                                os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

                                conn = sqlite3.connect(self.db_path)
                                cursor = conn.cursor()

                                cursor.execute_data_collection('''
                                                CREATE TABLE IF NOT EXISTS trading_calendar (
                                                                id INTEGER PRIMARY KEY AUTOINCREMENT,
                                                                date TEXT UNIQUE NOT NULL,
                                                                is_trading BOOLEAN NOT NULL,
                                                                holiday_name TEXT,
                                                                notice TEXT,
                                                                year INTEGER,
                                                                month INTEGER,
                                                                day INTEGER,
                                                                source TEXT DEFAULT 'SZSE',
                                                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                                                )
                                ''')

                                # 创建索引
                                cursor.execute_data_collection('CREATE INDEX IF NOT EXISTS idx_date ON trading_calendar(date)')
                                cursor.execute_data_collection('CREATE INDEX IF NOT EXISTS idx_year_month ON trading_calendar(year, month)')
                                cursor.execute_data_collection('CREATE INDEX IF NOT EXISTS idx_is_trading ON trading_calendar(is_trading)')

                                conn.commit()
                                conn.close()
                                logger.info("交易日历数据库初始化完成")

                def save_trading_days(self, trading_days: List[TradingDay]) -> int:
                                """保存交易日数据"""
                                conn = sqlite3.connect(self.db_path)
                                cursor = conn.cursor()

                                saved_count = 0
                                for day in trading_days:
                                                try:
                                                                cursor.execute_data_collection('''
                                                                                INSERT OR REPLACE INTO trading_calendar
                                                                                (date, is_trading, holiday_name, notice, year, month, day, updated_at)
                                                                                VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                                                                ''', (
                                                                                day.date, day.is_trading, day.holiday_name, day.notice,
                                                                                day.year, day.month, day.day
                                                                ))
                                                                saved_count += 1
                                                except Exception as e:
                                                                logger.warning(f"保存交易日{day.date}失败: {e}")

                                conn.commit()
                                conn.close()
                                return saved_count

                def get_trading_days(self, start_date: str, end_date: str) -> List[str]:
                                """获取指定时间范围的交易日"""
                                conn = sqlite3.connect(self.db_path)
                                cursor = conn.cursor()

                                cursor.execute_data_collection('''
                                                SELECT date FROM trading_calendar
                                                WHERE date BETWEEN ? AND ? AND is_trading = 1
                                                ORDER BY date
                                ''', (start_date, end_date))

                                trading_days = [row[0] for row in cursor.fetchall()]
                                conn.close()
                                return trading_days

                def is_trading_day(self, date_str: str) -> bool:
                                """判断是否为交易日"""
                                conn = sqlite3.connect(self.db_path)
                                cursor = conn.cursor()

                                cursor.execute_data_collection('''
                                                SELECT is_trading FROM trading_calendar WHERE date = ?
                                ''', (date_str,))

                                result = cursor.fetchone()
                                conn.close()

                                if result:
                                                return bool(result[0])

                                # 如果数据库中没有，使用简单规则判断
                                try:
                                                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                                                return date_obj.weekday() < 5  # 周一到周五
                                except:
                                                return False

                def get_calendar_stats(self) -> Dict[str, Any]:
                                """获取日历统计信息"""
                                conn = sqlite3.connect(self.db_path)
                                cursor = conn.cursor()

                                # 总记录数
                                cursor.execute_data_collection('SELECT COUNT(*) FROM trading_calendar')
                                total_records = cursor.fetchone()[0]

                                # 交易日数量
                                cursor.execute_data_collection('SELECT COUNT(*) FROM trading_calendar WHERE is_trading = 1')
                                trading_days = cursor.fetchone()[0]

                                # 节假日数量
                                cursor.execute_data_collection('SELECT COUNT(*) FROM trading_calendar WHERE is_trading = 0')
                                holidays = cursor.fetchone()[0]

                                # 年份范围
                                cursor.execute_data_collection('SELECT MIN(year), MAX(year) FROM trading_calendar')
                                year_range = cursor.fetchone()

                                # 最新更新时间
                                cursor.execute_data_collection('SELECT MAX(updated_at) FROM trading_calendar')
                                last_update = cursor.fetchone()[0]

                                conn.close()

                                return {
                                                "total_records": total_records,
                                                "trading_days": trading_days,
                                                "holidays": holidays,
                                                "year_range": year_range,
                                                "last_update": last_update
                                }

class TradingCalendarCrawler:
                """交易日历爬虫"""

                def __init__(self):
                                self.service_name = "TradingCalendarCrawler"
                                self.database = TradingCalendarDatabase()

                                # 深交所交易日历URL
                                self.szse_calendar_url = "https://www.szse.cn/aboutus/calendar/"

                                # 上交所交易日历API (备用)
                                self.sse_api_url = "http://query.sse.com.cn/commonQuery.do"

                                logger.info("交易日历爬虫初始化完成")

                async def crawl_szse_calendar(self, year: int = None) -> List[TradingDay]:
                                """爬取深交所交易日历"""
                                if year is None:
                                                year = datetime.now().year

                                logger.info(f"爬取深交所{year}年交易日历")

                                if CRAWLER4AI_AVAILABLE:
                                                return await self._crawl_with_crawler4ai(year)
                                else:
                                                return await self._crawl_with_aiohttp(year)

                async def _crawl_with_crawler4ai(self, year: int) -> List[TradingDay]:
                                """使用Crawler4AI爬取"""
                                try:
                                                browser_config = BrowserConfig(
                                                                headless=True,
                                                                verbose=False,
                                                                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                                                )

                                                async with AsyncWebCrawler(config=browser_config) as crawler:
                                                                # 构建URL，包含年份参数
                                                                url = f"{self.szse_calendar_url}?year={year}"

                                                                result = await crawler.arun(
                                                                                url=url,
                                                                                wait_for="table",  # 等待表格加载
                                                                                js_code="""
                                                                                // 提取交易日历数据
                                                                                const calendar = [];
                                                                                const tables = document.querySelectorAll('table');

                                                                                tables.forEach(table => {
                                                                                                const rows = table.querySelectorAll('tr');
                                                                                                rows.forEach(row => {
                                                                                                                const cells = row.querySelectorAll('td');
                                                                                                                if (cells.length >= 3) {
                                                                                                                                const date = cells[0]?.textContent?.trim();
                                                                                                                                const status = cells[1]?.textContent?.trim();
                                                                                                                                const notice = cells[2]?.textContent?.trim();

                                                                                                                                if (date && date.match(/\\d{4}-\\d{2}-\\d{2}/)) {
                                                                                                                                                calendar.push({
                                                                                                                                                                date: date,
                                                                                                                                                                status: status,
                                                                                                                                                                notice: notice
                                                                                                                                                });
                                                                                                                                }
                                                                                                                }
                                                                                                });
                                                                                });

                                                                                return calendar;
                                                                                """
                                                                )

                                                                if result.success and result.js_execution_result:
                                                                                calendar_data = result.js_execution_result
                                                                                return self._parse_calendar_data(calendar_data, year)
                                                                else:
                                                                                logger.warning(f"Crawler4AI爬取失败: {result.error_message}")
                                                                                return []

                                except Exception as e:
                                                logger.error(f"Crawler4AI爬取异常: {e}")
                                                return []

                async def _crawl_with_aiohttp(self, year: int) -> List[TradingDay]:
                                """使用aiohttp爬取（备用方案）"""
                                try:
                                                async with aiohttp.ClientSession() as session:
                                                                headers = {
                                                                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                                                                'Referer': 'https://www.szse.cn/'
                                                                }

                                                                # 尝试获取深交所日历页面
                                                                async with session.get(self.szse_calendar_url, headers=headers) as response:
                                                                                if response.status == 200:
                                                                                                html_content = await response.text()
                                                                                                # 这里需要解析HTML，提取交易日历数据
                                                                                                # 由于深交所可能使用JavaScript动态加载，aiohttp可能无法获取完整数据
                                                                                                logger.warning("aiohttp可能无法获取完整的交易日历数据，建议使用Crawler4AI")

                                                                                else:
                                                                                                logger.error(f"HTTP请求失败: {response.status}")

                                except Exception as e:
                                                logger.error(f"aiohttp爬取异常: {e}")

                def _parse_calendar_data(self, calendar_data: List[Dict], year: int) -> List[TradingDay]:
                                """解析日历数据"""
                                trading_days = []

                                for item in calendar_data:
                                                try:
                                                                date_str = item.get('date', '')
                                                                status = item.get('status', '')
                                                                notice = item.get('notice', '')

                                                                # 判断是否为交易日
                                                                is_trading = '休市' not in status and '节假日' not in status

                                                                # 提取节假日名称
                                                                holiday_name = None
                                                                if not is_trading:
                                                                                holiday_name = status

                                                                # 解析日期
                                                                date_obj = datetime.strptime(date_str, '%Y-%m-%d')

                                                                trading_day = TradingDay(
                                                                                date=date_str,
                                                                                is_trading=is_trading,
                                                                                holiday_name=holiday_name,
                                                                                notice=notice,
                                                                                year=date_obj.year,
                                                                                month=date_obj.month,
                                                                                day=date_obj.day
                                                                )

                                                                trading_days.append(trading_day)

                                                except Exception as e:
                                                                logger.warning(f"解析日历数据失败: {item}, 错误: {e}")

                                return trading_days

                def _generate_fallback_calendar(self, year: int) -> List[TradingDay]:
                                """生成备用日历（基于规则）"""
                                logger.info(f"生成{year}年备用交易日历")

                                # 这里使用基本规则生成日历
                                # 实际应该从可靠的数据源获取
                                trading_days = []

                                holidays = {
                                                2024: [
                                                                datetime.now().strftime("%Y-%m-%d"),  # 元旦
                                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), 
                                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 春节
                                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 清明节
                                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 劳动节
                                                                datetime.now().strftime("%Y-%m-%d"),  # 端午节
                                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 中秋节
                                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), 
                                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 国庆节
                                                ],
                                                2025: [
                                                                datetime.now().strftime("%Y-%m-%d"),  # 元旦
                                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),
                                                                datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"), datetime.now().strftime("%Y-%m-%d"),  # 春节
                                                                # ... 其他节假日需要查询官方公告
                                                ]
                                }

                                year_holidays = holidays.get(year, [])

                                # 生成全年日历
                                start_date = datetime(year, 1, 1)
                                end_date = datetime(year, 12, 31)

                                current_date = start_date
                                while current_date <= end_date:
                                                date_str = current_date.strftime('%Y-%m-%d')

                                                # 判断是否为交易日
                                                is_weekend = current_date.weekday() >= 5
                                                is_holiday = date_str in year_holidays
                                                is_trading = not (is_weekend or is_holiday)

                                                holiday_name = None
                                                if is_holiday:
                                                                # 这里可以添加具体的节假日名称逻辑
                                                                holiday_name = "法定节假日"
                                                elif is_weekend:
                                                                holiday_name = "周末"

                                                trading_day = TradingDay(
                                                                date=date_str,
                                                                is_trading=is_trading,
                                                                holiday_name=holiday_name,
                                                                notice="基于规则生成",
                                                                year=current_date.year,
                                                                month=current_date.month,
                                                                day=current_date.day
                                                )

                                                trading_days.append(trading_day)
                                                current_date += timedelta(days=1)

                                return trading_days

                async def update_calendar_for_years(self, start_year: int, end_year: int) -> Dict[str, Any]:
                                """更新多年的交易日历"""
                                logger.info(f"更新{start_year}-{end_year}年交易日历")

                                results = {
                                                "total_years": end_year - start_year + 1,
                                                "success_years": 0,
                                                "failed_years": 0,
                                                "total_days": 0,
                                                "details": []
                                }

                                for year in range(start_year, end_year + 1):
                                                try:
                                                                trading_days = await self.crawl_szse_calendar(year)

                                                                if trading_days:
                                                                                saved_count = self.database.save_trading_days(trading_days)
                                                                                results["success_years"] += 1
                                                                                results["total_days"] += saved_count

                                                                                results["details"].append({
                                                                                                "year": year,
                                                                                                "status": "success",
                                                                                                "days_saved": saved_count
                                                                                })

                                                                                logger.info(f"{year}年交易日历更新成功: {saved_count}天")
                                                                else:
                                                                                results["failed_years"] += 1
                                                                                results["details"].append({
                                                                                                "year": year,
                                                                                                "status": "failed",
                                                                                                "error": "无法获取数据"
                                                                                })

                                                                                logger.warning(f"{year}年交易日历更新失败")

                                                                # 年份间延迟，避免请求过快
                                                                await asyncio.sleep(2)

                                                except Exception as e:
                                                                results["failed_years"] += 1
                                                                results["details"].append({
                                                                                "year": year,
                                                                                "status": "error",
                                                                                "error": str(e)
                                                                })
                                                                logger.error(f"{year}年交易日历更新异常: {e}")

                                return results

# 创建全局实例
trading_calendar_crawler = TradingCalendarCrawler()
