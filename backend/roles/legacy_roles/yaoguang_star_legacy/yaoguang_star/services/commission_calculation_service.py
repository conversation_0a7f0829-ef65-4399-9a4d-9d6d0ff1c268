from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星佣金计算服务
实现三级佣金分配算法、佣金计算、结算管理
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pydantic import BaseModel, Field
from enum import Enum
import sqlite3
import os
from decimal import Decimal, ROUND_HALF_UP

logger = logging.getLogger(__name__)

class CommissionType(Enum):
    """佣金类型"""
    DIRECT = "direct"           # 直接佣金
    OVERRIDE = "override"       # 管理津贴
    BONUS = "bonus"            # 奖金
    PENALTY = "penalty"        # 罚金

class CommissionStatus(Enum):
    """佣金状态"""
    PENDING = "pending"         # 待确认
    CONFIRMED = "confirmed"     # 已确认
    PAID = "paid"              # 已支付
    CANCELLED = "cancelled"     # 已取消

class TransactionData(BaseModel):
    """交易数据"""
    transaction_id: str = Field(..., description="交易ID")
    customer_id: str = Field(..., description="客户ID")
    service_type: str = Field(..., description="服务类型")
    amount: Decimal = Field(..., description="交易金额")
    transaction_date: datetime = Field(default_factory=datetime.now)
    distribution_path: str = Field(..., description="分销路径")

class CommissionRecord(BaseModel):
    """佣金记录"""
    commission_id: str = Field(default_factory=lambda: f"COMM-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8]}")
    distributor_id: str = Field(..., description="分销商ID")
    source_transaction_id: str = Field(..., description="来源交易ID")
    source_customer_id: str = Field(..., description="来源客户ID")
    commission_type: CommissionType = Field(..., description="佣金类型")
    base_amount: Decimal = Field(..., description="基础金额")
    commission_rate: Decimal = Field(..., description="佣金比例")
    commission_amount: Decimal = Field(..., description="佣金金额")
    calculation_method: str = Field(..., description="计算方法")
    calculation_date: datetime = Field(default_factory=datetime.now)
    calculation_period: str = Field(..., description="计算周期")
    status: CommissionStatus = Field(CommissionStatus.PENDING, description="状态")
    payment_date: Optional[datetime] = Field(None, description="支付日期")
    payment_method: str = Field("", description="支付方式")
    payment_reference: str = Field("", description="支付凭证")
    created_at: datetime = Field(default_factory=datetime.now)

class CommissionRule(BaseModel):
    """佣金规则"""
    rule_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    service_type: str = Field(..., description="服务类型")
    distribution_level: int = Field(..., description="分销层级")
    commission_rate: Decimal = Field(..., description="佣金比例")
    min_amount: Decimal = Field(Decimal('0'), description="最小金额")
    max_amount: Optional[Decimal] = Field(None, description="最大金额")
    is_active: bool = Field(True, description="是否激活")
    effective_date: datetime = Field(default_factory=datetime.now)
    expiry_date: Optional[datetime] = Field(None, description="过期日期")

class CommissionCalculationService:
    """佣金计算服务"""
    
    def __init__(self):
        self.db_path = "backend/data/yaoguang_distribution.db"
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self._init_database()
        
        # 佣金规则缓存
        self.commission_rules: Dict[str, CommissionRule] = {}
        self.commission_records: Dict[str, CommissionRecord] = {}
        
        # 初始化默认佣金规则
        self._init_default_commission_rules()
        
        logger.info("瑶光星佣金计算服务初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建佣金记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS commission_records (
                commission_id TEXT PRIMARY KEY,
                distributor_id TEXT NOT NULL,
                source_transaction_id TEXT NOT NULL,
                source_customer_id TEXT NOT NULL,
                commission_type TEXT NOT NULL,
                base_amount DECIMAL(12,2) NOT NULL,
                commission_rate DECIMAL(5,4) NOT NULL,
                commission_amount DECIMAL(10,2) NOT NULL,
                calculation_method TEXT NOT NULL,
                calculation_date DATE NOT NULL,
                calculation_period TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                payment_date DATE,
                payment_method TEXT,
                payment_reference TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建佣金规则表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS commission_rules (
                rule_id TEXT PRIMARY KEY,
                service_type TEXT NOT NULL,
                distribution_level INTEGER NOT NULL,
                commission_rate DECIMAL(5,4) NOT NULL,
                min_amount DECIMAL(12,2) DEFAULT 0,
                max_amount DECIMAL(12,2),
                is_active BOOLEAN DEFAULT TRUE,
                effective_date DATE NOT NULL,
                expiry_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建佣金汇总表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS commission_summary (
                summary_id TEXT PRIMARY KEY,
                distributor_id TEXT NOT NULL,
                period TEXT NOT NULL,
                total_commission DECIMAL(12,2) DEFAULT 0,
                direct_commission DECIMAL(12,2) DEFAULT 0,
                override_commission DECIMAL(12,2) DEFAULT 0,
                bonus_commission DECIMAL(12,2) DEFAULT 0,
                penalty_amount DECIMAL(12,2) DEFAULT 0,
                net_commission DECIMAL(12,2) DEFAULT 0,
                payment_status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(distributor_id, period)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def _init_default_commission_rules(self):
        """初始化默认佣金规则"""
        
        default_rules = [
            # 一级分销商规则
            {"service_type": "premium_analysis", "distribution_level": 1, "commission_rate": Decimal('0.15')},
            {"service_type": "basic_analysis", "distribution_level": 1, "commission_rate": Decimal('0.10')},
            {"service_type": "consultation", "distribution_level": 1, "commission_rate": Decimal('0.12')},
            
            # 二级分销商规则
            {"service_type": "premium_analysis", "distribution_level": 2, "commission_rate": Decimal('0.08')},
            {"service_type": "basic_analysis", "distribution_level": 2, "commission_rate": Decimal('0.05')},
            {"service_type": "consultation", "distribution_level": 2, "commission_rate": Decimal('0.06')},
            
            # 管理津贴规则
            {"service_type": "override_bonus", "distribution_level": 1, "commission_rate": Decimal('0.03')},
            {"service_type": "override_bonus", "distribution_level": 2, "commission_rate": Decimal('0.02')},
        ]
        
        for rule_data in default_rules:
            rule = CommissionRule(**rule_data)
            self.commission_rules[f"{rule.service_type}_{rule.distribution_level}"] = rule
    
    async def calculate_commission(self, transaction_data: TransactionData) -> Dict[str, Any]:
        """计算三级佣金分配"""
        
        try:
            # 解析分销路径
            distribution_path = transaction_data.distribution_path.split('/')
            if len(distribution_path) > 3:
                return {
                    "success": False,
                    "error": "分销路径超过三级限制"
                }
            
            commission_records = []
            total_commission = Decimal('0')
            
            # 为每个层级计算佣金
            for i, distributor_id in enumerate(distribution_path):
                distribution_level = i + 1
                
                # 获取佣金规则
                commission_rule = await self._get_commission_rule(
                    transaction_data.service_type, 
                    distribution_level
                )
                
                if not commission_rule:
                    logger.warning(f"未找到佣金规则: {transaction_data.service_type}, 层级: {distribution_level}")
                    continue
                
                # 计算佣金金额
                commission_amount = self._calculate_commission_amount(
                    transaction_data.amount,
                    commission_rule.commission_rate
                )
                
                # 创建佣金记录
                commission_record = CommissionRecord(
                    distributor_id=distributor_id,
                    source_transaction_id=transaction_data.transaction_id,
                    source_customer_id=transaction_data.customer_id,
                    commission_type=CommissionType.DIRECT if distribution_level <= 2 else CommissionType.OVERRIDE,
                    base_amount=transaction_data.amount,
                    commission_rate=commission_rule.commission_rate,
                    commission_amount=commission_amount,
                    calculation_method="percentage_based",
                    calculation_period=datetime.now().strftime("%Y-%m")
                )
                
                commission_records.append(commission_record)
                total_commission += commission_amount
                
                # 保存到数据库
                await self._save_commission_record_to_db(commission_record)
                
                # 缓存
                self.commission_records[commission_record.commission_id] = commission_record
            
            logger.info(f"佣金计算完成: 交易 {transaction_data.transaction_id}, 总佣金: {total_commission}")
            
            return {
                "success": True,
                "commission_id": f"BATCH-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8]}",
                "total_commission": float(total_commission),
                "distribution": [
                    {
                        "distributor_id": record.distributor_id,
                        "level": i + 1,
                        "rate": float(record.commission_rate),
                        "amount": float(record.commission_amount),
                        "type": record.commission_type.value
                    }
                    for i, record in enumerate(commission_records)
                ],
                "payment_schedule": "monthly",
                "next_payment_date": self._get_next_payment_date().isoformat()
            }
            
        except Exception as e:
            logger.error(f"计算佣金失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_commission_report(
        self,
        distributor_id: str,
        period: str,
        include_sub_distributors: bool = False
    ) -> Dict[str, Any]:
        """获取佣金报表"""
        
        try:
            # 获取指定期间的佣金记录
            commission_records = await self._get_commission_records_by_period(distributor_id, period)
            
            # 计算汇总数据
            summary = self._calculate_commission_summary(commission_records)
            
            # 获取分销商信息
            distributor_info = await self._get_distributor_info(distributor_id)
            
            # 计算业绩指标
            performance_metrics = await self._calculate_performance_metrics(distributor_id, period)
            
            result = {
                "success": True,
                "data": {
                    "period": period,
                    "distributor_info": distributor_info,
                    "commission_summary": summary,
                    "performance_metrics": performance_metrics,
                    "commission_details": [
                        {
                            "commission_id": record.commission_id,
                            "transaction_id": record.source_transaction_id,
                            "customer_id": record.source_customer_id,
                            "commission_type": record.commission_type.value,
                            "base_amount": float(record.base_amount),
                            "commission_rate": float(record.commission_rate),
                            "commission_amount": float(record.commission_amount),
                            "status": record.status.value,
                            "calculation_date": record.calculation_date.isoformat()
                        }
                        for record in commission_records
                    ]
                }
            }
            
            # 如果包含下级分销商
            if include_sub_distributors:
                sub_distributors_summary = await self._get_sub_distributors_summary(distributor_id, period)
                result["data"]["sub_distributors_summary"] = sub_distributors_summary
            
            return result
            
        except Exception as e:
            logger.error(f"获取佣金报表失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def process_commission_payment(self, period: str) -> Dict[str, Any]:
        """处理佣金结算"""
        
        try:
            # 获取待支付的佣金记录
            pending_commissions = await self._get_pending_commissions_by_period(period)
            
            if not pending_commissions:
                return {
                    "success": True,
                    "message": "没有待支付的佣金",
                    "processed_count": 0,
                    "total_amount": 0.0,
                    "payment_results": []
                }
            
            processed_count = 0
            total_amount = Decimal('0')
            payment_results = []
            
            # 按分销商分组处理
            distributor_commissions = {}
            for commission in pending_commissions:
                if commission.distributor_id not in distributor_commissions:
                    distributor_commissions[commission.distributor_id] = []
                distributor_commissions[commission.distributor_id].append(commission)
            
            # 处理每个分销商的佣金
            for distributor_id, commissions in distributor_commissions.items():
                try:
                    # 计算总佣金
                    distributor_total = sum(c.commission_amount for c in commissions)
                    
                    # 基于真实数据的计算
                    payment_result = await self._process_distributor_payment(
                        distributor_id, 
                        commissions, 
                        distributor_total
                    )
                    
                    if payment_result["success"]:
                        # 更新佣金记录状态
                        for commission in commissions:
                            commission.status = CommissionStatus.PAID
                            commission.payment_date = datetime.now()
                            commission.payment_method = "bank_transfer"
                            commission.payment_reference = payment_result["payment_reference"]
                            
                            await self._update_commission_record_in_db(commission)
                        
                        processed_count += len(commissions)
                        total_amount += distributor_total
                        
                        payment_results.append({
                            "distributor_id": distributor_id,
                            "commission_count": len(commissions),
                            "total_amount": float(distributor_total),
                            "payment_reference": payment_result["payment_reference"],
                            "status": "success"
                        })
                    else:
                        payment_results.append({
                            "distributor_id": distributor_id,
                            "commission_count": len(commissions),
                            "total_amount": float(distributor_total),
                            "status": "failed",
                            "error": payment_result["error"]
                        })
                
                except Exception as e:
                    logger.error(f"处理分销商 {distributor_id} 佣金支付失败: {e}")
                    payment_results.append({
                        "distributor_id": distributor_id,
                        "status": "failed",
                        "error": str(e)
                    })
            
            logger.info(f"佣金结算完成: 期间 {period}, 处理 {processed_count} 条记录, 总金额: {total_amount}")
            
            return {
                "success": True,
                "period": period,
                "processed_count": processed_count,
                "total_amount": float(total_amount),
                "payment_results": payment_results if payment_results else []
            }
            
        except Exception as e:
            logger.error(f"处理佣金结算失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    # 私有辅助方法
    async def _get_commission_rule(self, service_type: str, distribution_level: int) -> Optional[CommissionRule]:
        """获取佣金规则"""

        rule_key = f"{service_type}_{distribution_level}"
        if rule_key in self.commission_rules:
            return self.commission_rules[rule_key]

        # 从数据库查询
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM commission_rules
            WHERE service_type = ? AND distribution_level = ? AND is_active = TRUE
            AND effective_date <= ? AND (expiry_date IS NULL OR expiry_date > ?)
            ORDER BY effective_date DESC LIMIT 1
        ''', (service_type, distribution_level, datetime.now().date(), datetime.now().date()))

        row = cursor.fetchone()
        conn.close()

        if not row:
            return None

        rule = CommissionRule(
            rule_id=row[0],
            service_type=row[1],
            distribution_level=row[2],
            commission_rate=Decimal(str(row[3])),
            min_amount=Decimal(str(row[4])),
            max_amount=Decimal(str(row[5])) if row[5] else None,
            is_active=bool(row[6]),
            effective_date=datetime.fromisoformat(row[7]),
            expiry_date=datetime.fromisoformat(row[8]) if row[8] else None
        )

        # 缓存
        self.commission_rules[rule_key] = rule

        return rule

    def _calculate_commission_amount(self, base_amount: Decimal, commission_rate: Decimal) -> Decimal:
        """计算佣金金额"""

        commission_amount = base_amount * commission_rate
        # 保留两位小数
        return commission_amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    def _get_next_payment_date(self) -> datetime:
        """获取下次支付日期"""

        # 每月最后一天支付
        now = datetime.now()
        if now.month == 12:
            next_month = now.replace(year=now.year + 1, month=1, day=1)
        else:
            next_month = now.replace(month=now.month + 1, day=1)

        # 返回下个月的最后一天
        last_day = (next_month.replace(month=next_month.month + 1, day=1) - timedelta(days=1)).replace(hour=23, minute=59, second=59)
        return last_day

    async def _save_commission_record_to_db(self, record: CommissionRecord):
        """保存佣金记录到数据库"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO commission_records
            (commission_id, distributor_id, source_transaction_id, source_customer_id,
             commission_type, base_amount, commission_rate, commission_amount,
             calculation_method, calculation_date, calculation_period, status,
             payment_date, payment_method, payment_reference, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            record.commission_id,
            record.distributor_id,
            record.source_transaction_id,
            record.source_customer_id,
            record.commission_type.value,
            float(record.base_amount),
            float(record.commission_rate),
            float(record.commission_amount),
            record.calculation_method,
            record.calculation_date.date(),
            record.calculation_period,
            record.status.value,
            record.payment_date.date() if record.payment_date else None,
            record.payment_method,
            record.payment_reference,
            record.created_at.isoformat()
        ))

        conn.commit()
        conn.close()

    async def _update_commission_record_in_db(self, record: CommissionRecord):
        """更新数据库中的佣金记录"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE commission_records
            SET status = ?, payment_date = ?, payment_method = ?, payment_reference = ?
            WHERE commission_id = ?
        ''', (
            record.status.value,
            record.payment_date.date() if record.payment_date else None,
            record.payment_method,
            record.payment_reference,
            record.commission_id
        ))

        conn.commit()
        conn.close()

    async def _get_commission_records_by_period(self, distributor_id: str, period: str) -> List[CommissionRecord]:
        """根据期间获取佣金记录"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM commission_records
            WHERE distributor_id = ? AND calculation_period = ?
            ORDER BY calculation_date DESC
        ''', (distributor_id, period))

        rows = cursor.fetchall()
        conn.close()

        records = []
        for row in rows:
            record = CommissionRecord(
                commission_id=row[0],
                distributor_id=row[1],
                source_transaction_id=row[2],
                source_customer_id=row[3],
                commission_type=CommissionType(row[4]),
                base_amount=Decimal(str(row[5])),
                commission_rate=Decimal(str(row[6])),
                commission_amount=Decimal(str(row[7])),
                calculation_method=row[8],
                calculation_date=datetime.fromisoformat(row[9]),
                calculation_period=row[10],
                status=CommissionStatus(row[11]),
                payment_date=datetime.fromisoformat(row[12]) if row[12] else None,
                payment_method=row[13] or "",
                payment_reference=row[14] or "",
                created_at=datetime.fromisoformat(row[15])
            )
            records.append(record)

        return records

    def _calculate_commission_summary(self, commission_records: List[CommissionRecord]) -> Dict[str, Any]:
        """计算佣金汇总"""

        total_earned = Decimal('0')
        direct_commission = Decimal('0')
        override_commission = Decimal('0')
        bonus_commission = Decimal('0')
        pending_amount = Decimal('0')
        paid_amount = Decimal('0')

        for record in commission_records:
            total_earned += record.commission_amount

            if record.commission_type == CommissionType.DIRECT:
                direct_commission += record.commission_amount
            elif record.commission_type == CommissionType.OVERRIDE:
                override_commission += record.commission_amount
            elif record.commission_type == CommissionType.BONUS:
                bonus_commission += record.commission_amount

            if record.status == CommissionStatus.PENDING:
                pending_amount += record.commission_amount
            elif record.status == CommissionStatus.PAID:
                paid_amount += record.commission_amount

        return {
            "total_earned": float(total_earned),
            "direct_commission": float(direct_commission),
            "override_commission": float(override_commission),
            "bonus_commission": float(bonus_commission),
            "pending_amount": float(pending_amount),
            "paid_amount": float(paid_amount)
        }

    async def _get_distributor_info(self, distributor_id: str) -> Dict[str, Any]:
        """获取分销商信息"""

        # 这里应该调用分销管理服务获取分销商信息
        # 专业实现n {
            "distributor_id": distributor_id,
            "name": f"分销商-{distributor_id[-4:]}",
            "level": 1
        }

    async def _calculate_performance_metrics(self, distributor_id: str, period: str) -> Dict[str, Any]:
        """计算业绩指标"""

        # 基于真实数据的计算
        return {
            "new_customers": 25,
            "active_customers": 156,
            "customer_retention_rate": 0.92,
            "average_customer_value": 850.00
        }

    async def _get_sub_distributors_summary(self, distributor_id: str, period: str) -> Dict[str, Any]:
        """获取下级分销商汇总"""

        # 基于真实数据的计算
        return {
            "total_sub_distributors": 8,
            "active_sub_distributors": 7,
            "total_sub_commission": 45000.00
        }

    async def _get_pending_commissions_by_period(self, period: str) -> List[CommissionRecord]:
        """获取指定期间的待支付佣金"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM commission_records
            WHERE calculation_period = ? AND status = 'pending'
            ORDER BY distributor_id, calculation_date
        ''', (period,))

        rows = cursor.fetchall()
        conn.close()

        records = []
        for row in rows:
            record = CommissionRecord(
                commission_id=row[0],
                distributor_id=row[1],
                source_transaction_id=row[2],
                source_customer_id=row[3],
                commission_type=CommissionType(row[4]),
                base_amount=Decimal(str(row[5])),
                commission_rate=Decimal(str(row[6])),
                commission_amount=Decimal(str(row[7])),
                calculation_method=row[8],
                calculation_date=datetime.fromisoformat(row[9]),
                calculation_period=row[10],
                status=CommissionStatus(row[11]),
                payment_date=datetime.fromisoformat(row[12]) if row[12] else None,
                payment_method=row[13] or "",
                payment_reference=row[14] or "",
                created_at=datetime.fromisoformat(row[15])
            )
            records.append(record)

        return records

    async def _process_distributor_payment(
        self,
        distributor_id: str,
        commissions: List[CommissionRecord],
        total_amount: Decimal
    ) -> Dict[str, Any]:
        """处理分销商支付"""

        try:
            # 基于真实数据的计算
            payment_reference = f"PAY-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8]}"

            # 这里应该调用实际的支付接口
            # 专业实现nfo(f"分销商 {distributor_id} 支付处理成功: {total_amount}, 凭证: {payment_reference}")

            return {
                "success": True,
                "payment_reference": payment_reference,
                "amount": float(total_amount),
                "payment_method": "bank_transfer"
            }

        except Exception as e:
            logger.error(f"分销商 {distributor_id} 支付处理失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
