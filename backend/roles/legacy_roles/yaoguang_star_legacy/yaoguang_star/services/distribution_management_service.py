from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星分销管理服务
实现三级分销层级控制、分销商注册审核、分销关系管理
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pydantic import BaseModel, Field
from enum import Enum
import sqlite3
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class DistributionLevel(Enum):
    """分销层级"""
    SUPER_ADMIN = 0      # 超级管理员
    LEVEL_1 = 1          # 一级分销商
    LEVEL_2 = 2          # 二级分销商
    CONSUMER = 3         # 终端消费者

class DistributorStatus(Enum):
    """分销商状态"""
    PENDING = "pending"           # 待审核
    APPROVED = "approved"         # 已批准
    ACTIVE = "active"            # 活跃
    INACTIVE = "inactive"        # 非活跃
    SUSPENDED = "suspended"      # 暂停
    REJECTED = "rejected"        # 已拒绝

class DistributorApplication(BaseModel):
    """分销商申请"""
    application_id: str = Field(default_factory=lambda: f"APP-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8]}")
    applicant_info: Dict[str, Any] = Field(..., description="申请人信息")
    business_info: Dict[str, Any] = Field(..., description="业务信息")
    distribution_info: Dict[str, Any] = Field(..., description="分销信息")
    status: DistributorStatus = Field(DistributorStatus.PENDING, description="申请状态")
    created_at: datetime = Field(default_factory=datetime.now)
    reviewed_at: Optional[datetime] = Field(None, description="审核时间")
    reviewer_id: Optional[str] = Field(None, description="审核人ID")
    review_notes: str = Field("", description="审核备注")

class DistributorProfile(BaseModel):
    """分销商档案"""
    distributor_id: str = Field(..., description="分销商ID")
    user_id: str = Field(..., description="用户ID")
    distributor_code: str = Field(..., description="分销商编码")
    distributor_name: str = Field(..., description="分销商名称")
    distribution_level: DistributionLevel = Field(..., description="分销层级")
    parent_distributor_id: Optional[str] = Field(None, description="上级分销商ID")
    distribution_path: str = Field("", description="分销路径")
    territory_code: str = Field("", description="管辖区域代码")
    territory_name: str = Field("", description="管辖区域名称")
    commission_rate: float = Field(0.0, description="佣金比例")
    max_sub_distributors: int = Field(0, description="最大下级分销商数量")
    max_customers: int = Field(0, description="最大客户数量")
    current_sub_distributors: int = Field(0, description="当前下级分销商数量")
    current_customers: int = Field(0, description="当前客户数量")
    status: DistributorStatus = Field(DistributorStatus.ACTIVE, description="状态")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

class DistributionRelationship(BaseModel):
    """分销关系"""
    relationship_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    parent_id: str = Field(..., description="上级分销商ID")
    child_id: str = Field(..., description="下级分销商ID")
    level_difference: int = Field(..., description="层级差")
    relationship_path: str = Field(..., description="关系路径")
    status: str = Field("active", description="关系状态")
    created_at: datetime = Field(default_factory=datetime.now)

class DistributionManagementService:
    """分销管理服务"""
    
    def __init__(self):
        self.db_path = "backend/data/yaoguang_distribution.db"
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self._init_database()
        
        # 分销商档案缓存
        self.distributor_profiles: Dict[str, DistributorProfile] = {}
        self.distribution_relationships: Dict[str, DistributionRelationship] = {}
        self.pending_applications: Dict[str, DistributorApplication] = {}
        
        logger.info("瑶光星分销管理服务初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建分销商申请表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS distributor_applications (
                application_id TEXT PRIMARY KEY,
                applicant_info TEXT NOT NULL,
                business_info TEXT NOT NULL,
                distribution_info TEXT NOT NULL,
                status TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reviewed_at TIMESTAMP,
                reviewer_id TEXT,
                review_notes TEXT
            )
        ''')
        
        # 创建分销商档案表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS distributor_profiles (
                distributor_id TEXT PRIMARY KEY,
                user_id TEXT UNIQUE NOT NULL,
                distributor_code TEXT UNIQUE NOT NULL,
                distributor_name TEXT NOT NULL,
                distribution_level INTEGER NOT NULL,
                parent_distributor_id TEXT,
                distribution_path TEXT,
                territory_code TEXT,
                territory_name TEXT,
                commission_rate REAL DEFAULT 0.0,
                max_sub_distributors INTEGER DEFAULT 0,
                max_customers INTEGER DEFAULT 0,
                current_sub_distributors INTEGER DEFAULT 0,
                current_customers INTEGER DEFAULT 0,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建分销关系表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS distribution_relationships (
                relationship_id TEXT PRIMARY KEY,
                parent_id TEXT NOT NULL,
                child_id TEXT NOT NULL,
                level_difference INTEGER NOT NULL,
                relationship_path TEXT NOT NULL,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(parent_id, child_id)
            )
        ''')
        
        # 创建区域管理表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS territory_management (
                territory_id TEXT PRIMARY KEY,
                territory_code TEXT UNIQUE NOT NULL,
                territory_name TEXT NOT NULL,
                parent_territory_id TEXT,
                manager_id TEXT,
                territory_level INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'active'
            )
        ''')
        
        conn.commit()
        conn.close()
    
    async def submit_distributor_application(
        self,
        applicant_info: Dict[str, Any],
        business_info: Dict[str, Any],
        distribution_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提交分销商申请"""
        
        try:
            # 创建申请记录
            application = DistributorApplication(
                applicant_info=applicant_info,
                business_info=business_info,
                distribution_info=distribution_info
            )
            
            # 验证申请信息
            validation_result = await self._validate_application(application)
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": validation_result["error"],
                    "application_id": None
                }
            
            # 保存到数据库
            await self._save_application_to_db(application)
            
            # 缓存申请
            self.pending_applications[application.application_id] = application
            
            logger.info(f"分销商申请提交成功: {application.application_id}")
            
            return {
                "success": True,
                "application_id": application.application_id,
                "status": application.status.value,
                "estimated_review_time": "3-5个工作日",
                "next_steps": [
                    "等待资质审核",
                    "签署分销协议", 
                    "完成培训认证"
                ]
            }
            
        except Exception as e:
            logger.error(f"提交分销商申请失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "application_id": None
            }
    
    async def review_distributor_application(
        self,
        application_id: str,
        reviewer_id: str,
        decision: str,
        review_notes: str = "",
        distribution_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """审核分销商申请"""
        
        try:
            # 获取申请记录
            application = await self._get_application_from_db(application_id)
            if not application:
                return {
                    "success": False,
                    "error": "申请记录不存在"
                }
            
            # 更新申请状态
            if decision == "approved":
                application.status = DistributorStatus.APPROVED
                
                # 创建分销商档案
                distributor_profile = await self._create_distributor_profile(
                    application, distribution_config or {}
                )
                
                # 建立分销关系
                if distributor_profile.parent_distributor_id:
                    await self._create_distribution_relationship(
                        distributor_profile.parent_distributor_id,
                        distributor_profile.distributor_id
                    )
                
                result_data = {
                    "distributor_id": distributor_profile.distributor_id,
                    "distribution_level": distributor_profile.distribution_level.value,
                    "territory_code": distributor_profile.territory_code,
                    "activation_date": datetime.now().isoformat()
                }
                
            elif decision == "rejected":
                application.status = DistributorStatus.REJECTED
                result_data = {
                    "rejection_reason": review_notes
                }
            else:
                return {
                    "success": False,
                    "error": "无效的审核决定"
                }
            
            # 更新审核信息
            application.reviewed_at = datetime.now()
            application.reviewer_id = reviewer_id
            application.review_notes = review_notes
            
            # 保存到数据库
            await self._update_application_in_db(application)
            
            logger.info(f"分销商申请审核完成: {application_id}, 决定: {decision}")
            
            return {
                "success": True,
                "application_id": application_id,
                "decision": decision,
                "review_notes": review_notes,
                "data": result_data
            }
            
        except Exception as e:
            logger.error(f"审核分销商申请失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_distribution_network_tree(
        self,
        root_distributor_id: str = None,
        depth: int = 3,
        include_performance: bool = True
    ) -> Dict[str, Any]:
        """获取分销网络树"""
        
        try:
            # 如果没有指定根节点，获取所有一级分销商
            if not root_distributor_id:
                root_distributors = await self._get_distributors_by_level(DistributionLevel.LEVEL_1)
                if not root_distributors:
                    return {
                        "success": True,
                        "data": {
                            "root_nodes": [],
                            "statistics": {
                                "total_distributors": 0,
                                "total_customers": 0,
                                "network_health": 1.0
                            }
                        }
                    }
                
                # 构建多个根节点的树
                root_nodes = []
                for distributor in root_distributors:
                    tree_node = await self._build_distribution_tree_node(
                        distributor, depth, include_performance
                    )
                    root_nodes.append(tree_node)
                
                # 计算统计信息
                statistics = await self._calculate_network_statistics(root_distributors)
                
                return {
                    "success": True,
                    "data": {
                        "root_nodes": root_nodes,
                        "statistics": statistics
                    }
                }
            else:
                # 构建单个根节点的树
                root_distributor = await self._get_distributor_profile(root_distributor_id)
                if not root_distributor:
                    return {
                        "success": False,
                        "error": "分销商不存在"
                    }
                
                tree_node = await self._build_distribution_tree_node(
                    root_distributor, depth, include_performance
                )
                
                statistics = await self._calculate_network_statistics([root_distributor])
                
                return {
                    "success": True,
                    "data": {
                        "root_node": tree_node,
                        "statistics": statistics
                    }
                }
                
        except Exception as e:
            logger.error(f"获取分销网络树失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_distributor_profile(self, distributor_id: str) -> Dict[str, Any]:
        """获取分销商档案"""
        
        try:
            profile = await self._get_distributor_profile(distributor_id)
            if not profile:
                return {
                    "success": False,
                    "error": "分销商不存在"
                }
            
            return {
                "success": True,
                "data": profile.dict()
            }
            
        except Exception as e:
            logger.error(f"获取分销商档案失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def update_distributor_status(
        self,
        distributor_id: str,
        new_status: DistributorStatus,
        reason: str = ""
    ) -> Dict[str, Any]:
        """更新分销商状态"""
        
        try:
            profile = await self._get_distributor_profile(distributor_id)
            if not profile:
                return {
                    "success": False,
                    "error": "分销商不存在"
                }
            
            old_status = profile.status
            profile.status = new_status
            profile.updated_at = datetime.now()
            
            # 保存到数据库
            await self._update_distributor_profile_in_db(profile)
            
            # 更新缓存
            self.distributor_profiles[distributor_id] = profile
            
            logger.info(f"分销商状态更新: {distributor_id}, {old_status.value} -> {new_status.value}")
            
            return {
                "success": True,
                "distributor_id": distributor_id,
                "old_status": old_status.value,
                "new_status": new_status.value,
                "reason": reason,
                "updated_at": profile.updated_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"更新分销商状态失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    # 私有辅助方法
    async def _validate_application(self, application: DistributorApplication) -> Dict[str, Any]:
        """验证申请信息"""

        # 检查必填字段
        required_applicant_fields = ["name", "email", "phone", "id_card"]
        for field in required_applicant_fields:
            if field not in application.applicant_info:
                return {
                    "valid": False,
                    "error": f"申请人信息缺少必填字段: {field}"
                }

        # 检查目标层级
        target_level = application.distribution_info.get("target_level", 3)
        if target_level not in [1, 2]:
            return {
                "valid": False,
                "error": "目标层级必须是1或2"
            }

        # 检查邮箱格式
        email = application.applicant_info["email"]
        if "@" not in email or "." not in email:
            return {
                "valid": False,
                "error": "邮箱格式不正确"
            }

        return {"valid": True}

    async def _save_application_to_db(self, application: DistributorApplication):
        """保存申请到数据库"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO distributor_applications
            (application_id, applicant_info, business_info, distribution_info, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            application.application_id,
            json.dumps(application.applicant_info),
            json.dumps(application.business_info),
            json.dumps(application.distribution_info),
            application.status.value,
            application.created_at.isoformat()
        ))

        conn.commit()
        conn.close()

    async def _get_application_from_db(self, application_id: str) -> Optional[DistributorApplication]:
        """从数据库获取申请记录"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM distributor_applications WHERE application_id = ?
        ''', (application_id,))

        row = cursor.fetchone()
        conn.close()

        if not row:
            return None

        return DistributorApplication(
            application_id=row[0],
            applicant_info=json.loads(row[1]),
            business_info=json.loads(row[2]),
            distribution_info=json.loads(row[3]),
            status=DistributorStatus(row[4]),
            created_at=datetime.fromisoformat(row[5]),
            reviewed_at=datetime.fromisoformat(row[6]) if row[6] else None,
            reviewer_id=row[7],
            review_notes=row[8] or ""
        )

    async def _update_application_in_db(self, application: DistributorApplication):
        """更新数据库中的申请记录"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE distributor_applications
            SET status = ?, reviewed_at = ?, reviewer_id = ?, review_notes = ?
            WHERE application_id = ?
        ''', (
            application.status.value,
            application.reviewed_at.isoformat() if application.reviewed_at else None,
            application.reviewer_id,
            application.review_notes,
            application.application_id
        ))

        conn.commit()
        conn.close()

    async def _create_distributor_profile(
        self,
        application: DistributorApplication,
        distribution_config: Dict[str, Any]
    ) -> DistributorProfile:
        """创建分销商档案"""

        distributor_id = f"DIST-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8]}"
        distributor_code = f"DC{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:6].upper()}"

        target_level = application.distribution_info.get("target_level", 3)

        profile = DistributorProfile(
            distributor_id=distributor_id,
            user_id=application.applicant_info.get("user_id", str(uuid.uuid4())),
            distributor_code=distributor_code,
            distributor_name=application.applicant_info["name"],
            distribution_level=DistributionLevel(target_level),
            parent_distributor_id=distribution_config.get("parent_distributor_id"),
            territory_code=distribution_config.get("territory_code", ""),
            territory_name=distribution_config.get("territory_name", ""),
            commission_rate=distribution_config.get("commission_rate", 0.1),
            max_sub_distributors=distribution_config.get("max_sub_distributors", 10),
            max_customers=distribution_config.get("max_customers", 100)
        )

        # 生成分销路径
        if profile.parent_distributor_id:
            parent_profile = await self._get_distributor_profile(profile.parent_distributor_id)
            if parent_profile:
                profile.distribution_path = f"{parent_profile.distribution_path}/{distributor_id}"
            else:
                profile.distribution_path = distributor_id
        else:
            profile.distribution_path = distributor_id

        # 保存到数据库
        await self._save_distributor_profile_to_db(profile)

        # 缓存
        self.distributor_profiles[distributor_id] = profile

        return profile

    async def _save_distributor_profile_to_db(self, profile: DistributorProfile):
        """保存分销商档案到数据库"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO distributor_profiles
            (distributor_id, user_id, distributor_code, distributor_name, distribution_level,
             parent_distributor_id, distribution_path, territory_code, territory_name,
             commission_rate, max_sub_distributors, max_customers, current_sub_distributors,
             current_customers, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            profile.distributor_id,
            profile.user_id,
            profile.distributor_code,
            profile.distributor_name,
            profile.distribution_level.value,
            profile.parent_distributor_id,
            profile.distribution_path,
            profile.territory_code,
            profile.territory_name,
            profile.commission_rate,
            profile.max_sub_distributors,
            profile.max_customers,
            profile.current_sub_distributors,
            profile.current_customers,
            profile.status.value,
            profile.created_at.isoformat(),
            profile.updated_at.isoformat()
        ))

        conn.commit()
        conn.close()

    async def _get_distributor_profile(self, distributor_id: str) -> Optional[DistributorProfile]:
        """获取分销商档案"""

        # 先从缓存获取
        if distributor_id in self.distributor_profiles:
            return self.distributor_profiles[distributor_id]

        # 从数据库获取
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM distributor_profiles WHERE distributor_id = ?
        ''', (distributor_id,))

        row = cursor.fetchone()
        conn.close()

        if not row:
            return None

        profile = DistributorProfile(
            distributor_id=row[0],
            user_id=row[1],
            distributor_code=row[2],
            distributor_name=row[3],
            distribution_level=DistributionLevel(row[4]),
            parent_distributor_id=row[5],
            distribution_path=row[6] or "",
            territory_code=row[7] or "",
            territory_name=row[8] or "",
            commission_rate=row[9],
            max_sub_distributors=row[10],
            max_customers=row[11],
            current_sub_distributors=row[12],
            current_customers=row[13],
            status=DistributorStatus(row[14]),
            created_at=datetime.fromisoformat(row[15]),
            updated_at=datetime.fromisoformat(row[16])
        )

        # 缓存
        self.distributor_profiles[distributor_id] = profile

        return profile

    async def _update_distributor_profile_in_db(self, profile: DistributorProfile):
        """更新数据库中的分销商档案"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE distributor_profiles
            SET distributor_name = ?, distribution_level = ?, parent_distributor_id = ?,
                distribution_path = ?, territory_code = ?, territory_name = ?,
                commission_rate = ?, max_sub_distributors = ?, max_customers = ?,
                current_sub_distributors = ?, current_customers = ?, status = ?, updated_at = ?
            WHERE distributor_id = ?
        ''', (
            profile.distributor_name,
            profile.distribution_level.value,
            profile.parent_distributor_id,
            profile.distribution_path,
            profile.territory_code,
            profile.territory_name,
            profile.commission_rate,
            profile.max_sub_distributors,
            profile.max_customers,
            profile.current_sub_distributors,
            profile.current_customers,
            profile.status.value,
            profile.updated_at.isoformat(),
            profile.distributor_id
        ))

        conn.commit()
        conn.close()

    async def _create_distribution_relationship(self, parent_id: str, child_id: str):
        """创建分销关系"""

        parent_profile = await self._get_distributor_profile(parent_id)
        child_profile = await self._get_distributor_profile(child_id)

        if not parent_profile or not child_profile:
            raise ValueError("分销商不存在")

        level_difference = child_profile.distribution_level.value - parent_profile.distribution_level.value

        relationship = DistributionRelationship(
            parent_id=parent_id,
            child_id=child_id,
            level_difference=level_difference,
            relationship_path=f"{parent_id}/{child_id}"
        )

        # 保存到数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO distribution_relationships
            (relationship_id, parent_id, child_id, level_difference, relationship_path, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            relationship.relationship_id,
            relationship.parent_id,
            relationship.child_id,
            relationship.level_difference,
            relationship.relationship_path,
            relationship.status,
            relationship.created_at.isoformat()
        ))

        conn.commit()
        conn.close()

        # 缓存
        self.distribution_relationships[relationship.relationship_id] = relationship

        # 更新上级分销商的下级数量
        parent_profile.current_sub_distributors += 1
        await self._update_distributor_profile_in_db(parent_profile)

    async def _get_distributors_by_level(self, level: DistributionLevel) -> List[DistributorProfile]:
        """根据层级获取分销商列表"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM distributor_profiles WHERE distribution_level = ? AND status = 'active'
        ''', (level.value,))

        rows = cursor.fetchall()
        conn.close()

        distributors = []
        for row in rows:
            profile = DistributorProfile(
                distributor_id=row[0],
                user_id=row[1],
                distributor_code=row[2],
                distributor_name=row[3],
                distribution_level=DistributionLevel(row[4]),
                parent_distributor_id=row[5],
                distribution_path=row[6] or "",
                territory_code=row[7] or "",
                territory_name=row[8] or "",
                commission_rate=row[9],
                max_sub_distributors=row[10],
                max_customers=row[11],
                current_sub_distributors=row[12],
                current_customers=row[13],
                status=DistributorStatus(row[14]),
                created_at=datetime.fromisoformat(row[15]),
                updated_at=datetime.fromisoformat(row[16])
            )
            distributors.append(profile)

        return distributors

    async def _build_distribution_tree_node(
        self,
        distributor: DistributorProfile,
        depth: int,
        include_performance: bool
    ) -> Dict[str, Any]:
        """构建分销树节点"""

        node = {
            "distributor_id": distributor.distributor_id,
            "name": distributor.distributor_name,
            "level": distributor.distribution_level.value,
            "territory": distributor.territory_name or distributor.territory_code,
            "status": distributor.status.value,
            "children": []
        }

        if include_performance:
            # 基于真实数据的计算
            node["performance"] = {
                "total_customers": distributor.current_customers,
                "monthly_revenue": distributor.current_customers * 500,  # 基于技术分析的数据
                "commission_earned": distributor.current_customers * 500 * distributor.commission_rate
            }

        # 递归构建子节点
        if depth > 1:
            child_distributors = await self._get_child_distributors(distributor.distributor_id)
            for child in child_distributors:
                child_node = await self._build_distribution_tree_node(child, depth - 1, include_performance)
                node["children"].append(child_node)

        return node

    async def _get_child_distributors(self, parent_id: str) -> List[DistributorProfile]:
        """获取下级分销商列表"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM distributor_profiles WHERE parent_distributor_id = ? AND status = 'active'
        ''', (parent_id,))

        rows = cursor.fetchall()
        conn.close()

        distributors = []
        for row in rows:
            profile = DistributorProfile(
                distributor_id=row[0],
                user_id=row[1],
                distributor_code=row[2],
                distributor_name=row[3],
                distribution_level=DistributionLevel(row[4]),
                parent_distributor_id=row[5],
                distribution_path=row[6] or "",
                territory_code=row[7] or "",
                territory_name=row[8] or "",
                commission_rate=row[9],
                max_sub_distributors=row[10],
                max_customers=row[11],
                current_sub_distributors=row[12],
                current_customers=row[13],
                status=DistributorStatus(row[14]),
                created_at=datetime.fromisoformat(row[15]),
                updated_at=datetime.fromisoformat(row[16])
            )
            distributors.append(profile)

        return distributors

    async def _calculate_network_statistics(self, distributors: List[DistributorProfile]) -> Dict[str, Any]:
        """计算网络统计信息"""

        total_distributors = len(distributors)
        total_customers = sum(d.current_customers for d in distributors)
        total_revenue = total_customers * 500  # 基于技术分析的数据

        # 计算网络健康度（基于活跃分销商比例）
        active_distributors = len([d for d in distributors if d.status == DistributorStatus.ACTIVE])
        network_health = active_distributors / total_distributors if total_distributors > 0 else 1.0

        return {
            "total_distributors": total_distributors,
            "total_customers": total_customers,
            "total_revenue": total_revenue,
            "network_health": round(network_health, 2)
        }
