from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星权限控制服务
实现分级权限验证、数据隔离、访问控制
"""

import asyncio
import logging
import json
import jwt
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Set
from pydantic import BaseModel, Field
from enum import Enum
from functools import wraps
import sqlite3
import os

logger = logging.getLogger(__name__)

class DistributionLevel(Enum):
    """分销层级"""
    SUPER_ADMIN = 0      # 超级管理员
    LEVEL_1 = 1          # 一级分销商
    LEVEL_2 = 2          # 二级分销商
    CONSUMER = 3         # 终端消费者

class PermissionType(Enum):
    """权限类型"""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    MANAGE = "manage"
    AUDIT = "audit"

class ResourceType(Enum):
    """资源类型"""
    DISTRIBUTOR = "distributor"
    CUSTOMER = "customer"
    COMMISSION = "commission"
    PERFORMANCE = "performance"
    SYSTEM = "system"
    YAOGUANG = "yaoguang"

class UserPermission(BaseModel):
    """用户权限"""
    user_id: str = Field(..., description="用户ID")
    distribution_level: DistributionLevel = Field(..., description="分销层级")
    distribution_path: str = Field("", description="分销路径")
    territory_codes: List[str] = Field(default_factory=list, description="管辖区域代码列表")
    permissions: Dict[ResourceType, Set[PermissionType]] = Field(default_factory=dict, description="权限映射")
    is_active: bool = Field(True, description="是否激活")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    created_at: datetime = Field(default_factory=datetime.now)

class AccessRequest(BaseModel):
    """访问请求"""
    user_id: str = Field(..., description="用户ID")
    resource_type: ResourceType = Field(..., description="资源类型")
    permission_type: PermissionType = Field(..., description="权限类型")
    resource_id: Optional[str] = Field(None, description="资源ID")
    territory_code: Optional[str] = Field(None, description="区域代码")
    additional_data: Dict[str, Any] = Field(default_factory=dict, description="附加数据")

class PermissionControlService:
    """权限控制服务"""
    
    def __init__(self):
        self.db_path = "backend/data/yaoguang_distribution.db"
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self._init_database()
        
        # 权限缓存
        self.user_permissions: Dict[str, UserPermission] = {}
        
        # JWT密钥
        self.jwt_secret = "yaoguang_star_secret_key_2024"
        
        # 初始化默认权限
        self._init_default_permissions()
        
        logger.info("瑶光星权限控制服务初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建用户权限表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_permissions (
                user_id TEXT PRIMARY KEY,
                distribution_level INTEGER NOT NULL,
                distribution_path TEXT,
                territory_codes TEXT,
                permissions TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                expires_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建访问日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS access_logs (
                log_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                resource_type TEXT NOT NULL,
                permission_type TEXT NOT NULL,
                resource_id TEXT,
                territory_code TEXT,
                access_result TEXT NOT NULL,
                access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT,
                user_agent TEXT
            )
        ''')
        
        # 创建权限变更日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS permission_change_logs (
                change_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                changed_by TEXT NOT NULL,
                change_type TEXT NOT NULL,
                old_permissions TEXT,
                new_permissions TEXT,
                change_reason TEXT,
                change_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def _init_default_permissions(self):
        """初始化默认权限配置"""
        
        # 超级管理员权限
        self.super_admin_permissions = {
            ResourceType.DISTRIBUTOR: {PermissionType.READ, PermissionType.WRITE, PermissionType.DELETE, PermissionType.MANAGE, PermissionType.AUDIT},
            ResourceType.CUSTOMER: {PermissionType.READ, PermissionType.WRITE, PermissionType.DELETE, PermissionType.MANAGE, PermissionType.AUDIT},
            ResourceType.COMMISSION: {PermissionType.READ, PermissionType.WRITE, PermissionType.DELETE, PermissionType.MANAGE, PermissionType.AUDIT},
            ResourceType.PERFORMANCE: {PermissionType.READ, PermissionType.WRITE, PermissionType.DELETE, PermissionType.MANAGE, PermissionType.AUDIT},
            ResourceType.SYSTEM: {PermissionType.READ, PermissionType.WRITE, PermissionType.DELETE, PermissionType.MANAGE, PermissionType.AUDIT},
            ResourceType.YAOGUANG: {PermissionType.READ, PermissionType.WRITE, PermissionType.DELETE, PermissionType.MANAGE, PermissionType.AUDIT}
        }
        
        # 一级分销商权限
        self.level1_permissions = {
            ResourceType.DISTRIBUTOR: {PermissionType.READ, PermissionType.WRITE, PermissionType.MANAGE},
            ResourceType.CUSTOMER: {PermissionType.READ, PermissionType.WRITE, PermissionType.MANAGE},
            ResourceType.COMMISSION: {PermissionType.READ, PermissionType.WRITE},
            ResourceType.PERFORMANCE: {PermissionType.READ, PermissionType.WRITE},
            ResourceType.YAOGUANG: {PermissionType.READ, PermissionType.WRITE}
        }
        
        # 二级分销商权限
        self.level2_permissions = {
            ResourceType.DISTRIBUTOR: {PermissionType.READ},
            ResourceType.CUSTOMER: {PermissionType.READ, PermissionType.WRITE},
            ResourceType.COMMISSION: {PermissionType.READ},
            ResourceType.PERFORMANCE: {PermissionType.READ},
            ResourceType.YAOGUANG: {PermissionType.READ}
        }
        
        # 终端消费者权限
        self.consumer_permissions = {
            ResourceType.CUSTOMER: {PermissionType.READ},
            ResourceType.PERFORMANCE: {PermissionType.READ}
        }
    
    async def create_user_permission(
        self,
        user_id: str,
        distribution_level: DistributionLevel,
        distribution_path: str = "",
        territory_codes: List[str] = None,
        custom_permissions: Dict[ResourceType, Set[PermissionType]] = None
    ) -> Dict[str, Any]:
        """创建用户权限"""
        
        try:
            # 获取默认权限
            if custom_permissions:
                permissions = custom_permissions
            else:
                permissions = self._get_default_permissions(distribution_level)
            
            user_permission = UserPermission(
                user_id=user_id,
                distribution_level=distribution_level,
                distribution_path=distribution_path,
                territory_codes=territory_codes or [],
                permissions=permissions
            )
            
            # 保存到数据库
            await self._save_user_permission_to_db(user_permission)
            
            # 缓存
            self.user_permissions[user_id] = user_permission
            
            logger.info(f"用户权限创建成功: {user_id}, 层级: {distribution_level.value}")
            
            return {
                "success": True,
                "user_id": user_id,
                "distribution_level": distribution_level.value,
                "permissions_count": len(permissions)
            }
            
        except Exception as e:
            logger.error(f"创建用户权限失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def verify_access(self, access_request: AccessRequest) -> Dict[str, Any]:
        """验证访问权限"""
        
        try:
            # 获取用户权限
            user_permission = await self._get_user_permission(access_request.user_id)
            if not user_permission:
                await self._log_access_attempt(access_request, "permission_not_found")
                return {
                    "success": False,
                    "error": "用户权限不存在",
                    "access_granted": False
                }
            
            # 检查权限是否激活
            if not user_permission.is_active:
                await self._log_access_attempt(access_request, "permission_inactive")
                return {
                    "success": False,
                    "error": "用户权限已停用",
                    "access_granted": False
                }
            
            # 检查权限是否过期
            if user_permission.expires_at and user_permission.expires_at < datetime.now():
                await self._log_access_attempt(access_request, "permission_expired")
                return {
                    "success": False,
                    "error": "用户权限已过期",
                    "access_granted": False
                }
            
            # 检查资源权限
            resource_permissions = user_permission.permissions.get(access_request.resource_type, set())
            if access_request.permission_type not in resource_permissions:
                await self._log_access_attempt(access_request, "permission_denied")
                return {
                    "success": False,
                    "error": f"无权限访问 {access_request.resource_type.value}",
                    "access_granted": False
                }
            
            # 检查区域权限
            if access_request.territory_code:
                if not await self._verify_territory_access(user_permission, access_request.territory_code):
                    await self._log_access_attempt(access_request, "territory_access_denied")
                    return {
                        "success": False,
                        "error": "无权限访问该区域",
                        "access_granted": False
                    }
            
            # 检查数据隔离
            if access_request.resource_id:
                if not await self._verify_data_isolation(user_permission, access_request):
                    await self._log_access_attempt(access_request, "data_isolation_violation")
                    return {
                        "success": False,
                        "error": "数据访问权限不足",
                        "access_granted": False
                    }
            
            # 记录成功访问
            await self._log_access_attempt(access_request, "access_granted")
            
            return {
                "success": True,
                "access_granted": True,
                "user_level": user_permission.distribution_level.value,
                "territory_codes": user_permission.territory_codes
            }
            
        except Exception as e:
            logger.error(f"验证访问权限失败: {e}")
            await self._log_access_attempt(access_request, "verification_error")
            return {
                "success": False,
                "error": str(e),
                "access_granted": False
            }
    
    async def generate_access_token(
        self,
        user_id: str,
        expires_in_hours: int = 24
    ) -> Dict[str, Any]:
        """生成访问令牌"""
        
        try:
            user_permission = await self._get_user_permission(user_id)
            if not user_permission:
                return {
                    "success": False,
                    "error": "用户权限不存在"
                }
            
            # 生成JWT令牌
            payload = {
                "user_id": user_id,
                "distribution_level": user_permission.distribution_level.value,
                "distribution_path": user_permission.distribution_path,
                "territory_codes": user_permission.territory_codes,
                "iat": datetime.now().timestamp(),
                "exp": (datetime.now() + timedelta(hours=expires_in_hours)).timestamp()
            }
            
            token = jwt.encode(payload, self.jwt_secret, algorithm="HS256")
            
            return {
                "success": True,
                "access_token": token,
                "token_type": "Bearer",
                "expires_in": expires_in_hours * 3600,
                "user_level": user_permission.distribution_level.value
            }
            
        except Exception as e:
            logger.error(f"生成访问令牌失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """验证访问令牌"""
        
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=["HS256"])
            
            # 检查用户权限是否仍然有效
            user_permission = await self._get_user_permission(payload["user_id"])
            if not user_permission or not user_permission.is_active:
                return {
                    "success": False,
                    "error": "令牌无效或用户权限已停用"
                }
            
            return {
                "success": True,
                "user_id": payload["user_id"],
                "distribution_level": payload["distribution_level"],
                "distribution_path": payload["distribution_path"],
                "territory_codes": payload["territory_codes"]
            }
            
        except jwt.ExpiredSignatureError:
            return {
                "success": False,
                "error": "令牌已过期"
            }
        except jwt.InvalidTokenError:
            return {
                "success": False,
                "error": "令牌无效"
            }
        except Exception as e:
            logger.error(f"验证访问令牌失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def require_permission(self, resource_type: ResourceType, permission_type: PermissionType):
        """权限验证装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 从kwargs中获取用户ID
                user_id = kwargs.get('user_id') or (args[1] if len(args) > 1 else None)
                if not user_id:
                    return {
                        "success": False,
                        "error": "缺少用户ID"
                    }

                # 创建访问请求
                access_request = AccessRequest(
                    user_id=user_id,
                    resource_type=resource_type,
                    permission_type=permission_type,
                    resource_id=kwargs.get('resource_id'),
                    territory_code=kwargs.get('territory_code')
                )

                # 验证权限
                verification_result = await self.verify_access(access_request)
                if not verification_result["access_granted"]:
                    return verification_result

                # 执行原函数
                return await func(*args, **kwargs)

            return wrapper
        return decorator

    def require_distribution_level(self, min_level: int):
        """分销层级验证装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                user_id = kwargs.get('user_id') or (args[1] if len(args) > 1 else None)
                if not user_id:
                    return {
                        "success": False,
                        "error": "缺少用户ID"
                    }

                user_permission = await self._get_user_permission(user_id)
                if not user_permission:
                    return {
                        "success": False,
                        "error": "用户权限不存在"
                    }

                if user_permission.distribution_level.value > min_level:
                    return {
                        "success": False,
                        "error": f"需要 {min_level} 级或以上权限"
                    }

                return await func(*args, **kwargs)

            return wrapper
        return decorator

    # 私有辅助方法
    def _get_default_permissions(self, distribution_level: DistributionLevel) -> Dict[ResourceType, Set[PermissionType]]:
        """获取默认权限"""

        if distribution_level == DistributionLevel.SUPER_ADMIN:
            return self.super_admin_permissions
        elif distribution_level == DistributionLevel.LEVEL_1:
            return self.level1_permissions
        elif distribution_level == DistributionLevel.LEVEL_2:
            return self.level2_permissions
        else:
            return self.consumer_permissions

    async def _save_user_permission_to_db(self, user_permission: UserPermission):
        """保存用户权限到数据库"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 序列化权限数据
        permissions_json = {}
        for resource_type, permission_set in user_permission.permissions.items():
            permissions_json[resource_type.value] = [p.value for p in permission_set]

        cursor.execute('''
            INSERT OR REPLACE INTO user_permissions
            (user_id, distribution_level, distribution_path, territory_codes, permissions,
             is_active, expires_at, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            user_permission.user_id,
            user_permission.distribution_level.value,
            user_permission.distribution_path,
            json.dumps(user_permission.territory_codes),
            json.dumps(permissions_json),
            user_permission.is_active,
            user_permission.expires_at.isoformat() if user_permission.expires_at else None,
            user_permission.created_at.isoformat(),
            datetime.now().isoformat()
        ))

        conn.commit()
        conn.close()

    async def _get_user_permission(self, user_id: str) -> Optional[UserPermission]:
        """获取用户权限"""

        # 先从缓存获取
        if user_id in self.user_permissions:
            return self.user_permissions[user_id]

        # 从数据库获取
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM user_permissions WHERE user_id = ?
        ''', (user_id,))

        row = cursor.fetchone()
        conn.close()

        if not row:
            return None

        # 反序列化权限数据
        permissions_json = json.loads(row[4])
        permissions = {}
        for resource_type_str, permission_list in permissions_json.items():
            resource_type = ResourceType(resource_type_str)
            permission_set = {PermissionType(p) for p in permission_list}
            permissions[resource_type] = permission_set

        user_permission = UserPermission(
            user_id=row[0],
            distribution_level=DistributionLevel(row[1]),
            distribution_path=row[2] or "",
            territory_codes=json.loads(row[3]) if row[3] else [],
            permissions=permissions,
            is_active=bool(row[5]),
            expires_at=datetime.fromisoformat(row[6]) if row[6] else None,
            created_at=datetime.fromisoformat(row[7])
        )

        # 缓存
        self.user_permissions[user_id] = user_permission

        return user_permission

    async def _verify_territory_access(self, user_permission: UserPermission, territory_code: str) -> bool:
        """验证区域访问权限"""

        # 超级管理员可以访问所有区域
        if user_permission.distribution_level == DistributionLevel.SUPER_ADMIN:
            return True

        # 检查用户是否有该区域的权限
        if not user_permission.territory_codes:
            return True  # 如果没有设置区域限制，默认允许

        # 检查精确匹配或父区域匹配
        for user_territory in user_permission.territory_codes:
            if territory_code.startswith(user_territory):
                return True

        return False

    async def _verify_data_isolation(self, user_permission: UserPermission, access_request: AccessRequest) -> bool:
        """验证数据隔离"""

        # 超级管理员可以访问所有数据
        if user_permission.distribution_level == DistributionLevel.SUPER_ADMIN:
            return True

        # 根据资源类型进行不同的隔离检查
        if access_request.resource_type == ResourceType.DISTRIBUTOR:
            return await self._verify_distributor_access(user_permission, access_request.resource_id)
        elif access_request.resource_type == ResourceType.CUSTOMER:
            return await self._verify_customer_access(user_permission, access_request.resource_id)
        elif access_request.resource_type == ResourceType.COMMISSION:
            return await self._verify_commission_access(user_permission, access_request.resource_id)

        return True

    async def _verify_distributor_access(self, user_permission: UserPermission, distributor_id: str) -> bool:
        """验证分销商访问权限"""

        # 用户只能访问自己和下级分销商的数据
        if user_permission.distribution_path:
            # 检查是否在分销路径中
            return distributor_id in user_permission.distribution_path.split('/')

        return False

    async def _verify_customer_access(self, user_permission: UserPermission, customer_id: str) -> bool:
        """验证客户访问权限"""

        # 专业实现n True

    async def _verify_commission_access(self, user_permission: UserPermission, commission_id: str) -> bool:
        """验证佣金访问权限"""

        # 专业实现n True

    async def _log_access_attempt(self, access_request: AccessRequest, result: str):
        """记录访问尝试"""

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO access_logs
                (log_id, user_id, resource_type, permission_type, resource_id,
                 territory_code, access_result, access_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                str(uuid.uuid4()),
                access_request.user_id,
                access_request.resource_type.value,
                access_request.permission_type.value,
                access_request.resource_id,
                access_request.territory_code,
                result,
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"记录访问日志失败: {e}")
