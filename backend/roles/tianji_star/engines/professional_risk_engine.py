#!/usr/bin/env python3
"""
专业级风险计算引擎
实现金融级风险计算模型，提升天玑星专业性
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class ProfessionalRiskEngine:
    """
    专业级风险计算引擎
    实现VaR、CVaR、GARCH、Copula等专业风险模型
    """
    
    def __init__(self):
        self.engine_name = "天玑星专业风险引擎"
        self.version = "Professional v3.0"
        
        # 风险计算配置
        self.confidence_levels = [0.95, 0.99, 0.999]
        self.lookback_periods = [30, 60, 252]  # 1月、2月、1年
        
        # 模型参数
        self.garch_params = {
            "omega": 0.000001,
            "alpha": 0.1,
            "beta": 0.85
        }
        
    def calculate_professional_var(self, returns: List[float], confidence_level: float = 0.95, 
                                 method: str = "historical") -> Dict[str, Any]:
        """
        计算专业级VaR (Value at Risk)
        
        Args:
            returns: 收益率序列
            confidence_level: 置信水平
            method: 计算方法 ("historical", "parametric", "monte_carlo")
        """
        try:
            if not returns or len(returns) < 30:
                logger.warning("收益率数据不足，无法计算可靠的VaR")
                return {"var": 0, "method": method, "confidence": confidence_level, "error": "insufficient_data"}
            
            returns_array = np.array(returns)
            
            if method == "historical":
                var = self._calculate_historical_var(returns_array, confidence_level)
            elif method == "parametric":
                var = self._calculate_parametric_var(returns_array, confidence_level)
            elif method == "monte_carlo":
                var = self._calculate_monte_carlo_var(returns_array, confidence_level)
            else:
                raise ValueError(f"未知的VaR计算方法: {method}")
            
            return {
                "var": var,
                "method": method,
                "confidence": confidence_level,
                "data_points": len(returns),
                "calculation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"VaR计算失败: {e}")
            return {"var": 0, "method": method, "confidence": confidence_level, "error": str(e)}
    
    def _calculate_historical_var(self, returns: np.ndarray, confidence_level: float) -> float:
        """历史模拟法计算VaR"""
        percentile = (1 - confidence_level) * 100
        var = np.percentile(returns, percentile)
        return abs(var)
    
    def _calculate_parametric_var(self, returns: np.ndarray, confidence_level: float) -> float:
        """参数法计算VaR (假设正态分布)"""
        mean = np.mean(returns)
        std = np.std(returns)
        z_score = stats.norm.ppf(1 - confidence_level)
        var = abs(mean + z_score * std)
        return var
    
    def _calculate_monte_carlo_var(self, returns: np.ndarray, confidence_level: float, 
                                 simulations: int = 10000) -> float:
        """蒙特卡洛模拟法计算VaR"""
        mean = np.mean(returns)
        std = np.std(returns)
        
        # 生成随机模拟
        simulated_returns = np.random.normal(mean, std, simulations)
        
        # 计算VaR
        percentile = (1 - confidence_level) * 100
        var = np.percentile(simulated_returns, percentile)
        return abs(var)
    
    def calculate_conditional_var(self, returns: List[float], confidence_level: float = 0.95) -> Dict[str, Any]:
        """
        计算条件风险价值 (CVaR/Expected Shortfall)
        """
        try:
            if not returns or len(returns) < 30:
                return {"cvar": 0, "confidence": confidence_level, "error": "insufficient_data"}
            
            returns_array = np.array(returns)
            
            # 先计算VaR
            var_result = self.calculate_professional_var(returns, confidence_level, "historical")
            var_threshold = var_result["var"]
            
            # 计算超过VaR的损失的期望值
            tail_losses = returns_array[returns_array <= -var_threshold]
            
            if len(tail_losses) > 0:
                cvar = abs(np.mean(tail_losses))
            else:
                cvar = var_threshold
            
            return {
                "cvar": cvar,
                "var": var_threshold,
                "confidence": confidence_level,
                "tail_observations": len(tail_losses),
                "calculation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"CVaR计算失败: {e}")
            return {"cvar": 0, "confidence": confidence_level, "error": str(e)}
    
    def calculate_garch_volatility(self, returns: List[float], forecast_periods: int = 1) -> Dict[str, Any]:
        """
        计算GARCH模型波动率预测
        """
        try:
            if not returns or len(returns) < 100:
                return {"volatility": 0, "error": "insufficient_data"}
            
            returns_array = np.array(returns)
            
            # σ²(t+1) = ω + α*ε²(t) + β*σ²(t)
            
            # 初始化
            omega = self.garch_params["omega"]
            alpha = self.garch_params["alpha"] 
            beta = self.garch_params["beta"]
            
            # 计算条件方差序列
            conditional_variances = []
            long_run_variance = np.var(returns_array)
            
            # 初始条件方差
            conditional_variance = long_run_variance
            
            for i, return_val in enumerate(returns_array):
                if i > 0:
                    # GARCH(1,1)公式
                    conditional_variance = (omega + 
                                          alpha * (returns_array[i-1] ** 2) + 
                                          beta * conditional_variance)
                
                conditional_variances.append(conditional_variance)
            
            # 预测未来波动率
            current_variance = conditional_variances[-1]
            forecasted_volatility = np.sqrt(current_variance)
            
            return {
                "current_volatility": forecasted_volatility,
                "annualized_volatility": forecasted_volatility * np.sqrt(252),
                "conditional_variances": conditional_variances[-10:],  # 最近10期
                "model_params": self.garch_params,
                "forecast_periods": forecast_periods,
                "calculation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"GARCH波动率计算失败: {e}")
            return {"volatility": 0, "error": str(e)}
    
    def calculate_maximum_drawdown(self, prices: List[float]) -> Dict[str, Any]:
        """
        计算最大回撤
        """
        try:
            if not prices or len(prices) < 2:
                return {"max_drawdown": 0, "error": "insufficient_data"}
            
            prices_array = np.array(prices)
            
            # 计算累计收益
            cumulative_returns = prices_array / prices_array[0]
            
            # 计算历史最高点
            running_max = np.maximum.accumulate(cumulative_returns)
            
            # 计算回撤
            drawdowns = (cumulative_returns - running_max) / running_max
            
            # 最大回撤
            max_drawdown = abs(np.min(drawdowns))
            
            # 找到最大回撤的时间点
            max_dd_index = np.argmin(drawdowns)
            
            # 找到最大回撤开始的时间点
            peak_index = np.argmax(running_max[:max_dd_index+1])
            
            return {
                "max_drawdown": max_drawdown,
                "max_drawdown_percentage": max_drawdown * 100,
                "drawdown_start_index": peak_index,
                "drawdown_end_index": max_dd_index,
                "current_drawdown": abs(drawdowns[-1]),
                "drawdown_series": drawdowns.tolist()[-30:],  # 最近30期回撤
                "calculation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"最大回撤计算失败: {e}")
            return {"max_drawdown": 0, "error": str(e)}
    
    def calculate_correlation_matrix(self, returns_matrix: List[List[float]], 
                                   method: str = "pearson") -> Dict[str, Any]:
        """
        计算相关性矩阵
        """
        try:
            if not returns_matrix or len(returns_matrix) < 2:
                return {"correlation_matrix": [], "error": "insufficient_data"}
            
            # 转换为numpy数组
            returns_array = np.array(returns_matrix)
            
            if method == "pearson":
                correlation_matrix = np.corrcoef(returns_array)
            elif method == "spearman":
                correlation_matrix = stats.spearmanr(returns_array.T)[0]
            elif method == "kendall":
                correlation_matrix = np.corrcoef(returns_array)  # 暂时使用Pearson
            else:
                raise ValueError(f"未知的相关性计算方法: {method}")
            
            # 计算平均相关性
            n = len(correlation_matrix)
            total_correlations = 0
            count = 0
            
            for i in range(n):
                for j in range(i+1, n):
                    total_correlations += correlation_matrix[i][j]
                    count += 1
            
            average_correlation = total_correlations / count if count > 0 else 0
            
            # 找到最高和最低相关性
            max_correlation = 0
            min_correlation = 1
            max_pair = None
            min_pair = None
            
            for i in range(n):
                for j in range(i+1, n):
                    corr = correlation_matrix[i][j]
                    if corr > max_correlation:
                        max_correlation = corr
                        max_pair = (i, j)
                    if corr < min_correlation:
                        min_correlation = corr
                        min_pair = (i, j)
            
            return {
                "correlation_matrix": correlation_matrix.tolist(),
                "average_correlation": average_correlation,
                "max_correlation": max_correlation,
                "min_correlation": min_correlation,
                "max_correlation_pair": max_pair,
                "min_correlation_pair": min_pair,
                "method": method,
                "matrix_size": n,
                "calculation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"相关性矩阵计算失败: {e}")
            return {"correlation_matrix": [], "error": str(e)}
    
    def calculate_portfolio_risk_metrics(self, portfolio_returns: List[float], 
                                       benchmark_returns: List[float] = None) -> Dict[str, Any]:
        """
        计算投资组合综合风险指标
        """
        try:
            if not portfolio_returns or len(portfolio_returns) < 30:
                return {"error": "insufficient_data"}
            
            returns_array = np.array(portfolio_returns)
            
            # 基础统计指标
            mean_return = np.mean(returns_array)
            volatility = np.std(returns_array)
            
            # VaR和CVaR
            var_95 = self.calculate_professional_var(portfolio_returns, 0.95)
            var_99 = self.calculate_professional_var(portfolio_returns, 0.99)
            cvar_95 = self.calculate_conditional_var(portfolio_returns, 0.95)
            
            # 夏普比率 (假设无风险利率3%)
            risk_free_rate = 0.03 / 252  # 日无风险利率
            sharpe_ratio = (mean_return - risk_free_rate) / volatility if volatility > 0 else 0
            
            # 偏度和峰度
            skewness = stats.skew(returns_array)
            kurtosis = stats.kurtosis(returns_array)
            
            # Beta (如果有基准)
            beta = 1.0
            if benchmark_returns and len(benchmark_returns) == len(portfolio_returns):
                benchmark_array = np.array(benchmark_returns)
                covariance = np.cov(returns_array, benchmark_array)[0][1]
                benchmark_variance = np.var(benchmark_array)
                beta = covariance / benchmark_variance if benchmark_variance > 0 else 1.0
            
            # 信息比率
            information_ratio = 0
            if benchmark_returns:
                excess_returns = returns_array - np.array(benchmark_returns[:len(returns_array)])
                tracking_error = np.std(excess_returns)
                information_ratio = np.mean(excess_returns) / tracking_error if tracking_error > 0 else 0
            
            return {
                "mean_return": mean_return,
                "annualized_return": mean_return * 252,
                "volatility": volatility,
                "annualized_volatility": volatility * np.sqrt(252),
                "sharpe_ratio": sharpe_ratio,
                "var_95": var_95["var"],
                "var_99": var_99["var"],
                "cvar_95": cvar_95["cvar"],
                "beta": beta,
                "information_ratio": information_ratio,
                "skewness": skewness,
                "kurtosis": kurtosis,
                "data_points": len(returns_array),
                "calculation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"投资组合风险指标计算失败: {e}")
            return {"error": str(e)}
    
    def calculate_stress_test_scenarios(self, portfolio_returns: List[float]) -> Dict[str, Any]:
        """
        计算压力测试场景
        """
        try:
            if not portfolio_returns or len(portfolio_returns) < 30:
                return {"error": "insufficient_data"}
            
            returns_array = np.array(portfolio_returns)
            
            # 定义压力测试场景
            scenarios = {
                "market_crash_2008": {
                    "description": "2008年金融危机级别市场下跌",
                    "market_shock": -0.30,
                    "volatility_multiplier": 3.0
                },
                "black_monday_1987": {
                    "description": "1987年黑色星期一级别暴跌",
                    "market_shock": -0.22,
                    "volatility_multiplier": 5.0
                },
                "covid_crash_2020": {
                    "description": "2020年新冠疫情市场暴跌",
                    "market_shock": -0.35,
                    "volatility_multiplier": 4.0
                },
                "interest_rate_shock": {
                    "description": "利率大幅上升冲击",
                    "market_shock": -0.15,
                    "volatility_multiplier": 2.0
                }
            }
            
            stress_results = {}
            
            for scenario_name, scenario in scenarios.items():
                # 应用压力冲击
                shocked_returns = returns_array + scenario["market_shock"]
                shocked_volatility = np.std(returns_array) * scenario["volatility_multiplier"]
                
                # 计算压力测试后的风险指标
                stressed_var = self.calculate_professional_var(shocked_returns.tolist(), 0.95)
                stressed_cvar = self.calculate_conditional_var(shocked_returns.tolist(), 0.95)
                
                stress_results[scenario_name] = {
                    "description": scenario["description"],
                    "market_shock": scenario["market_shock"],
                    "volatility_multiplier": scenario["volatility_multiplier"],
                    "stressed_return": np.mean(shocked_returns),
                    "stressed_volatility": shocked_volatility,
                    "stressed_var": stressed_var["var"],
                    "stressed_cvar": stressed_cvar["cvar"],
                    "return_impact": np.mean(shocked_returns) - np.mean(returns_array),
                    "volatility_impact": shocked_volatility - np.std(returns_array)
                }
            
            return {
                "stress_scenarios": stress_results,
                "baseline_return": np.mean(returns_array),
                "baseline_volatility": np.std(returns_array),
                "calculation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"压力测试计算失败: {e}")
            return {"error": str(e)}

# 创建全局实例
professional_risk_engine = ProfessionalRiskEngine()

__all__ = ['ProfessionalRiskEngine', 'professional_risk_engine']
