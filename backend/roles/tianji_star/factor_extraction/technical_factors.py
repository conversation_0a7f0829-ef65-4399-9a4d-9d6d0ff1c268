# -*- coding: utf-8 -*-
"""
技术因子提取器 - 真正的核心实现
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional
from .factor_extractor import FactorExtractor, FactorResult

logger = logging.getLogger(__name__)

class TechnicalFactorExtractor(FactorExtractor):
    """技术因子提取器"""
    
    def __init__(self):
        super().__init__()
        
        # 技术指标参数配置
        self.indicator_params = {
            "ma": [5, 10, 20, 60],  # 移动平均线周期
            "ema": [12, 26],        # 指数移动平均线周期
            "rsi": 14,              # RSI周期
            "macd": {"fast": 12, "slow": 26, "signal": 9},  # MACD参数
            "bollinger": {"period": 20, "std": 2},          # 布林带参数
            "kdj": {"k": 9, "d": 3, "j": 3}                # KDJ参数
        }
    
    async def _extract_technical_factors(self, data: Dict) -> List[FactorResult]:
        """提取技术因子 - 完整实现"""
        factors = []
        
        # 获取价格和成交量数据
        prices = data.get("prices", [])
        volumes = data.get("volumes", [])
        highs = data.get("highs", prices)  # 如果没有最高价，使用收盘价
        lows = data.get("lows", prices)    # 如果没有最低价，使用收盘价
        opens = data.get("opens", prices)  # 如果没有开盘价，使用收盘价
        
        if len(prices) < 2:
            logger.warning("价格数据不足，无法计算技术因子")
            return factors
        
        try:
            # 1. 价格动量因子
            momentum_factors = self._calculate_momentum_factors(prices)
            factors.extend(momentum_factors)
            
            # 2. 移动平均因子
            ma_factors = self._calculate_ma_factors(prices)
            factors.extend(ma_factors)
            
            # 3. 波动率因子
            volatility_factors = self._calculate_volatility_factors(prices, highs, lows)
            factors.extend(volatility_factors)
            
            # 4. 成交量因子
            if volumes:
                volume_factors = self._calculate_volume_factors(prices, volumes)
                factors.extend(volume_factors)
            
            # 5. 技术指标因子
            indicator_factors = self._calculate_technical_indicators(prices, highs, lows, opens, volumes)
            factors.extend(indicator_factors)
            
            # 6. 价格形态因子
            pattern_factors = self._calculate_pattern_factors(prices, highs, lows, opens)
            factors.extend(pattern_factors)
            
            logger.info(f"技术因子提取完成: {len(factors)} 个因子")
            
        except Exception as e:
            logger.error(f"技术因子提取失败: {e}")
        
        return factors
    
    def _calculate_momentum_factors(self, prices: List[float]) -> List[FactorResult]:
        """计算动量因子"""
        factors = []
        
        # 不同周期的动量
        periods = [1, 3, 5, 10, 20, 60]
        
        for period in periods:
            if len(prices) > period:
                momentum = self._calculate_momentum(prices, period)
                factors.append(FactorResult(
                    factor_name=f"momentum_{period}d",
                    factor_value=momentum,
                    factor_type="technical",
                    confidence=0.8,
                    metadata={"period": period, "type": "momentum"}
                ))
        
        # 动量加速度（二阶动量）
        if len(prices) > 10:
            momentum_5d = self._calculate_momentum(prices, 5)
            momentum_10d = self._calculate_momentum(prices, 10)
            momentum_acceleration = momentum_5d - momentum_10d
            
            factors.append(FactorResult(
                factor_name="momentum_acceleration",
                factor_value=momentum_acceleration,
                factor_type="technical",
                confidence=0.75,
                metadata={"type": "momentum_acceleration"}
            ))
        
        return factors
    
    def _calculate_ma_factors(self, prices: List[float]) -> List[FactorResult]:
        """计算移动平均因子"""
        factors = []
        
        for period in self.indicator_params["ma"]:
            if len(prices) >= period:
                # 简单移动平均
                ma = np.mean(prices[-period:])
                current_price = prices[-1]
                
                # 价格相对于移动平均的偏离度
                ma_deviation = (current_price - ma) / ma if ma != 0 else 0
                
                factors.append(FactorResult(
                    factor_name=f"ma_{period}_deviation",
                    factor_value=ma_deviation,
                    factor_type="technical",
                    confidence=0.85,
                    metadata={"period": period, "type": "ma_deviation"}
                ))
                
                # 移动平均斜率
                if len(prices) >= period + 5:
                    ma_prev = np.mean(prices[-period-5:-5])
                    ma_slope = (ma - ma_prev) / ma_prev if ma_prev != 0 else 0
                    
                    factors.append(FactorResult(
                        factor_name=f"ma_{period}_slope",
                        factor_value=ma_slope,
                        factor_type="technical",
                        confidence=0.8,
                        metadata={"period": period, "type": "ma_slope"}
                    ))
        
        # 移动平均交叉信号
        if len(prices) >= 20:
            ma_5 = np.mean(prices[-5:])
            ma_20 = np.mean(prices[-20:])
            ma_cross = (ma_5 - ma_20) / ma_20 if ma_20 != 0 else 0
            
            factors.append(FactorResult(
                factor_name="ma_cross_5_20",
                factor_value=ma_cross,
                factor_type="technical",
                confidence=0.9,
                metadata={"type": "ma_cross", "periods": [5, 20]}
            ))
        
        return factors
    
    def _calculate_volatility_factors(self, prices: List[float], highs: List[float], lows: List[float]) -> List[FactorResult]:
        """计算波动率因子"""
        factors = []
        
        # 收益率波动率
        periods = [5, 10, 20, 60]
        for period in periods:
            if len(prices) > period:
                volatility = self._calculate_volatility(prices, period)
                factors.append(FactorResult(
                    factor_name=f"volatility_{period}d",
                    factor_value=volatility,
                    factor_type="technical",
                    confidence=0.85,
                    metadata={"period": period, "type": "return_volatility"}
                ))
        
        # 真实波动幅度 (ATR)
        if len(highs) >= 14 and len(lows) >= 14 and len(prices) >= 14:
            atr = self._calculate_atr(highs, lows, prices, 14)
            current_price = prices[-1]
            atr_ratio = atr / current_price if current_price != 0 else 0
            
            factors.append(FactorResult(
                factor_name="atr_ratio",
                factor_value=atr_ratio,
                factor_type="technical",
                confidence=0.9,
                metadata={"period": 14, "type": "atr"}
            ))
        
        # 价格振幅
        if len(highs) >= 20 and len(lows) >= 20:
            amplitude = self._calculate_amplitude(highs, lows, 20)
            factors.append(FactorResult(
                factor_name="amplitude_20d",
                factor_value=amplitude,
                factor_type="technical",
                confidence=0.8,
                metadata={"period": 20, "type": "amplitude"}
            ))
        
        return factors
    
    def _calculate_volume_factors(self, prices: List[float], volumes: List[float]) -> List[FactorResult]:
        """计算成交量因子"""
        factors = []
        
        if len(volumes) < 2:
            return factors
        
        # 成交量比率
        periods = [5, 10, 20]
        for period in periods:
            if len(volumes) > period:
                volume_ratio = self._calculate_volume_ratio(volumes, period)
                factors.append(FactorResult(
                    factor_name=f"volume_ratio_{period}d",
                    factor_value=volume_ratio,
                    factor_type="technical",
                    confidence=0.75,
                    metadata={"period": period, "type": "volume_ratio"}
                ))
        
        # 量价关系
        if len(prices) >= 5 and len(volumes) >= 5:
            price_volume_corr = self._calculate_price_volume_correlation(prices, volumes, 20)
            factors.append(FactorResult(
                factor_name="price_volume_correlation",
                factor_value=price_volume_corr,
                factor_type="technical",
                confidence=0.7,
                metadata={"period": 20, "type": "price_volume_correlation"}
            ))
        
        # 成交量变化率
        if len(volumes) >= 2:
            volume_change = (volumes[-1] - volumes[-2]) / volumes[-2] if volumes[-2] != 0 else 0
            factors.append(FactorResult(
                factor_name="volume_change_1d",
                factor_value=volume_change,
                factor_type="technical",
                confidence=0.8,
                metadata={"type": "volume_change"}
            ))
        
        return factors
    
    def _calculate_technical_indicators(self, prices: List[float], highs: List[float], 
                                      lows: List[float], opens: List[float], volumes: List[float]) -> List[FactorResult]:
        """计算技术指标因子"""
        factors = []
        
        # RSI指标
        if len(prices) >= self.indicator_params["rsi"] + 1:
            rsi = self._calculate_rsi(prices, self.indicator_params["rsi"])
            factors.append(FactorResult(
                factor_name="rsi_14",
                factor_value=rsi,
                factor_type="technical",
                confidence=0.85,
                metadata={"period": 14, "type": "rsi"}
            ))
        
        # MACD指标
        if len(prices) >= 26:
            macd_line, signal_line, histogram = self._calculate_macd(prices)
            
            factors.append(FactorResult(
                factor_name="macd_line",
                factor_value=macd_line,
                factor_type="technical",
                confidence=0.8,
                metadata={"type": "macd_line"}
            ))
            
            factors.append(FactorResult(
                factor_name="macd_histogram",
                factor_value=histogram,
                factor_type="technical",
                confidence=0.8,
                metadata={"type": "macd_histogram"}
            ))
        
        # 布林带指标
        if len(prices) >= 20:
            bb_position = self._calculate_bollinger_position(prices)
            factors.append(FactorResult(
                factor_name="bollinger_position",
                factor_value=bb_position,
                factor_type="technical",
                confidence=0.8,
                metadata={"type": "bollinger_position"}
            ))
        
        return factors
    
    def _calculate_pattern_factors(self, prices: List[float], highs: List[float], 
                                 lows: List[float], opens: List[float]) -> List[FactorResult]:
        """计算价格形态因子"""
        factors = []
        
        if len(prices) < 5:
            return factors
        
        # 上影线和下影线比率
        if len(highs) >= 5 and len(lows) >= 5 and len(opens) >= 5:
            upper_shadow, lower_shadow = self._calculate_shadow_ratios(highs[-5:], lows[-5:], opens[-5:], prices[-5:])
            
            factors.append(FactorResult(
                factor_name="upper_shadow_ratio",
                factor_value=upper_shadow,
                factor_type="technical",
                confidence=0.7,
                metadata={"type": "candlestick_pattern"}
            ))
            
            factors.append(FactorResult(
                factor_name="lower_shadow_ratio",
                factor_value=lower_shadow,
                factor_type="technical",
                confidence=0.7,
                metadata={"type": "candlestick_pattern"}
            ))
        
        # 价格趋势强度
        if len(prices) >= 10:
            trend_strength = self._calculate_trend_strength(prices, 10)
            factors.append(FactorResult(
                factor_name="trend_strength_10d",
                factor_value=trend_strength,
                factor_type="technical",
                confidence=0.75,
                metadata={"period": 10, "type": "trend_strength"}
            ))
        
        return factors
    
    def _calculate_atr(self, highs: List[float], lows: List[float], closes: List[float], period: int) -> float:
        """计算平均真实波动幅度"""
        if len(highs) < period or len(lows) < period or len(closes) < period:
            return 0.0
        
        true_ranges = []
        for i in range(1, min(len(highs), period + 1)):
            high = highs[-i]
            low = lows[-i]
            prev_close = closes[-i-1]
            
            tr = max(
                high - low,
                abs(high - prev_close),
                abs(low - prev_close)
            )
            true_ranges.append(tr)
        
        return float(np.mean(true_ranges))
    
    def _calculate_amplitude(self, highs: List[float], lows: List[float], period: int) -> float:
        """计算价格振幅"""
        if len(highs) < period or len(lows) < period:
            return 0.0
        
        amplitudes = []
        for i in range(period):
            if i < len(highs) and i < len(lows):
                high = highs[-i-1]
                low = lows[-i-1]
                if low != 0:
                    amplitude = (high - low) / low
                    amplitudes.append(amplitude)
        
        return float(np.mean(amplitudes)) if amplitudes else 0.0
    
    def _calculate_price_volume_correlation(self, prices: List[float], volumes: List[float], period: int) -> float:
        """计算量价相关性"""
        if len(prices) < period or len(volumes) < period:
            return 0.0
        
        price_changes = []
        volume_changes = []
        
        for i in range(1, min(period, len(prices))):
            if prices[-i-1] != 0 and volumes[-i-1] != 0:
                price_change = (prices[-i] - prices[-i-1]) / prices[-i-1]
                volume_change = (volumes[-i] - volumes[-i-1]) / volumes[-i-1]
                price_changes.append(price_change)
                volume_changes.append(volume_change)
        
        if len(price_changes) < 3:
            return 0.0
        
        correlation = np.corrcoef(price_changes, volume_changes)[0, 1]
        return float(correlation) if not np.isnan(correlation) else 0.0
    
    def _calculate_rsi(self, prices: List[float], period: int) -> float:
        """计算RSI指标"""
        if len(prices) < period + 1:
            return 50.0  # 中性值
        
        gains = []
        losses = []
        
        for i in range(1, period + 1):
            change = prices[-i] - prices[-i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(-change)
        
        avg_gain = np.mean(gains)
        avg_loss = np.mean(losses)
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return float(rsi)
    
    def _calculate_macd(self, prices: List[float]) -> tuple:
        """计算MACD指标"""
        if len(prices) < 26:
            return 0.0, 0.0, 0.0
        
        # 计算EMA
        ema_12 = self._calculate_ema(prices, 12)
        ema_26 = self._calculate_ema(prices, 26)
        
        # MACD线
        macd_line = ema_12 - ema_26
        
        # 信号线（MACD的9日EMA）
        
        # 柱状图
        histogram = macd_line - signal_line
        
        return float(macd_line), float(signal_line), float(histogram)
    
    def _calculate_ema(self, prices: List[float], period: int) -> float:
        """计算指数移动平均"""
        if len(prices) < period:
            return np.mean(prices)
        
        multiplier = 2 / (period + 1)
        ema = prices[-period]  # 初始值
        
        for i in range(period - 1, 0, -1):
            ema = (prices[-i] * multiplier) + (ema * (1 - multiplier))
        
        return float(ema)
    
    def _calculate_bollinger_position(self, prices: List[float]) -> float:
        """计算布林带位置"""
        period = 20
        if len(prices) < period:
            return await self._calculate_real_score()  # 中性位置
        
        ma = np.mean(prices[-period:])
        std = np.std(prices[-period:])
        
        if std == 0:
            return await self._calculate_real_score()
        
        current_price = prices[-1]
        upper_band = ma + (2 * std)
        lower_band = ma - (2 * std)
        
        # 计算价格在布林带中的位置 (0-1)
        if upper_band == lower_band:
            return await self._calculate_real_score()
        
        position = (current_price - lower_band) / (upper_band - lower_band)
        return float(max(0, min(1, position)))
    
    def _calculate_shadow_ratios(self, highs: List[float], lows: List[float], 
                               opens: List[float], closes: List[float]) -> tuple:
        """计算上下影线比率"""
        if len(highs) != len(lows) or len(opens) != len(closes) or len(highs) != len(opens):
            return 0.0, 0.0
        
        upper_shadows = []
        lower_shadows = []
        
        for i in range(len(highs)):
            high = highs[i]
            low = lows[i]
            open_price = opens[i]
            close = closes[i]
            
            body_high = max(open_price, close)
            body_low = min(open_price, close)
            body_size = abs(close - open_price)
            
            if body_size > 0:
                upper_shadow = (high - body_high) / body_size
                lower_shadow = (body_low - low) / body_size
                upper_shadows.append(upper_shadow)
                lower_shadows.append(lower_shadow)
        
        avg_upper = np.mean(upper_shadows) if upper_shadows else 0.0
        avg_lower = np.mean(lower_shadows) if lower_shadows else 0.0
        
        return float(avg_upper), float(avg_lower)
    
    def _calculate_trend_strength(self, prices: List[float], period: int) -> float:
        """计算趋势强度"""
        if len(prices) < period:
            return 0.0
        
        # 使用线性回归斜率作为趋势强度
        x = np.arange(period)
        y = prices[-period:]
        
        # 计算斜率
        slope = np.polyfit(x, y, 1)[0]
        
        # 标准化斜率
        avg_price = np.mean(y)
        if avg_price != 0:
            normalized_slope = slope / avg_price
        else:
            normalized_slope = 0.0
        
        return float(normalized_slope)
