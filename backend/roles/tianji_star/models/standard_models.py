#!/usr/bin/env python3
"""
天玑星-风险管理 - 标准数据模型
基于基础模型的角色特定数据模型
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from enum import Enum

# 导入基础模型 - 修复路径
try:
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir))))
    core_path = os.path.join(backend_dir, "core")

    if core_path not in sys.path:
        sys.path.insert(0, core_path)

    from domain.base_models import (
        BaseRequest, BaseResponse, BaseStatus, ServiceStatus, TaskInfo, Priority
    )
except ImportError:
    from dataclasses import dataclass, field
    from typing import Dict, Any, List
    from enum import Enum

    class BaseStatus(Enum):
        PENDING = "pending"
        RUNNING = "running"
        COMPLETED = "completed"
        FAILED = "failed"

    @dataclass
    class BaseRequest:
        request_id: str = ""
        timestamp: str = ""

    @dataclass
    class BaseResponse:
        request_id: str = ""
        success: bool = False
        status: BaseStatus = BaseStatus.PENDING
        message: str = ""
        data: Dict[str, Any] = field(default_factory=dict)
        error_code: str = ""
        error_details: str = ""

    @dataclass
    class ServiceStatus:
        service_name: str = ""
        is_running: bool = False

        def to_dict(self):
            return {"service_name": self.service_name, "is_running": self.is_running}

    @dataclass
    class TaskInfo:
        task_id: str = ""
        task_name: str = ""

        def to_dict(self):
            return {"task_id": self.task_id, "task_name": self.task_name}

    class Priority(Enum):
        LOW = "low"
        MEDIUM = "medium"
        HIGH = "high"

class TianjiStarTaskType(Enum):
    """任务类型枚举"""
    RISK_ASSESSMENT = "risk_assessment"
    PORTFOLIO_ANALYSIS = "portfolio_analysis"
    STRESS_TESTING = "stress_testing"
    RISK_MONITORING = "risk_monitoring"

@dataclass
class TianjiStarRequest(BaseRequest):
    """请求模型"""
    risk_type: str = "portfolio"
    portfolio_data: Dict[str, Any] = field(default_factory=dict)
    risk_metrics: List[str] = field(default_factory=list)
    confidence_level: float = 0.95

    def validate(self) -> List[str]:
        """验证请求数据"""
        errors = []

        # 基础验证
        try:
            from domain.base_models import ModelValidator
            errors.extend(ModelValidator.validate_request(self))
        except ImportError:
            # 简单验证
            if not self.request_id:
                errors.append("request_id不能为空")

        # 角色特定验证
        if not self.portfolio_data:
            errors.append("portfolio_data不能为空")
        if not 0 < self.confidence_level < 1:
            errors.append("confidence_level必须在0和1之间")

        return errors

@dataclass
class TianjiStarResponse(BaseResponse):
    """响应模型"""
    risk_assessment: Dict[str, Any] = field(default_factory=dict)
    risk_score: float = 0.0
    recommendations: List[str] = field(default_factory=list)

    @classmethod
    def create_success(cls, request_id: str, result_data: Dict[str, Any],
                      message: str = "操作成功") -> 'TianjiStarResponse':
        """创建成功响应"""
        return cls(
            request_id=request_id,
            success=True,
            status=BaseStatus.COMPLETED,
            message=message,
            data=result_data,
            risk_assessment=result_data,
            risk_score=result_data.get("risk_score", 0.5)
        )

    @classmethod
    def create_error(cls, request_id: str, error_message: str,
                    error_code: str = None) -> 'TianjiStarResponse':
        """创建错误响应"""
        return cls(
            request_id=request_id,
            success=False,
            status=BaseStatus.FAILED,
            message=error_message,
            error_code=error_code,
            error_details=error_message,
            risk_assessment={},
            risk_score=1.0
        )

@dataclass
class TianjiStarStatus(ServiceStatus):
    """服务状态模型"""
    active_assessments: int = 0
    risk_models_count: int = 0
    alert_count: int = 0

    def get_health_metrics(self) -> Dict[str, Any]:
        """获取健康指标"""
        base_metrics = self.to_dict()

        # 添加角色特定指标
        role_metrics = {
            "active_assessments": self.active_assessments,
            "risk_models_count": self.risk_models_count,
            "alert_count": self.alert_count
        }

        base_metrics.update(role_metrics)
        return base_metrics

@dataclass
class TianjiStarTaskInfo(TaskInfo):
    """任务信息模型"""
    task_type: TianjiStarTaskType = TianjiStarTaskType.RISK_ASSESSMENT
    risk_type: str = ""
    portfolios_assessed: int = 0
    alerts_generated: int = 0

    def get_task_summary(self) -> Dict[str, Any]:
        """获取任务摘要"""
        summary = self.to_dict()

        # 添加角色特定摘要信息
        summary.update({
            "risk_type": self.risk_type,
            "portfolios_assessed": self.portfolios_assessed,
            "alerts_generated": self.alerts_generated
        })

        return summary

# 角色特定的数据类型
@dataclass
class RiskMetric:
    """风险指标"""
    metric_name: str
    value: float
    threshold: float
    status: str

@dataclass
class PortfolioRisk:
    """组合风险"""
    var_95: float
    var_99: float
    max_drawdown: float
    volatility: float

__all__ = [
    'TianjiStarTaskType', 'TianjiStarRequest', 'TianjiStarResponse',
    'TianjiStarStatus', 'TianjiStarTaskInfo'
]
