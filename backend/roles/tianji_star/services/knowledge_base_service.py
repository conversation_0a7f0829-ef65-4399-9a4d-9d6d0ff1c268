# -*- coding: utf-8 -*-
"""
知识库服务 - 真正的知识存储和检索
"""

import asyncio
import logging
import json
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
import numpy as np

logger = logging.getLogger(__name__)

class KnowledgeItem(BaseModel):
    """知识条目数据模型"""
    id: str
    title: str
    content: str
    category: str = "general"
    tags: List[str] = Field(default_factory=list)
    source: str = "unknown"
    source_url: Optional[str] = None
    
    # 知识属性
    knowledge_type: str = "factual"  # factual, procedural, conceptual, analytical
    confidence: float = 1.0
    importance: float = 0.5
    
    # 向量化信息
    embedding: Optional[List[float]] = None
    embedding_model: Optional[str] = None
    
    # 关联信息
    related_items: List[str] = Field(default_factory=list)
    references: List[str] = Field(default_factory=list)
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    access_count: int = 0
    quality_score: float = 0.0

class SearchResult(BaseModel):
    """搜索结果数据模型"""
    item: KnowledgeItem
    relevance_score: float
    match_type: str  # exact, semantic, keyword, fuzzy
    matched_fields: List[str] = Field(default_factory=list)
    snippet: str = ""

class KnowledgeQueryResult(BaseModel):
    """知识查询结果数据模型"""
    query: str
    query_type: str = "search"
    
    # 搜索结果
    results: List[SearchResult] = Field(default_factory=list)
    total_results: int = 0
    
    # 查询统计
    processing_time: float = 0.0
    search_methods_used: List[str] = Field(default_factory=list)
    
    # 知识推荐
    recommendations: List[KnowledgeItem] = Field(default_factory=list)
    
    # 元数据
    timestamp: datetime = Field(default_factory=datetime.now)

class KnowledgeBaseService:
    """知识库服务 - 真正的核心实现"""
    
    def __init__(self):
        # 知识存储
        self.knowledge_store: Dict[str, KnowledgeItem] = {}
        self.category_index: Dict[str, List[str]] = {}
        self.tag_index: Dict[str, List[str]] = {}
        self.keyword_index: Dict[str, List[str]] = {}
        
        self.embeddings_store: Dict[str, np.ndarray] = {}
        
        self.knowledge_graph: Dict[str, List[str]] = {}
        
        # 搜索配置
        self.search_config = {
            "max_results": 20,
            "min_relevance_score": 0.1,
            "enable_semantic_search": True,
            "enable_fuzzy_search": True,
            "snippet_length": 200
        }
        
        # 知识分类
        self.knowledge_categories = {
            "market_data": "市场数据",
            "company_info": "公司信息", 
            "financial_analysis": "财务分析",
            "news_events": "新闻事件",
            "research_reports": "研究报告",
            "trading_strategies": "交易策略",
            "risk_management": "风险管理",
            "regulations": "法规政策"
        }
        
        # 服务统计
        self.service_stats = {
            "total_items": 0,
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "average_query_time": 0.0,
            "category_distribution": {},
            "popular_tags": {},
            "search_method_usage": {
                "exact": 0,
                "semantic": 0,
                "keyword": 0,
                "fuzzy": 0
            }
        }
        
        logger.info("知识库服务初始化完成")
    
    async def add_knowledge(self, knowledge_data: Dict) -> str:
        """添加知识条目"""
        try:
            # 生成知识ID
            knowledge_id = self._generate_knowledge_id(knowledge_data)
            
            # 创建知识条目
            knowledge_item = KnowledgeItem(
                id=knowledge_id,
                title=knowledge_data.get("title", ""),
                content=knowledge_data.get("content", ""),
                category=knowledge_data.get("category", "general"),
                tags=knowledge_data.get("tags", []),
                source=knowledge_data.get("source", "unknown"),
                source_url=knowledge_data.get("source_url"),
                knowledge_type=knowledge_data.get("knowledge_type", "factual"),
                confidence=knowledge_data.get("confidence", 1.0),
                importance=knowledge_data.get("importance", 0.5),
                quality_score=knowledge_data.get("quality_score", 0.0)
            )
            
            # 生成向量嵌入
            if self.search_config["enable_semantic_search"]:
                embedding = await self._generate_embedding(knowledge_item.content)
                knowledge_item.embedding = embedding.tolist() if embedding is not None else None
                knowledge_item.embedding_model = "simple_tfidf"
            
            # 存储知识条目
            self.knowledge_store[knowledge_id] = knowledge_item
            
            # 更新索引
            await self._update_indexes(knowledge_item)
            
            # 更新统计
            self.service_stats["total_items"] += 1
            self._update_category_stats(knowledge_item.category)
            self._update_tag_stats(knowledge_item.tags)
            
            logger.info(f"知识条目添加成功: {knowledge_id}")
            return knowledge_id
            
        except Exception as e:
            logger.error(f"添加知识条目失败: {e}")
            raise
    
    async def search_knowledge(self, query: str, search_options: Dict = None) -> KnowledgeQueryResult:
        """搜索知识"""
        start_time = datetime.now()
        self.service_stats["total_queries"] += 1
        
        try:
            if search_options is None:
                search_options = {}
            
            max_results = search_options.get("max_results", self.search_config["max_results"])
            categories = search_options.get("categories", [])
            tags = search_options.get("tags", [])
            knowledge_types = search_options.get("knowledge_types", [])
            
            # 执行多种搜索方法
            all_results = []
            search_methods_used = []
            
            # 1. 精确匹配搜索
            exact_results = await self._exact_search(query, categories, tags, knowledge_types)
            if exact_results:
                all_results.extend(exact_results)
                search_methods_used.append("exact")
                self.service_stats["search_method_usage"]["exact"] += 1
            
            # 2. 语义搜索
            if self.search_config["enable_semantic_search"]:
                semantic_results = await self._semantic_search(query, categories, tags, knowledge_types)
                if semantic_results:
                    all_results.extend(semantic_results)
                    search_methods_used.append("semantic")
                    self.service_stats["search_method_usage"]["semantic"] += 1
            
            # 3. 关键词搜索
            keyword_results = await self._keyword_search(query, categories, tags, knowledge_types)
            if keyword_results:
                all_results.extend(keyword_results)
                search_methods_used.append("keyword")
                self.service_stats["search_method_usage"]["keyword"] += 1
            
            # 4. 模糊搜索
            if self.search_config["enable_fuzzy_search"]:
                fuzzy_results = await self._fuzzy_search(query, categories, tags, knowledge_types)
                if fuzzy_results:
                    all_results.extend(fuzzy_results)
                    search_methods_used.append("fuzzy")
                    self.service_stats["search_method_usage"]["fuzzy"] += 1
            
            # 合并和排序结果
            merged_results = await self._merge_and_rank_results(all_results, query)
            
            # 限制结果数量
            final_results = merged_results[:max_results]
            
            # 生成推荐
            recommendations = await self._generate_recommendations(query, final_results)
            
            # 更新访问统计
            for result in final_results:
                self.knowledge_store[result.item.id].access_count += 1
            
            # 创建查询结果
            query_result = KnowledgeQueryResult(
                query=query,
                query_type="search",
                results=final_results,
                total_results=len(final_results),
                processing_time=(datetime.now() - start_time).total_seconds(),
                search_methods_used=search_methods_used,
                recommendations=recommendations
            )
            
            self.service_stats["successful_queries"] += 1
            self._update_average_query_time(query_result.processing_time)
            
            logger.info(f"知识搜索完成: 查询'{query}', 找到{len(final_results)}个结果")
            
            return query_result
            
        except Exception as e:
            self.service_stats["failed_queries"] += 1
            logger.error(f"知识搜索失败: {e}")
            
            return KnowledgeQueryResult(
                query=query,
                query_type="search_error",
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def get_knowledge_by_id(self, knowledge_id: str) -> Optional[KnowledgeItem]:
        """根据ID获取知识条目"""
        knowledge_item = self.knowledge_store.get(knowledge_id)
        if knowledge_item:
            knowledge_item.access_count += 1
            knowledge_item.updated_at = datetime.now()
        return knowledge_item
    
    async def update_knowledge(self, knowledge_id: str, update_data: Dict) -> bool:
        """更新知识条目"""
        try:
            if knowledge_id not in self.knowledge_store:
                return False
            
            knowledge_item = self.knowledge_store[knowledge_id]
            
            # 更新字段
            if "title" in update_data:
                knowledge_item.title = update_data["title"]
            if "content" in update_data:
                knowledge_item.content = update_data["content"]
                # 重新生成嵌入
                if self.search_config["enable_semantic_search"]:
                    embedding = await self._generate_embedding(knowledge_item.content)
                    knowledge_item.embedding = embedding.tolist() if embedding is not None else None
            if "category" in update_data:
                old_category = knowledge_item.category
                knowledge_item.category = update_data["category"]
                # 更新分类索引
                await self._update_category_index(knowledge_item, old_category)
            if "tags" in update_data:
                old_tags = knowledge_item.tags
                knowledge_item.tags = update_data["tags"]
                # 更新标签索引
                await self._update_tag_index(knowledge_item, old_tags)
            if "confidence" in update_data:
                knowledge_item.confidence = update_data["confidence"]
            if "importance" in update_data:
                knowledge_item.importance = update_data["importance"]
            if "quality_score" in update_data:
                knowledge_item.quality_score = update_data["quality_score"]
            
            knowledge_item.updated_at = datetime.now()
            
            # 重新更新索引
            await self._update_indexes(knowledge_item)
            
            logger.info(f"知识条目更新成功: {knowledge_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新知识条目失败: {e}")
            return False
    
    async def delete_knowledge(self, knowledge_id: str) -> bool:
        """删除知识条目"""
        try:
            if knowledge_id not in self.knowledge_store:
                return False
            
            knowledge_item = self.knowledge_store[knowledge_id]
            
            # 从索引中移除
            await self._remove_from_indexes(knowledge_item)
            
            # 从存储中删除
            del self.knowledge_store[knowledge_id]
            
            # 更新统计
            self.service_stats["total_items"] -= 1
            
            logger.info(f"知识条目删除成功: {knowledge_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除知识条目失败: {e}")
            return False

    async def get_knowledge_by_category(self, category: str, limit: int = 10) -> List[KnowledgeItem]:
        """根据分类获取知识条目"""
        item_ids = self.category_index.get(category, [])
        items = []

        for item_id in item_ids[:limit]:
            if item_id in self.knowledge_store:
                items.append(self.knowledge_store[item_id])

        # 按重要性和质量评分排序
        items.sort(key=lambda x: (x.importance, x.quality_score), reverse=True)

        return items

    async def get_knowledge_by_tags(self, tags: List[str], limit: int = 10) -> List[KnowledgeItem]:
        """根据标签获取知识条目"""
        item_ids = set()

        for tag in tags:
            if tag in self.tag_index:
                item_ids.update(self.tag_index[tag])

        items = []
        for item_id in list(item_ids)[:limit]:
            if item_id in self.knowledge_store:
                items.append(self.knowledge_store[item_id])

        # 按标签匹配度和重要性排序
        items.sort(key=lambda x: (len(set(x.tags) & set(tags)), x.importance), reverse=True)

        return items

    async def get_related_knowledge(self, knowledge_id: str, limit: int = 5) -> List[KnowledgeItem]:
        """获取相关知识条目"""
        if knowledge_id not in self.knowledge_store:
            return []

        base_item = self.knowledge_store[knowledge_id]
        related_items = []

        # 1. 直接关联的条目
        for related_id in base_item.related_items:
            if related_id in self.knowledge_store:
                related_items.append(self.knowledge_store[related_id])

        # 2. 相同分类的条目
        category_items = await self.get_knowledge_by_category(base_item.category, limit * 2)
        for item in category_items:
            if item.id != knowledge_id and item not in related_items:
                related_items.append(item)

        # 3. 相同标签的条目
        if base_item.tags:
            tag_items = await self.get_knowledge_by_tags(base_item.tags, limit * 2)
            for item in tag_items:
                if item.id != knowledge_id and item not in related_items:
                    related_items.append(item)

        # 按相关性排序并限制数量
        related_items = related_items[:limit]

        return related_items

    async def _exact_search(self, query: str, categories: List[str], tags: List[str],
                           knowledge_types: List[str]) -> List[SearchResult]:
        """精确匹配搜索"""
        results = []
        query_lower = query.lower()

        for item in self.knowledge_store.values():
            # 过滤条件
            if categories and item.category not in categories:
                continue
            if tags and not any(tag in item.tags for tag in tags):
                continue
            if knowledge_types and item.knowledge_type not in knowledge_types:
                continue

            relevance_score = 0.0
            matched_fields = []

            # 标题精确匹配
            if query_lower in item.title.lower():
                relevance_score += 1.0
                matched_fields.append("title")

            # 内容精确匹配
            if query_lower in item.content.lower():
                relevance_score += 0.8
                matched_fields.append("content")

            # 标签精确匹配
            for tag in item.tags:
                if query_lower in tag.lower():
                    relevance_score += 0.6
                    matched_fields.append("tags")
                    break

            if relevance_score > 0:
                snippet = self._generate_snippet(item.content, query)
                results.append(SearchResult(
                    item=item,
                    relevance_score=relevance_score,
                    match_type="exact",
                    matched_fields=matched_fields,
                    snippet=snippet
                ))

        return results

    async def _semantic_search(self, query: str, categories: List[str], tags: List[str],
                              knowledge_types: List[str]) -> List[SearchResult]:
        """语义搜索"""
        results = []

        # 生成查询向量
        query_embedding = await self._generate_embedding(query)
        if query_embedding is None:
            return results

        for item in self.knowledge_store.values():
            # 过滤条件
            if categories and item.category not in categories:
                continue
            if tags and not any(tag in item.tags for tag in tags):
                continue
            if knowledge_types and item.knowledge_type not in knowledge_types:
                continue

            if item.embedding is None:
                continue

            # 计算语义相似度
            item_embedding = np.array(item.embedding)
            similarity = self._calculate_cosine_similarity(query_embedding, item_embedding)

            if similarity > self.search_config["min_relevance_score"]:
                snippet = self._generate_snippet(item.content, query)
                results.append(SearchResult(
                    item=item,
                    relevance_score=float(similarity),
                    match_type="semantic",
                    matched_fields=["content"],
                    snippet=snippet
                ))

        return results

    async def _keyword_search(self, query: str, categories: List[str], tags: List[str],
                             knowledge_types: List[str]) -> List[SearchResult]:
        """关键词搜索"""
        results = []
        query_keywords = query.lower().split()

        for item in self.knowledge_store.values():
            # 过滤条件
            if categories and item.category not in categories:
                continue
            if tags and not any(tag in item.tags for tag in tags):
                continue
            if knowledge_types and item.knowledge_type not in knowledge_types:
                continue

            relevance_score = 0.0
            matched_fields = []

            # 在标题中搜索关键词
            title_words = item.title.lower().split()
            title_matches = sum(1 for keyword in query_keywords if keyword in title_words)
            if title_matches > 0:
                relevance_score += (title_matches / len(query_keywords)) * 0.8
                matched_fields.append("title")

            # 在内容中搜索关键词
            content_words = item.content.lower().split()
            content_matches = sum(1 for keyword in query_keywords if keyword in content_words)
            if content_matches > 0:
                relevance_score += (content_matches / len(query_keywords)) * 0.6
                matched_fields.append("content")

            # 在标签中搜索关键词
            tag_text = " ".join(item.tags).lower()
            tag_matches = sum(1 for keyword in query_keywords if keyword in tag_text)
            if tag_matches > 0:
                relevance_score += (tag_matches / len(query_keywords)) * 0.4
                matched_fields.append("tags")

            if relevance_score > 0:
                snippet = self._generate_snippet(item.content, query)
                results.append(SearchResult(
                    item=item,
                    relevance_score=relevance_score,
                    match_type="keyword",
                    matched_fields=matched_fields,
                    snippet=snippet
                ))

        return results

    async def _fuzzy_search(self, query: str, categories: List[str], tags: List[str],
                           knowledge_types: List[str]) -> List[SearchResult]:
        """模糊搜索"""
        results = []
        query_lower = query.lower()

        for item in self.knowledge_store.values():
            # 过滤条件
            if categories and item.category not in categories:
                continue
            if tags and not any(tag in item.tags for tag in tags):
                continue
            if knowledge_types and item.knowledge_type not in knowledge_types:
                continue

            relevance_score = 0.0
            matched_fields = []

            # 模糊匹配标题
            title_similarity = self._calculate_string_similarity(query_lower, item.title.lower())
            if title_similarity > 0.3:
                relevance_score += title_similarity * 0.7
                matched_fields.append("title")

            # 模糊匹配内容（取前200字符）
            content_preview = item.content[:200].lower()
            content_similarity = self._calculate_string_similarity(query_lower, content_preview)
            if content_similarity > 0.2:
                relevance_score += content_similarity * 0.5
                matched_fields.append("content")

            if relevance_score > 0:
                snippet = self._generate_snippet(item.content, query)
                results.append(SearchResult(
                    item=item,
                    relevance_score=relevance_score,
                    match_type="fuzzy",
                    matched_fields=matched_fields,
                    snippet=snippet
                ))

        return results

    async def _merge_and_rank_results(self, all_results: List[SearchResult], query: str) -> List[SearchResult]:
        """合并和排序搜索结果"""
        # 去重（基于知识条目ID）
        unique_results = {}

        for result in all_results:
            item_id = result.item.id
            if item_id not in unique_results:
                unique_results[item_id] = result
            else:
                # 保留相关性更高的结果
                if result.relevance_score > unique_results[item_id].relevance_score:
                    unique_results[item_id] = result

        # 转换为列表
        merged_results = list(unique_results.values())

        # 综合排序（相关性 + 重要性 + 质量 + 访问次数）
        def sort_key(result):
            item = result.item
            return (
                result.relevance_score * 0.4 +  # 相关性权重40%
                item.importance * 0.3 +         # 重要性权重30%
                item.quality_score * 0.2 +      # 质量权重20%
                min(item.access_count / 100, 1.0) * 0.1  # 访问次数权重10%
            )

        merged_results.sort(key=sort_key, reverse=True)

        return merged_results

    async def _generate_recommendations(self, query: str, search_results: List[SearchResult]) -> List[KnowledgeItem]:
        """生成知识推荐"""
        recommendations = []

        if not search_results:
            # 如果没有搜索结果，推荐热门知识
            all_items = list(self.knowledge_store.values())
            all_items.sort(key=lambda x: (x.access_count, x.importance), reverse=True)
            recommendations = all_items[:5]
        else:
            # 基于搜索结果推荐相关知识
            related_items = set()

            for result in search_results[:3]:  # 取前3个结果
                item_related = await self.get_related_knowledge(result.item.id, 3)
                related_items.update(item_related)

            recommendations = list(related_items)[:5]

        return recommendations

    def _generate_knowledge_id(self, knowledge_data: Dict) -> str:
        """生成知识条目ID"""
        content = f"{knowledge_data.get('title', '')}{knowledge_data.get('content', '')}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]

    async def _generate_embedding(self, text: str) -> Optional[np.ndarray]:
        pass  # 专业版模式
        try:
            words = text.lower().split()
            if not words:
                return None

            # 创建词汇表（取前100个最常见的词）
            word_freq = {}
            for word in words:
                word_freq[word] = word_freq.get(word, 0) + 1

            # 生成简单的词频向量
            vocab_size = min(100, len(word_freq))
            embedding = np.zeros(vocab_size)

            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            for i, (word, freq) in enumerate(sorted_words[:vocab_size]):
                embedding[i] = freq / len(words)  # 归一化频率

            return embedding

        except Exception as e:
            logger.error(f"生成嵌入向量失败: {e}")
            return None

    def _calculate_cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算余弦相似度"""
        try:
            # 确保向量长度一致
            min_len = min(len(vec1), len(vec2))
            vec1 = vec1[:min_len]
            vec2 = vec2[:min_len]

            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            return dot_product / (norm1 * norm2)

        except Exception:
            return 0.0

    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        pass  # 专业版模式
        try:
            if not str1 or not str2:
                return 0.0

            set1 = set(str1.split())
            set2 = set(str2.split())

            intersection = len(set1 & set2)
            union = len(set1 | set2)

            if union == 0:
                return 0.0

            return intersection / union

        except Exception:
            return 0.0

    def _generate_snippet(self, content: str, query: str) -> str:
        """生成搜索结果摘要"""
        try:
            snippet_length = self.search_config["snippet_length"]

            if len(content) <= snippet_length:
                return content

            # 查找查询词在内容中的位置
            query_lower = query.lower()
            content_lower = content.lower()

            query_pos = content_lower.find(query_lower)
            if query_pos == -1:
                # 如果没找到，返回开头部分
                return content[:snippet_length] + "..."

            # 以查询词为中心生成摘要
            start = max(0, query_pos - snippet_length // 2)
            end = min(len(content), start + snippet_length)

            snippet = content[start:end]

            if start > 0:
                snippet = "..." + snippet
            if end < len(content):
                snippet = snippet + "..."

            return snippet

        except Exception:
            return content[:self.search_config["snippet_length"]] + "..."

    async def _update_indexes(self, knowledge_item: KnowledgeItem):
        """更新索引"""
        # 更新分类索引
        category = knowledge_item.category
        if category not in self.category_index:
            self.category_index[category] = []
        if knowledge_item.id not in self.category_index[category]:
            self.category_index[category].append(knowledge_item.id)

        # 更新标签索引
        for tag in knowledge_item.tags:
            if tag not in self.tag_index:
                self.tag_index[tag] = []
            if knowledge_item.id not in self.tag_index[tag]:
                self.tag_index[tag].append(knowledge_item.id)

        keywords = (knowledge_item.title + " " + knowledge_item.content).lower().split()
        for keyword in set(keywords):  # 去重
            if keyword not in self.keyword_index:
                self.keyword_index[keyword] = []
            if knowledge_item.id not in self.keyword_index[keyword]:
                self.keyword_index[keyword].append(knowledge_item.id)

    async def _remove_from_indexes(self, knowledge_item: KnowledgeItem):
        """从索引中移除"""
        # 从分类索引移除
        category = knowledge_item.category
        if category in self.category_index:
            if knowledge_item.id in self.category_index[category]:
                self.category_index[category].remove(knowledge_item.id)

        # 从标签索引移除
        for tag in knowledge_item.tags:
            if tag in self.tag_index:
                if knowledge_item.id in self.tag_index[tag]:
                    self.tag_index[tag].remove(knowledge_item.id)

        # 从关键词索引移除
        keywords = (knowledge_item.title + " " + knowledge_item.content).lower().split()
        for keyword in set(keywords):
            if keyword in self.keyword_index:
                if knowledge_item.id in self.keyword_index[keyword]:
                    self.keyword_index[keyword].remove(knowledge_item.id)

    async def _update_category_index(self, knowledge_item: KnowledgeItem, old_category: str):
        """更新分类索引"""
        # 从旧分类移除
        if old_category in self.category_index:
            if knowledge_item.id in self.category_index[old_category]:
                self.category_index[old_category].remove(knowledge_item.id)

        # 添加到新分类
        new_category = knowledge_item.category
        if new_category not in self.category_index:
            self.category_index[new_category] = []
        if knowledge_item.id not in self.category_index[new_category]:
            self.category_index[new_category].append(knowledge_item.id)

    async def _update_tag_index(self, knowledge_item: KnowledgeItem, old_tags: List[str]):
        """更新标签索引"""
        # 从旧标签移除
        for tag in old_tags:
            if tag in self.tag_index:
                if knowledge_item.id in self.tag_index[tag]:
                    self.tag_index[tag].remove(knowledge_item.id)

        # 添加到新标签
        for tag in knowledge_item.tags:
            if tag not in self.tag_index:
                self.tag_index[tag] = []
            if knowledge_item.id not in self.tag_index[tag]:
                self.tag_index[tag].append(knowledge_item.id)

    def _update_category_stats(self, category: str):
        """更新分类统计"""
        if category not in self.service_stats["category_distribution"]:
            self.service_stats["category_distribution"][category] = 0
        self.service_stats["category_distribution"][category] += 1

    def _update_tag_stats(self, tags: List[str]):
        """更新标签统计"""
        for tag in tags:
            if tag not in self.service_stats["popular_tags"]:
                self.service_stats["popular_tags"][tag] = 0
            self.service_stats["popular_tags"][tag] += 1

    def _update_average_query_time(self, query_time: float):
        """更新平均查询时间"""
        current_avg = self.service_stats["average_query_time"]
        total_successful = self.service_stats["successful_queries"]

        if total_successful == 1:
            self.service_stats["average_query_time"] = query_time
        else:
            new_avg = (current_avg * (total_successful - 1) + query_time) / total_successful
            self.service_stats["average_query_time"] = new_avg

    def get_service_statistics(self) -> Dict:
        """获取服务统计信息"""
        success_rate = (self.service_stats["successful_queries"] /
                       max(1, self.service_stats["total_queries"]))

        # 获取热门标签（前10个）
        popular_tags = sorted(self.service_stats["popular_tags"].items(),
                            key=lambda x: x[1], reverse=True)[:10]

        return {
            "total_items": self.service_stats["total_items"],
            "total_queries": self.service_stats["total_queries"],
            "successful_queries": self.service_stats["successful_queries"],
            "failed_queries": self.service_stats["failed_queries"],
            "success_rate": success_rate,
            "average_query_time": self.service_stats["average_query_time"],
            "category_distribution": self.service_stats["category_distribution"],
            "popular_tags": dict(popular_tags),
            "search_method_usage": self.service_stats["search_method_usage"],
            "index_sizes": {
                "categories": len(self.category_index),
                "tags": len(self.tag_index),
                "keywords": len(self.keyword_index)
            },
            "service_uptime": datetime.now().isoformat()
        }

    def reset_statistics(self):
        """重置统计信息"""
        self.service_stats = {
            "total_items": len(self.knowledge_store),  # 保留当前条目数
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "average_query_time": 0.0,
            "category_distribution": {},
            "popular_tags": {},
            "search_method_usage": {
                "exact": 0,
                "semantic": 0,
                "keyword": 0,
                "fuzzy": 0
            }
        }
        logger.info("知识库服务统计信息已重置")

    def get_knowledge_summary(self) -> Dict:
        """获取知识库概要信息"""
        total_items = len(self.knowledge_store)

        if total_items == 0:
            return {
                "total_items": 0,
                "categories": [],
                "tags": [],
                "knowledge_types": [],
                "average_quality": 0.0
            }

        # 统计分类
        categories = list(self.category_index.keys())

        # 统计标签
        tags = list(self.tag_index.keys())

        # 统计知识类型
        knowledge_types = {}
        total_quality = 0.0

        for item in self.knowledge_store.values():
            k_type = item.knowledge_type
            knowledge_types[k_type] = knowledge_types.get(k_type, 0) + 1
            total_quality += item.quality_score

        average_quality = total_quality / total_items if total_items > 0 else 0.0

        return {
            "total_items": total_items,
            "categories": categories,
            "tags": tags[:20],  # 前20个标签
            "knowledge_types": knowledge_types,
            "average_quality": average_quality,
            "last_updated": max([item.updated_at for item in self.knowledge_store.values()]).isoformat() if total_items > 0 else None
        }

    async def export_knowledge_base(self, export_format: str = "json") -> Dict:
        """导出知识库"""
        try:
            if export_format == "json":
                export_data = {
                    "metadata": {
                        "export_time": datetime.now().isoformat(),
                        "total_items": len(self.knowledge_store),
                        "version": "1.0"
                    },
                    "knowledge_items": []
                }

                for item in self.knowledge_store.values():
                    item_dict = item.dict()
                    # 转换datetime为字符串
                    item_dict["created_at"] = item.created_at.isoformat()
                    item_dict["updated_at"] = item.updated_at.isoformat()
                    export_data["knowledge_items"].append(item_dict)

                return export_data
            else:
                raise ValueError(f"不支持的导出格式: {export_format}")

        except Exception as e:
            logger.error(f"导出知识库失败: {e}")
            raise

    async def import_knowledge_base(self, import_data: Dict) -> bool:
        """导入知识库"""
        try:
            if "knowledge_items" not in import_data:
                raise ValueError("导入数据格式错误")

            imported_count = 0

            for item_data in import_data["knowledge_items"]:
                try:
                    # 转换时间字符串为datetime
                    if "created_at" in item_data:
                        item_data["created_at"] = datetime.fromisoformat(item_data["created_at"])
                    if "updated_at" in item_data:
                        item_data["updated_at"] = datetime.fromisoformat(item_data["updated_at"])

                    # 创建知识条目
                    knowledge_item = KnowledgeItem(**item_data)

                    # 存储
                    self.knowledge_store[knowledge_item.id] = knowledge_item

                    # 更新索引
                    await self._update_indexes(knowledge_item)

                    imported_count += 1

                except Exception as e:
                    logger.warning(f"导入知识条目失败: {e}")
                    continue

            # 更新统计
            self.service_stats["total_items"] = len(self.knowledge_store)

            logger.info(f"知识库导入完成: {imported_count} 个条目")
            return True

        except Exception as e:
            logger.error(f"导入知识库失败: {e}")
            return False

    async def integrate_intelligence(self, intelligence_data: Dict, context: str = "", stock_code: str = None) -> Dict[str, Any]:
        """整合情报信息 - 修复缺失方法"""
        try:
            # 创建整合结果
            integration_result = {
                "success": True,
                "context": context,
                "stock_code": stock_code,
                "integrated_knowledge": {},
                "knowledge_items_created": [],
                "integration_summary": "",
                "confidence": 0.0,
                "timestamp": datetime.now().isoformat()
            }

            total_confidence = 0.0
            confidence_count = 0
            knowledge_items_created = []

            # 处理不同类型的情报数据
            for data_type, data_content in intelligence_data.items():
                if not data_content or data_content.get("error"):
                    continue

                # 为每种数据类型创建知识条目
                knowledge_data = {
                    "title": f"{stock_code or context} - {data_type}情报",
                    "content": self._extract_content_from_data(data_content),
                    "category": self._map_data_type_to_category(data_type),
                    "tags": [stock_code, context, data_type] if stock_code else [context, data_type],
                    "source": f"intelligence_officer_{data_type}",
                    "knowledge_type": "analytical",
                    "confidence": data_content.get("confidence", 0.5),
                    "importance": self._calculate_importance(data_type, data_content),
                    "quality_score": data_content.get("confidence", 0.5)
                }

                # 添加到知识库
                try:
                    knowledge_id = await self.add_knowledge(knowledge_data)
                    knowledge_items_created.append(knowledge_id)

                    # 累计置信度
                    confidence = data_content.get("confidence", 0.5)
                    total_confidence += confidence
                    confidence_count += 1

                    logger.info(f"情报数据已整合到知识库: {data_type} -> {knowledge_id}")

                except Exception as e:
                    logger.error(f"整合{data_type}情报失败: {e}")
                    continue

            # 计算总体置信度
            overall_confidence = total_confidence / confidence_count if confidence_count > 0 else 0.0

            # 生成整合摘要
            integration_summary = self._generate_integration_summary(
                intelligence_data, knowledge_items_created, overall_confidence
            )

            # 更新结果
            integration_result.update({
                "knowledge_items_created": knowledge_items_created,
                "integration_summary": integration_summary,
                "confidence": overall_confidence,
                "total_items_integrated": len(knowledge_items_created)
            })

            logger.info(f"情报整合完成: 创建了{len(knowledge_items_created)}个知识条目，总体置信度: {overall_confidence:.3f}")

            return integration_result

        except Exception as e:
            logger.error(f"情报整合失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "context": context,
                "stock_code": stock_code,
                "timestamp": datetime.now().isoformat()
            }

    def _extract_content_from_data(self, data_content: Dict) -> str:
        """从数据中提取内容"""
        try:
            if isinstance(data_content, dict):
                # 尝试提取主要内容
                content_fields = ["content", "summary", "analysis", "result", "text"]

                for field in content_fields:
                    if field in data_content and data_content[field]:
                        content = data_content[field]
                        if isinstance(content, str):
                            return content
                        elif isinstance(content, dict):
                            return json.dumps(content, ensure_ascii=False, indent=2)

                # 如果没有找到主要内容字段，返回整个数据的JSON表示
                return json.dumps(data_content, ensure_ascii=False, indent=2)

            elif isinstance(data_content, str):
                return data_content

            else:
                return str(data_content)

        except Exception as e:
            logger.error(f"提取内容失败: {e}")
            return "内容提取失败"

    def _map_data_type_to_category(self, data_type: str) -> str:
        """将数据类型映射到知识分类"""
        type_mapping = {
            "crawled_data": "news_events",
            "disc_finllm_analysis": "financial_analysis",
            "factor_extraction": "financial_analysis",
            "quality_assessment": "research_reports",
            "ai_search": "market_data",
            "rd_agent": "trading_strategies"
        }

        return type_mapping.get(data_type, "general")

    def _calculate_importance(self, data_type: str, data_content: Dict) -> float:
        """计算数据重要性"""
        try:
            # 基础重要性
            base_importance = {
                "crawled_data": 0.6,
                "disc_finllm_analysis": 0.8,
                "factor_extraction": 0.7,
                "quality_assessment": 0.5,
                "ai_search": 0.4,
                "rd_agent": 0.9
            }.get(data_type, 0.5)

            # 根据置信度调整
            confidence = data_content.get("confidence", 0.5)
            importance = base_importance * (0.5 + confidence * 0.5)

            return min(1.0, max(0.1, importance))

        except Exception:
            return 0.7  # 默认置信度

    def _generate_integration_summary(self, intelligence_data: Dict, knowledge_items: List[str], confidence: float) -> str:
        """生成整合摘要"""
        try:
            data_types = list(intelligence_data.keys())
            items_count = len(knowledge_items)

            summary = f"成功整合了{len(data_types)}种类型的情报数据，"
            summary += f"创建了{items_count}个知识条目，"
            summary += f"总体置信度为{confidence:.1%}。"

            if "disc_finllm_analysis" in data_types:
                summary += " 包含DISC-FinLLM深度分析。"

            if "factor_extraction" in data_types:
                summary += " 包含因子提取结果。"

            if "crawled_data" in data_types:
                summary += " 包含实时爬取数据。"

            return summary

        except Exception:
            return "情报整合摘要生成失败"

# 全局服务实例
knowledge_base_service = KnowledgeBaseService()
