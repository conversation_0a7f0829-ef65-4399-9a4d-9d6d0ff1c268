#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强情报官工作流 - 集成记忆系统、成本管理和双层闭环
在现有情报工作流基础上添加智能记忆和自动化协作功能
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from roles.base_role_enhanced import RoleEnhancementMixin
from roles.intelligence_officer.workflows.intelligence_workflow_service import (
    IntelligenceWorkflowCollection, IntelligenceWorkflowConfig, IntelligenceDataPacket
)
from backend.core.domain.messaging.message import InvestmentMessage, MessageType, MessagePriority

logger = logging.getLogger(__name__)

class EnhancedIntelligenceWorkflow(RoleEnhancementMixin):
    """增强情报官工作流 - 集成记忆系统和双层闭环"""
    
    def __init__(self, config: IntelligenceWorkflowConfig = None):
        # 初始化增强功能
        super().__init__()
        
        # 原有工作流
        self.original_workflow = IntelligenceWorkflowCollection(config)
        
        # 增强配置
        self.role_type = "intelligence_officer"
        self.role_id = "intelligence_officer_enhanced"
        
        # 工作流状态
        self.is_initialized = False
        
        logger.info("增强情报官工作流初始化")
    
    async def initialize(self):
        """初始化增强功能"""
        
        if self.is_initialized:
            return
        
        try:
            # 初始化角色增强功能
            await self.initialize_role_enhancement(self.role_id, self.role_type)
            
            self.is_initialized = True
            logger.info("增强情报官工作流初始化完成")
            
        except Exception as e:
            logger.error(f"增强情报官工作流初始化失败: {e}")
            raise
    
    def set_external_services(self, **services):
        """设置外部服务"""
        self.original_workflow.set_external_services(**services)
    
    async def execute_enhanced_intelligence_workflow(
        self, 
        trigger_event: str, 
        stock_code: Optional[str] = None,
        analysis_type: str = "comprehensive"
    ) -> IntelligenceDataPacket:
        """执行增强的情报工作流 - 集成双层闭环和记忆系统"""
        
        if not self.is_initialized:
            await self.initialize()
        
        logger.info(f"开始执行增强情报工作流: {trigger_event}")
        
        # 1. 启动外层闭环协作
        outer_loop_id = await self.start_outer_loop_collaboration(
            f"情报收集与分析: {trigger_event}"
        )
        
        try:
            # 2. 创建触发消息
            trigger_message = InvestmentMessage(
                content=f"情报工作流触发: {trigger_event}",
                sent_from=self.role_id,
                send_to={"intelligence_workflow"},
                cause_by="workflow_trigger",
                message_type=MessageType.MARKET_ANALYSIS,
                priority=MessagePriority.HIGH,
                metadata={
                    "stock_code": stock_code,
                    "analysis_type": analysis_type,
                    "trigger_event": trigger_event
                }
            )
            
            # 3. 使用增强的消息处理（包含内层闭环）
            enhanced_responses = await self.enhanced_process_message(
                trigger_message,
                self._original_workflow_handler
            )
            
            # 4. 从响应中提取数据包
            data_packet = None
            if enhanced_responses and hasattr(enhanced_responses[0], 'metadata'):
                data_packet_id = enhanced_responses[0].metadata.get('data_packet_id')
                if data_packet_id and data_packet_id in self.original_workflow.workflow_history:
                    for packet in self.original_workflow.workflow_history:
                        if packet.packet_id == data_packet_id:
                            data_packet = packet
                            break
            
            # 5. 如果没有找到数据包，创建一个基础的
            if not data_packet:
                pass
            # 6. 增强数据包（添加记忆洞察）
            await self._enhance_data_packet_with_memory(data_packet)
            
            # 7. 完成外层闭环协作
            await self.complete_outer_loop_collaboration(
                success=True,
                results={
                    "data_packet_id": data_packet.packet_id,
                    "overall_confidence": data_packet.overall_confidence,
                    "analysis_stages_completed": 5
                }
            )
            
            logger.info(f"增强情报工作流执行完成: {data_packet.packet_id}")
            return data_packet
            
        except Exception as e:
            logger.error(f"增强情报工作流执行失败: {e}")
            
            # 失败时也要完成外层闭环
            await self.complete_outer_loop_collaboration(
                success=False,
                results={"error": str(e)}
            )
            
            raise
    
    async def _original_workflow_handler(self, message: InvestmentMessage) -> List[InvestmentMessage]:
        """原始工作流处理器"""
        
        try:
            # 从消息中提取参数
            trigger_event = message.metadata.get('trigger_event', message.content)
            stock_code = message.metadata.get('stock_code')
            analysis_type = message.metadata.get('analysis_type', 'comprehensive')
            
            # 执行原始工作流
            data_packet = await self.original_workflow.execute_intelligence_workflow(
                trigger_event=trigger_event,
                stock_code=stock_code,
                analysis_type=analysis_type
            )
            
            # 创建响应消息
            response = InvestmentMessage(
                content=f"情报工作流完成: {data_packet.packet_id}",
                sent_from=self.role_id,
                send_to={message.sent_from},
                cause_by="workflow_completion",
                message_type=MessageType.MARKET_ANALYSIS,
                priority=MessagePriority.HIGH,
                metadata={
                    "data_packet_id": data_packet.packet_id,
                    "overall_confidence": data_packet.overall_confidence,
                    "analysis_complete": True
                }
            )
            
            return [response]
            
        except Exception as e:
            logger.error(f"原始工作流处理失败: {e}")
            
            # 创建错误响应
            error_response = InvestmentMessage(
                content=f"情报工作流执行失败: {str(e)}",
                sent_from=self.role_id,
                send_to={message.sent_from},
                cause_by="workflow_error",
                message_type=MessageType.SYSTEM_NOTIFICATION,
                priority=MessagePriority.HIGH,
                metadata={"error": str(e)}
            )
            
            return [error_response]
    
    async def _create_fallback_data_packet(self, trigger_event: str, stock_code: Optional[str]) -> IntelligenceDataPacket:
        """创建备用数据包"""
        
        data_packet = IntelligenceDataPacket(

            stock_code=stock_code
        )
        
        # 添加基础信息
        data_packet.integrated_intelligence = {
            "trigger_event": trigger_event,

            "timestamp": datetime.now().isoformat()
        }
        
        data_packet.overall_confidence = 0.5  # 中等置信度
        
        return data_packet
    
    async def _enhance_data_packet_with_memory(self, data_packet: IntelligenceDataPacket):
        """使用记忆系统增强数据包"""
        
        if not self.memory_manager:
            return
        
        try:
            # 1. 搜索相关历史案例
            if data_packet.stock_code:
                search_query = f"股票 {data_packet.stock_code} 情报分析"
            else:
                search_query = "市场情报分析"
            
            similar_cases = await self.memory_manager.search_memory(
                search_query,
                message_types=[MessageType.MARKET_ANALYSIS],
                include_longterm=True
            )
            
            # 2. 获取上下文推荐
            recommendations = await self.memory_manager.get_contextual_recommendations(
                f"基于历史经验，如何分析当前情况: {data_packet.integrated_intelligence.get('trigger_event', '')}"
            )
            
            # 3. 生成记忆洞察
            insights = await self.memory_manager.generate_insights()
            
            # 4. 将记忆信息添加到数据包
            memory_enhancement = {
                "similar_cases_count": len(similar_cases),
                "recommendations_count": len(recommendations),
                "insights_count": len(insights),
                "memory_enhanced": True,
                "enhancement_timestamp": datetime.now().isoformat()
            }
            
            # 如果有推荐，添加到分析中
            if recommendations:
                memory_enhancement["top_recommendation"] = recommendations[0]
            
            # 如果有洞察，添加到分析中
            if insights:
                memory_enhancement["key_insights"] = [
                    {
                        "title": insight.title,
                        "description": insight.description,
                        "confidence": insight.confidence
                    }
                    for insight in insights[:3]  # 只取前3个洞察
                ]
            
            # 更新数据包
            if "memory_enhancement" not in data_packet.integrated_intelligence:
                data_packet.integrated_intelligence["memory_enhancement"] = memory_enhancement
            
            # 基于记忆调整置信度
            if similar_cases and recommendations:
                # 如果有相关历史案例和推荐，提高置信度
                memory_boost = min(0.2, len(similar_cases) * 0.05)
                data_packet.overall_confidence = min(1.0, data_packet.overall_confidence + memory_boost)
            
            logger.info(f"记忆增强完成: {len(similar_cases)}个相似案例, {len(recommendations)}个推荐")
            
        except Exception as e:
            logger.error(f"记忆增强失败: {e}")
            # 添加错误信息但不影响主流程
            data_packet.integrated_intelligence["memory_enhancement"] = {
                "error": str(e),
                "memory_enhanced": False
            }
    
    async def get_workflow_status(self) -> Dict[str, Any]:
        """获取工作流状态"""
        
        base_status = self.get_enhancement_status()
        
        workflow_status = {
            "active_workflows": len(self.original_workflow.active_workflows),
            "completed_workflows": len(self.original_workflow.workflow_history),
            "config": self.original_workflow.config.dict(),
            "services_configured": {
                "crawl4ai": self.original_workflow.crawl4ai_service is not None,
                "disc_finllm": self.original_workflow.disc_finllm_service is not None,
                "factor_extraction": self.original_workflow.factor_extraction_service is not None,
                "ai_evaluation": self.original_workflow.ai_evaluation_service is not None,
                "knowledge_base": self.original_workflow.knowledge_base_service is not None,
                "ai_search": self.original_workflow.ai_search_service is not None,
                "rd_agent": self.original_workflow.rd_agent_service is not None
            }
        }
        
        return {**base_status, "workflow_status": workflow_status}
    
    async def get_recent_intelligence_summary(self, limit: int = 5) -> Dict[str, Any]:
        """获取最近的情报摘要"""
        
        recent_packets = self.original_workflow.workflow_history[-limit:] if self.original_workflow.workflow_history else []
        
        summary = {
            "total_packets": len(self.original_workflow.workflow_history),
            "recent_packets": [],
            "avg_confidence": 0.0,
            "memory_enhanced_count": 0
        }
        
        total_confidence = 0.0
        memory_enhanced_count = 0
        
        for packet in recent_packets:
            packet_summary = {
                "packet_id": packet.packet_id,
                "stock_code": packet.stock_code,
                "timestamp": packet.timestamp.isoformat(),
                "overall_confidence": packet.overall_confidence,
                "memory_enhanced": "memory_enhancement" in packet.integrated_intelligence
            }
            
            summary["recent_packets"].append(packet_summary)
            total_confidence += packet.overall_confidence
            
            if packet_summary["memory_enhanced"]:
                memory_enhanced_count += 1
        
        if recent_packets:
            summary["avg_confidence"] = total_confidence / len(recent_packets)
            summary["memory_enhanced_count"] = memory_enhanced_count
        
        return summary

# 全局增强情报官工作流实例
enhanced_intelligence_workflow = EnhancedIntelligenceWorkflow()
