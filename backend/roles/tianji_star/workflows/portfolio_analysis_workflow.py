from backend.config.database_config import get_database_path
#!/usr/bin/env python3
"""
Portfolio Analysis 工作流
天玑星-风险管理 - portfolio analysis工作流实现
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class WorkflowStatus(Enum):
    """工作流状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class PortfolioAnalysisWorkflow:
    """
    Portfolio Analysis工作流
    """
    
    def __init__(self):
        self.workflow_id = f"portfolio_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.status = WorkflowStatus.PENDING
        self.start_time = None
        self.end_time = None
        self.results = {}
        self.errors = []
        
        # 工作流步骤定义
        self.steps = self._define_workflow_steps()
        self.current_step = 0
    
    def _define_workflow_steps(self) -> List[Dict[str, Any]]:
        """定义工作流步骤"""
        return [
            {
                "name": "initialize",
                "description": "初始化工作流",
                "required": True,
                "timeout": 30
            },
            {
                "name": "execute_main_logic",
                "description": "执行主要逻辑",
                "required": True,
                "timeout": 300
            },
            {
                "name": "validate_results",
                "description": "验证结果",
                "required": True,
                "timeout": 60
            },
            {
                "name": "finalize",
                "description": "完成工作流",
                "required": True,
                "timeout": 30
            }
        ]
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行工作流"""
        try:
            logger.info(f"开始执行工作流: {self.workflow_id}")
            self.status = WorkflowStatus.RUNNING
            self.start_time = datetime.now()
            
            # 执行所有步骤
            for i, step in enumerate(self.steps):
                self.current_step = i
                await self._execute_step(step, input_data)
            
            self.status = WorkflowStatus.COMPLETED
            self.end_time = datetime.now()
            
            logger.info(f"工作流执行完成: {self.workflow_id}")
            return self.results
            
        except Exception as e:
            self.status = WorkflowStatus.FAILED
            self.end_time = datetime.now()
            error_msg = f"工作流执行失败: {e}"
            self.errors.append(error_msg)
            logger.error(error_msg)
            return {"error": error_msg}
    
    async def _execute_step(self, step: Dict[str, Any], input_data: Dict[str, Any]):
        """执行单个步骤"""
        step_name = step["name"]
        
        try:
            logger.debug(f"执行步骤: {step_name}")
            
            # 根据步骤名称调用相应的方法
            if step_name == "initialize":
                await self._step_initialize(input_data)
            elif step_name == "execute_main_logic":
                await self._step_execute_main_logic(input_data)
            elif step_name == "validate_results":
                await self._step_validate_results(input_data)
            elif step_name == "finalize":
                await self._step_finalize(input_data)
            else:
                logger.warning(f"未知步骤: {step_name}")
            
        except Exception as e:
            error_msg = f"步骤 {step_name} 执行失败: {e}"
            self.errors.append(error_msg)
            logger.error(error_msg)
            raise
    
    async def _step_initialize(self, input_data: Dict[str, Any]):
        """初始化步骤"""
        # 验证输入数据
        if not input_data:
            raise ValueError("输入数据不能为空")
        
        # 初始化结果字典
        self.results["initialized"] = True
        self.results["input_data"] = input_data
        
        logger.debug("工作流初始化完成")
    
    async def _step_execute_main_logic(self, input_data: Dict[str, Any]):
        """执行主要逻辑步骤 - 投资组合分析"""
        portfolio = input_data.get("portfolio", [])

        if not portfolio:
            raise ValueError("投资组合数据不能为空")

        # 1. 收集市场数据
        market_data = await self._collect_portfolio_market_data(portfolio)
        self.results["market_data"] = market_data

        # 2. 计算投资组合指标
        portfolio_metrics = await self._calculate_portfolio_metrics(portfolio, market_data)
        self.results["portfolio_metrics"] = portfolio_metrics

        # 3. 风险分解分析
        risk_decomposition = await self._analyze_risk_decomposition(portfolio, portfolio_metrics)
        self.results["risk_decomposition"] = risk_decomposition

        # 4. 相关性分析
        correlation_analysis = await self._analyze_correlations(portfolio, market_data)
        self.results["correlation_analysis"] = correlation_analysis

        # 5. 生成优化建议
        optimization_suggestions = await self._generate_optimization_suggestions(
            portfolio_metrics, risk_decomposition, correlation_analysis
        )
        self.results["optimization_suggestions"] = optimization_suggestions

        logger.debug("投资组合分析主要逻辑执行完成")

    async def _step_validate_results(self, input_data: Dict[str, Any]):
        """验证结果步骤"""
        required_keys = ["portfolio_metrics", "risk_decomposition", "correlation_analysis"]

        for key in required_keys:
            if key not in self.results:
                raise ValueError(f"缺少分析结果: {key}")

        # 检查数据质量
        quality_score = await self._check_analysis_quality()
        self.results["quality_score"] = quality_score

        if quality_score < 0.7:
            logger.warning(f"分析质量较低: {quality_score}")

        logger.debug("投资组合分析结果验证完成")

    async def _step_finalize(self, input_data: Dict[str, Any]):
        """完成步骤"""
        # 生成最终报告
        final_report = {
            "analysis_id": self.workflow_id,
            "analysis_time": datetime.now().isoformat(),
            "portfolio_summary": self._generate_portfolio_summary(input_data.get("portfolio", [])),
            "risk_metrics": self.results.get("portfolio_metrics", {}),
            "risk_analysis": self.results.get("risk_decomposition", {}),
            "correlation_analysis": self.results.get("correlation_analysis", {}),
            "optimization_suggestions": self.results.get("optimization_suggestions", []),
            "quality_score": self.results.get("quality_score", 0.0)
        }

        self.results["final_report"] = final_report
        self.results["workflow_id"] = self.workflow_id
        self.results["execution_time"] = (self.end_time or datetime.now() - self.start_time).total_seconds()
        self.results["status"] = "completed"

        logger.debug("投资组合分析工作流完成")

    async def _collect_portfolio_market_data(self, portfolio: List[Dict[str, Any]]) -> Dict[str, Any]:
        """收集投资组合市场数据"""
        market_data = {}

        for position in portfolio:
            stock_code = position.get("stock_code")
            if not stock_code:
                continue

            try:
                # 从数据库获取股票数据
                stock_data = await self._get_stock_data(stock_code)
                market_data[stock_code] = stock_data
            except Exception as e:
                logger.warning(f"获取股票数据失败 {stock_code}: {e}")
                # 使用默认数据
                market_data[stock_code] = {
                    "returns": [0.0] * 30,
                    "volatility": 0.2,
                    "beta": 1.0
                }

        return market_data

    async def _get_stock_data(self, stock_code: str) -> Dict[str, Any]:
        """获取单只股票数据"""
        try:
            import sqlite3
            import numpy as np

            conn = sqlite3.connect(get_database_path("stock_database"))
            cursor = conn.cursor()

            # 处理股票代码格式 (去掉交易所后缀)
            clean_stock_code = stock_code.split('.')[0] if '.' in stock_code else stock_code

            # 获取历史价格数据
            cursor.execute("""
                SELECT close_price, change_percent
                FROM daily_data
                WHERE stock_code = ?
                ORDER BY trade_date DESC
                LIMIT 60
            """, (clean_stock_code,))

            data = cursor.fetchall()
            conn.close()

            if not data:
                raise ValueError(f"没有找到股票数据: {stock_code}")

            prices = [float(row[0]) for row in data if row[0]]
            returns = [float(row[1])/100 for row in data if row[1] is not None]

            # 计算统计指标
            volatility = np.std(returns) if len(returns) > 1 else 0.2

            return {
                "prices": prices,
                "returns": returns,
                "volatility": volatility,
                "beta": beta,
                "data_points": len(prices)
            }

        except Exception as e:
            logger.error(f"获取股票数据失败 {stock_code}: {e}")
            # 返回默认数据
            return {
                "prices": [100.0] * 30,
                "returns": [0.0] * 30,
                "volatility": 0.2,
                "beta": 1.0,
                "data_points": 30
            }

    async def _check_analysis_quality(self) -> float:
        """检查分析质量"""
        quality_factors = []

        # 检查投资组合指标完整性
        portfolio_metrics = self.results.get("portfolio_metrics", {})
        if "portfolio_return" in portfolio_metrics and "portfolio_volatility" in portfolio_metrics:
            quality_factors.append(1.0)

        # 检查风险分解完整性
        risk_decomposition = self.results.get("risk_decomposition", {})
        if "individual_risk_contributions" in risk_decomposition:
            quality_factors.append(1.0)

        # 检查相关性分析完整性
        correlation_analysis = self.results.get("correlation_analysis", {})
        if "correlation_matrix" in correlation_analysis:
            quality_factors.append(1.0)

        # 检查优化建议
        suggestions = self.results.get("optimization_suggestions", [])
        if suggestions:
            quality_factors.append(1.0)

        return sum(quality_factors) / len(quality_factors) if quality_factors else 0.0

    async def _calculate_portfolio_metrics(self, portfolio: List[Dict[str, Any]], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算投资组合指标"""
        import numpy as np

        # 计算总市值和权重
        total_value = sum(pos.get("market_value", 0) for pos in portfolio)
        weights = []
        returns = []
        volatilities = []
        betas = []

        for position in portfolio:
            stock_code = position.get("stock_code")
            market_value = position.get("market_value", 0)
            weight = market_value / total_value if total_value > 0 else 0
            weights.append(weight)

            stock_market_data = market_data.get(stock_code, {})
            stock_returns = stock_market_data.get("returns", [0.0])

            avg_return = np.mean(stock_returns) if stock_returns else 0.0
            returns.append(avg_return)

            volatilities.append(stock_market_data.get("volatility", 0.2))
            betas.append(stock_market_data.get("beta", 1.0))

        # 投资组合指标计算
        weights_array = np.array(weights)
        returns_array = np.array(returns)
        volatilities_array = np.array(volatilities)
        betas_array = np.array(betas)

        portfolio_return = np.sum(weights_array * returns_array)
        portfolio_volatility = np.sqrt(np.sum((weights_array * volatilities_array)**2))
        portfolio_beta = np.sum(weights_array * betas_array)

        # 夏普比率（假设无风险利率3%）
        risk_free_rate = 0.03 / 252
        sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_volatility if portfolio_volatility > 0 else 0

        # 集中度指标
        max_weight = max(weights) if weights else 0
        effective_stocks = 1 / np.sum(weights_array**2) if len(weights) > 0 else 0

        return {
            "total_value": total_value,
            "portfolio_return": portfolio_return,
            "portfolio_volatility": portfolio_volatility,
            "portfolio_beta": portfolio_beta,
            "sharpe_ratio": sharpe_ratio,
            "max_weight_concentration": max_weight,
            "effective_number_of_stocks": effective_stocks,
            "weights": weights,
            "individual_returns": returns,
            "individual_volatilities": volatilities,
            "individual_betas": betas
        }

    async def _analyze_risk_decomposition(self, portfolio: List[Dict[str, Any]], metrics: Dict[str, Any]) -> Dict[str, Any]:
        """风险分解分析"""
        import numpy as np

        weights = np.array(metrics["weights"])
        volatilities = np.array(metrics["individual_volatilities"])

        # 个股风险贡献
        individual_risk_contributions = (weights * volatilities)**2
        total_risk = np.sum(individual_risk_contributions)

        risk_contributions = []
        for i, position in enumerate(portfolio):
            contribution = individual_risk_contributions[i] / total_risk if total_risk > 0 else 0
            risk_contributions.append({
                "stock_code": position.get("stock_code"),
                "weight": weights[i],
                "volatility": volatilities[i],
                "risk_contribution": contribution,
                "risk_contribution_percentage": contribution * 100
            })

        # 按风险贡献排序
        risk_contributions.sort(key=lambda x: x["risk_contribution"], reverse=True)

        # 风险集中度分析
        top_3_risk = sum(item["risk_contribution"] for item in risk_contributions[:3])
        top_5_risk = sum(item["risk_contribution"] for item in risk_contributions[:5])

        return {
            "individual_risk_contributions": risk_contributions,
            "top_3_risk_concentration": top_3_risk,
            "top_5_risk_concentration": top_5_risk,
            "risk_diversification_score": 1 - top_3_risk,
            "total_idiosyncratic_risk": total_risk
        }

    async def _analyze_correlations(self, portfolio: List[Dict[str, Any]], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """相关性分析"""
        import numpy as np

        stock_codes = [pos.get("stock_code") for pos in portfolio]
        returns_matrix = []

        # 构建收益率矩阵
        for stock_code in stock_codes:
            stock_returns = market_data.get(stock_code, {}).get("returns", [])
            if len(stock_returns) < 20:
                # 数据不足，使用保守的零收益率
                logger.warning(f"股票 {stock_code} 数据不足，使用保守估计")
                stock_returns = [0.0] * 30
            returns_matrix.append(stock_returns[:30])

        # 计算相关性矩阵
        if len(returns_matrix) > 1:
            correlation_matrix = np.corrcoef(returns_matrix)

            # 平均相关性
            n = len(correlation_matrix)
            total_correlations = 0
            count = 0

            for i in range(n):
                for j in range(i+1, n):
                    total_correlations += correlation_matrix[i][j]
                    count += 1

            average_correlation = total_correlations / count if count > 0 else 0

            # 最高相关性对
            max_correlation = 0
            max_pair = None

            for i in range(n):
                for j in range(i+1, n):
                    corr = correlation_matrix[i][j]
                    if abs(corr) > abs(max_correlation):
                        max_correlation = corr
                        max_pair = (stock_codes[i], stock_codes[j])
        else:
            correlation_matrix = [[1.0]]
            average_correlation = 0
            max_correlation = 0
            max_pair = None

        return {
            "correlation_matrix": correlation_matrix.tolist() if hasattr(correlation_matrix, 'tolist') else correlation_matrix,
            "average_correlation": average_correlation,
            "max_correlation": max_correlation,
            "max_correlation_pair": max_pair,
            "diversification_ratio": 1 - abs(average_correlation)
        }

    async def _generate_optimization_suggestions(self, metrics: Dict[str, Any], risk_decomposition: Dict[str, Any], correlation_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成优化建议"""
        suggestions = []

        # 基于风险集中度的建议
        if risk_decomposition.get("top_3_risk_concentration", 0) > 0.6:
            suggestions.append({
                "type": "risk_concentration",
                "priority": "high",
                "description": "前3只股票风险集中度过高，建议分散投资",
                "action": "减少高风险贡献股票的权重",
                "current_value": risk_decomposition.get("top_3_risk_concentration", 0),
                "target_value": 0.5
            })

        # 基于相关性的建议
        if correlation_analysis.get("average_correlation", 0) > 0.7:
            suggestions.append({
                "type": "correlation",
                "priority": "medium",
                "description": "投资组合相关性过高，分散化效果有限",
                "action": "增加不同行业或风格的股票",
                "current_value": correlation_analysis.get("average_correlation", 0),
                "target_value": 0.5
            })

        # 基于夏普比率的建议
        if metrics.get("sharpe_ratio", 0) < 0.5:
            suggestions.append({
                "type": "risk_return",
                "priority": "medium",
                "description": "风险调整后收益较低",
                "action": "考虑调整资产配置或降低波动率",
                "current_value": metrics.get("sharpe_ratio", 0),
                "target_value": 1.0
            })

        # 基于权重集中度的建议
        if metrics.get("max_weight_concentration", 0) > 0.3:
            suggestions.append({
                "type": "weight_concentration",
                "priority": "medium",
                "description": "单只股票权重过高",
                "action": "降低最大权重股票的配置比例",
                "current_value": metrics.get("max_weight_concentration", 0),
                "target_value": 0.2
            })

        if not suggestions:
            suggestions.append({
                "type": "general",
                "priority": "low",
                "description": "投资组合配置合理",
                "action": "继续保持当前配置，定期监控"
            })

        return suggestions

    def _generate_portfolio_summary(self, portfolio: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成投资组合摘要"""
        total_value = sum(pos.get("market_value", 0) for pos in portfolio)

        return {
            "total_value": total_value,
            "position_count": len(portfolio),
            "stocks": [pos.get("stock_code") for pos in portfolio]
        }

    def get_status(self) -> Dict[str, Any]:
        """获取工作流状态"""
        return {
            "workflow_id": self.workflow_id,
            "status": self.status.value,
            "current_step": self.current_step,
            "total_steps": len(self.steps),
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "errors": self.errors
        }
    
    async def cancel(self):
        """取消工作流"""
        self.status = WorkflowStatus.CANCELLED
        self.end_time = datetime.now()
        logger.info(f"工作流已取消: {self.workflow_id}")

# 工作流实例创建函数
def create_portfolio_analysis_workflow() -> PortfolioAnalysisWorkflow:
    """创建portfolio analysis工作流实例"""
    return PortfolioAnalysisWorkflow()

__all__ = ['PortfolioAnalysisWorkflow', 'create_portfolio_analysis_workflow']
