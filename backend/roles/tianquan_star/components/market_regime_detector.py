#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场环境自适应检测器
实现专业级市场状态识别和参数动态调整系统
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class MarketRegime:
    """市场环境状态"""
    regime_type: str  # BULL, BEAR, SIDEWAYS, VOLATILE
    confidence: float  # 0-1
    volatility_level: str  # LOW, MEDIUM, HIGH, EXTREME
    trend_strength: float  # -1 to 1
    momentum: float  # -1 to 1
    volume_profile: str  # INCREASING, DECREASING, STABLE
    
    # 量化指标
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    correlation_breakdown: bool
    
    # 建议参数调整
    suggested_params: Dict[str, Any]

class MarketRegimeDetector:
    """市场环境检测器"""
    
    def __init__(self):
        self.name = "MarketRegimeDetector"
        self.version = "1.0.0"
        
        # 市场环境分类器
        self.regime_classifier = None
        self.scaler = StandardScaler()
        
        # 历史环境记录
        self.regime_history = []
        
        # 环境特征阈值
        self.thresholds = {
            "volatility": {
                "low": 0.15,
                "medium": 0.25,
                "high": 0.40,
                "extreme": 0.60
            },
            "trend_strength": {
                "weak": 0.3,
                "medium": 0.6,
                "strong": 0.8
            },
            "volume_change": {
                "significant": 0.2
            }
        }
        
        logger.info(f"  {self.name} v{self.version} 初始化完成")
        logger.info("  专业级市场环境检测和参数自适应系统")
    
    def detect_market_regime(self, price_data: pd.DataFrame, 
                           market_data: Optional[pd.DataFrame] = None,
                           lookback_period: int = 60) -> MarketRegime:
        """
        检测当前市场环境
        
        Args:
            price_data: 价格数据 (OHLCV)
            market_data: 市场整体数据 (可选)
            lookback_period: 回看期间
            
        Returns:
            MarketRegime: 市场环境状态
        """
        
        logger.info("🔍 开始市场环境检测...")
        
        if len(price_data) < lookback_period:
            lookback_period = len(price_data)
        
        # 提取最近数据
        recent_data = price_data.tail(lookback_period).copy()
        
        # 计算市场特征
        features = self._calculate_market_features(recent_data)
        
        # 检测趋势
        trend_analysis = self._analyze_trend(recent_data)
        
        # 检测波动率环境
        volatility_analysis = self._analyze_volatility(recent_data)
        
        # 检测成交量模式
        volume_analysis = self._analyze_volume_pattern(recent_data)
        
        # 检测相关性破裂
        correlation_analysis = self._analyze_correlation_breakdown(recent_data, market_data)
        
        # 综合判断市场环境
        regime_type = self._classify_market_regime(features, trend_analysis, volatility_analysis)
        
        # 计算置信度
        confidence = self._calculate_regime_confidence(features, trend_analysis, volatility_analysis)
        
        # 生成参数建议
        suggested_params = self._generate_adaptive_parameters(
            regime_type, volatility_analysis, trend_analysis
        )
        
        regime = MarketRegime(
            regime_type=regime_type,
            confidence=confidence,
            volatility_level=volatility_analysis["level"],
            trend_strength=trend_analysis["strength"],
            momentum=trend_analysis["momentum"],
            volume_profile=volume_analysis["profile"],
            volatility=volatility_analysis["value"],
            sharpe_ratio=features["sharpe_ratio"],
            max_drawdown=features["max_drawdown"],
            correlation_breakdown=correlation_analysis["breakdown_detected"],
            suggested_params=suggested_params
        )
        
        # 记录历史
        self.regime_history.append({
            "timestamp": datetime.now(),
            "regime": regime
        })
        
        logger.info(f"  市场环境检测完成: {regime_type} (置信度: {confidence:.1%})")
        logger.info(f" 波动率: {volatility_analysis['level']}, 趋势强度: {trend_analysis['strength']:.2f}")
        
        return regime
    
    def _calculate_market_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算市场特征"""
        
        closes = data['close'].values
        highs = data['high'].values
        lows = data['low'].values
        volumes = data['volume'].values
        
        # 收益率序列
        returns = np.diff(np.log(closes))
        
        # 基础统计特征
        features = {
            # 收益特征
            "mean_return": np.mean(returns),
            "volatility": np.std(returns) * np.sqrt(252),
            "skewness": stats.skew(returns),
            "kurtosis": stats.kurtosis(returns),
            
            # 风险指标
            "sharpe_ratio": np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0,
            "max_drawdown": self._calculate_max_drawdown(closes),
            "var_95": np.percentile(returns, 5),
            "cvar_95": np.mean(returns[returns <= np.percentile(returns, 5)]),
            
            # 趋势特征
            "trend_slope": self._calculate_trend_slope(closes),
            "trend_r_squared": self._calculate_trend_r_squared(closes),
            
            # 波动率特征
            "volatility_of_volatility": self._calculate_vol_of_vol(returns),
            "garch_persistence": self._estimate_garch_persistence(returns),
            
            # 成交量特征
            "volume_trend": self._calculate_volume_trend(volumes),
            "volume_volatility": np.std(volumes) / np.mean(volumes) if np.mean(volumes) > 0 else 0,
            
            # 价格行为特征
            "up_down_ratio": np.sum(returns > 0) / len(returns),
            "consecutive_moves": self._calculate_consecutive_moves(returns),
            "gap_frequency": self._calculate_gap_frequency(data),
            
            # 技术指标特征
            "rsi_level": self._calculate_current_rsi(closes),
            "bollinger_position": self._calculate_bollinger_position(closes),
            "macd_signal": self._calculate_macd_signal(closes)
        }
        
        return features
    
    def _analyze_trend(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析趋势特征"""
        
        closes = data['close'].values
        
        # 多时间框架趋势分析
        short_trend = self._calculate_trend_slope(closes[-20:]) if len(closes) >= 20 else 0
        medium_trend = self._calculate_trend_slope(closes[-40:]) if len(closes) >= 40 else 0
        long_trend = self._calculate_trend_slope(closes)
        
        # 趋势一致性
        trends = [short_trend, medium_trend, long_trend]
        trend_consistency = len([t for t in trends if t * trends[0] > 0]) / len(trends)
        
        # 趋势强度
        trend_strength = abs(np.mean(trends))
        
        # 动量分析
        momentum = self._calculate_momentum(closes)
        
        # 趋势分类
        if trend_strength > self.thresholds["trend_strength"]["strong"]:
            strength_level = "STRONG"
        elif trend_strength > self.thresholds["trend_strength"]["medium"]:
            strength_level = "MEDIUM"
        elif trend_strength > self.thresholds["trend_strength"]["weak"]:
            strength_level = "WEAK"
        else:
            strength_level = "NONE"
        
        return {
            "strength": trend_strength,
            "direction": "UP" if np.mean(trends) > 0 else "DOWN",
            "consistency": trend_consistency,
            "momentum": momentum,
            "level": strength_level,
            "short_trend": short_trend,
            "medium_trend": medium_trend,
            "long_trend": long_trend
        }
    
    def _analyze_volatility(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析波动率环境"""
        
        closes = data['close'].values
        returns = np.diff(np.log(closes))
        
        # 当前波动率
        current_vol = np.std(returns) * np.sqrt(252)
        
        # 历史波动率分位数
        rolling_vol = pd.Series(returns).rolling(20).std() * np.sqrt(252)
        vol_percentile = stats.percentileofscore(rolling_vol.dropna(), current_vol) / 100
        
        # 波动率趋势
        vol_trend = self._calculate_volatility_trend(returns)
        
        # 波动率聚集性
        vol_clustering = self._detect_volatility_clustering(returns)
        
        # 波动率分类
        if current_vol > self.thresholds["volatility"]["extreme"]:
            vol_level = "EXTREME"
        elif current_vol > self.thresholds["volatility"]["high"]:
            vol_level = "HIGH"
        elif current_vol > self.thresholds["volatility"]["medium"]:
            vol_level = "MEDIUM"
        else:
            vol_level = "LOW"
        
        return {
            "value": current_vol,
            "level": vol_level,
            "percentile": vol_percentile,
            "trend": vol_trend,
            "clustering": vol_clustering,
            "is_regime_change": vol_percentile > 0.8 or vol_percentile < 0.2
        }
    
    def _analyze_volume_pattern(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析成交量模式"""
        
        volumes = data['volume'].values
        closes = data['close'].values
        
        # 成交量趋势
        volume_trend = self._calculate_volume_trend(volumes)
        
        # 价量关系
        price_volume_corr = np.corrcoef(np.diff(closes), volumes[1:])[0, 1] if len(closes) > 1 else 0
        
        # 成交量异常检测
        volume_anomalies = self._detect_volume_anomalies(volumes)
        
        # 成交量模式分类
        if volume_trend > self.thresholds["volume_change"]["significant"]:
            profile = "INCREASING"
        elif volume_trend < -self.thresholds["volume_change"]["significant"]:
            profile = "DECREASING"
        else:
            profile = "STABLE"
        
        return {
            "profile": profile,
            "trend": volume_trend,
            "price_correlation": price_volume_corr,
            "anomalies": volume_anomalies,
            "avg_volume": np.mean(volumes),
            "volume_volatility": np.std(volumes) / np.mean(volumes) if np.mean(volumes) > 0 else 0
        }
    
    def _analyze_correlation_breakdown(self, price_data: pd.DataFrame, 
                                     market_data: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """分析相关性破裂"""
        
        if market_data is None:
            return {"breakdown_detected": False, "correlation": 0.0}
        
        # 计算与市场的相关性
        stock_returns = np.diff(np.log(price_data['close'].values))
        market_returns = np.diff(np.log(market_data['close'].values))
        
        # 对齐数据长度
        min_length = min(len(stock_returns), len(market_returns))
        stock_returns = stock_returns[-min_length:]
        market_returns = market_returns[-min_length:]
        
        # 滚动相关性
        window = 20
        rolling_corr = []
        for i in range(window, len(stock_returns)):
            corr = np.corrcoef(stock_returns[i-window:i], market_returns[i-window:i])[0, 1]
            rolling_corr.append(corr)
        
        if not rolling_corr:
            return {"breakdown_detected": False, "correlation": 0.0}
        
        current_corr = rolling_corr[-1]
        avg_corr = np.mean(rolling_corr)
        
        # 检测相关性破裂
        breakdown_detected = abs(current_corr - avg_corr) > 0.3
        
        return {
            "breakdown_detected": breakdown_detected,
            "correlation": current_corr,
            "avg_correlation": avg_corr,
            "correlation_volatility": np.std(rolling_corr)
        }
    
    def _classify_market_regime(self, features: Dict[str, float], 
                              trend_analysis: Dict[str, Any],
                              volatility_analysis: Dict[str, Any]) -> str:
        """分类市场环境"""
        
        # 基于多个维度的综合判断
        vol_level = volatility_analysis["level"]
        trend_strength = trend_analysis["strength"]
        trend_direction = trend_analysis["direction"]
        
        # 高波动率环境
        if vol_level in ["HIGH", "EXTREME"]:
            if trend_strength > 0.6:
                return self._analyze_market_volatility_trend(market_data)
            else:
                return self._calculate_volatility_level(market_data)
        
        # 强趋势环境
        if trend_strength > 0.6:
            if trend_direction == "UP":
                return self._detect_bull_market(market_data)
            else:
                return self._detect_bear_market(market_data)
        
        # 低波动率横盘
        if vol_level == "LOW" and trend_strength < 0.3:
            return self._detect_sideways_market(market_data)
        
        # 中等波动率环境
        if trend_direction == "UP":
            return self._detect_bull_market(market_data)
        elif trend_direction == "DOWN":
            return self._detect_bear_market(market_data)
        else:
            return self._detect_sideways_market(market_data)
    
    def _calculate_regime_confidence(self, features: Dict[str, float],
                                   trend_analysis: Dict[str, Any],
                                   volatility_analysis: Dict[str, Any]) -> float:
        """计算环境判断置信度"""
        
        # 趋势一致性贡献
        trend_confidence = trend_analysis["consistency"]
        
        # 波动率稳定性贡献
        vol_confidence = await self._calculate_confidence_level(data) - min(1.0, volatility_analysis.get("clustering", 0))
        
        # 统计显著性贡献
        stat_confidence = min(1.0, abs(features["trend_r_squared"]))
        
        # 综合置信度
        confidence = (trend_confidence + vol_confidence + stat_confidence) / 3
        
        return confidence
    
    def _generate_adaptive_parameters(self, regime_type: str,
                                    volatility_analysis: Dict[str, Any],
                                    trend_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成自适应参数"""
        
        base_params = {
            "rsi_period": 14,
            "rsi_overbought": 70,
            "rsi_oversold": 30,
            "macd_fast": 12,
            "macd_slow": 26,
            "macd_signal": 9,
            "bollinger_period": 20,
            "bollinger_std": 2.0,
            "position_size": 0.1,
            "stop_loss": 0.05,
            "take_profit": 0.10
        }
        
        # 根据市场环境调整参数
        if regime_type == "VOLATILE":
            # 高波动率环境：缩短周期，扩大止损
            base_params.update({
                "rsi_period": 10,
                "rsi_overbought": 75,
                "rsi_oversold": 25,
                "bollinger_std": 2.5,
                "position_size": 0.05,
                "stop_loss": 0.08,
                "take_profit": 0.15
            })
        
        elif regime_type in ["BULL", "BEAR"]:
            # 趋势环境：延长周期，趋势跟踪
            base_params.update({
                "rsi_period": 21,
                "rsi_overbought": 80,
                "rsi_oversold": 20,
                "position_size": 0.15,
                "stop_loss": 0.03,
                "take_profit": 0.20
            })
        
        elif regime_type == "SIDEWAYS":
            # 横盘环境：均值回归策略
            base_params.update({
                "rsi_period": 14,
                "rsi_overbought": 65,
                "rsi_oversold": 35,
                "bollinger_std": 1.5,
                "position_size": 0.08,
                "stop_loss": 0.04,
                "take_profit": 0.08
            })
        
        # 根据波动率微调
        vol_multiplier = {
            "LOW": 0.8,
            "MEDIUM": 1.0,
            "HIGH": 1.3,
            "EXTREME": 1.6
        }.get(volatility_analysis["level"], 1.0)
        
        base_params["stop_loss"] *= vol_multiplier
        base_params["take_profit"] *= vol_multiplier
        
        return base_params
    
    # 辅助计算方法
    def _calculate_max_drawdown(self, prices: np.ndarray) -> float:
        """计算最大回撤"""
        peak = np.maximum.accumulate(prices)
        drawdown = (prices - peak) / peak
        return np.min(drawdown)
    
    def _calculate_trend_slope(self, prices: np.ndarray) -> float:
        """计算趋势斜率"""
        if len(prices) < 2:
            return 0.0
        x = np.arange(len(prices))
        slope, _, _, _, _ = stats.linregress(x, prices)
        return slope / np.mean(prices)  # 标准化
    
    def _calculate_trend_r_squared(self, prices: np.ndarray) -> float:
        """计算趋势R²"""
        if len(prices) < 2:
            return 0.0
        x = np.arange(len(prices))
        _, _, r_value, _, _ = stats.linregress(x, prices)
        return r_value ** 2
    
    def _calculate_vol_of_vol(self, returns: np.ndarray, window: int = 20) -> float:
        """计算波动率的波动率"""
        if len(returns) < window * 2:
            return 0.0
        
        rolling_vol = pd.Series(returns).rolling(window).std()
        return rolling_vol.std() / rolling_vol.mean() if rolling_vol.mean() > 0 else 0.0
    
    def _estimate_garch_persistence(self, returns: np.ndarray) -> float:
        """估计GARCH持续性"""
        if len(returns) < 50:
            return await self._calculate_real_score()
        
        squared_returns = returns ** 2
        alpha = np.corrcoef(squared_returns[1:], squared_returns[:-1])[0, 1]
        return max(0, min(1, alpha))  # 限制在[0,1]
    
    def _calculate_volume_trend(self, volumes: np.ndarray) -> float:
        """计算成交量趋势"""
        if len(volumes) < 2:
            return 0.0
        x = np.arange(len(volumes))
        slope, _, _, _, _ = stats.linregress(x, volumes)
        return slope / np.mean(volumes)  # 标准化
    
    def _calculate_consecutive_moves(self, returns: np.ndarray) -> float:
        """计算连续同向移动的平均长度"""
        if len(returns) < 2:
            return await self._get_real_value()
        
        signs = np.sign(returns)
        changes = np.diff(signs) != 0
        runs = np.split(signs, np.where(changes)[0] + 1)
        run_lengths = [len(run) for run in runs if len(run) > 0]
        
        return np.mean(run_lengths) if run_lengths else 1.0
    
    def _calculate_gap_frequency(self, data: pd.DataFrame) -> float:
        """计算跳空频率"""
        if len(data) < 2:
            return 0.0
        
        gaps = 0
        for i in range(1, len(data)):
            prev_close = data.iloc[i-1]['close']
            curr_open = data.iloc[i]['open']
            gap_size = abs(curr_open - prev_close) / prev_close
            
            if gap_size > 0.02:  # 2%以上算跳空
                gaps += 1
        
        return gaps / (len(data) - 1)
    
    def _calculate_current_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算当前RSI"""
        if len(prices) < period + 1:
            return 50.0
        
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_bollinger_position(self, prices: np.ndarray, period: int = 20) -> float:
        """计算布林带位置"""
        if len(prices) < period:
            return await self._calculate_real_score()
        
        sma = np.mean(prices[-period:])
        std = np.std(prices[-period:])
        
        upper = sma + 2 * std
        lower = sma - 2 * std
        
        current = prices[-1]
        return (current - lower) / (upper - lower) if upper != lower else 0.5
    
    def _calculate_macd_signal(self, prices: np.ndarray) -> float:
        """计算MACD信号"""
        if len(prices) < 26:
            return 0.0
        
        ema12 = pd.Series(prices).ewm(span=12).mean().iloc[-1]
        ema26 = pd.Series(prices).ewm(span=26).mean().iloc[-1]
        
        return (ema12 - ema26) / ema26 if ema26 != 0 else 0.0
    
    def _calculate_momentum(self, prices: np.ndarray, period: int = 10) -> float:
        """计算动量"""
        if len(prices) < period + 1:
            return 0.0
        
        return (prices[-1] - prices[-period-1]) / prices[-period-1]
    
    def _calculate_volatility_trend(self, returns: np.ndarray, window: int = 20) -> float:
        """计算波动率趋势"""
        if len(returns) < window * 2:
            return 0.0
        
        rolling_vol = pd.Series(returns).rolling(window).std()
        recent_vol = rolling_vol.tail(window).mean()
        earlier_vol = rolling_vol.tail(window * 2).head(window).mean()
        
        return (recent_vol - earlier_vol) / earlier_vol if earlier_vol > 0 else 0.0
    
    def _detect_volatility_clustering(self, returns: np.ndarray, window: int = 20) -> float:
        """检测波动率聚集性"""
        if len(returns) < window:
            return 0.0
        
        squared_returns = returns ** 2
        autocorr = np.corrcoef(squared_returns[1:], squared_returns[:-1])[0, 1]
        
        return max(0, autocorr)  # 只返回正相关
    
    def _detect_volume_anomalies(self, volumes: np.ndarray, threshold: float = 3.0) -> int:
        """检测成交量异常"""
        if len(volumes) < 20:
            return 0
        
        mean_vol = np.mean(volumes)
        std_vol = np.std(volumes)
        
        anomalies = np.sum(np.abs(volumes - mean_vol) > threshold * std_vol)
        return anomalies

# 全局市场环境检测器实例
market_regime_detector = MarketRegimeDetector()
