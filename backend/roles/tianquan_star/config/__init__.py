from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天权星配置模块
"""

from typing import Dict, Any, List
from dataclasses import dataclass
from enum import Enum

class DecisionMode(Enum):
    """决策模式"""
    CONSERVATIVE = "conservative"  # 保守模式
    BALANCED = "balanced"         # 平衡模式
    AGGRESSIVE = "aggressive"     # 激进模式
    ADAPTIVE = "adaptive"         # 自适应模式

class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"

@dataclass
class TianquanConfig:
    """天权星配置"""

    # 决策配置
    decision_mode: DecisionMode = DecisionMode.BALANCED
    risk_tolerance: RiskLevel = RiskLevel.MEDIUM
    confidence_threshold: float = 0.7

    # 协调配置
    max_concurrent_tasks: int = 10
    task_timeout: int = 300  # 5分钟
    retry_attempts: int = 3

    # 监控配置
    monitoring_interval: int = 30  # 30秒
    performance_threshold: float = 0.8
    alert_threshold: float = 0.6

    # 策略配置
    strategy_weights: Dict[str, float] = None

    # 数据库配置
    strategies_db_path: str = "backend/data/tianquan_strategies.db"
    decision_db_path: str = "backend/data/decision_making.db"

    # API配置
    api_timeout: int = 30
    max_retries: int = 3

    def __post_init__(self):
        if self.strategy_weights is None:
            self.strategy_weights = {
                "longtou": 0.25,
                "shouban": 0.20,
                "fanbao": 0.20,
                "boduan": 0.20,
                "event_driven": 0.15
            }

# 默认配置实例
default_config = TianquanConfig()

# 配置预设
CONFIG_PRESETS = {
    "conservative": TianquanConfig(
        decision_mode=DecisionMode.CONSERVATIVE,
        risk_tolerance=RiskLevel.LOW,
        confidence_threshold=0.8,
        strategy_weights={
            "longtou": 0.4,
            "boduan": 0.3,
            "shouban": 0.1,
            "fanbao": 0.1,
            "event_driven": 0.1
        }
    ),

    "aggressive": TianquanConfig(
        decision_mode=DecisionMode.AGGRESSIVE,
        risk_tolerance=RiskLevel.HIGH,
        confidence_threshold=0.6,
        strategy_weights={
            "shouban": 0.3,
            "fanbao": 0.3,
            "event_driven": 0.2,
            "longtou": 0.1,
            "boduan": 0.1
        }
    ),

    "balanced": TianquanConfig(
        decision_mode=DecisionMode.BALANCED,
        risk_tolerance=RiskLevel.MEDIUM,
        confidence_threshold=0.7,
        strategy_weights={
            "longtou": 0.25,
            "shouban": 0.20,
            "fanbao": 0.20,
            "boduan": 0.20,
            "event_driven": 0.15
        }
    )
}

def get_config(preset_name: str = "balanced") -> TianquanConfig:
    """获取配置"""
    return CONFIG_PRESETS.get(preset_name, default_config)

def update_config(config: TianquanConfig, updates: Dict[str, Any]) -> TianquanConfig:
    """更新配置"""
    for key, value in updates.items():
        if hasattr(config, key):
            setattr(config, key, value)
    return config

__all__ = [
    "TianquanConfig",
    "DecisionMode",
    "RiskLevel",
    "default_config",
    "CONFIG_PRESETS",
    "get_config",
    "update_config"
]
