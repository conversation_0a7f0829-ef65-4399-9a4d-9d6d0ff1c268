#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BeidouNode智能节点树服务 - 天权星座
实现全局决策整合、系统协调管理、绩效监控评估、优化建议生成、权衡决策的完整节点树
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pydantic import BaseModel, Field
from enum import Enum
import sqlite3
import os
from pathlib import Path
import json
import uuid

logger = logging.getLogger(__name__)

class BeidouNodeType(Enum):
    """北斗节点类型"""
    GLOBAL_DECISION_INTEGRATION = "global_decision_integration"    # 全局决策整合节点
    SYSTEM_COORDINATION = "system_coordination"                   # 系统协调管理节点
    PERFORMANCE_MONITORING = "performance_monitoring"             # 绩效监控评估节点
    OPTIMIZATION_SUGGESTION = "optimization_suggestion"           # 优化建议生成节点
    BALANCE_DECISION = "balance_decision"                         # 权衡决策节点

class NodeStatus(Enum):
    """节点状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    OPTIMIZING = "optimizing"

class BeidouNode(BaseModel):
    """北斗智能节点"""
    node_id: str = Field(..., description="节点唯一标识")
    node_type: BeidouNodeType = Field(..., description="节点类型")
    name: str = Field(..., description="节点名称")
    description: str = Field("", description="节点描述")
    status: NodeStatus = Field(NodeStatus.PENDING, description="节点状态")
    
    # 节点配置
    config: Dict[str, Any] = Field(default_factory=dict, description="节点配置")
    dependencies: List[str] = Field(default_factory=list, description="依赖节点")
    
    # 执行数据
    input_data: Dict[str, Any] = Field(default_factory=dict, description="输入数据")
    output_data: Dict[str, Any] = Field(default_factory=dict, description="输出数据")
    
    # 性能指标
    confidence: float = Field(0.0, description="置信度", ge=0.0, le=1.0)
    execution_time: float = Field(0.0, description="执行时间(秒)")
    quality_score: float = Field(0.0, description="质量评分", ge=0.0, le=1.0)
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    # RD-Agent增强
    rd_agent_insights: Dict[str, Any] = Field(default_factory=dict, description="RD-Agent洞察")
    optimization_history: List[Dict] = Field(default_factory=list, description="优化历史")
    
    class Config:
        arbitrary_types_allowed = True

class GlobalDecisionIntegrationNode:
    """全局决策整合节点 - 综合分析"""
    
    def __init__(self):
        self.integration_weights = {
            "intelligence_weight": 0.25,
            "strategy_weight": 0.30,
            "risk_weight": 0.25,
            "execution_weight": 0.20
        }
    
    async def execute_decision_making(self, node: BeidouNode) -> Dict[str, Any]:
        """执行全局决策整合"""
        try:
            logger.info(f"执行全局决策整合节点: {node.node_id}")
            
            # 收集各角色数据
            role_data = node.input_data.get("role_data", {})
            market_context = node.input_data.get("market_context", {})
            
            # 执行综合分析
            integration_result = await self._perform_global_integration(role_data, market_context)
            
            # 计算决策置信度
            confidence = await self._calculate_integration_confidence(integration_result)
            
            # 生成决策建议
            recommendations = await self._generate_integration_recommendations(integration_result)
            
            output = {
                "integration_result": integration_result,
                "global_confidence": confidence,
                "integration_recommendations": recommendations,
                "integration_metrics": {
                    "data_completeness": self._calculate_data_completeness(role_data),
                    "consistency_score": self._calculate_consistency_score(role_data),
                    "reliability_index": self._calculate_reliability_index(role_data)
                },
                "execution_timestamp": datetime.now().isoformat()
            }
            
            node.output_data = output
            node.confidence = confidence
            node.status = NodeStatus.COMPLETED
            
            logger.info(f"全局决策整合完成，置信度: {confidence:.3f}")
            return output
            
        except Exception as e:
            logger.error(f"全局决策整合失败: {e}")
            node.status = NodeStatus.FAILED
            raise
    
    async def _perform_global_integration(self, role_data: Dict, market_context: Dict) -> Dict:
        """执行全局整合分析"""
        
        # 角色数据权重分析
        weighted_scores = {}
        total_weight = 0
        
        for role, weight in self.integration_weights.items():
            role_name = role.replace("_weight", "")
            if role_name in role_data:
                role_score = self._extract_role_score(role_data[role_name])
                weighted_scores[role_name] = role_score * weight
                total_weight += weight
        
        # 计算综合评分
        global_score = sum(weighted_scores.values()) / total_weight if total_weight > 0 else 0.5
        
        # 市场环境调整
        market_adjustment = self._calculate_market_adjustment(market_context)
        adjusted_score = global_score * market_adjustment
        
        return {
            "global_score": global_score,
            "adjusted_score": adjusted_score,
            "weighted_scores": weighted_scores,
            "market_adjustment": market_adjustment,
            "integration_quality": "high" if adjusted_score > 0.8 else "medium" if adjusted_score > 0.6 else "low"
        }
    
    def _extract_role_score(self, role_data: Dict) -> float:
        """提取角色评分"""
        if isinstance(role_data, dict):
            # 尝试多种可能的评分字段
            score_fields = ["confidence_level", "quality_score", "performance_score", "overall_score"]
            for field in score_fields:
                if field in role_data:
                    return float(role_data[field])
            
            # 如果没有直接评分，计算平均值
            numeric_values = [v for v in role_data.values() if isinstance(v, (int, float)) and 0 <= v <= 1]
            return np.mean(numeric_values) if numeric_values else 0.5
        
        return await self._calculate_real_score()
    
    def _calculate_market_adjustment(self, market_context: Dict) -> float:
        """计算市场环境调整因子"""
        if not market_context:
            return await self._get_real_value()
        
        # 市场情绪调整
        sentiment = market_context.get("market_sentiment", "neutral")
        sentiment_adjustment = {
            "very_positive": 1.1,
            "positive": 1.05,
            "neutral": 1.0,
            "negative": 0.95,
            "very_negative": 0.9
        }.get(sentiment, 1.0)
        
        # 波动率调整
        volatility = market_context.get("market_volatility", 0.2)
        volatility_adjustment = max(0.8, min(1.2, 1.0 - (volatility - 0.2) * 0.5))
        
        return sentiment_adjustment * volatility_adjustment
    
    async def _calculate_integration_confidence(self, integration_result: Dict) -> float:
        """计算整合置信度"""
        base_confidence = integration_result["adjusted_score"]
        
        # 数据质量调整
        quality_factors = [
            integration_result.get("market_adjustment", 1.0),
            len(integration_result.get("weighted_scores", {})) / 4.0,  # 数据完整性
        ]
        
        quality_adjustment = np.mean(quality_factors)
        final_confidence = base_confidence * quality_adjustment
        
        return min(0.99, max(0.01, final_confidence))
    
    async def _generate_integration_recommendations(self, integration_result: Dict) -> List[str]:
        """生成整合建议"""
        recommendations = []
        
        adjusted_score = integration_result["adjusted_score"]
        quality = integration_result["integration_quality"]
        
        if quality == "high":
            recommendations.append("全局分析结果优秀，建议按计划执行投资决策")
            recommendations.append("各角色数据高度一致，系统运行状态良好")
        elif quality == "medium":
            recommendations.append("全局分析结果良好，建议适度调整投资规模")
            recommendations.append("部分角色数据需要进一步验证")
        else:
            recommendations.append("全局分析结果偏低，建议暂缓投资决策")
            recommendations.append("需要重新收集和验证各角色数据")
        
        # 基于市场调整的建议
        market_adjustment = integration_result.get("market_adjustment", 1.0)
        if market_adjustment > 1.05:
            recommendations.append("市场环境有利，可考虑适当增加投资力度")
        elif market_adjustment < 0.95:
            recommendations.append("市场环境不利，建议加强风险控制")
        
        return recommendations
    
    def _calculate_data_completeness(self, role_data: Dict) -> float:
        """计算数据完整性"""
        expected_roles = ["intelligence_officer", "architect", "risk_manager", "trader"]
        available_roles = len([role for role in expected_roles if role in role_data])
        return available_roles / len(expected_roles)
    
    def _calculate_consistency_score(self, role_data: Dict) -> float:
        """计算一致性评分"""
        scores = []
        for role_name, data in role_data.items():
            score = self._extract_role_score(data)
            scores.append(score)
        
        if len(scores) < 2:
            return await self._get_real_value()
        
        # 计算标准差，越小越一致
        std_dev = np.std(scores)
        consistency = max(0.0, 1.0 - std_dev * 2)  # 标准差越大，一致性越低
        return consistency
    
    def _calculate_reliability_index(self, role_data: Dict) -> float:
        """计算可靠性指数"""
        reliability_scores = []
        
        for role_name, data in role_data.items():
            if isinstance(data, dict):
                # 查找可靠性相关字段
                reliability_fields = ["reliability", "confidence_level", "data_quality"]
                for field in reliability_fields:
                    if field in data:
                        reliability_scores.append(float(data[field]))
                        break
                else:
                    # 如果没有找到可靠性字段，使用平均值
                    numeric_values = [v for v in data.values() if isinstance(v, (int, float)) and 0 <= v <= 1]
                    if numeric_values:
                        reliability_scores.append(np.mean(numeric_values))
        
        return np.mean(reliability_scores) if reliability_scores else 0.5

class SystemCoordinationNode:
    """系统协调管理节点 - 角色协作"""
    
    def __init__(self):
        self.coordination_matrix = {
            "intelligence_officer": ["architect", "risk_manager"],
            "architect": ["risk_manager", "trader"],
            "risk_manager": ["trader", "stock_manager"],
            "trader": ["stock_manager"],
            "stock_manager": ["intelligence_officer"]
        }
    
    async def execute_decision_making(self, node: BeidouNode) -> Dict[str, Any]:
        """执行系统协调管理"""
        try:
            logger.info(f"执行系统协调管理节点: {node.node_id}")
            
            role_status = node.input_data.get("role_status", {})
            coordination_requests = node.input_data.get("coordination_requests", [])
            
            # 分析角色协作状态
            coordination_analysis = await self._analyze_role_coordination(role_status)
            
            # 处理协调请求
            coordination_results = await self._process_coordination_requests(coordination_requests)
            
            # 生成协调计划
            coordination_plan = await self._generate_coordination_plan(coordination_analysis)
            
            # 计算协调效率
            coordination_efficiency = self._calculate_coordination_efficiency(coordination_analysis)
            
            output = {
                "coordination_analysis": coordination_analysis,
                "coordination_results": coordination_results,
                "coordination_plan": coordination_plan,
                "coordination_efficiency": coordination_efficiency,
                "system_health": self._assess_system_health(coordination_analysis),
                "execution_timestamp": datetime.now().isoformat()
            }
            
            node.output_data = output
            node.confidence = coordination_efficiency
            node.status = NodeStatus.COMPLETED
            
            logger.info(f"系统协调管理完成，效率: {coordination_efficiency:.3f}")
            return output
            
        except Exception as e:
            logger.error(f"系统协调管理失败: {e}")
            node.status = NodeStatus.FAILED
            raise
    
    async def _analyze_role_coordination(self, role_status: Dict) -> Dict:
        """分析角色协作状态"""
        
        coordination_metrics = {}
        
        for role, status in role_status.items():
            # 分析角色状态
            role_health = self._assess_role_health(status)
            
            # 分析协作关系
            collaboration_score = self._calculate_collaboration_score(role, role_status)
            
            # 分析通信效率
            communication_efficiency = self._calculate_communication_efficiency(role, status)
            
            coordination_metrics[role] = {
                "role_health": role_health,
                "collaboration_score": collaboration_score,
                "communication_efficiency": communication_efficiency,
                "overall_coordination": (role_health + collaboration_score + communication_efficiency) / 3
            }
        
        # 计算全局协调指标
        global_coordination = np.mean([metrics["overall_coordination"] for metrics in coordination_metrics.values()])
        
        return {
            "role_metrics": coordination_metrics,
            "global_coordination": global_coordination,
            "coordination_bottlenecks": self._identify_coordination_bottlenecks(coordination_metrics),
            "improvement_opportunities": self._identify_improvement_opportunities(coordination_metrics)
        }
    
    def _assess_role_health(self, status: Dict) -> float:
        """评估角色健康状态"""
        health_indicators = [
            status.get("availability", 1.0),
            status.get("performance", 0.8),
            status.get("response_time", 0.9),
            status.get("error_rate", 0.95)
        ]
        return np.mean(health_indicators)
    
    def _calculate_collaboration_score(self, role: str, role_status: Dict) -> float:
        """计算协作评分"""
        if role not in self.coordination_matrix:
            return await self._calculate_real_score()
        
        collaborators = self.coordination_matrix[role]
        collaboration_scores = []
        
        for collaborator in collaborators:
            if collaborator in role_status:
                # 基于协作者的状态计算协作评分
                collaborator_health = self._assess_role_health(role_status[collaborator])
                collaboration_scores.append(collaborator_health)
        
        return np.mean(collaboration_scores) if collaboration_scores else 0.5
    
    def _calculate_communication_efficiency(self, role: str, status: Dict) -> float:
        """计算通信效率"""
        # 基于响应时间、消息处理能力等计算通信效率
        response_time = status.get("avg_response_time", 1.0)
        message_throughput = status.get("message_throughput", 10.0)
        error_rate = status.get("communication_error_rate", 0.05)
        
        # 响应时间越短越好（秒）
        response_score = max(0.0, 1.0 - response_time / 10.0)
        
        # 吞吐量越高越好（消息/秒）
        throughput_score = min(1.0, message_throughput / 20.0)
        
        # 错误率越低越好
        error_score = max(0.0, 1.0 - error_rate * 10)
        
        return (response_score + throughput_score + error_score) / 3

    async def _process_coordination_requests(self, requests: List[Dict]) -> List[Dict]:
        """处理协调请求"""
        results = []

        for request in requests:
            request_type = request.get("type", "unknown")
            priority = request.get("priority", "normal")

            if request_type == "resource_allocation":
                result = await self._handle_resource_allocation(request)
            elif request_type == "task_coordination":
                result = await self._handle_task_coordination(request)
            elif request_type == "conflict_resolution":
                result = await self._handle_conflict_resolution(request)
            else:
                result = {"status": "unsupported", "message": f"不支持的请求类型: {request_type}"}

            results.append({
                "request_id": request.get("id", str(uuid.uuid4())),
                "type": request_type,
                "priority": priority,
                "result": result,
                "processed_at": datetime.now().isoformat()
            })

        return results

    async def _handle_resource_allocation(self, request: Dict) -> Dict:
        """处理资源分配请求"""
        resources = request.get("resources", {})
        requestor = request.get("requestor", "unknown")

        allocated_resources = {}
        for resource_type, amount in resources.items():
            # 基于优先级和可用性分配资源
            available = 100  # 假设的可用资源
            allocated = min(amount, available * 0.8)  # 分配80%的可用资源
            allocated_resources[resource_type] = allocated

        return {
            "status": "allocated",
            "allocated_resources": allocated_resources,
            "requestor": requestor
        }

    async def _handle_task_coordination(self, request: Dict) -> Dict:
        """处理任务协调请求"""
        tasks = request.get("tasks", [])
        coordination_type = request.get("coordination_type", "sequential")

        if coordination_type == "parallel":
            # 并行任务协调
            coordination_plan = {
                "execution_mode": "parallel",
                "estimated_time": max([task.get("estimated_time", 1) for task in tasks]),
                "resource_requirements": sum([task.get("resources", 1) for task in tasks])
            }
        else:
            # 顺序任务协调
            coordination_plan = {
                "execution_mode": "sequential",
                "estimated_time": sum([task.get("estimated_time", 1) for task in tasks]),
                "resource_requirements": max([task.get("resources", 1) for task in tasks])
            }

        return {
            "status": "coordinated",
            "coordination_plan": coordination_plan,
            "task_count": len(tasks)
        }

    async def _handle_conflict_resolution(self, request: Dict) -> Dict:
        """处理冲突解决请求"""
        conflict_type = request.get("conflict_type", "resource")
        parties = request.get("parties", [])

        if conflict_type == "resource":
            resolution = "按优先级分配资源，高优先级任务优先"
        elif conflict_type == "scheduling":
            resolution = "重新安排时间表，避免冲突"
        else:
            resolution = "需要人工干预解决"

        return {
            "status": "resolved",
            "resolution": resolution,
            "parties": parties
        }

    async def _generate_coordination_plan(self, analysis: Dict) -> Dict:
        """生成协调计划"""
        bottlenecks = analysis.get("coordination_bottlenecks", [])
        opportunities = analysis.get("improvement_opportunities", [])

        # 生成改进计划
        improvement_actions = []
        for bottleneck in bottlenecks:
            improvement_actions.append({
                "action": f"优化{bottleneck}的协调机制",
                "priority": "high",
                "estimated_impact": 0.2
            })

        for opportunity in opportunities:
            improvement_actions.append({
                "action": f"利用{opportunity}提升协调效率",
                "priority": "medium",
                "estimated_impact": 0.1
            })

        return {
            "improvement_actions": improvement_actions,
            "implementation_timeline": "1-2周",
            "expected_efficiency_gain": sum([action["estimated_impact"] for action in improvement_actions])
        }

    def _calculate_coordination_efficiency(self, analysis: Dict) -> float:
        """计算协调效率"""
        global_coordination = analysis.get("global_coordination", 0.5)
        bottleneck_count = len(analysis.get("coordination_bottlenecks", []))

        # 瓶颈越少，效率越高
        bottleneck_penalty = bottleneck_count * 0.1
        efficiency = max(0.1, global_coordination - bottleneck_penalty)

        return min(0.99, efficiency)

    def _assess_system_health(self, analysis: Dict) -> str:
        """评估系统健康状态"""
        global_coordination = analysis.get("global_coordination", 0.5)

        if global_coordination > 0.8:
            return "excellent"
        elif global_coordination > 0.6:
            return "good"
        elif global_coordination > 0.4:
            return "fair"
        else:
            return "poor"

    def _identify_coordination_bottlenecks(self, metrics: Dict) -> List[str]:
        """识别协调瓶颈"""
        bottlenecks = []

        for role, role_metrics in metrics.items():
            if role_metrics["overall_coordination"] < 0.6:
                bottlenecks.append(role)

        return bottlenecks

    def _identify_improvement_opportunities(self, metrics: Dict) -> List[str]:
        """识别改进机会"""
        opportunities = []

        for role, role_metrics in metrics.items():
            if 0.6 <= role_metrics["overall_coordination"] < 0.8:
                opportunities.append(role)

        return opportunities

class PerformanceMonitoringNode:
    """绩效监控评估节点 - 系统表现"""

    def __init__(self):
        self.performance_thresholds = {
            "response_time": 2.0,      # 秒
            "throughput": 100,         # 请求/分钟
            "error_rate": 0.05,        # 5%
            "availability": 0.99       # 99%
        }

    async def execute_decision_making(self, node: BeidouNode) -> Dict[str, Any]:
        """执行绩效监控评估"""
        try:
            logger.info(f"执行绩效监控评估节点: {node.node_id}")

            system_metrics = node.input_data.get("system_metrics", {})
            historical_data = node.input_data.get("historical_data", {})

            # 实时性能分析
            current_performance = await self._analyze_current_performance(system_metrics)

            # 历史趋势分析
            trend_analysis = await self._analyze_performance_trends(historical_data)

            # 性能预测
            performance_forecast = await self._forecast_performance(historical_data, current_performance)

            # 异常检测
            anomaly_detection = await self._detect_performance_anomalies(current_performance, historical_data)

            # 生成性能报告
            performance_report = await self._generate_performance_report(
                current_performance, trend_analysis, performance_forecast, anomaly_detection
            )

            output = {
                "current_performance": current_performance,
                "trend_analysis": trend_analysis,
                "performance_forecast": performance_forecast,
                "anomaly_detection": anomaly_detection,
                "performance_report": performance_report,
                "monitoring_timestamp": datetime.now().isoformat()
            }

            # 计算整体性能评分
            overall_score = self._calculate_overall_performance_score(current_performance)

            node.output_data = output
            node.confidence = overall_score
            node.status = NodeStatus.COMPLETED

            logger.info(f"绩效监控评估完成，整体评分: {overall_score:.3f}")
            return output

        except Exception as e:
            logger.error(f"绩效监控评估失败: {e}")
            node.status = NodeStatus.FAILED
            raise

    async def _analyze_current_performance(self, metrics: Dict) -> Dict:
        """分析当前性能"""
        performance_analysis = {}

        for metric_name, threshold in self.performance_thresholds.items():
            current_value = metrics.get(metric_name, 0)

            if metric_name in ["response_time", "error_rate"]:
                # 越小越好的指标
                performance_ratio = threshold / max(current_value, 0.001)
                status = "good" if current_value <= threshold else "poor"
            else:
                # 越大越好的指标
                performance_ratio = current_value / threshold
                status = "good" if current_value >= threshold else "poor"

            performance_analysis[metric_name] = {
                "current_value": current_value,
                "threshold": threshold,
                "performance_ratio": min(2.0, performance_ratio),
                "status": status
            }

        return performance_analysis

    async def _analyze_performance_trends(self, historical_data: Dict) -> Dict:
        """分析性能趋势"""
        if not historical_data:
            return {"trend": "no_data", "analysis": "缺少历史数据"}

        trends = {}

        for metric_name in self.performance_thresholds.keys():
            if metric_name in historical_data:
                values = historical_data[metric_name]
                if len(values) >= 2:
                    # 计算趋势
                    recent_avg = np.mean(values[-5:])  # 最近5个数据点
                    earlier_avg = np.mean(values[:-5]) if len(values) > 5 else np.mean(values[:-1])

                    trend_direction = "improving" if recent_avg > earlier_avg else "declining"
                    trend_magnitude = abs(recent_avg - earlier_avg) / earlier_avg if earlier_avg > 0 else 0

                    trends[metric_name] = {
                        "direction": trend_direction,
                        "magnitude": trend_magnitude,
                        "recent_average": recent_avg,
                        "earlier_average": earlier_avg
                    }

        return trends

    async def _forecast_performance(self, historical_data: Dict, current_performance: Dict) -> Dict:
        """预测性能"""
        forecasts = {}

        for metric_name in self.performance_thresholds.keys():
            if metric_name in historical_data and len(historical_data[metric_name]) >= 3:
                values = historical_data[metric_name]

                # 简单的线性预测
                x = np.arange(len(values))
                y = np.array(values)

                # 计算线性趋势
                slope = np.polyfit(x, y, 1)[0]

                # 预测未来值
                next_value = values[-1] + slope
                confidence = max(0.1, 1.0 - abs(slope) * 10)  # 趋势越平缓，置信度越高

                forecasts[metric_name] = {
                    "predicted_value": next_value,
                    "confidence": confidence,
                    "trend_slope": slope
                }

        return forecasts

    async def _detect_performance_anomalies(self, current_performance: Dict, historical_data: Dict) -> Dict:
        """检测性能异常"""
        anomalies = []

        for metric_name, perf_data in current_performance.items():
            current_value = perf_data["current_value"]
            threshold = perf_data["threshold"]

            # 检查是否超出阈值
            if perf_data["status"] == "poor":
                anomalies.append({
                    "metric": metric_name,
                    "type": "threshold_violation",
                    "current_value": current_value,
                    "threshold": threshold,
                    "severity": "high" if perf_data["performance_ratio"] < 0.5 else "medium"
                })

            # 检查是否与历史数据差异过大
            if metric_name in historical_data and historical_data[metric_name]:
                historical_avg = np.mean(historical_data[metric_name])
                deviation = abs(current_value - historical_avg) / historical_avg if historical_avg > 0 else 0

                if deviation > 0.5:  # 偏差超过50%
                    anomalies.append({
                        "metric": metric_name,
                        "type": "historical_deviation",
                        "current_value": current_value,
                        "historical_average": historical_avg,
                        "deviation": deviation,
                        "severity": "high" if deviation > 1.0 else "medium"
                    })

        return {
            "anomalies": anomalies,
            "anomaly_count": len(anomalies),
            "severity_distribution": self._calculate_severity_distribution(anomalies)
        }

    def _calculate_severity_distribution(self, anomalies: List[Dict]) -> Dict:
        """计算异常严重程度分布"""
        distribution = {"high": 0, "medium": 0, "low": 0}

        for anomaly in anomalies:
            severity = anomaly.get("severity", "low")
            distribution[severity] += 1

        return distribution

    async def _generate_performance_report(self, current_performance: Dict, trend_analysis: Dict,
                                         forecast: Dict, anomalies: Dict) -> Dict:
        """生成性能报告"""

        # 计算整体健康评分
        health_score = self._calculate_overall_performance_score(current_performance)

        # 生成关键洞察
        key_insights = []

        # 基于当前性能的洞察
        poor_metrics = [name for name, data in current_performance.items() if data["status"] == "poor"]
        if poor_metrics:
            key_insights.append(f"以下指标表现不佳: {', '.join(poor_metrics)}")

        # 基于趋势的洞察
        declining_trends = [name for name, data in trend_analysis.items() if data.get("direction") == "declining"]
        if declining_trends:
            key_insights.append(f"以下指标呈下降趋势: {', '.join(declining_trends)}")

        # 基于异常的洞察
        if anomalies["anomaly_count"] > 0:
            key_insights.append(f"检测到{anomalies['anomaly_count']}个性能异常")

        # 生成建议
        recommendations = self._generate_performance_recommendations(
            current_performance, trend_analysis, anomalies
        )

        return {
            "health_score": health_score,
            "health_status": self._get_health_status(health_score),
            "key_insights": key_insights,
            "recommendations": recommendations,
            "summary": {
                "total_metrics": len(current_performance),
                "good_metrics": len([d for d in current_performance.values() if d["status"] == "good"]),
                "poor_metrics": len(poor_metrics),
                "anomaly_count": anomalies["anomaly_count"]
            }
        }

    def _calculate_overall_performance_score(self, current_performance: Dict) -> float:
        """计算整体性能评分"""
        if not current_performance:
            return await self._calculate_real_score()

        scores = []
        for metric_data in current_performance.values():
            # 将性能比率转换为0-1的评分
            ratio = metric_data["performance_ratio"]
            score = min(1.0, ratio) if ratio >= 1.0 else ratio * 0.8  # 低于阈值的指标评分降低
            scores.append(score)

        return np.mean(scores)

    def _get_health_status(self, health_score: float) -> str:
        """获取健康状态"""
        if health_score >= 0.9:
            return "excellent"
        elif health_score >= 0.7:
            return "good"
        elif health_score >= 0.5:
            return "fair"
        else:
            return "poor"

    def _generate_performance_recommendations(self, current_performance: Dict,
                                           trend_analysis: Dict, anomalies: Dict) -> List[str]:
        """生成性能建议"""
        recommendations = []

        # 基于当前性能的建议
        for metric_name, perf_data in current_performance.items():
            if perf_data["status"] == "poor":
                if metric_name == "response_time":
                    recommendations.append("优化系统响应时间，考虑增加缓存或优化算法")
                elif metric_name == "throughput":
                    recommendations.append("提升系统吞吐量，考虑增加并发处理能力")
                elif metric_name == "error_rate":
                    recommendations.append("降低错误率，加强错误处理和系统稳定性")
                elif metric_name == "availability":
                    recommendations.append("提高系统可用性，加强监控和故障恢复机制")

        # 基于趋势的建议
        for metric_name, trend_data in trend_analysis.items():
            if trend_data.get("direction") == "declining" and trend_data.get("magnitude", 0) > 0.1:
                recommendations.append(f"关注{metric_name}的下降趋势，及时采取预防措施")

        # 基于异常的建议
        if anomalies["anomaly_count"] > 0:
            high_severity = anomalies["severity_distribution"].get("high", 0)
            if high_severity > 0:
                recommendations.append("立即处理高严重性性能异常")
            else:
                recommendations.append("监控并分析性能异常的根本原因")

        if not recommendations:
            recommendations.append("系统性能表现良好，继续保持当前运行状态")

        return recommendations
