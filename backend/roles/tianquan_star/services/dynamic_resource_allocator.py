#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指挥官角色 - 动态资源分配系统
基于实时负载和优先级的智能资源分配
"""

import asyncio
import psutil
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
import sqlite3
from pathlib import Path
import threading
import queue

logger = logging.getLogger(__name__)

class ResourceType(Enum):
    """资源类型"""
    CPU = "cpu"
    MEMORY = "memory"
    DISK_IO = "disk_io"
    NETWORK_IO = "network_io"
    GPU = "gpu"
    DATABASE_CONNECTIONS = "database_connections"
    API_RATE_LIMITS = "api_rate_limits"
    COMPUTATION_THREADS = "computation_threads"

class TaskPriority(Enum):
    """任务优先级"""
    CRITICAL = "critical"      # 关键任务（风险监控、紧急交易）
    HIGH = "high"             # 高优先级（实时分析、订单执行）
    MEDIUM = "medium"         # 中优先级（因子研究、回测）
    LOW = "low"              # 低优先级（数据下载、清理）
    BACKGROUND = "background" # 后台任务（学习、归档）

@dataclass
class ResourceRequest:
    """资源请求"""
    request_id: str
    task_name: str
    task_type: str
    priority: TaskPriority
    resource_requirements: Dict[ResourceType, float]
    estimated_duration: int  # 秒
    deadline: Optional[datetime] = None
    can_preempt: bool = False
    can_be_preempted: bool = True
    dependencies: List[str] = None

@dataclass
class ResourceAllocation:
    """资源分配"""
    allocation_id: str
    request_id: str
    allocated_resources: Dict[ResourceType, float]
    allocation_time: datetime
    expected_completion: datetime
    actual_completion: Optional[datetime] = None
    efficiency_score: float = 0.0
    was_preempted: bool = False

@dataclass
class SystemResource:
    """系统资源状态"""
    resource_type: ResourceType
    total_capacity: float
    available_capacity: float
    allocated_capacity: float
    utilization_rate: float
    peak_usage_1h: float
    average_usage_24h: float
    last_update: datetime

class DynamicResourceAllocator:
    """动态资源分配器"""
    
    def __init__(self, data_dir: str = "data/resources"):
        """初始化动态资源分配器"""
        self.service_name = "DynamicResourceAllocator"
        self.version = "2.0.0"
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 资源数据库
        self.db_path = self.data_dir / "resources.db"
        self.init_resource_database()
        
        # 系统资源状态
        self.system_resources: Dict[ResourceType, SystemResource] = {}
        self.init_system_resources()
        
        # 资源分配队列
        self.pending_requests: queue.PriorityQueue = queue.PriorityQueue()
        self.active_allocations: Dict[str, ResourceAllocation] = {}
        self.completed_allocations: List[ResourceAllocation] = []
        
        # 分配策略配置
        self.allocation_config = {
            "cpu_overcommit_ratio": 1.2,      # CPU超分配比例
            "memory_safety_margin": 0.1,      # 内存安全边际
            "preemption_threshold": 0.9,       # 抢占阈值
            "load_balancing_interval": 30,     # 负载均衡间隔（秒）
            "resource_monitoring_interval": 5, # 资源监控间隔（秒）
            "allocation_timeout": 300,         # 分配超时（秒）
            "priority_weights": {
                TaskPriority.CRITICAL: 10.0,
                TaskPriority.HIGH: 5.0,
                TaskPriority.MEDIUM: 2.0,
                TaskPriority.LOW: 1.0,
                TaskPriority.BACKGROUND: 0.5
            }
        }
        
        # 性能统计
        self.performance_stats = {
            "total_requests": 0,
            "successful_allocations": 0,
            "preempted_tasks": 0,
            "average_wait_time": 0.0,
            "average_utilization": 0.0,
            "allocation_efficiency": 0.0,
            "resource_contention_events": 0
        }
        
        # 启动资源监控和分配线程
        self.running = True
        self.monitor_thread = threading.Thread(target=self._resource_monitor_loop, daemon=True)
        self.allocator_thread = threading.Thread(target=self._allocation_loop, daemon=True)
        self.monitor_thread.start()
        self.allocator_thread.start()
        
        logger.info(f"  {self.service_name} v{self.version} 动态资源分配器初始化完成")
    
    def init_resource_database(self):
        """初始化资源数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 资源请求表
        cursor.execute_decision_making('''
            CREATE TABLE IF NOT EXISTS resource_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                request_id TEXT UNIQUE,
                task_name TEXT,
                task_type TEXT,
                priority TEXT,
                resource_requirements TEXT,
                estimated_duration INTEGER,
                deadline TEXT,
                request_time TEXT,
                allocation_time TEXT,
                completion_time TEXT,
                status TEXT
            )
        ''')
        
        # 资源分配表
        cursor.execute_decision_making('''
            CREATE TABLE IF NOT EXISTS resource_allocations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                allocation_id TEXT UNIQUE,
                request_id TEXT,
                allocated_resources TEXT,
                allocation_time TEXT,
                expected_completion TEXT,
                actual_completion TEXT,
                efficiency_score REAL,
                was_preempted BOOLEAN
            )
        ''')
        
        # 资源使用历史表
        cursor.execute_decision_making('''
            CREATE TABLE IF NOT EXISTS resource_usage_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                resource_type TEXT,
                timestamp TEXT,
                total_capacity REAL,
                available_capacity REAL,
                utilization_rate REAL,
                active_tasks INTEGER
            )
        ''')
        
        # 性能指标表
        cursor.execute_decision_making('''
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                metric_name TEXT,
                metric_value REAL,
                metric_type TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("资源数据库初始化完成")
    
    def init_system_resources(self):
        """初始化系统资源状态"""
        
        # CPU资源
        cpu_count = psutil.cpu_count()
        self.system_resources[ResourceType.CPU] = SystemResource(
            resource_type=ResourceType.CPU,
            total_capacity=float(cpu_count),
            available_capacity=float(cpu_count),
            allocated_capacity=0.0,
            utilization_rate=0.0,
            peak_usage_1h=0.0,
            average_usage_24h=0.0,
            last_update=datetime.now()
        )
        
        # 内存资源
        memory_info = psutil.virtual_memory()
        memory_gb = memory_info.total / (1024**3)
        self.system_resources[ResourceType.MEMORY] = SystemResource(
            resource_type=ResourceType.MEMORY,
            total_capacity=memory_gb,
            available_capacity=memory_gb * (1 - self.allocation_config["memory_safety_margin"]),
            allocated_capacity=0.0,
            utilization_rate=0.0,
            peak_usage_1h=0.0,
            average_usage_24h=0.0,
            last_update=datetime.now()
        )
        
        # 磁盘IO资源（抽象单位）
        self.system_resources[ResourceType.DISK_IO] = SystemResource(
            resource_type=ResourceType.DISK_IO,
            total_capacity=100.0,  # 抽象单位
            available_capacity=100.0,
            allocated_capacity=0.0,
            utilization_rate=0.0,
            peak_usage_1h=0.0,
            average_usage_24h=0.0,
            last_update=datetime.now()
        )
        
        # 网络IO资源（抽象单位）
        self.system_resources[ResourceType.NETWORK_IO] = SystemResource(
            resource_type=ResourceType.NETWORK_IO,
            total_capacity=100.0,  # 抽象单位
            available_capacity=100.0,
            allocated_capacity=0.0,
            utilization_rate=0.0,
            peak_usage_1h=0.0,
            average_usage_24h=0.0,
            last_update=datetime.now()
        )
        
        # 数据库连接池
        self.system_resources[ResourceType.DATABASE_CONNECTIONS] = SystemResource(
            resource_type=ResourceType.DATABASE_CONNECTIONS,
            total_capacity=50.0,  # 最大连接数
            available_capacity=50.0,
            allocated_capacity=0.0,
            utilization_rate=0.0,
            peak_usage_1h=0.0,
            average_usage_24h=0.0,
            last_update=datetime.now()
        )
        
        # API速率限制
        self.system_resources[ResourceType.API_RATE_LIMITS] = SystemResource(
            resource_type=ResourceType.API_RATE_LIMITS,
            total_capacity=1000.0,  # 每分钟请求数
            available_capacity=1000.0,
            allocated_capacity=0.0,
            utilization_rate=0.0,
            peak_usage_1h=0.0,
            average_usage_24h=0.0,
            last_update=datetime.now()
        )
        
        # 计算线程池
        self.system_resources[ResourceType.COMPUTATION_THREADS] = SystemResource(
            resource_type=ResourceType.COMPUTATION_THREADS,
            total_capacity=float(cpu_count * 2),  # 超线程
            available_capacity=float(cpu_count * 2),
            allocated_capacity=0.0,
            utilization_rate=0.0,
            peak_usage_1h=0.0,
            average_usage_24h=0.0,
            last_update=datetime.now()
        )
        
        logger.info(f"系统资源初始化完成: {len(self.system_resources)}种资源类型")
    
    async def request_resources(
        self,
        task_name: str,
        task_type: str,
        priority: TaskPriority,
        resource_requirements: Dict[ResourceType, float],
        estimated_duration: int,
        deadline: Optional[datetime] = None,
        can_preempt: bool = False,
        can_be_preempted: bool = True,
        dependencies: List[str] = None
    ) -> str:
        """请求资源分配"""
        
        request_id = f"req_{task_type}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        # 创建资源请求
        request = ResourceRequest(
            request_id=request_id,
            task_name=task_name,
            task_type=task_type,
            priority=priority,
            resource_requirements=resource_requirements,
            estimated_duration=estimated_duration,
            deadline=deadline,
            can_preempt=can_preempt,
            can_be_preempted=can_be_preempted,
            dependencies=dependencies or []
        )
        
        # 计算优先级分数（用于队列排序）
        priority_score = self._calculate_priority_score(request)
        
        # 添加到请求队列
        self.pending_requests.put((-priority_score, datetime.now(), request))
        
        # 记录请求
        await self._save_resource_request(request)
        
        self.performance_stats["total_requests"] += 1
        
        logger.info(f" 资源请求已提交: {request_id}")
        logger.info(f"  任务: {task_name} ({task_type})")
        logger.info(f"  优先级: {priority.value} (分数: {priority_score:.2f})")
        logger.info(f"  资源需求: {resource_requirements}")
        logger.info(f"  预计时长: {estimated_duration}秒")
        
        return request_id
    
    def _calculate_priority_score(self, request: ResourceRequest) -> float:
        """计算优先级分数"""
        
        base_score = self.allocation_config["priority_weights"][request.priority]
        
        # 截止时间紧迫性调整
        urgency_multiplier = 1.0
        if request.deadline:
            time_to_deadline = (request.deadline - datetime.now()).total_seconds()
            if time_to_deadline < request.estimated_duration * 2:
                urgency_multiplier = 2.0  # 紧急任务
            elif time_to_deadline < request.estimated_duration * 5:
                urgency_multiplier = 1.5  # 较紧急
        
        # 资源需求复杂度调整
        complexity_factor = 1.0
        total_resource_demand = sum(request.resource_requirements.values())
        if total_resource_demand > 10.0:  # 高资源需求
            complexity_factor = 0.8  # 降低优先级
        elif total_resource_demand < 2.0:  # 低资源需求
            complexity_factor = 1.2  # 提高优先级
        
        # 抢占能力调整
        preemption_factor = 1.1 if request.can_preempt else 1.0
        
        final_score = base_score * urgency_multiplier * complexity_factor * preemption_factor
        
        return final_score
    
    def _resource_monitor_loop(self):
        """资源监控循环"""
        
        while self.running:
            try:
                # 更新系统资源状态
                self._update_system_resources()
                
                # 检查资源争用
                self._check_resource_contention()
                
                # 记录资源使用历史
                asyncio.run(self._record_resource_usage())
                
                # 等待下一次监控
                threading.Event().wait(self.allocation_config["resource_monitoring_interval"])
                
            except Exception as e:
                logger.error(f"  资源监控循环错误: {e}")
                threading.Event().wait(5)  # 错误后短暂等待
    
    def _allocation_loop(self):
        """资源分配循环"""
        
        while self.running:
            try:
                # 处理待分配请求
                if not self.pending_requests.empty():
                    _, request_time, request = self.pending_requests.get(timeout=1)
                    
                    # 尝试分配资源
                    allocation = asyncio.run(self._attempt_allocation(request))
                    
                    if allocation:
                        self.active_allocations[allocation.allocation_id] = allocation
                        self.performance_stats["successful_allocations"] += 1
                        logger.info(f"  资源分配成功: {allocation.allocation_id}")
                    else:
                        # 分配失败，重新排队（降低优先级）
                        priority_score = self._calculate_priority_score(request) * 0.9
                        self.pending_requests.put((-priority_score, datetime.now(), request))
                        logger.warning(f"  资源分配失败，重新排队: {request.request_id}")
                
                # 检查完成的分配
                self._check_completed_allocations()
                
                # 负载均衡
                if datetime.now().second % self.allocation_config["load_balancing_interval"] == 0:
                    asyncio.run(self._perform_load_balancing())
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"  资源分配循环错误: {e}")
                threading.Event().wait(1)
    
    def _update_system_resources(self):
        """更新系统资源状态"""
        
        current_time = datetime.now()
        
        # 更新CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_resource = self.system_resources[ResourceType.CPU]
        cpu_resource.utilization_rate = cpu_percent / 100.0
        cpu_resource.available_capacity = cpu_resource.total_capacity * (1 - cpu_resource.utilization_rate)
        cpu_resource.last_update = current_time
        
        # 更新内存使用率
        memory_info = psutil.virtual_memory()
        memory_resource = self.system_resources[ResourceType.MEMORY]
        memory_resource.utilization_rate = memory_info.percent / 100.0
        memory_resource.available_capacity = memory_resource.total_capacity * (1 - memory_resource.utilization_rate)
        memory_resource.last_update = current_time
        
        # 更新磁盘IO
        disk_io = psutil.disk_io_counters()
        if hasattr(self, '_last_disk_io'):
            disk_delta = disk_io.read_bytes + disk_io.write_bytes - self._last_disk_io

            self.system_resources[ResourceType.DISK_IO].utilization_rate = disk_utilization
        self._last_disk_io = disk_io.read_bytes + disk_io.write_bytes
        
        # 更新网络IO
        net_io = psutil.net_io_counters()
        if hasattr(self, '_last_net_io'):
            net_delta = net_io.bytes_sent + net_io.bytes_recv - self._last_net_io

            self.system_resources[ResourceType.NETWORK_IO].utilization_rate = net_utilization
        self._last_net_io = net_io.bytes_sent + net_io.bytes_recv
        
        # 更新其他资源的可用容量（基于当前分配）
        for resource_type in [ResourceType.DATABASE_CONNECTIONS, ResourceType.API_RATE_LIMITS, ResourceType.COMPUTATION_THREADS]:
            resource = self.system_resources[resource_type]
            resource.available_capacity = resource.total_capacity - resource.allocated_capacity
            resource.utilization_rate = resource.allocated_capacity / resource.total_capacity
            resource.last_update = current_time

    def _check_resource_contention(self):
        """检查资源争用"""
        for resource_type, resource in self.system_resources.items():
            if resource.utilization_rate > self.allocation_config["preemption_threshold"]:
                self.performance_stats["resource_contention_events"] += 1
                logger.warning(f"  资源争用: {resource_type.value} 使用率 {resource.utilization_rate:.1%}")

    async def _record_resource_usage(self):
        """记录资源使用历史"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for resource_type, resource in self.system_resources.items():
            cursor.execute_decision_making('''
                INSERT INTO resource_usage_history
                (resource_type, timestamp, total_capacity, available_capacity, utilization_rate, active_tasks)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                resource_type.value,
                datetime.now().isoformat(),
                resource.total_capacity,
                resource.available_capacity,
                resource.utilization_rate,
                len(self.active_allocations)
            ))

        conn.commit()
        conn.close()

    async def _attempt_allocation(self, request: ResourceRequest) -> Optional[ResourceAllocation]:
        """尝试分配资源"""
        # 检查资源可用性
        can_allocate = True
        for resource_type, required_amount in request.resource_requirements.items():
            if resource_type in self.system_resources:
                available = self.system_resources[resource_type].available_capacity
                if available < required_amount:
                    can_allocate = False
                    break

        if can_allocate:
            # 分配资源
            allocation_id = f"alloc_{request.request_id}_{datetime.now().strftime('%H%M%S')}"

            # 更新资源分配
            for resource_type, required_amount in request.resource_requirements.items():
                if resource_type in self.system_resources:
                    self.system_resources[resource_type].allocated_capacity += required_amount

            allocation = ResourceAllocation(
                allocation_id=allocation_id,
                request_id=request.request_id,
                allocated_resources=request.resource_requirements.copy(),
                allocation_time=datetime.now(),
                expected_completion=datetime.now() + timedelta(seconds=request.estimated_duration)
            )

            return allocation

        return None

    def _check_completed_allocations(self):
        """检查完成的分配"""
        current_time = datetime.now()
        completed_allocations = []

        for allocation_id, allocation in self.active_allocations.items():
            if current_time >= allocation.expected_completion:
                # 释放资源
                for resource_type, allocated_amount in allocation.allocated_resources.items():
                    if resource_type in self.system_resources:
                        self.system_resources[resource_type].allocated_capacity -= allocated_amount

                allocation.actual_completion = current_time

                completed_allocations.append(allocation_id)
                self.completed_allocations.append(allocation)

        # 移除完成的分配
        for allocation_id in completed_allocations:
            del self.active_allocations[allocation_id]

    async def _perform_load_balancing(self):
        """执行负载均衡"""
        high_utilization_resources = [
            (rt, r) for rt, r in self.system_resources.items()
            if r.utilization_rate > 0.8
        ]

        if high_utilization_resources:
            logger.info(f"  执行负载均衡: {len(high_utilization_resources)}个高负载资源")

    async def _save_resource_request(self, request: ResourceRequest):
        """保存资源请求"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute_decision_making('''
            INSERT OR REPLACE INTO resource_requests
            (request_id, task_name, task_type, priority, resource_requirements,
             estimated_duration, deadline, request_time, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            request.request_id,
            request.task_name,
            request.task_type,
            request.priority.value,
            str(request.resource_requirements),
            request.estimated_duration,
            request.deadline.isoformat() if request.deadline else None,
            datetime.now().isoformat(),
            "pending"
        ))

        conn.commit()
        conn.close()
