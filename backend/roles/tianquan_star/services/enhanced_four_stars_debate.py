#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一四星智能体辩论系统
四颗星（天玑、天璇、天枢、玉衡）进行智能体辩论，天权星作为决策者
实现真正的多智能体观点交锋和综合决策
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import random

logger = logging.getLogger(__name__)

@dataclass
class DebateArgument:
    """辩论论点"""
    speaker: str
    position: str  # BUY/SELL/HOLD
    confidence: float
    reasoning: str
    evidence: List[str]
    counter_arguments: List[str] = None
    timestamp: datetime = None

@dataclass
class DebateRound:
    """辩论轮次"""
    round_number: int
    arguments: List[DebateArgument]
    consensus_level: float
    key_conflicts: List[str]
    resolution_attempts: List[str]

class EnhancedFourStarsDebate:
    """统一四星智能体辩论系统

    四颗星智能体辩论参与者：
    - 天玑星：风险管理专家
    - 天璇星：技术分析专家
    - 天枢星：情报收集专家
    - 玉衡星：交易执行专家

    天权星：综合决策者，基于四星辩论结果做最终决策
    """

    def __init__(self):
        self.debate_config = {
            "max_rounds": 5,
            "consensus_threshold": 0.75,
            "min_confidence_change": 0.05,
            "conflict_resolution_threshold": 0.6
        }
        self.debate_participants = ["tianshu", "tianxuan", "tianji", "yuheng"]  # 四星辩论参与者
        self.decision_maker = "tianquan"  # 天权星作为决策者
        logger.info("🌟 统一四星智能体辩论系统初始化完成 - 四星辩论+天权决策")

    async def start_four_star_debate(self, topic: str, stock_code: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """启动四星智能体辩论（天玑、天璇、天枢、玉衡辩论，天权决策）"""
        try:
            logger.info(f"🗣️ 启动四星智能体辩论: {topic} - {stock_code}")

            # 创建任务ID
            task_id = f"four_star_debate_{stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 构建初始分析上下文
            initial_analysis = context or {}
            initial_analysis.update({
                "stock_code": stock_code,
                "debate_topic": topic,
                "participants": self.debate_participants,
                "decision_maker": self.decision_maker
            })

            # 执行四星辩论+天权决策
            result = await self.conduct_enhanced_debate(task_id, stock_code, initial_analysis)

            return result

        except Exception as e:
            logger.error(f"❌ 四星智能体辩论失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "final_decision": {
                    "final_recommendation": "HOLD",
                    "reasoning": "辩论系统异常，采用保守策略",
                    "confidence": 0.5
                }
            }

    async def conduct_comprehensive_debate(self, debate_context: Dict[str, Any]) -> Dict[str, Any]:
        """执行综合辩论（兼容方法）"""
        try:
            stock_code = debate_context.get("stock_code", "UNKNOWN")
            session_id = debate_context.get("session_id", "default_session")

            # 提取分析数据
            initial_analysis = {
                "market_data": debate_context.get("market_data", {}),
                "news_analysis": debate_context.get("news_analysis", {}),
                "risk_analysis": debate_context.get("risk_analysis", {}),
                "technical_analysis": debate_context.get("technical_analysis", {})
            }

            # 调用增强版辩论
            result = await self.conduct_enhanced_debate(session_id, stock_code, initial_analysis)

            return result

        except Exception as e:
            logger.error(f"综合辩论失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "final_decision": {
                    "action": "hold",
                    "confidence": 0.5,
                    "reasoning": "辩论系统异常，采用保守策略"
                }
            }

    async def conduct_enhanced_debate(self, task_id: str, target_stock: str,
                                    initial_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """执行增强版四星辩论"""
        logger.info(f"🌟 开始增强版四星辩论: {task_id} - {target_stock}")
        
        try:
            # 第一阶段：收集初始观点和证据
            initial_positions = await self._collect_detailed_positions(target_stock, initial_analysis)
            
            # 第二阶段：多轮深度辩论
            debate_rounds = await self._conduct_multi_round_debate(initial_positions, target_stock)
            
            # 第三阶段：冲突解决和共识形成
            consensus_result = await self._resolve_conflicts_and_build_consensus(debate_rounds)
            
            # 第四阶段：天权星综合决策
            final_decision = await self._tianquan_comprehensive_decision(
                initial_positions, debate_rounds, consensus_result, target_stock
            )
            
            # 计算辩论质量指标
            debate_quality = self._calculate_debate_quality(debate_rounds, consensus_result)
            
            result = {
                "success": True,
                "task_id": task_id,
                "target_stock": target_stock,
                "initial_positions": initial_positions,
                "debate_rounds": [round.__dict__ for round in debate_rounds],
                "consensus_result": consensus_result,
                "final_decision": final_decision,
                "debate_quality": debate_quality,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f" 增强版四星辩论完成: {task_id}, 质量评分: {debate_quality['overall_score']:.2f}")
            return result
            
        except Exception as e:
            logger.error(f" 增强版四星辩论失败: {task_id}, 错误: {e}")
            return {
                "success": False,
                "task_id": task_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _collect_detailed_positions(self, target_stock: str, 
                                        initial_analysis: Dict[str, Any]) -> Dict[str, DebateArgument]:
        """收集详细的初始立场"""
        logger.info(" 收集四星详细立场...")
        
        positions = {}
        
        # 天枢星：数据情报立场
        positions["tianshu"] = await self._get_tianshu_detailed_position(target_stock, initial_analysis)
        
        # 天璇星：技术分析立场
        positions["tianxuan"] = await self._get_tianxuan_detailed_position(target_stock, initial_analysis)
        
        # 天玑星：风险管理立场
        positions["tianji"] = await self._get_tianji_detailed_position(target_stock, initial_analysis)
        
        # 玉衡星：执行策略立场
        positions["yuheng"] = await self._get_yuheng_detailed_position(target_stock, initial_analysis)
        
        return positions
    
    async def _get_tianshu_detailed_position(self, target_stock: str,
                                           analysis: Dict[str, Any]) -> DebateArgument:
        """获取天枢星详细立场 - 调用真实智能体"""
        try:
            # 调用天枢星自动化系统
            from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system

            # 执行市场分析
            result = await tianshu_automation_system.execute_market_analysis(
                symbol=target_stock,
                task_type="debate_analysis",
                session_id=f"debate_{datetime.now().strftime('%H%M%S')}"
            )

            if result.get("success"):
                analysis_result = result.get("analysis_result", {})
                sentiment_analysis = analysis_result.get("sentiment_analysis", {})
                overall_sentiment = sentiment_analysis.get("overall_sentiment", "neutral")
                confidence_score = sentiment_analysis.get("confidence", 0.6)

                # 基于真实分析结果确定立场
                if overall_sentiment == "positive":
                    position = "BUY"
                    confidence = min(0.9, confidence_score + 0.1)
                    reasoning = f"天枢星情报分析：{overall_sentiment}情绪主导，市场信息支持买入"
                    evidence = [
                        f"新闻情绪分析: {overall_sentiment}",
                        f"信息质量评分: {confidence_score:.2f}",
                        "市场情报支持上涨"
                    ]
                elif overall_sentiment == "negative":
                    position = "SELL"
                    confidence = min(0.9, confidence_score + 0.1)
                    reasoning = f"天枢星情报分析：{overall_sentiment}情绪主导，建议减仓"
                    evidence = [
                        f"新闻情绪分析: {overall_sentiment}",
                        "负面信息增多",
                        "市场情报偏向谨慎"
                    ]
                else:
                    position = "HOLD"
                    confidence = confidence_score
                    reasoning = f"天枢星情报分析：{overall_sentiment}情绪，信息面中性"
                    evidence = [
                        f"新闻情绪分析: {overall_sentiment}",
                        "信息面中性",
                        "等待更多市场信号"
                    ]
            else:
                # 降级处理
                position = "HOLD"
                confidence = 0.5
                reasoning = "天枢星分析服务暂时不可用，建议观望"
                evidence = ["分析服务异常", "等待系统恢复"]

        except Exception as e:
            logger.error(f"天枢星立场获取失败: {e}")
            position = "HOLD"
            confidence = 0.3
            reasoning = f"天枢星分析异常: {str(e)}"
            evidence = ["系统异常", "需要人工干预"]

        return DebateArgument(
            speaker="tianshu",
            position=position,
            confidence=confidence,
            reasoning=reasoning,
            evidence=evidence,
            timestamp=datetime.now()
        )
    
    async def _get_tianxuan_detailed_position(self, target_stock: str,
                                            analysis: Dict[str, Any]) -> DebateArgument:
        """获取天璇星详细立场 - 调用真实技术分析"""
        try:
            # 调用天璇星自动化系统
            from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system

            # 执行技术分析
            result = await tianxuan_automation_system.execute_technical_analysis(
                symbol=target_stock,
                analysis_type="comprehensive",
                session_id=f"debate_{datetime.now().strftime('%H%M%S')}"
            )

            if result.get("success"):
                analysis_result = result.get("analysis_result", {})
                indicators = analysis_result.get("technical_indicators", {})
                patterns = analysis_result.get("price_patterns", [])

                # 基于真实技术指标确定立场
                rsi = indicators.get("rsi", 50)
                macd = indicators.get("macd", {})
                trend_analysis = analysis_result.get("trend_analysis", {})

                # 技术分析决策逻辑
                if rsi < 30 and macd.get("histogram", 0) > 0:
                    position = "BUY"
                    confidence = 0.8
                    reasoning = f"天璇星技术分析：RSI超卖({rsi:.1f})且MACD转正，技术面支持买入"
                    evidence = [
                        f"RSI指标: {rsi:.1f} (超卖)",
                        f"MACD柱状图: {macd.get('histogram', 0):.3f}",
                        f"识别到{len(patterns)}个价格模式"
                    ]
                elif rsi > 70 and macd.get("histogram", 0) < 0:
                    position = "SELL"
                    confidence = 0.8
                    reasoning = f"天璇星技术分析：RSI超买({rsi:.1f})且MACD转负，技术面偏弱"
                    evidence = [
                        f"RSI指标: {rsi:.1f} (超买)",
                        f"MACD柱状图: {macd.get('histogram', 0):.3f}",
                        "技术指标显示调整压力"
                    ]
                else:
                    # 基于趋势分析
                    trend = trend_analysis.get("trend", "横盘整理")
                    if "上涨" in trend:
                        position = "BUY"
                        confidence = 0.7
                    elif "下跌" in trend:
                        position = "SELL"
                        confidence = 0.7
                    else:
                        position = "HOLD"
                        confidence = 0.6

                    reasoning = f"天璇星技术分析：当前趋势为{trend}，RSI={rsi:.1f}"
                    evidence = [
                        f"趋势分析: {trend}",
                        f"RSI指标: {rsi:.1f}",
                        f"技术模式: {len(patterns)}个"
                    ]
            else:
                # 降级处理
                position = "HOLD"
                confidence = 0.5
                reasoning = "天璇星技术分析服务暂时不可用"
                evidence = ["技术分析服务异常", "等待系统恢复"]

        except Exception as e:
            logger.error(f"天璇星立场获取失败: {e}")
            position = "HOLD"
            confidence = 0.3
            reasoning = f"天璇星技术分析异常: {str(e)}"
            evidence = ["系统异常", "需要人工干预"]

        return DebateArgument(
            speaker="tianxuan",
            position=position,
            confidence=confidence,
            reasoning=reasoning,
            evidence=evidence,
            timestamp=datetime.now()
        )
    
    async def _get_tianji_detailed_position(self, target_stock: str,
                                          analysis: Dict[str, Any]) -> DebateArgument:
        """获取天玑星详细立场 - 调用真实风险分析"""
        try:
            # 调用天玑星自动化系统
            from roles.tianji_star.services.tianji_automation_system import tianji_automation_system

            # 执行风险分析
            result = await tianji_automation_system.execute_risk_analysis(
                symbol=target_stock,
                analysis_depth="comprehensive",
                session_id=f"debate_{datetime.now().strftime('%H%M%S')}"
            )

            if result.get("success"):
                analysis_result = result.get("analysis_result", {})
                risk_level = analysis_result.get("overall_risk_level", "medium")
                risk_factors = analysis_result.get("risk_factors", [])
                risk_score = analysis_result.get("risk_score", 0.5)

                # 基于真实风险分析确定立场
                if risk_level == "low" and risk_score < 0.4:
                    position = "BUY"
                    confidence = 0.8
                    reasoning = f"天玑星风险分析：{risk_level}风险等级，风险评分{risk_score:.2f}，风险可控"
                    evidence = [
                        f"风险等级: {risk_level}",
                        f"风险评分: {risk_score:.2f}",
                        f"识别风险因子: {len(risk_factors)}个"
                    ]
                elif risk_level == "high" or risk_score > 0.7:
                    position = "SELL"
                    confidence = 0.8
                    reasoning = f"天玑星风险分析：{risk_level}风险等级，风险评分{risk_score:.2f}，建议规避"
                    evidence = [
                        f"风险等级: {risk_level}",
                        f"风险评分: {risk_score:.2f}",
                        "风险因子较多，需要谨慎"
                    ]
                else:
                    position = "HOLD"
                    confidence = 0.6
                    reasoning = f"天玑星风险分析：{risk_level}风险等级，风险评分{risk_score:.2f}，建议观望"
                    evidence = [
                        f"风险等级: {risk_level}",
                        f"风险评分: {risk_score:.2f}",
                        "风险中性，需要持续监控"
                    ]
            else:
                # 降级处理
                position = "HOLD"
                confidence = 0.5
                reasoning = "天玑星风险分析服务暂时不可用，建议谨慎"
                evidence = ["风险分析服务异常", "采用保守策略"]

        except Exception as e:
            logger.error(f"天玑星立场获取失败: {e}")
            position = "SELL"  # 风险分析失败时采用保守策略
            confidence = 0.7
            reasoning = f"天玑星风险分析异常: {str(e)}，出于风险控制建议减仓"
            evidence = ["系统异常", "风险控制优先", "需要人工干预"]

        return DebateArgument(
            speaker="tianji",
            position=position,
            confidence=confidence,
            reasoning=reasoning,
            evidence=evidence,
            timestamp=datetime.now()
        )
    
    async def _get_yuheng_detailed_position(self, target_stock: str,
                                          analysis: Dict[str, Any]) -> DebateArgument:
        """获取玉衡星详细立场 - 调用真实交易执行分析"""
        try:
            # 调用玉衡星自动化系统
            from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system

            # 执行交易分析
            result = await yuheng_automation_system.execute_trading_automation(
                symbol=target_stock,
                action="analyze",
                quantity=1000,
                session_id=f"debate_{datetime.now().strftime('%H%M%S')}"
            )

            if result.get("success"):
                execution_analysis = result.get("execution_analysis", {})
                liquidity_score = execution_analysis.get("liquidity_score", 0.6)
                execution_cost = execution_analysis.get("execution_cost", 0.4)
                market_impact = execution_analysis.get("market_impact", 0.3)

                # 基于真实执行分析确定立场
                if liquidity_score > 0.7 and execution_cost < 0.3:
                    position = "BUY"
                    confidence = 0.8
                    reasoning = f"玉衡星执行分析：流动性充足({liquidity_score:.2f})，执行成本低({execution_cost:.2f})，适合买入"
                    evidence = [
                        f"流动性评分: {liquidity_score:.2f}",
                        f"执行成本: {execution_cost:.2f}",
                        f"市场冲击: {market_impact:.2f}"
                    ]
                elif liquidity_score < 0.4 or execution_cost > 0.6:
                    position = "SELL"
                    confidence = 0.7
                    reasoning = f"玉衡星执行分析：流动性不足({liquidity_score:.2f})或成本过高({execution_cost:.2f})，建议减仓"
                    evidence = [
                        f"流动性评分: {liquidity_score:.2f}",
                        f"执行成本: {execution_cost:.2f}",
                        "执行条件不利"
                    ]
                else:
                    position = "HOLD"
                    confidence = 0.6
                    reasoning = f"玉衡星执行分析：执行条件一般，流动性{liquidity_score:.2f}，成本{execution_cost:.2f}"
                    evidence = [
                        f"流动性评分: {liquidity_score:.2f}",
                        f"执行成本: {execution_cost:.2f}",
                        "执行条件中性"
                    ]
            else:
                # 降级处理
                position = "HOLD"
                confidence = 0.5
                reasoning = "玉衡星交易执行分析服务暂时不可用"
                evidence = ["执行分析服务异常", "等待系统恢复"]

        except Exception as e:
            logger.error(f"玉衡星立场获取失败: {e}")
            position = "HOLD"
            confidence = 0.3
            reasoning = f"玉衡星执行分析异常: {str(e)}"
            evidence = ["系统异常", "暂停交易操作", "需要人工干预"]

        return DebateArgument(
            speaker="yuheng",
            position=position,
            confidence=confidence,
            reasoning=reasoning,
            evidence=evidence,
            timestamp=datetime.now()
        )

    async def _conduct_multi_round_debate(self, initial_positions: Dict[str, DebateArgument],
                                         target_stock: str) -> List[DebateRound]:
        """执行多轮深度辩论"""
        logger.info("🗣️ 开始多轮深度辩论...")

        debate_rounds = []
        current_positions = initial_positions.copy()

        for round_num in range(1, self.debate_config["max_rounds"] + 1):
            logger.info(f"  第{round_num}轮辩论...")

            # 识别观点冲突
            conflicts = self._identify_conflicts(current_positions)

            if not conflicts:
                logger.info("  无明显冲突，辩论结束")
                break

            # 执行单轮辩论
            round_arguments = await self._conduct_single_debate_round(
                round_num, current_positions, conflicts, target_stock
            )

            # 更新立场
            updated_positions = await self._update_positions_after_debate(
                current_positions, round_arguments
            )

            # 计算共识水平
            consensus_level = self._calculate_consensus_level(updated_positions)

            # 记录本轮辩论
            debate_round = DebateRound(
                round_number=round_num,
                arguments=round_arguments,
                consensus_level=consensus_level,
                key_conflicts=conflicts,
                resolution_attempts=self._generate_resolution_attempts(conflicts, round_arguments)
            )

            debate_rounds.append(debate_round)
            current_positions = updated_positions

            logger.info(f"  第{round_num}轮完成，共识水平: {consensus_level:.2f}")

            # 检查是否达成共识
            if consensus_level >= self.debate_config["consensus_threshold"]:
                logger.info("  达成共识，辩论结束")
                break

        return debate_rounds

    def _identify_conflicts(self, positions: Dict[str, DebateArgument]) -> List[str]:
        """识别观点冲突"""
        conflicts = []
        position_counts = {"BUY": 0, "SELL": 0, "HOLD": 0}

        for arg in positions.values():
            position_counts[arg.position] += 1

        # 检查是否存在明显分歧
        if position_counts["BUY"] > 0 and position_counts["SELL"] > 0:
            conflicts.append("买卖观点分歧")

        if max(position_counts.values()) < len(positions) * 0.6:
            conflicts.append("缺乏主导观点")

        # 检查信心度差异
        confidences = [arg.confidence for arg in positions.values()]
        if max(confidences) - min(confidences) > 0.3:
            conflicts.append("信心度差异过大")

        return conflicts

    async def _conduct_single_debate_round(self, round_num: int,
                                         current_positions: Dict[str, DebateArgument],
                                         conflicts: List[str],
                                         target_stock: str) -> List[DebateArgument]:
        """执行单轮辩论"""
        round_arguments = []

        # 针对每个冲突进行辩论
        for conflict in conflicts:
            if "买卖观点分歧" in conflict:
                # 多空辩论
                bull_args = await self._generate_bull_arguments(current_positions, target_stock)
                bear_args = await self._generate_bear_arguments(current_positions, target_stock)
                round_arguments.extend(bull_args + bear_args)

            elif "信心度差异" in conflict:
                # 信心度质疑
                confidence_args = await self._generate_confidence_challenges(current_positions)
                round_arguments.extend(confidence_args)

        return round_arguments

    async def _generate_bull_arguments(self, positions: Dict[str, DebateArgument],
                                     target_stock: str) -> List[DebateArgument]:
        """生成多头论点"""
        bull_arguments = []

        # 找出支持买入的角色
        bull_supporters = [name for name, pos in positions.items() if pos.position == "BUY"]

        for supporter in bull_supporters:
            original_pos = positions[supporter]

            # 生成反驳空头的论点
            counter_arg = DebateArgument(
                speaker=supporter,
                position="BUY",
                confidence=min(original_pos.confidence + 0.1, 1.0),
                reasoning=f"反驳空头观点：{original_pos.reasoning}，并且市场基本面支持上涨",
                evidence=original_pos.evidence + [f"{supporter}专业分析支持买入"],
                counter_arguments=["空头过度悲观", "忽略了积极因素"],
                timestamp=datetime.now()
            )
            bull_arguments.append(counter_arg)

        return bull_arguments

    async def _generate_bear_arguments(self, positions: Dict[str, DebateArgument],
                                     target_stock: str) -> List[DebateArgument]:
        """生成空头论点"""
        bear_arguments = []

        # 找出支持卖出的角色
        bear_supporters = [name for name, pos in positions.items() if pos.position == "SELL"]

        for supporter in bear_supporters:
            original_pos = positions[supporter]

            # 生成反驳多头的论点
            counter_arg = DebateArgument(
                speaker=supporter,
                position="SELL",
                confidence=min(original_pos.confidence + 0.1, 1.0),
                reasoning=f"反驳多头观点：{original_pos.reasoning}，风险因素被低估",
                evidence=original_pos.evidence + [f"{supporter}专业分析支持卖出"],
                counter_arguments=["多头过度乐观", "忽略了风险因素"],
                timestamp=datetime.now()
            )
            bear_arguments.append(counter_arg)

        return bear_arguments

    async def _generate_confidence_challenges(self, positions: Dict[str, DebateArgument]) -> List[DebateArgument]:
        """生成信心度质疑"""
        challenges = []

        # 找出信心度最高和最低的角色
        sorted_positions = sorted(positions.items(), key=lambda x: x[1].confidence)
        lowest_confidence = sorted_positions[0]
        highest_confidence = sorted_positions[-1]

        # 低信心度角色质疑高信心度角色
        challenge_arg = DebateArgument(
            speaker=lowest_confidence[0],
            position=lowest_confidence[1].position,
            confidence=lowest_confidence[1].confidence,
            reasoning=f"质疑{highest_confidence[0]}的高信心度：{highest_confidence[1].reasoning}可能过于乐观",
            evidence=lowest_confidence[1].evidence + ["信心度差异过大需要谨慎"],
            counter_arguments=[f"{highest_confidence[0]}信心度过高", "可能存在认知偏差"],
            timestamp=datetime.now()
        )
        challenges.append(challenge_arg)

        return challenges

    async def _update_positions_after_debate(self, original_positions: Dict[str, DebateArgument],
                                           round_arguments: List[DebateArgument]) -> Dict[str, DebateArgument]:
        """辩论后更新立场"""
        updated_positions = {}

        for name, original_pos in original_positions.items():
            # 查找该角色在本轮的论点
            role_arguments = [arg for arg in round_arguments if arg.speaker == name]

            if role_arguments:
                # 基于辩论调整信心度
                latest_arg = role_arguments[-1]
                confidence_adjustment = self._calculate_confidence_adjustment(original_pos, role_arguments)

                updated_positions[name] = DebateArgument(
                    speaker=name,
                    position=latest_arg.position,
                    confidence=max(0.1, min(1.0, original_pos.confidence + confidence_adjustment)),
                    reasoning=latest_arg.reasoning,
                    evidence=latest_arg.evidence,
                    counter_arguments=latest_arg.counter_arguments,
                    timestamp=datetime.now()
                )
            else:
                # 没有参与辩论，保持原立场但略微降低信心度
                updated_positions[name] = DebateArgument(
                    speaker=name,
                    position=original_pos.position,
                    confidence=max(0.1, original_pos.confidence - 0.05),
                    reasoning=original_pos.reasoning + "（未参与本轮辩论）",
                    evidence=original_pos.evidence,
                    counter_arguments=original_pos.counter_arguments,
                    timestamp=datetime.now()
                )

        return updated_positions

    def _calculate_confidence_adjustment(self, original_pos: DebateArgument,
                                       arguments: List[DebateArgument]) -> float:
        """计算信心度调整"""
        adjustment = 0.0

        for arg in arguments:
            if arg.counter_arguments:
                # 有反驳论点，信心度可能提高
                adjustment += 0.05
            else:
                # 纯粹重申，信心度略微下降
                adjustment -= 0.02

        return adjustment

    def _calculate_consensus_level(self, positions: Dict[str, DebateArgument]) -> float:
        """计算共识水平"""
        if not positions:
            return 0.0

        # 统计各立场的支持度
        position_weights = {"BUY": 0, "SELL": 0, "HOLD": 0}
        total_confidence = 0

        for pos in positions.values():
            position_weights[pos.position] += pos.confidence
            total_confidence += pos.confidence

        # 计算主导立场的比例
        if total_confidence > 0:
            max_weight = max(position_weights.values())
            consensus_level = max_weight / total_confidence
        else:
            consensus_level = 0.0

        return consensus_level

    def _generate_resolution_attempts(self, conflicts: List[str],
                                    arguments: List[DebateArgument]) -> List[str]:
        """生成解决方案尝试"""
        resolutions = []

        for conflict in conflicts:
            if "买卖观点分歧" in conflict:
                resolutions.append("通过风险调整仓位寻求平衡")
            elif "信心度差异" in conflict:
                resolutions.append("要求提供更多证据支持高信心度观点")
            elif "缺乏主导观点" in conflict:
                resolutions.append("寻找各方都能接受的折中方案")

        return resolutions

    async def _resolve_conflicts_and_build_consensus(self, debate_rounds: List[DebateRound]) -> Dict[str, Any]:
        """解决冲突并形成共识"""
        logger.info("🤝 解决冲突并形成共识...")

        if not debate_rounds:
            return {"consensus_reached": False, "final_positions": {}}

        # 获取最后一轮的立场
        final_round = debate_rounds[-1]
        final_consensus_level = final_round.consensus_level

        # 分析整个辩论过程的趋势
        consensus_trend = [round.consensus_level for round in debate_rounds]
        consensus_improving = len(consensus_trend) > 1 and consensus_trend[-1] > consensus_trend[0]

        # 识别主导观点
        position_strengths = self._analyze_position_strengths(debate_rounds)
        dominant_position = max(position_strengths.items(), key=lambda x: x[1])

        consensus_result = {
            "consensus_reached": final_consensus_level >= self.debate_config["consensus_threshold"],
            "consensus_level": final_consensus_level,
            "consensus_trend": "improving" if consensus_improving else "stable",
            "dominant_position": dominant_position[0],
            "position_strength": dominant_position[1],
            "remaining_conflicts": final_round.key_conflicts,
            "resolution_quality": self._assess_resolution_quality(debate_rounds)
        }

        return consensus_result

    def _analyze_position_strengths(self, debate_rounds: List[DebateRound]) -> Dict[str, float]:
        """分析各立场的强度"""
        position_strengths = {"BUY": 0.0, "SELL": 0.0, "HOLD": 0.0}

        for round in debate_rounds:
            for arg in round.arguments:
                # 根据信心度和论证质量计算强度
                evidence_quality = len(arg.evidence) * 0.1
                argument_strength = arg.confidence + evidence_quality
                position_strengths[arg.position] += argument_strength

        return position_strengths

    def _assess_resolution_quality(self, debate_rounds: List[DebateRound]) -> float:
        """评估解决方案质量"""
        if not debate_rounds:
            return 0.0

        # 基于多个因素评估
        factors = []

        # 1. 共识改善程度
        if len(debate_rounds) > 1:
            consensus_improvement = debate_rounds[-1].consensus_level - debate_rounds[0].consensus_level
            factors.append(max(0, consensus_improvement))

        # 2. 冲突解决程度
        initial_conflicts = len(debate_rounds[0].key_conflicts) if debate_rounds else 0
        final_conflicts = len(debate_rounds[-1].key_conflicts)
        conflict_resolution = max(0, (initial_conflicts - final_conflicts) / max(1, initial_conflicts))
        factors.append(conflict_resolution)

        # 3. 论证质量
        avg_evidence_count = sum(len(arg.evidence) for round in debate_rounds for arg in round.arguments) / max(1, sum(len(round.arguments) for round in debate_rounds))
        evidence_quality = min(1.0, avg_evidence_count / 3.0)
        factors.append(evidence_quality)

        return sum(factors) / len(factors) if factors else 0.0

    async def _tianquan_comprehensive_decision(self, initial_positions: Dict[str, DebateArgument],
                                             debate_rounds: List[DebateRound],
                                             consensus_result: Dict[str, Any],
                                             target_stock: str) -> Dict[str, Any]:
        """天权星综合决策"""
        logger.info("👑 天权星综合决策...")

        # 权重配置
        role_weights = {
            "tianshu": 0.25,    # 数据情报
            "tianxuan": 0.30,   # 技术分析
            "tianji": 0.25,     # 风险管理
            "yuheng": 0.20      # 执行策略
        }

        # 计算加权决策
        weighted_scores = {"BUY": 0.0, "SELL": 0.0, "HOLD": 0.0}
        total_confidence = 0.0

        # 使用最终立场进行加权计算
        if debate_rounds:
            final_arguments = debate_rounds[-1].arguments
            final_positions = {}

            # 获取每个角色的最终立场
            for arg in final_arguments:
                if arg.speaker not in final_positions or arg.timestamp > final_positions[arg.speaker].timestamp:
                    final_positions[arg.speaker] = arg

            # 补充未参与最后一轮的角色
            for role, initial_pos in initial_positions.items():
                if role not in final_positions:
                    final_positions[role] = initial_pos
        else:
            final_positions = initial_positions

        # 加权计算
        for role, position in final_positions.items():
            weight = role_weights.get(role, 0.25)
            weighted_scores[position.position] += position.confidence * weight
            total_confidence += position.confidence * weight

        # 确定最终决策
        final_recommendation = max(weighted_scores.items(), key=lambda x: x[1])[0]
        final_confidence = weighted_scores[final_recommendation] / max(0.1, total_confidence) if total_confidence > 0 else 0.5

        # 决策理由
        decision_rationale = self._generate_decision_rationale(
            final_recommendation, final_confidence, consensus_result, final_positions
        )

        decision = {
            "decision_maker": "tianquan",
            "final_recommendation": final_recommendation,
            "final_confidence": final_confidence,
            "weighted_scores": weighted_scores,
            "consensus_level": consensus_result.get("consensus_level", 0.0),
            "decision_rationale": decision_rationale,
            "risk_assessment": self._generate_risk_assessment(final_positions),
            "execution_plan": self._generate_execution_plan(final_recommendation, final_confidence),
            "decision_timestamp": datetime.now().isoformat()
        }

        logger.info(f"👑 天权星决策: {final_recommendation} (信心度: {final_confidence:.2f})")
        return decision

    def _generate_decision_rationale(self, recommendation: str, confidence: float,
                                   consensus_result: Dict[str, Any],
                                   final_positions: Dict[str, DebateArgument]) -> str:
        """生成决策理由"""
        rationale_parts = []

        # 基于共识水平
        consensus_level = consensus_result.get("consensus_level", 0.0)
        if consensus_level >= 0.8:
            rationale_parts.append(f"四星高度共识({consensus_level:.1%})支持{recommendation}")
        elif consensus_level >= 0.6:
            rationale_parts.append(f"四星基本共识({consensus_level:.1%})倾向{recommendation}")
        else:
            rationale_parts.append(f"四星观点分歧，综合权衡后选择{recommendation}")

        # 基于主要支持者
        supporters = [role for role, pos in final_positions.items() if pos.position == recommendation]
        if supporters:
            rationale_parts.append(f"得到{', '.join(supporters)}专业支持")

        # 基于信心度
        if confidence >= 0.8:
            rationale_parts.append("决策信心度高")
        elif confidence >= 0.6:
            rationale_parts.append("决策信心度中等")
        else:
            rationale_parts.append("决策信心度较低，建议谨慎")

        return "；".join(rationale_parts)

    def _generate_risk_assessment(self, final_positions: Dict[str, DebateArgument]) -> Dict[str, Any]:
        """生成风险评估"""
        tianji_position = final_positions.get("tianji")
        if tianji_position:
            risk_level = 1.0 - tianji_position.confidence if tianji_position.position == "SELL" else tianji_position.confidence
        else:
            risk_level = 0.5

        return {
            "overall_risk": "高" if risk_level > 0.7 else "中" if risk_level > 0.4 else "低",
            "risk_score": risk_level,
            "key_risks": tianji_position.evidence if tianji_position else ["风险评估不完整"],
            "risk_mitigation": "建议分批建仓，设置止损" if risk_level > 0.6 else "风险可控，正常操作"
        }

    def _generate_execution_plan(self, recommendation: str, confidence: float) -> Dict[str, Any]:
        """生成执行计划"""
        if recommendation == "BUY":
            position_size = min(0.1, confidence * 0.15)  # 最大10%仓位
            execution_strategy = "分批买入" if confidence < 0.8 else "快速建仓"
        elif recommendation == "SELL":
            position_size = min(0.1, confidence * 0.15)
            execution_strategy = "分批卖出" if confidence < 0.8 else "快速减仓"
        else:  # HOLD
            position_size = 0.0
            execution_strategy = "维持现状"

        return {
            "action": recommendation,
            "position_size": position_size,
            "execution_strategy": execution_strategy,
            "urgency": "高" if confidence > 0.8 else "中" if confidence > 0.6 else "低"
        }

    def _calculate_debate_quality(self, debate_rounds: List[DebateRound],
                                consensus_result: Dict[str, Any]) -> Dict[str, Any]:
        """计算辩论质量"""
        if not debate_rounds:
            return {"overall_score": 0.0, "details": {}}

        # 多个维度评估
        participation_score = len(debate_rounds) / self.debate_config["max_rounds"]
        consensus_score = consensus_result.get("consensus_level", 0.0)
        resolution_score = consensus_result.get("resolution_quality", 0.0)

        # 论证质量评估
        total_evidence = sum(len(arg.evidence) for round in debate_rounds for arg in round.arguments)
        total_arguments = sum(len(round.arguments) for round in debate_rounds)
        evidence_score = min(1.0, total_evidence / max(1, total_arguments * 2))

        overall_score = (participation_score * 0.2 + consensus_score * 0.3 +
                        resolution_score * 0.3 + evidence_score * 0.2)

        return {
            "overall_score": overall_score,
            "details": {
                "participation": participation_score,
                "consensus": consensus_score,
                "resolution": resolution_score,
                "evidence_quality": evidence_score
            },
            "grade": "优秀" if overall_score >= 0.8 else "良好" if overall_score >= 0.6 else "一般"
        }

# 全局实例
enhanced_four_stars_debate = EnhancedFourStarsDebate()
