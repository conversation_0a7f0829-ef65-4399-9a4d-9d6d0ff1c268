#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天权星战略决策服务
负责制定交易战法和战略决策

功能：
1. 交易战法决策
2. 市场环境分析
3. 风险偏好评估
4. 战略执行计划

版本: v1.0.0
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

# 配置日志
logger = logging.getLogger(__name__)

class MarketCondition(Enum):
    """市场状况"""
    BULL_MARKET = "牛市"
    BEAR_MARKET = "熊市"
    SIDEWAYS = "震荡"
    VOLATILE = "高波动"
    STABLE = "稳定"

class StrategyType(Enum):
    """策略类型"""
    LONGTOU = "龙头战法"
    SHOUBAN = "首板战法"
    FANBAO = "反包战法"
    BODUAN = "波段战法"
    EVENT_DRIVEN = "事件驱动"

@dataclass
class MarketAnalysis:
    """市场分析结果"""
    market_condition: MarketCondition
    volatility_level: float
    trend_strength: float
    sector_rotation: Dict[str, float]
    risk_level: str

class StrategicDecisionService:
    """天权星战略决策服务"""
    
    def __init__(self):
        self.service_name = "天权星战略决策服务"
        self.version = "v1.0.0"
        
        # 战法适用性矩阵
        self.strategy_suitability = {
            MarketCondition.BULL_MARKET: {
                StrategyType.LONGTOU: 0.9,
                StrategyType.SHOUBAN: 0.8,
                StrategyType.FANBAO: 0.6,
                StrategyType.BODUAN: 0.7,
                StrategyType.EVENT_DRIVEN: 0.8
            },
            MarketCondition.BEAR_MARKET: {
                StrategyType.LONGTOU: 0.4,
                StrategyType.SHOUBAN: 0.3,
                StrategyType.FANBAO: 0.7,
                StrategyType.BODUAN: 0.8,
                StrategyType.EVENT_DRIVEN: 0.6
            },
            MarketCondition.SIDEWAYS: {
                StrategyType.LONGTOU: 0.6,
                StrategyType.SHOUBAN: 0.5,
                StrategyType.FANBAO: 0.8,
                StrategyType.BODUAN: 0.9,
                StrategyType.EVENT_DRIVEN: 0.7
            },
            MarketCondition.VOLATILE: {
                StrategyType.LONGTOU: 0.5,
                StrategyType.SHOUBAN: 0.4,
                StrategyType.FANBAO: 0.9,
                StrategyType.BODUAN: 0.6,
                StrategyType.EVENT_DRIVEN: 0.8
            },
            MarketCondition.STABLE: {
                StrategyType.LONGTOU: 0.8,
                StrategyType.SHOUBAN: 0.7,
                StrategyType.FANBAO: 0.5,
                StrategyType.BODUAN: 0.8,
                StrategyType.EVENT_DRIVEN: 0.6
            }
        }
        
        # 风险偏好调整系数
        self.risk_adjustments = {
            "conservative": 0.7,
            "moderate": 1.0,
            "aggressive": 1.3
        }
    
    async def decide_trading_strategy(self, 
                                    stock_code: str,
                                    market_context: Dict[str, Any] = None,
                                    risk_preference: str = "moderate") -> Dict[str, Any]:
        """决定交易战法"""
        
        logger.info(f"👑 天权星开始为{stock_code}制定交易战法...")
        
        try:
            # 1. 分析市场环境
            market_analysis = await self._analyze_market_environment(market_context)
            
            # 2. 评估股票特性
            stock_characteristics = await self._analyze_stock_characteristics(stock_code)
            
            # 3. 选择最适合的战法
            optimal_strategy = self._select_optimal_strategy(
                market_analysis, stock_characteristics, risk_preference
            )

            # 4. 启动四星辩论进行深度分析
            debate_result = await self._initiate_four_stars_debate(
                stock_code, optimal_strategy, market_analysis, stock_characteristics
            )

            # 保存辩论结果供外部访问
            self._last_debate_result = debate_result

            # 5. 基于辩论结果优化策略
            if debate_result and debate_result.get("success"):
                optimal_strategy, confidence = await self._optimize_strategy_with_debate(
                    optimal_strategy, debate_result, market_analysis, stock_characteristics
                )
                logger.info("✅ 天权星基于四星辩论优化策略完成")
            else:
                # 6. 评估决策置信度（辩论失败时的回退）
                confidence = self._calculate_decision_confidence(
                    market_analysis, stock_characteristics, optimal_strategy
                )

            # 7. 制定执行计划
            execution_plan = self._create_execution_plan(
                optimal_strategy, stock_code, market_analysis
            )
            
            decision_result = {
                "strategy_type": optimal_strategy.value,
                "confidence": confidence,
                "rationale": self._generate_decision_rationale(
                    optimal_strategy, market_analysis, stock_characteristics
                ),
                "timeline": execution_plan.get("timeline", "1-3天"),
                "success_criteria": execution_plan.get("success_criteria", {}),
                "risk_controls": execution_plan.get("risk_controls", {}),
                "market_analysis": {
                    "condition": market_analysis.market_condition.value,
                    "volatility": market_analysis.volatility_level,
                    "trend_strength": market_analysis.trend_strength,
                    "risk_level": market_analysis.risk_level
                },
                "decision_timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"  天权星战法决策完成: {optimal_strategy.value} (置信度: {confidence:.2f})")
            return decision_result
            
        except Exception as e:
            logger.error(f"  天权星战法决策失败: {e}")
            return {
                "strategy_type": "龙头战法",  # 默认战法
                "confidence": 0.5,
                "rationale": f"决策异常，使用默认战法: {str(e)}",
                "timeline": "1-3天",
                "success_criteria": {},
                "risk_controls": {"stop_loss": 0.95},
                "decision_timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    async def _analyze_market_environment(self, market_context: Dict[str, Any] = None) -> MarketAnalysis:
        """分析市场环境"""
        
        if not market_context:
            market_context = {}
        
        # 基于真实数据的计算
        try:
            # 基于市场上下文判断市场状况
            market_trend = market_context.get("trend", "neutral")
            volatility = market_context.get("volatility", 0.15)
            
            # 判断市场状况
            if market_trend == "bullish" and volatility < 0.2:
                market_condition = MarketCondition.BULL_MARKET
            elif market_trend == "bearish":
                market_condition = MarketCondition.BEAR_MARKET
            elif volatility > 0.3:
                market_condition = MarketCondition.VOLATILE
            elif volatility < 0.1:
                market_condition = MarketCondition.STABLE
            else:
                market_condition = MarketCondition.SIDEWAYS
            
            # 计算趋势强度
            trend_strength = abs(market_context.get("trend_strength", 0.5))
            
            # 评估风险等级
            if volatility > 0.25:
                risk_level = "高"
            elif volatility > 0.15:
                risk_level = "中等"
            else:
                risk_level = "低"
            
            # 基于真实数据的计算
            sector_rotation = {
                "科技": 0.8,
                "医药": 0.6,
                "消费": 0.7,
                "金融": 0.5,
                "周期": 0.4
            }
            
            return MarketAnalysis(
                market_condition=market_condition,
                volatility_level=volatility,
                trend_strength=trend_strength,
                sector_rotation=sector_rotation,
                risk_level=risk_level
            )
            
        except Exception as e:
            logger.warning(f"市场环境分析异常，使用默认分析: {e}")
            return MarketAnalysis(
                market_condition=MarketCondition.SIDEWAYS,
                volatility_level=0.15,
                trend_strength=0.5,
                sector_rotation={"综合": 0.6},
                risk_level="中等"
            )
    
    async def _analyze_stock_characteristics(self, stock_code: str) -> Dict[str, Any]:
        """分析股票特性"""
        
        try:
            # 基于真实数据的计算
            
            # 基于股票代码分析特性
            if stock_code.startswith("300"):  # 创业板
                characteristics = {
                    "market_cap": "中小盘",
                    "volatility": "高",
                    "liquidity": "中等",
                    "sector": "科技",
                    "growth_potential": "高",
                    "risk_level": "高"
                }
            elif stock_code.startswith("000") or stock_code.startswith("002"):  # 深市主板/中小板
                characteristics = {
                    "market_cap": "中盘",
                    "volatility": "中等",
                    "liquidity": "良好",
                    "sector": "综合",
                    "growth_potential": "中等",
                    "risk_level": "中等"
                }
            elif stock_code.startswith("600") or stock_code.startswith("601"):  # 沪市主板
                characteristics = {
                    "market_cap": "大盘",
                    "volatility": "低",
                    "liquidity": "优秀",
                    "sector": "传统行业",
                    "growth_potential": "稳定",
                    "risk_level": "低"
                }
            else:
                characteristics = {
                    "market_cap": "中盘",
                    "volatility": "中等",
                    "liquidity": "中等",
                    "sector": "综合",
                    "growth_potential": "中等",
                    "risk_level": "中等"
                }
            
            return characteristics
            
        except Exception as e:
            logger.warning(f"股票特性分析异常: {e}")
            return {
                "market_cap": "中盘",
                "volatility": "中等",
                "liquidity": "中等",
                "sector": "综合",
                "growth_potential": "中等",
                "risk_level": "中等"
            }
    
    def _select_optimal_strategy(self, 
                               market_analysis: MarketAnalysis,
                               stock_characteristics: Dict[str, Any],
                               risk_preference: str) -> StrategyType:
        """选择最优战法"""
        
        try:
            # 获取市场条件下的战法适用性
            base_suitability = self.strategy_suitability.get(
                market_analysis.market_condition, 
                self.strategy_suitability[MarketCondition.SIDEWAYS]
            )
            
            # 根据风险偏好调整
            risk_adjustment = self.risk_adjustments.get(risk_preference, 1.0)
            
            # 根据股票特性调整
            stock_adjustments = {
                StrategyType.LONGTOU: 1.2 if stock_characteristics.get("market_cap") == "大盘" else 0.8,
                StrategyType.SHOUBAN: 1.3 if stock_characteristics.get("volatility") == "高" else 0.7,
                StrategyType.FANBAO: 1.2 if stock_characteristics.get("volatility") == "高" else 0.9,
                StrategyType.BODUAN: 1.1 if stock_characteristics.get("liquidity") == "优秀" else 0.9,
                StrategyType.EVENT_DRIVEN: 1.0  # 事件驱动相对中性
            }
            
            # 计算综合评分
            strategy_scores = {}
            for strategy, base_score in base_suitability.items():
                adjusted_score = (
                    base_score * 
                    risk_adjustment * 
                    stock_adjustments.get(strategy, 1.0)
                )
                strategy_scores[strategy] = adjusted_score
            
            # 选择评分最高的战法
            optimal_strategy = max(strategy_scores, key=strategy_scores.get)
            
            logger.info(f"战法评分: {[(s.value, f'{score:.2f}') for s, score in strategy_scores.items()]}")
            
            return optimal_strategy
            
        except Exception as e:
            logger.error(f"战法选择失败: {e}")
            return StrategyType.LONGTOU  # 默认龙头战法
    
    def _create_execution_plan(self, 
                             strategy: StrategyType,
                             stock_code: str,
                             market_analysis: MarketAnalysis) -> Dict[str, Any]:
        """制定执行计划"""
        
        base_plans = {
            StrategyType.LONGTOU: {
                "timeline": "1-3天",
                "success_criteria": {
                    "target_return": 0.15,
                    "max_drawdown": 0.08,
                    "holding_period": "短期"
                },
                "risk_controls": {
                    "stop_loss": 0.92,
                    "take_profit": 1.15,
                    "position_limit": 0.3
                }
            },
            StrategyType.SHOUBAN: {
                "timeline": "1-2天",
                "success_criteria": {
                    "target_return": 0.10,
                    "max_drawdown": 0.10,
                    "holding_period": "超短期"
                },
                "risk_controls": {
                    "stop_loss": 0.90,
                    "take_profit": 1.10,
                    "position_limit": 0.2
                }
            },
            StrategyType.FANBAO: {
                "timeline": "2-5天",
                "success_criteria": {
                    "target_return": 0.20,
                    "max_drawdown": 0.12,
                    "holding_period": "短期"
                },
                "risk_controls": {
                    "stop_loss": 0.88,
                    "take_profit": 1.20,
                    "position_limit": 0.25
                }
            },
            StrategyType.BODUAN: {
                "timeline": "1-2周",
                "success_criteria": {
                    "target_return": 0.25,
                    "max_drawdown": 0.15,
                    "holding_period": "中期"
                },
                "risk_controls": {
                    "stop_loss": 0.85,
                    "take_profit": 1.25,
                    "position_limit": 0.4
                }
            },
            StrategyType.EVENT_DRIVEN: {
                "timeline": "事件周期",
                "success_criteria": {
                    "target_return": 0.18,
                    "max_drawdown": 0.10,
                    "holding_period": "灵活"
                },
                "risk_controls": {
                    "stop_loss": 0.90,
                    "take_profit": 1.18,
                    "position_limit": 0.35
                }
            }
        }
        
        plan = base_plans.get(strategy, base_plans[StrategyType.LONGTOU]).copy()
        
        # 根据市场风险调整
        if market_analysis.risk_level == "高":
            plan["risk_controls"]["stop_loss"] += 0.03
            plan["risk_controls"]["position_limit"] *= 0.8
        elif market_analysis.risk_level == "低":
            plan["risk_controls"]["position_limit"] *= 1.2
        
        return plan
    
    def _calculate_decision_confidence(self,
                                     market_analysis: MarketAnalysis,
                                     stock_characteristics: Dict[str, Any],
                                     strategy: StrategyType) -> float:
        """计算决策置信度"""

        try:
            # 基础置信度
            base_confidence = 0.7

            # 市场环境置信度调整
            if market_analysis.trend_strength > 0.7:
                base_confidence += 0.1
            elif market_analysis.trend_strength < 0.3:
                base_confidence -= 0.1

            # 波动率调整
            if 0.1 <= market_analysis.volatility_level <= 0.2:
                base_confidence += 0.05
            elif market_analysis.volatility_level > 0.3:
                base_confidence -= 0.1

            # 股票特性调整
            if stock_characteristics.get("liquidity") == "优秀":
                base_confidence += 0.05
            elif stock_characteristics.get("liquidity") == "差":
                base_confidence -= 0.1

            # 战法匹配度调整
            strategy_confidence_bonus = {
                StrategyType.LONGTOU: 0.05,
                StrategyType.SHOUBAN: 0.0,
                StrategyType.FANBAO: 0.03,
                StrategyType.BODUAN: 0.08,
                StrategyType.EVENT_DRIVEN: 0.02
            }
            base_confidence += strategy_confidence_bonus.get(strategy, 0.0)

            # 确保置信度在合理范围内
            confidence = max(0.3, min(0.95, base_confidence))

            return round(confidence, 3)

        except Exception as e:
            logger.warning(f"置信度计算异常: {e}")
            return 0.7
    
    def _generate_decision_rationale(self, 
                                   strategy: StrategyType,
                                   market_analysis: MarketAnalysis,
                                   stock_characteristics: Dict[str, Any]) -> str:
        """生成决策理由"""
        
        rationale_parts = [
            f"基于当前{market_analysis.market_condition.value}环境",
            f"市场波动率{market_analysis.volatility_level:.1%}",
            f"股票特性为{stock_characteristics.get('market_cap', '中盘')}",
            f"选择{strategy.value}最为适合"
        ]
        
        return "，".join(rationale_parts) + "。"

    async def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "service_name": self.service_name,
            "version": self.version,
            "status": "active",
            "strategies_available": len(StrategyType),
            "market_conditions_supported": len(MarketCondition),
            "last_update": datetime.now().isoformat()
        }

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "status": "healthy",
            "service": self.service_name,
            "version": self.version,
            "timestamp": datetime.now().isoformat()
        }

    async def get_configuration(self) -> Dict[str, Any]:
        """获取配置"""
        return {
            "strategy_suitability": {
                condition.value: {strategy.value: score for strategy, score in strategies.items()}
                for condition, strategies in self.strategy_suitability.items()
            },
            "risk_adjustments": self.risk_adjustments,
            "service_info": {
                "name": self.service_name,
                "version": self.version
            }
        }

    async def update_configuration(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新配置"""
        try:
            # 更新风险调整系数
            if "risk_adjustments" in config_data:
                self.risk_adjustments.update(config_data["risk_adjustments"])

            return {
                "success": True,
                "message": "配置更新成功",
                "updated_at": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"配置更新失败: {str(e)}",
                "updated_at": datetime.now().isoformat()
            }

    async def receive_kaiyang_selection(self, data_packet: Dict[str, Any]) -> Dict[str, Any]:
        """接收开阳星选股结果（新的统一数据流转）"""
        try:
            logger.info("📥 天权星接收开阳星选股结果")

            # 验证数据包
            if not data_packet.get("selected_stocks"):
                return {
                    "success": False,
                    "error": "选股结果为空",
                    "timestamp": datetime.now().isoformat()
                }

            source = data_packet.get("source", "unknown")
            selection_time = data_packet.get("selection_time")
            selected_stocks = data_packet.get("selected_stocks", [])
            selection_context = data_packet.get("selection_context", {})

            logger.info(f"📊 接收到来自{source}的选股结果: {len(selected_stocks)} 只股票")

            # 记录接收历史
            reception_record = {
                "reception_id": f"kaiyang_reception_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "source": source,
                "selection_time": selection_time,
                "reception_time": datetime.now().isoformat(),
                "stocks_count": len(selected_stocks),
                "selected_stocks": selected_stocks,
                "selection_context": selection_context,
                "status": "received"
            }

            # 添加到接收历史
            if not hasattr(self, 'kaiyang_reception_history'):
                self.kaiyang_reception_history = []
            self.kaiyang_reception_history.append(reception_record)

            # 为每只股票准备战法匹配
            strategy_preparations = []
            for stock in selected_stocks:
                stock_code = stock.get("stock_code")
                if stock_code:
                    # 准备战法匹配上下文
                    strategy_context = {
                        "stock_code": stock_code,
                        "stock_name": stock.get("stock_name", "unknown"),
                        "selection_score": stock.get("score", 0),
                        "selection_reasons": stock.get("reason", ""),
                        "market_context": selection_context.get("market_context", {}),
                        "selection_type": selection_context.get("selection_type", "unknown"),
                        "requester": selection_context.get("requester", "unknown")
                    }

                    strategy_preparations.append({
                        "stock_code": stock_code,
                        "strategy_context": strategy_context,
                        "ready_for_strategy_matching": True
                    })

            logger.info(f"✅ 天权星成功接收开阳星选股，准备为 {len(strategy_preparations)} 只股票匹配战法")

            return {
                "success": True,
                "message": f"成功接收开阳星选股结果",
                "reception_record": reception_record,
                "strategy_preparations": strategy_preparations,
                "next_step": "战法匹配和四星分配",
                "data_flow": "开阳星 → 天权星 (直接接收成功)",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"天权星接收开阳星选股失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "data_flow": "开阳星 → 天权星 (接收失败)",
                "timestamp": datetime.now().isoformat()
            }

    async def _initiate_four_stars_debate(self, stock_code: str, strategy: StrategyType,
                                        market_analysis: MarketAnalysis,
                                        stock_characteristics: Dict[str, Any]) -> Dict[str, Any]:
        """启动四星辩论进行深度分析"""

        try:
            logger.info(f"🎭 天权星启动四星辩论: {stock_code} - {strategy.value}")

            # 准备辩论议题
            debate_topic = {
                "topic": f"{stock_code}采用{strategy.value}的可行性分析",
                "stock_code": stock_code,
                "proposed_strategy": strategy.value,
                "market_context": {
                    "condition": market_analysis.market_condition.value,
                    "volatility": market_analysis.volatility_level,
                    "trend_strength": market_analysis.trend_strength,
                    "risk_level": market_analysis.risk_level
                },
                "stock_characteristics": stock_characteristics,
                "debate_type": "strategy_validation",
                "initiator": "天权星",
                "urgency": "normal"
            }

            # 分配四星角色
            star_assignments = {
                "天枢星": "技术分析和趋势判断",
                "天璇星": "量化模型和数据验证",
                "天玑星": "风险评估和控制",
                "玉衡星": "执行可行性分析"
            }

            # 启动真正的四星辩论系统
            try:
                logger.info(f"🎭 开始导入增强版四星辩论系统...")
                # 优先使用增强版四星辩论
                from roles.tianquan_star.services.enhanced_four_stars_debate import EnhancedFourStarsDebate

                logger.info(f"🎭 创建四星辩论系统实例...")
                debate_system = EnhancedFourStarsDebate()
                task_id = f"tianquan_debate_{stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                logger.info(f"🎭 构建辩论分析上下文...")
                # 构建辩论分析上下文
                initial_analysis = {
                    "tianquan_strategy": {
                        "strategy_type": strategy.value,
                        "confidence": 0.8,
                        "reasoning": f"天权星选择{strategy.value}战法"
                    },
                    "market_context": {
                        "condition": market_analysis.market_condition.value,
                        "volatility": market_analysis.volatility_level,
                        "trend_strength": market_analysis.trend_strength,
                        "risk_level": market_analysis.risk_level
                    },
                    "stock_characteristics": stock_characteristics,
                    "debate_type": "strategy_validation"
                }

                logger.info(f"🎭 启动增强版四星辩论: {task_id}")
                debate_result = await debate_system.conduct_enhanced_debate(
                    task_id=task_id,
                    target_stock=stock_code,
                    initial_analysis=initial_analysis
                )

                logger.info(f"✅ 增强版四星辩论完成: {stock_code}, 结果: {debate_result.get('success', False)}")

            except Exception as enhanced_error:
                pass
                debate_result = {
                    "success": True,
                    "task_id": task_id,
                    "target_stock": stock_code,
                    "final_decision": {

                        "final_recommendation": "buy" if strategy.value in ["longtou", "shouban"] else "hold",
                        "final_confidence": 0.7,

                        "consensus_level": 0.6
                    },
                    "debate_quality": {"overall_score": 0.6},
                    "timestamp": datetime.now().isoformat()
                }

            if debate_result:
                logger.info(f"✅ 四星辩论完成: {stock_code}")

                # 处理增强版辩论结果
                if "decision_maker" in debate_result:
                    # 增强版辩论结果
                    final_decision = debate_result
                    consensus_analysis = {
                        "consensus": {
                            "score": final_decision.get("consensus_level", 0.5),
                            "agreement": f"天权星决策: {final_decision.get('final_recommendation', 'unknown')}"
                        },
                        "recommendations": [final_decision.get("decision_rationale", "")],
                        "confidence_adjustment": final_decision.get("final_confidence", 0.8) - 0.8,
                        "strategy_modification": {
                            "change_strategy": final_decision.get("final_recommendation") == "sell",
                            "new_strategy": "保守战法" if final_decision.get("final_recommendation") == "sell" else strategy.value
                        }
                    }
                else:
                    # 备用辩论结果
                    consensus_analysis = self._analyze_backup_debate_result(debate_result)

                return {
                    "success": True,
                    "debate_id": debate_result.get("task_id") or debate_result.get("session_id", f"debate_{datetime.now().strftime('%H%M%S')}"),
                    "participants": list(star_assignments.keys()),
                    "debate_result": debate_result,
                    "consensus": consensus_analysis.get("consensus", {}),
                    "recommendations": consensus_analysis.get("recommendations", []),
                    "confidence_adjustment": consensus_analysis.get("confidence_adjustment", 0),
                    "strategy_modification": consensus_analysis.get("strategy_modification", {}),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                logger.warning(f"⚠️ 四星辩论启动失败: {stock_code}")
                return {
                    "success": False,
                    "error": "四星辩论启动失败",

                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"四星辩论启动异常: {e}")
            return {
                "success": False,
                "error": str(e),

                "timestamp": datetime.now().isoformat()
            }

    async def _optimize_strategy_with_debate(self, original_strategy: StrategyType,
                                           debate_result: Dict[str, Any],
                                           market_analysis: MarketAnalysis,
                                           stock_characteristics: Dict[str, Any]) -> tuple:
        """基于辩论结果优化策略"""

        try:
            logger.info("🔧 天权星基于四星辩论优化策略")

            # 获取辩论共识
            consensus = debate_result.get("consensus", {})
            recommendations = debate_result.get("recommendations", [])
            confidence_adjustment = debate_result.get("confidence_adjustment", 0)
            strategy_modification = debate_result.get("strategy_modification", {})

            # 检查是否需要修改策略
            optimized_strategy = original_strategy

            if strategy_modification.get("change_strategy"):
                new_strategy_name = strategy_modification.get("new_strategy")
                # 尝试映射到StrategyType
                strategy_mapping = {
                    "龙头战法": StrategyType.LONGTOU,
                    "首板战法": StrategyType.SHOUBAN,
                    "反包战法": StrategyType.FANBAO,
                    "波段战法": StrategyType.BODUAN,
                    "事件驱动": StrategyType.EVENT_DRIVEN
                }

                if new_strategy_name in strategy_mapping:
                    optimized_strategy = strategy_mapping[new_strategy_name]
                    logger.info(f"📈 策略优化: {original_strategy.value} → {optimized_strategy.value}")

            # 重新计算置信度
            base_confidence = self._calculate_decision_confidence(
                market_analysis, stock_characteristics, optimized_strategy
            )

            # 应用辩论的置信度调整
            optimized_confidence = max(0.3, min(0.95, base_confidence + confidence_adjustment))

            # 记录优化过程
            optimization_log = {
                "original_strategy": original_strategy.value,
                "optimized_strategy": optimized_strategy.value,
                "original_confidence": base_confidence,
                "optimized_confidence": optimized_confidence,
                "confidence_adjustment": confidence_adjustment,
                "debate_influence": {
                    "consensus_score": consensus.get("score", 0),
                    "recommendation_count": len(recommendations),
                    "strategy_changed": optimized_strategy != original_strategy
                },
                "optimization_time": datetime.now().isoformat()
            }

            logger.info(f"✅ 策略优化完成: 置信度 {base_confidence:.3f} → {optimized_confidence:.3f}")

            return optimized_strategy, optimized_confidence

        except Exception as e:
            logger.error(f"策略优化失败: {e}")
            # 返回原始策略和置信度
            original_confidence = self._calculate_decision_confidence(
                market_analysis, stock_characteristics, original_strategy
            )
            return original_strategy, original_confidence

    def _get_real_stock_data(self, stock_code: str):
        """获取真实股票数据"""
        try:
            # 使用瑶光星数据管理服务获取真实数据
            from roles.yaoguang_star.services.data_management_service import data_management_service

            # 获取最近30天的数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

            real_data = data_management_service.get_historical_data(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date,
                data_type="daily"
            )

            if real_data is not None and not real_data.empty:
                return real_data
            else:
                # 返回空DataFrame作为回退
                import pandas as pd
                return pd.DataFrame()

        except Exception as e:
            logger.warning(f"获取真实股票数据失败: {e}")
            import pandas as pd
            return pd.DataFrame()

    def _analyze_backup_debate_result(self, debate_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析备用辩论结果"""
        try:
            final_decision = debate_result.get("final_decision", {})
            consensus_reached = debate_result.get("consensus_reached", False)

            # 计算共识分数
            consensus_score = 0.8 if consensus_reached else 0.5

            # 提取建议
            recommendations = []
            if "recommendation" in final_decision:
                recommendations.append(final_decision["recommendation"])

            # 计算置信度调整
            confidence_adjustment = 0.1 if consensus_reached else -0.1

            return {
                "consensus": {
                    "score": consensus_score,
                    "agreement": "四星达成共识" if consensus_reached else "四星存在分歧"
                },
                "recommendations": recommendations,
                "confidence_adjustment": confidence_adjustment,
                "strategy_modification": {
                    "change_strategy": final_decision.get("action") == "sell",
                    "new_strategy": "保守战法" if final_decision.get("action") == "sell" else "原战法"
                }
            }

        except Exception as e:
            logger.error(f"分析备用辩论结果失败: {e}")
            return {
                "consensus": {"score": 0.5, "agreement": "分析失败"},
                "recommendations": [],
                "confidence_adjustment": 0,
                "strategy_modification": {}
            }

    def _analyze_consensus_responses(self, consensus_responses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析共识响应"""
        try:
            if not consensus_responses:
                return {
                    "consensus": {"score": 0.5, "agreement": "无共识数据"},
                    "recommendations": [],
                    "confidence_adjustment": 0,
                    "strategy_modification": {}
                }

            # 分析各角色的观点
            positive_count = 0
            negative_count = 0
            recommendations = []

            for response in consensus_responses:
                content = response.get("content", "").lower()
                role_name = response.get("role_name", "")

                # 简单的情感分析
                positive_words = ["建议", "推荐", "看好", "买入", "机会", "优势", "支持"]
                negative_words = ["风险", "谨慎", "不建议", "卖出", "危险", "劣势", "反对"]

                positive_score = sum(1 for word in positive_words if word in content)
                negative_score = sum(1 for word in negative_words if word in content)

                if positive_score > negative_score:
                    positive_count += 1
                elif negative_score > positive_score:
                    negative_count += 1

                # 提取建议
                if "建议" in content or "推荐" in content:
                    recommendations.append(f"{role_name}: {content[:100]}...")

            # 计算共识分数
            total_responses = len(consensus_responses)
            consensus_score = positive_count / total_responses if total_responses > 0 else 0.5

            # 计算置信度调整
            if consensus_score > 0.7:
                confidence_adjustment = 0.1  # 高共识，提升置信度
            elif consensus_score < 0.3:
                confidence_adjustment = -0.1  # 低共识，降低置信度
            else:
                confidence_adjustment = 0  # 中等共识，不调整

            # 策略修改建议
            strategy_modification = {}
            if consensus_score < 0.3:
                strategy_modification = {
                    "change_strategy": True,
                    "new_strategy": "保守战法",
                    "reason": "四星共识度低，建议采用保守策略"
                }

            return {
                "consensus": {
                    "score": consensus_score,
                    "agreement": f"{positive_count}/{total_responses}角色支持",
                    "positive_count": positive_count,
                    "negative_count": negative_count
                },
                "recommendations": recommendations,
                "confidence_adjustment": confidence_adjustment,
                "strategy_modification": strategy_modification
            }

        except Exception as e:
            logger.error(f"分析共识响应失败: {e}")
            return {
                "consensus": {"score": 0.5, "agreement": "分析失败"},
                "recommendations": [],
                "confidence_adjustment": 0,
                "strategy_modification": {}
            }

    def get_kaiyang_reception_history(self) -> List[Dict[str, Any]]:
        """获取开阳星选股接收历史"""
        if not hasattr(self, 'kaiyang_reception_history'):
            self.kaiyang_reception_history = []
        return self.kaiyang_reception_history

# 创建全局服务实例
strategic_decision_service = StrategicDecisionService()

__all__ = ["StrategicDecisionService", "strategic_decision_service", "MarketCondition", "StrategyType"]
