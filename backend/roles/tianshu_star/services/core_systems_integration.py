#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星核心系统集成服务
确保传奇记忆、绩效监控、DeepSeek配置、层级权限正确集成
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class TianshuCoreSystemsIntegration:
    """天枢星核心系统集成器"""
    
    def __init__(self):
        self.memory_system = None
        self.performance_monitor = None
        self.hierarchy_system = None
        self.deepseek_config = None
        self.is_initialized = False
    
    async def initialize(self) -> bool:
        """初始化所有核心系统"""
        try:
            # 1. 初始化传奇记忆系统
            await self._init_memory_system()
            
            # 2. 初始化绩效监控系统
            await self._init_performance_monitor()
            
            # 3. 初始化层级权限系统
            await self._init_hierarchy_system()
            
            # 4. 初始化DeepSeek配置
            await self._init_deepseek_config()
            
            self.is_initialized = True
            logger.info("天枢星核心系统集成完成")
            return True
            
        except Exception as e:
            logger.error(f"天枢星核心系统集成失败: {e}")
            return False
    
    async def _init_memory_system(self):
        """初始化传奇记忆系统"""
        try:
            from backend.core.domain.memory.legendary.interface import legendary_memory_interface
            
            self.memory_system = legendary_memory_interface
            await self.memory_system.initialize()
            
            logger.info("✅ 天枢星传奇记忆系统初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 天枢星传奇记忆系统初始化失败: {e}")
            self.memory_system = None
    
    async def _init_performance_monitor(self):
        """初始化绩效监控系统"""
        try:
            from backend.core.performance.star_performance_monitor import star_performance_monitor
            
            self.performance_monitor = star_performance_monitor
            
            logger.info("✅ 天枢星绩效监控系统初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 天枢星绩效监控系统初始化失败: {e}")
            self.performance_monitor = None
    
    async def _init_hierarchy_system(self):
        """初始化层级权限系统"""
        try:
            from backend.core.enhanced_seven_stars_hierarchy import EnhancedSevenStarsHierarchy
            
            self.hierarchy_system = EnhancedSevenStarsHierarchy()
            
            logger.info("✅ 天枢星层级权限系统初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 天枢星层级权限系统初始化失败: {e}")
            self.hierarchy_system = None
    
    async def _init_deepseek_config(self):
        """初始化DeepSeek配置"""
        try:
            from roles.tianshu_star.config.deepseek_config import (
                get_deepseek_config, get_role_setting, get_memory_config
            )
            
            self.deepseek_config = {
                "api_config": get_deepseek_config(),
                "role_setting": get_role_setting(),
                "memory_config": get_memory_config()
            }
            
            logger.info("✅ 天枢星DeepSeek配置初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 天枢星DeepSeek配置初始化失败: {e}")
            self.deepseek_config = None
    
    async def store_memory(self, memory_type: str, content: str, importance: float = 0.8) -> Dict[str, Any]:
        """存储记忆"""
        if not self.memory_system:
            return {"success": False, "error": "记忆系统不可用"}
        
        try:
            result = await self.memory_system.store_memory(
                role_name="天枢星",
                memory_type=memory_type,
                content=content,
                importance=importance
            )
            return result
            
        except Exception as e:
            logger.error(f"存储记忆失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def record_performance(self, metrics: Dict[str, float]) -> bool:
        """记录绩效"""
        if not self.performance_monitor:
            return False
        
        try:
            await self.performance_monitor.record_performance(
                star_name="天枢星",
                metrics=metrics
            )
            return True
            
        except Exception as e:
            logger.error(f"记录绩效失败: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "initialized": self.is_initialized,
            "memory_system": self.memory_system is not None,
            "performance_monitor": self.performance_monitor is not None,
            "hierarchy_system": self.hierarchy_system is not None,
            "deepseek_config": self.deepseek_config is not None
        }

# 全局实例
tianshu_core_systems = TianshuCoreSystemsIntegration()


