#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星事件识别服务
识别重要市场事件、公司事件、政策事件等
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import re
import json

logger = logging.getLogger(__name__)

class EventIdentificationService:
    """事件识别服务"""
    
    def __init__(self):
        self.service_name = "EventIdentificationService"
        self.version = "1.0.0"
        
        # 事件类型定义
        self.event_types = {
            "corporate": {
                "name": "公司事件",
                "keywords": ["财报", "业绩", "分红", "重组", "并购", "IPO", "增发", "回购", "高管", "董事会"],
                "weight": 0.8
            },
            "policy": {
                "name": "政策事件", 
                "keywords": ["政策", "监管", "央行", "证监会", "银保监会", "发改委", "财政部", "降准", "降息", "加息"],
                "weight": 0.9
            },
            "market": {
                "name": "市场事件",
                "keywords": ["涨停", "跌停", "暴涨", "暴跌", "异动", "放量", "缩量", "突破", "跳空"],
                "weight": 0.7
            },
            "industry": {
                "name": "行业事件",
                "keywords": ["行业", "板块", "概念", "题材", "风口", "热点", "龙头", "产业链"],
                "weight": 0.6
            },
            "international": {
                "name": "国际事件",
                "keywords": ["美联储", "美股", "外汇", "汇率", "贸易", "制裁", "地缘", "原油", "黄金"],
                "weight": 0.8
            },
            "emergency": {
                "name": "突发事件",
                "keywords": ["突发", "紧急", "停牌", "退市", "ST", "警示", "风险", "违规", "调查", "处罚"],
                "weight": 1.0
            }
        }
        
        # 重要性评级关键词
        self.importance_keywords = {
            "critical": ["重大", "特大", "史上", "首次", "创纪录", "里程碑", "突破性"],
            "high": ["重要", "关键", "显著", "大幅", "明显", "强烈"],
            "medium": ["一定", "部分", "适度", "温和", "稳定"],
            "low": ["轻微", "小幅", "略微", "基本", "维持"]
        }
        
        # 影响范围关键词
        self.impact_scope_keywords = {
            "systemic": ["系统性", "全市场", "整体", "全面", "宏观"],
            "sector": ["行业", "板块", "产业", "领域", "细分"],
            "individual": ["个股", "单一", "特定", "局部", "微观"]
        }
        
        logger.info(f" {self.service_name} v{self.version} 初始化完成")
    
    async def identify_events_from_news(self, news_list: List[Dict], threshold: float = 0.7) -> Dict[str, Any]:
        """从新闻中识别事件"""
        try:
            logger.info(f"🔍 开始从{len(news_list)}条新闻中识别事件")
            
            identified_events = []
            event_statistics = {
                "total_news": len(news_list),
                "events_found": 0,
                "event_types_count": {},
                "importance_distribution": {},
                "processing_time": datetime.now().isoformat()
            }
            
            for i, news in enumerate(news_list):
                try:
                    event = await self._analyze_single_news_for_events(news, threshold)
                    
                    if event and event.get("is_event"):
                        identified_events.append(event)
                        event_statistics["events_found"] += 1
                        
                        # 统计事件类型
                        event_type = event.get("event_type", "unknown")
                        event_statistics["event_types_count"][event_type] = event_statistics["event_types_count"].get(event_type, 0) + 1
                        
                        # 统计重要性分布
                        importance = event.get("importance_level", "low")
                        event_statistics["importance_distribution"][importance] = event_statistics["importance_distribution"].get(importance, 0) + 1
                        
                except Exception as e:
                    logger.warning(f"分析第{i+1}条新闻时出错: {e}")
                    continue
            
            # 事件聚类和去重
            clustered_events = await self._cluster_similar_events(identified_events)
            
            # 事件排序（按重要性和时间）
            sorted_events = sorted(clustered_events, key=lambda x: (
                self._get_importance_score(x.get("importance_level", "low")),
                x.get("event_time", "")
            ), reverse=True)
            
            result = {
                "success": True,
                "events": sorted_events,
                "statistics": event_statistics,
                "analysis_summary": {
                    "total_events": len(sorted_events),
                    "critical_events": len([e for e in sorted_events if e.get("importance_level") == "critical"]),
                    "high_priority_events": len([e for e in sorted_events if e.get("importance_level") in ["critical", "high"]]),
                    "most_common_type": max(event_statistics["event_types_count"].items(), key=lambda x: x[1])[0] if event_statistics["event_types_count"] else "none"
                }
            }
            
            logger.info(f" 事件识别完成: 发现{len(sorted_events)}个事件")
            return result
            
        except Exception as e:
            logger.error(f"事件识别失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "events": [],
                "statistics": event_statistics
            }
    
    async def _analyze_single_news_for_events(self, news: Dict, threshold: float) -> Optional[Dict]:
        """分析单条新闻是否包含事件"""
        try:
            title = news.get("title", "")
            content = news.get("content", "")
            full_text = f"{title} {content}"
            
            # 1. 事件类型识别
            event_type, type_confidence = self._identify_event_type(full_text)
            
            if type_confidence < threshold:
                return None
            
            # 2. 重要性评估
            importance_level = self._assess_importance_level(full_text)
            
            # 3. 影响范围评估
            impact_scope = self._assess_impact_scope(full_text)
            
            # 4. 提取关键信息
            key_entities = self._extract_key_entities(full_text)
            
            # 5. 时间信息提取
            event_time = self._extract_event_time(news)
            
            # 6. 计算事件分数
            event_score = self._calculate_event_score(type_confidence, importance_level, impact_scope)
            
            if event_score < threshold:
                return None
            
            event = {
                "is_event": True,
                "event_id": f"event_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(title) % 10000}",
                "event_type": event_type,
                "event_subtype": self._get_event_subtype(full_text, event_type),
                "title": title,
                "description": self._generate_event_description(full_text, event_type),
                "importance_level": importance_level,
                "impact_scope": impact_scope,
                "confidence_score": type_confidence,
                "event_score": event_score,
                "event_time": event_time,
                "source_news": {
                    "title": title,
                    "source": news.get("source", ""),
                    "url": news.get("url", ""),
                    "timestamp": news.get("timestamp", "")
                },
                "key_entities": key_entities,
                "related_symbols": self._extract_related_symbols(full_text),
                "tags": self._generate_event_tags(full_text, event_type)
            }
            
            return event
            
        except Exception as e:
            logger.warning(f"分析单条新闻事件失败: {e}")
            return None
    
    def _identify_event_type(self, text: str) -> Tuple[str, float]:
        """识别事件类型"""
        type_scores = {}
        
        for event_type, config in self.event_types.items():
            score = 0
            keywords = config["keywords"]
            weight = config["weight"]
            
            for keyword in keywords:
                if keyword in text:
                    score += weight
            
            # 归一化分数
            if keywords:
                type_scores[event_type] = min(1.0, score / len(keywords))
        
        if not type_scores:
            return "unknown", 0.0
        
        best_type = max(type_scores.items(), key=lambda x: x[1])
        return best_type[0], best_type[1]
    
    def _assess_importance_level(self, text: str) -> str:
        """评估重要性等级"""
        for level, keywords in self.importance_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    return level
        
        # 基于文本长度和关键词密度的启发式判断
        if len(text) > 500 and any(word in text for word in ["重大", "重要", "关键"]):
            return "high"
        elif len(text) > 200:
            return "medium"
        else:
            return "low"
    
    def _assess_impact_scope(self, text: str) -> str:
        """评估影响范围"""
        for scope, keywords in self.impact_scope_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    return scope
        
        return "individual"
    
    def _extract_key_entities(self, text: str) -> List[str]:
        """提取关键实体"""
        entities = []
        
        # 提取股票代码
        stock_pattern = r'\b\d{6}\b'
        stocks = re.findall(stock_pattern, text)
        entities.extend([f"股票:{stock}" for stock in stocks])
        
        company_pattern = r'([A-Z\u4e00-\u9fa5]{2,10})(公司|集团|股份|有限)'
        companies = re.findall(company_pattern, text)
        entities.extend([f"公司:{company[0]}" for company in companies])
        
        # 提取金额
        amount_pattern = r'(\d+\.?\d*)(亿|万|千万)元?'
        amounts = re.findall(amount_pattern, text)
        entities.extend([f"金额:{amount[0]}{amount[1]}元" for amount in amounts])
        
        return list(set(entities))[:10]  # 最多返回10个实体
    
    def _extract_event_time(self, news: Dict) -> str:
        """提取事件时间"""
        # 优先使用新闻时间戳
        if news.get("timestamp"):
            return news["timestamp"]
        
        # 尝试从标题或内容中提取时间
        text = f"{news.get('title', '')} {news.get('content', '')}"
        
        time_patterns = [
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
            r'(\d{1,2}月\d{1,2}日)',
            r'(今日|昨日|明日)'
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)
        
        return datetime.now().isoformat()
    
    def _calculate_event_score(self, type_confidence: float, importance_level: str, impact_scope: str) -> float:
        """计算事件分数"""
        importance_weights = {
            "critical": 1.0,
            "high": 0.8,
            "medium": 0.6,
            "low": 0.4
        }
        
        scope_weights = {
            "systemic": 1.0,
            "sector": 0.8,
            "individual": 0.6
        }
        
        importance_weight = importance_weights.get(importance_level, 0.4)
        scope_weight = scope_weights.get(impact_scope, 0.6)
        
        return type_confidence * importance_weight * scope_weight
    
    def _get_importance_score(self, importance_level: str) -> float:
        """获取重要性分数（用于排序）"""
        scores = {
            "critical": 4.0,
            "high": 3.0,
            "medium": 2.0,
            "low": 1.0
        }
        return scores.get(importance_level, 1.0)
    
    def _get_event_subtype(self, text: str, event_type: str) -> str:
        """获取事件子类型"""
        subtypes = {
            "corporate": {
                "财报": ["财报", "业绩", "年报", "季报"],
                "重组": ["重组", "并购", "收购", "合并"],
                "融资": ["IPO", "增发", "配股", "可转债"],
                "分红": ["分红", "派息", "送股", "转增"]
            },
            "policy": {
                "货币政策": ["降准", "降息", "加息", "MLF"],
                "监管政策": ["监管", "规定", "办法", "通知"],
                "财政政策": ["减税", "补贴", "支持", "扶持"]
            }
        }
        
        if event_type in subtypes:
            for subtype, keywords in subtypes[event_type].items():
                if any(keyword in text for keyword in keywords):
                    return subtype
        
        return "其他"
    
    def _generate_event_description(self, text: str, event_type: str) -> str:
        """生成事件描述"""
        # 提取前100个字符作为描述
        description = text[:100].strip()
        if len(text) > 100:
            description += "..."
        
        return description
    
    def _extract_related_symbols(self, text: str) -> List[str]:
        """提取相关股票代码"""
        stock_pattern = r'\b\d{6}\b'
        symbols = re.findall(stock_pattern, text)
        return list(set(symbols))[:5]  # 最多返回5个股票代码
    
    def _generate_event_tags(self, text: str, event_type: str) -> List[str]:
        """生成事件标签"""
        tags = [event_type]
        
        # 基于关键词生成标签
        tag_keywords = {
            "利好": ["利好", "上涨", "增长", "突破", "合作"],
            "利空": ["利空", "下跌", "亏损", "风险", "调查"],
            "重大": ["重大", "重要", "关键", "突破性"],
            "紧急": ["紧急", "突发", "停牌", "预警"]
        }
        
        for tag, keywords in tag_keywords.items():
            if any(keyword in text for keyword in keywords):
                tags.append(tag)
        
        return list(set(tags))
    
    async def _cluster_similar_events(self, events: List[Dict]) -> List[Dict]:
        """聚类相似事件"""
        if len(events) <= 1:
            return events
        
        clustered = []
        processed = set()
        
        for i, event in enumerate(events):
            if i in processed:
                continue
            
            cluster = [event]
            title1 = event.get("title", "")
            
            for j, other_event in enumerate(events[i+1:], i+1):
                if j in processed:
                    continue
                
                title2 = other_event.get("title", "")
                
                # 简单的相似度计算
                similarity = self._calculate_title_similarity(title1, title2)
                
                if similarity > 0.7:  # 相似度阈值
                    cluster.append(other_event)
                    processed.add(j)
            
            # 合并相似事件
            if len(cluster) > 1:
                merged_event = self._merge_similar_events(cluster)
                clustered.append(merged_event)
            else:
                clustered.append(event)
            
            processed.add(i)
        
        return clustered
    
    def _calculate_title_similarity(self, title1: str, title2: str) -> float:
        """计算标题相似度"""
        if not title1 or not title2:
            return 0.0
        
        # 简单的字符重叠度计算
        set1 = set(title1)
        set2 = set(title2)
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0
    
    def _merge_similar_events(self, events: List[Dict]) -> Dict:
        """合并相似事件"""
        # 选择分数最高的事件作为主事件
        main_event = max(events, key=lambda x: x.get("event_score", 0))
        
        # 合并相关信息
        all_sources = []
        all_symbols = []
        all_tags = []
        
        for event in events:
            all_sources.append(event.get("source_news", {}))
            all_symbols.extend(event.get("related_symbols", []))
            all_tags.extend(event.get("tags", []))
        
        main_event["related_sources"] = all_sources
        main_event["related_symbols"] = list(set(all_symbols))
        main_event["tags"] = list(set(all_tags))
        main_event["cluster_size"] = len(events)
        
        return main_event

# 全局实例
event_identification_service = EventIdentificationService()

__all__ = ["EventIdentificationService", "event_identification_service"]
