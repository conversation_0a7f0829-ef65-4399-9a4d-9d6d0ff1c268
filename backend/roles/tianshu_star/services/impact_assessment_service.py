#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星影响评估服务
评估新闻事件对股价、市场的具体影响
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import math

logger = logging.getLogger(__name__)

class ImpactAssessmentService:
    """影响评估服务"""
    
    def __init__(self):
        self.service_name = "ImpactAssessmentService"
        self.version = "1.0.0"
        
        # 影响类型权重
        self.impact_weights = {
            "price": 0.4,      # 价格影响
            "volume": 0.3,     # 成交量影响
            "volatility": 0.2, # 波动率影响
            "sentiment": 0.1   # 市场情绪影响
        }
        
        # 事件类型对股价影响的基础系数
        self.event_impact_coefficients = {
            "corporate": {
                "财报": {"positive": 0.15, "negative": -0.12},
                "重组": {"positive": 0.25, "negative": -0.08},
                "分红": {"positive": 0.08, "negative": -0.03},
                "高管变动": {"positive": 0.05, "negative": -0.10}
            },
            "policy": {
                "货币政策": {"positive": 0.20, "negative": -0.15},
                "监管政策": {"positive": 0.10, "negative": -0.18},
                "财政政策": {"positive": 0.12, "negative": -0.08}
            },
            "market": {
                "技术突破": {"positive": 0.08, "negative": -0.06},
                "资金流向": {"positive": 0.12, "negative": -0.10}
            },
            "emergency": {
                "突发事件": {"positive": 0.30, "negative": -0.25}
            }
        }
        
        # 时间衰减因子
        self.time_decay_factors = {
            "immediate": 1.0,    # 立即影响
            "short_term": 0.8,   # 短期影响（1-3天）
            "medium_term": 0.5,  # 中期影响（1-2周）
            "long_term": 0.3     # 长期影响（1个月+）
        }
        
        logger.info(f" {self.service_name} v{self.version} 初始化完成")
    
    async def assess_news_impact(self, news_data: Dict, symbols: List[str] = None) -> Dict[str, Any]:
        """评估新闻影响"""
        try:
            logger.info(f" 开始评估新闻影响: {news_data.get('title', '')[:50]}...")
            
            # 1. 基础影响分析
            base_impact = await self._analyze_base_impact(news_data)
            
            # 2. 股票特定影响分析
            stock_impacts = {}
            if symbols:
                for symbol in symbols:
                    stock_impact = await self._analyze_stock_specific_impact(news_data, symbol, base_impact)
                    stock_impacts[symbol] = stock_impact
            
            # 3. 市场整体影响分析
            market_impact = await self._analyze_market_impact(news_data, base_impact)
            
            # 4. 时间维度影响预测
            temporal_impact = await self._predict_temporal_impact(base_impact, news_data)
            
            # 5. 风险评估
            risk_assessment = await self._assess_impact_risks(news_data, base_impact)
            
            result = {
                "success": True,
                "news_title": news_data.get("title", ""),
                "assessment_time": datetime.now().isoformat(),
                "base_impact": base_impact,
                "stock_specific_impacts": stock_impacts,
                "market_impact": market_impact,
                "temporal_impact": temporal_impact,
                "risk_assessment": risk_assessment,
                "overall_assessment": self._generate_overall_assessment(
                    base_impact, stock_impacts, market_impact, temporal_impact
                )
            }
            
            logger.info(f" 新闻影响评估完成")
            return result
            
        except Exception as e:
            logger.error(f"新闻影响评估失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "assessment_time": datetime.now().isoformat()
            }
    
    async def assess_event_impact(self, event_data: Dict, symbols: List[str] = None) -> Dict[str, Any]:
        """评估事件影响"""
        try:
            logger.info(f" 开始评估事件影响: {event_data.get('title', '')[:50]}...")
            
            # 1. 事件影响强度计算
            impact_intensity = self._calculate_event_impact_intensity(event_data)
            
            # 2. 影响范围分析
            impact_scope = self._analyze_impact_scope(event_data)
            
            # 3. 股票影响预测
            stock_predictions = {}
            if symbols:
                for symbol in symbols:
                    prediction = await self._predict_stock_impact(event_data, symbol, impact_intensity)
                    stock_predictions[symbol] = prediction
            
            # 4. 行业影响分析
            industry_impact = await self._analyze_industry_impact(event_data, impact_intensity)
            
            # 5. 市场传导效应分析
            contagion_analysis = await self._analyze_contagion_effects(event_data, impact_intensity)
            
            result = {
                "success": True,
                "event_id": event_data.get("event_id", ""),
                "event_type": event_data.get("event_type", ""),
                "assessment_time": datetime.now().isoformat(),
                "impact_intensity": impact_intensity,
                "impact_scope": impact_scope,
                "stock_predictions": stock_predictions,
                "industry_impact": industry_impact,
                "contagion_analysis": contagion_analysis,
                "confidence_level": self._calculate_assessment_confidence(event_data, impact_intensity)
            }
            
            logger.info(f" 事件影响评估完成")
            return result
            
        except Exception as e:
            logger.error(f"事件影响评估失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "assessment_time": datetime.now().isoformat()
            }
    
    async def _analyze_base_impact(self, news_data: Dict) -> Dict[str, Any]:
        """分析基础影响"""
        title = news_data.get("title", "")
        content = news_data.get("content", "")
        full_text = f"{title} {content}"
        
        # 1. 情感倾向分析
        sentiment_impact = self._analyze_sentiment_impact(full_text)
        
        # 2. 关键词影响分析
        keyword_impact = self._analyze_keyword_impact(full_text)
        
        # 3. 数值影响分析
        numerical_impact = self._analyze_numerical_impact(full_text)
        
        # 4. 综合影响分数
        overall_score = (
            sentiment_impact["score"] * 0.4 +
            keyword_impact["score"] * 0.4 +
            numerical_impact["score"] * 0.2
        )
        
        return {
            "overall_score": overall_score,
            "impact_direction": "positive" if overall_score > 0 else "negative" if overall_score < 0 else "neutral",
            "impact_magnitude": abs(overall_score),
            "sentiment_impact": sentiment_impact,
            "keyword_impact": keyword_impact,
            "numerical_impact": numerical_impact,
            "confidence": min(0.9, (abs(overall_score) + 0.3))
        }
    
    def _analyze_sentiment_impact(self, text: str) -> Dict[str, Any]:
        """分析情感影响"""
        positive_words = ["利好", "上涨", "增长", "突破", "合作", "成功", "优秀", "强劲"]
        negative_words = ["利空", "下跌", "亏损", "风险", "失败", "困难", "下滑", "恶化"]
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        # 计算情感分数 (-1 到 1)
        total_words = positive_count + negative_count
        if total_words == 0:
            score = 0
        else:
            score = (positive_count - negative_count) / total_words
        
        return {
            "score": score,
            "positive_signals": positive_count,
            "negative_signals": negative_count,
            "dominant_sentiment": "positive" if score > 0.1 else "negative" if score < -0.1 else "neutral"
        }
    
    def _analyze_keyword_impact(self, text: str) -> Dict[str, Any]:
        """分析关键词影响"""
        high_impact_keywords = {
            "positive": ["重大突破", "战略合作", "业绩大增", "市场领先", "技术创新"],
            "negative": ["重大亏损", "违法违规", "市场萎缩", "技术落后", "经营困难"]
        }
        
        positive_score = 0
        negative_score = 0
        matched_keywords = []
        
        for keyword in high_impact_keywords["positive"]:
            if keyword in text:
                positive_score += 1
                matched_keywords.append(f"+{keyword}")
        
        for keyword in high_impact_keywords["negative"]:
            if keyword in text:
                negative_score += 1
                matched_keywords.append(f"-{keyword}")
        
        net_score = positive_score - negative_score
        normalized_score = max(-1, min(1, net_score / 3))  # 归一化到-1到1
        
        return {
            "score": normalized_score,
            "matched_keywords": matched_keywords,
            "keyword_count": len(matched_keywords)
        }
    
    def _analyze_numerical_impact(self, text: str) -> Dict[str, Any]:
        """分析数值影响"""
        import re
        
        # 提取百分比
        percentage_pattern = r'(\d+\.?\d*)%'
        percentages = [float(match) for match in re.findall(percentage_pattern, text)]
        
        # 提取金额
        amount_pattern = r'(\d+\.?\d*)(亿|万|千万)'
        amounts = re.findall(amount_pattern, text)
        
        # 计算数值影响分数
        score = 0
        impact_factors = []
        
        # 百分比影响
        if percentages:
            avg_percentage = sum(percentages) / len(percentages)
            if avg_percentage > 20:
                score += 0.3
                impact_factors.append(f"高百分比变化: {avg_percentage:.1f}%")
            elif avg_percentage > 10:
                score += 0.2
                impact_factors.append(f"中等百分比变化: {avg_percentage:.1f}%")
        
        # 金额影响
        if amounts:
            large_amounts = [float(amount[0]) for amount in amounts if amount[1] == "亿" and float(amount[0]) > 10]
            if large_amounts:
                score += 0.2
                impact_factors.append(f"大额资金: {max(large_amounts)}亿")
        
        return {
            "score": min(1.0, score),
            "percentages": percentages,
            "amounts": amounts,
            "impact_factors": impact_factors
        }
    
    async def _analyze_stock_specific_impact(self, news_data: Dict, symbol: str, base_impact: Dict) -> Dict[str, Any]:
        """分析股票特定影响"""
        try:
            # 1. 检查新闻是否直接提及该股票
            title = news_data.get("title", "")
            content = news_data.get("content", "")
            full_text = f"{title} {content}"
            
            direct_mention = symbol in full_text
            
            # 2. 获取股票基本信息
            stock_info = await self._get_stock_info(symbol)
            
            # 3. 行业关联度分析
            industry_relevance = self._calculate_industry_relevance(full_text, stock_info)
            
            # 4. 影响强度计算
            impact_multiplier = 1.0
            if direct_mention:
                impact_multiplier = 1.5
            elif industry_relevance > 0.7:
                impact_multiplier = 1.2
            elif industry_relevance > 0.4:
                impact_multiplier = 0.8
            else:
                impact_multiplier = 0.3
            
            # 5. 预测价格影响
            base_score = base_impact["overall_score"]
            predicted_impact = base_score * impact_multiplier
            
            # 6. 预测价格变动范围
            price_change_prediction = self._predict_price_change(predicted_impact, stock_info)
            
            return {
                "symbol": symbol,
                "direct_mention": direct_mention,
                "industry_relevance": industry_relevance,
                "impact_multiplier": impact_multiplier,
                "predicted_impact_score": predicted_impact,
                "price_change_prediction": price_change_prediction,
                "confidence": base_impact["confidence"] * (0.9 if direct_mention else 0.6),
                "risk_level": self._assess_stock_risk_level(predicted_impact)
            }
            
        except Exception as e:
            logger.warning(f"分析股票{symbol}特定影响失败: {e}")
            return {
                "symbol": symbol,
                "error": str(e),
                "predicted_impact_score": 0,
                "confidence": 0.1
            }
    
    async def _analyze_market_impact(self, news_data: Dict, base_impact: Dict) -> Dict[str, Any]:
        """分析市场整体影响"""
        event_type = news_data.get("event_type", "unknown")
        importance_level = news_data.get("importance_level", "low")
        
        # 市场影响系数
        market_coefficients = {
            "policy": 0.8,
            "emergency": 0.9,
            "corporate": 0.3,
            "international": 0.7,
            "market": 0.6
        }
        
        importance_multipliers = {
            "critical": 1.0,
            "high": 0.8,
            "medium": 0.5,
            "low": 0.2
        }
        
        base_score = base_impact["overall_score"]
        market_coefficient = market_coefficients.get(event_type, 0.4)
        importance_multiplier = importance_multipliers.get(importance_level, 0.3)
        
        market_impact_score = base_score * market_coefficient * importance_multiplier
        
        return {
            "market_impact_score": market_impact_score,
            "impact_level": "high" if abs(market_impact_score) > 0.6 else "medium" if abs(market_impact_score) > 0.3 else "low",
            "affected_sectors": self._identify_affected_sectors(news_data),
            "systemic_risk": abs(market_impact_score) > 0.7,
            "contagion_probability": min(1.0, abs(market_impact_score) * 1.2)
        }
    
    async def _predict_temporal_impact(self, base_impact: Dict, news_data: Dict) -> Dict[str, Any]:
        """预测时间维度影响"""
        base_score = base_impact["overall_score"]
        event_type = news_data.get("event_type", "unknown")
        
        # 不同事件类型的时间衰减模式
        decay_patterns = {
            "emergency": [1.0, 0.7, 0.4, 0.2],      # 快速衰减
            "corporate": [1.0, 0.8, 0.6, 0.4],     # 中等衰减
            "policy": [1.0, 0.9, 0.8, 0.7],        # 缓慢衰减
            "market": [1.0, 0.6, 0.3, 0.1]         # 很快衰减
        }
        
        pattern = decay_patterns.get(event_type, [1.0, 0.7, 0.5, 0.3])
        
        temporal_predictions = {
            "immediate": base_score * pattern[0],
            "1_day": base_score * pattern[1],
            "3_days": base_score * pattern[2],
            "1_week": base_score * pattern[3]
        }
        
        return {
            "predictions": temporal_predictions,
            "decay_pattern": event_type,
            "persistence_score": sum(pattern) / len(pattern),
            "peak_impact_time": "immediate" if pattern[0] == max(pattern) else "delayed"
        }
    
    async def _assess_impact_risks(self, news_data: Dict, base_impact: Dict) -> Dict[str, Any]:
        """评估影响风险"""
        risk_factors = []
        risk_score = 0
        
        # 1. 不确定性风险
        confidence = base_impact.get("confidence", 0.5)
        if confidence < 0.6:
            risk_factors.append("预测不确定性较高")
            risk_score += 0.3
        
        # 2. 事件类型风险
        event_type = news_data.get("event_type", "unknown")
        if event_type in ["emergency", "policy"]:
            risk_factors.append("事件类型具有高风险特征")
            risk_score += 0.4
        
        # 3. 影响强度风险
        impact_magnitude = base_impact.get("impact_magnitude", 0)
        if impact_magnitude > 0.7:
            risk_factors.append("影响强度较大，存在过度反应风险")
            risk_score += 0.3
        
        return {
            "overall_risk_score": min(1.0, risk_score),
            "risk_level": "high" if risk_score > 0.7 else "medium" if risk_score > 0.4 else "low",
            "risk_factors": risk_factors,
            "mitigation_suggestions": self._generate_risk_mitigation_suggestions(risk_factors)
        }
    
    def _generate_overall_assessment(self, base_impact: Dict, stock_impacts: Dict, 
                                   market_impact: Dict, temporal_impact: Dict) -> Dict[str, Any]:
        """生成整体评估"""
        base_score = base_impact["overall_score"]
        market_score = market_impact["market_impact_score"]
        
        # 计算综合影响分数
        overall_score = (base_score * 0.6 + market_score * 0.4)
        
        # 生成评估结论
        if abs(overall_score) > 0.7:
            assessment_level = "重大影响"
        elif abs(overall_score) > 0.4:
            assessment_level = "中等影响"
        elif abs(overall_score) > 0.2:
            assessment_level = "轻微影响"
        else:
            assessment_level = "影响有限"
        
        return {
            "overall_impact_score": overall_score,
            "assessment_level": assessment_level,
            "impact_direction": "positive" if overall_score > 0 else "negative" if overall_score < 0 else "neutral",
            "confidence_level": base_impact.get("confidence", 0.5),
            "key_conclusions": self._generate_key_conclusions(base_impact, market_impact, temporal_impact),
            "investment_implications": self._generate_investment_implications(overall_score, assessment_level)
        }
    
    # 辅助方法
    async def _get_stock_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        # 这里应该调用股票信息服务
        return {
            "symbol": symbol,
            "industry": "未知",
            "market_cap": 0,
            "sector": "未知"
        }
    
    def _calculate_industry_relevance(self, text: str, stock_info: Dict) -> float:
        """计算行业关联度"""
        industry = stock_info.get("industry", "")
        if industry and industry in text:
            return 0.8
        return 0.3
    
    def _predict_price_change(self, impact_score: float, stock_info: Dict) -> Dict[str, Any]:
        """预测价格变动"""
        base_change = impact_score * 5  # 假设最大5%的变动
        
        return {
            "expected_change_percent": round(base_change, 2),
            "range_low": round(base_change * 0.5, 2),
            "range_high": round(base_change * 1.5, 2),
            "probability": min(0.9, abs(impact_score) + 0.3)
        }
    
    def _assess_stock_risk_level(self, impact_score: float) -> str:
        """评估股票风险等级"""
        abs_score = abs(impact_score)
        if abs_score > 0.8:
            return "high"
        elif abs_score > 0.5:
            return "medium"
        else:
            return "low"
    
    def _identify_affected_sectors(self, news_data: Dict) -> List[str]:
        """识别受影响板块"""
        title = news_data.get("title", "")
        content = news_data.get("content", "")
        text = f"{title} {content}"
        
        sectors = []
        sector_keywords = {
            "科技": ["科技", "人工智能", "芯片", "软件"],
            "医药": ["医药", "医疗", "生物", "疫苗"],
            "金融": ["银行", "保险", "证券", "金融"],
            "地产": ["地产", "房地产", "建筑", "基建"]
        }
        
        for sector, keywords in sector_keywords.items():
            if any(keyword in text for keyword in keywords):
                sectors.append(sector)
        
        return sectors
    
    def _generate_risk_mitigation_suggestions(self, risk_factors: List[str]) -> List[str]:
        """生成风险缓解建议"""
        suggestions = []
        
        if "预测不确定性较高" in risk_factors:
            suggestions.append("建议等待更多信息确认后再做决策")
        
        if "事件类型具有高风险特征" in risk_factors:
            suggestions.append("建议采用分散投资策略降低风险")
        
        if "影响强度较大" in risk_factors:
            suggestions.append("建议设置止损点，控制潜在损失")
        
        return suggestions if suggestions else ["建议保持正常的风险管理策略"]
    
    def _generate_key_conclusions(self, base_impact: Dict, market_impact: Dict, temporal_impact: Dict) -> List[str]:
        """生成关键结论"""
        conclusions = []
        
        base_score = base_impact["overall_score"]
        if abs(base_score) > 0.5:
            direction = "正面" if base_score > 0 else "负面"
            conclusions.append(f"新闻具有明显的{direction}影响")
        
        if market_impact.get("systemic_risk", False):
            conclusions.append("存在系统性风险，可能影响整个市场")
        
        persistence = temporal_impact.get("persistence_score", 0)
        if persistence > 0.6:
            conclusions.append("影响具有较强的持续性")
        elif persistence < 0.3:
            conclusions.append("影响可能是短期的")
        
        return conclusions if conclusions else ["影响程度有限，需要持续观察"]
    
    def _generate_investment_implications(self, overall_score: float, assessment_level: str) -> List[str]:
        """生成投资启示"""
        implications = []
        
        if assessment_level == "重大影响":
            if overall_score > 0:
                implications.append("可考虑适当增加相关标的配置")
            else:
                implications.append("建议减少相关标的风险敞口")
        
        elif assessment_level == "中等影响":
            implications.append("建议密切关注后续发展，适时调整策略")
        
        else:
            implications.append("可维持现有投资策略，无需大幅调整")
        
        return implications

# 全局实例
impact_assessment_service = ImpactAssessmentService()

__all__ = ["ImpactAssessmentService", "impact_assessment_service"]
