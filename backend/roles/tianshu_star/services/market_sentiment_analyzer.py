#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星市场情感分析器
专业的市场情感分析服务
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
import json

logger = logging.getLogger(__name__)

class MarketSentimentAnalyzer:
    """市场情感分析器"""
    
    def __init__(self):
        self.analyzer_name = "天枢星市场情感分析器"
        self.version = "v1.0.0"
        self.is_initialized = False
        
    async def initialize(self):
        """初始化情感分析器"""
        try:
            logger.info(f"🔧 初始化{self.analyzer_name} {self.version}")
            
            # 初始化DeepSeek情感分析
            await self._initialize_deepseek_analyzer()
            
            self.is_initialized = True
            logger.info(f"✅ {self.analyzer_name}初始化完成")
            
        except Exception as e:
            logger.error(f"❌ {self.analyzer_name}初始化失败: {e}")
            raise
    
    async def _initialize_deepseek_analyzer(self):
        """初始化DeepSeek情感分析"""
        try:
            # 检查DeepSeek API是否可用
            from backend.services.ai.deepseek_service import deepseek_service
            
            # 测试连接
            test_result = await deepseek_service.analyze_sentiment("测试文本")
            if test_result.get("success"):
                logger.info("✅ DeepSeek情感分析服务连接成功")
            else:
                logger.warning("⚠️ DeepSeek情感分析服务连接失败，将使用备用方案")
                
        except Exception as e:
            logger.warning(f"⚠️ DeepSeek情感分析初始化失败: {e}")
    
    async def analyze_news_sentiment(self, news_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析新闻情感"""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            logger.info(f"📊 开始分析{len(news_list)}条新闻的情感")
            
            sentiment_results = []
            positive_count = 0
            negative_count = 0
            neutral_count = 0
            
            for news in news_list:
                try:
                    # 分析单条新闻情感
                    sentiment = await self._analyze_single_news_sentiment(news)
                    sentiment_results.append(sentiment)
                    
                    # 统计情感分布
                    if sentiment.get("sentiment") == "positive":
                        positive_count += 1
                    elif sentiment.get("sentiment") == "negative":
                        negative_count += 1
                    else:
                        neutral_count += 1
                        
                except Exception as e:
                    logger.warning(f"单条新闻情感分析失败: {e}")
                    sentiment_results.append({
                        "title": news.get("title", ""),
                        "sentiment": "neutral",
                        "confidence": 0.5,
                        "error": str(e)
                    })
                    neutral_count += 1
            
            # 计算整体情感
            total_news = len(news_list)
            overall_sentiment = self._calculate_overall_sentiment(
                positive_count, negative_count, neutral_count
            )
            
            result = {
                "success": True,
                "total_news": total_news,
                "sentiment_distribution": {
                    "positive": positive_count,
                    "negative": negative_count,
                    "neutral": neutral_count
                },
                "sentiment_percentages": {
                    "positive": (positive_count / total_news) * 100 if total_news > 0 else 0,
                    "negative": (negative_count / total_news) * 100 if total_news > 0 else 0,
                    "neutral": (neutral_count / total_news) * 100 if total_news > 0 else 0
                },
                "overall_sentiment": overall_sentiment,
                "detailed_results": sentiment_results,
                "analysis_time": datetime.now().isoformat()
            }
            
            logger.info(f"✅ 新闻情感分析完成: 正面{positive_count}条, 负面{negative_count}条, 中性{neutral_count}条")
            return result
            
        except Exception as e:
            logger.error(f"❌ 新闻情感分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis_time": datetime.now().isoformat()
            }
    
    async def _analyze_single_news_sentiment(self, news: Dict[str, Any]) -> Dict[str, Any]:
        """分析单条新闻情感"""
        try:
            title = news.get("title", "")
            content = news.get("content", "")
            
            # 组合标题和内容进行分析
            text_to_analyze = f"{title} {content}".strip()
            
            if not text_to_analyze:
                return {
                    "title": title,
                    "sentiment": "neutral",
                    "confidence": 0.5,
                    "reason": "无有效文本内容"
                }
            
            # 使用DeepSeek进行情感分析
            try:
                from backend.services.ai.deepseek_service import deepseek_service
                
                sentiment_result = await deepseek_service.analyze_sentiment(text_to_analyze)
                
                if sentiment_result.get("success"):
                    return {
                        "title": title,
                        "sentiment": sentiment_result.get("sentiment", "neutral"),
                        "confidence": sentiment_result.get("confidence", 0.5),
                        "reason": sentiment_result.get("reason", ""),
                        "keywords": sentiment_result.get("keywords", [])
                    }
                    
            except Exception as e:
                logger.debug(f"DeepSeek情感分析失败，使用备用方案: {e}")
            
            # 备用情感分析方案
            sentiment = self._simple_sentiment_analysis(text_to_analyze)
            
            return {
                "title": title,
                "sentiment": sentiment["sentiment"],
                "confidence": sentiment["confidence"],
                "reason": sentiment["reason"],
                "method": "简单关键词分析"
            }
            
        except Exception as e:
            logger.error(f"单条新闻情感分析异常: {e}")
            return {
                "title": news.get("title", ""),
                "sentiment": "neutral",
                "confidence": 0.5,
                "error": str(e)
            }
    
    def _simple_sentiment_analysis(self, text: str) -> Dict[str, Any]:
        """简单的情感分析备用方案"""
        try:
            # 正面关键词
            positive_keywords = [
                "上涨", "涨幅", "增长", "利好", "盈利", "收益", "突破", "创新高",
                "买入", "推荐", "看好", "乐观", "强势", "回升", "反弹", "机会"
            ]
            
            # 负面关键词
            negative_keywords = [
                "下跌", "跌幅", "下降", "利空", "亏损", "风险", "回调", "创新低",
                "卖出", "减持", "看空", "悲观", "弱势", "暴跌", "崩盘", "危机"
            ]
            
            # 计算情感得分
            positive_score = sum(1 for keyword in positive_keywords if keyword in text)
            negative_score = sum(1 for keyword in negative_keywords if keyword in text)
            
            # 确定情感
            if positive_score > negative_score:
                sentiment = "positive"
                confidence = min(0.8, 0.5 + (positive_score - negative_score) * 0.1)
                reason = f"检测到{positive_score}个正面关键词"
            elif negative_score > positive_score:
                sentiment = "negative"
                confidence = min(0.8, 0.5 + (negative_score - positive_score) * 0.1)
                reason = f"检测到{negative_score}个负面关键词"
            else:
                sentiment = "neutral"
                confidence = 0.5
                reason = "正负面关键词平衡或无明显倾向"
            
            return {
                "sentiment": sentiment,
                "confidence": confidence,
                "reason": reason
            }
            
        except Exception as e:
            return {
                "sentiment": "neutral",
                "confidence": 0.5,
                "reason": f"分析异常: {e}"
            }
    
    def _calculate_overall_sentiment(self, positive_count: int, negative_count: int, neutral_count: int) -> Dict[str, Any]:
        """计算整体情感"""
        try:
            total = positive_count + negative_count + neutral_count
            
            if total == 0:
                return {
                    "sentiment": "neutral",
                    "confidence": 0.5,
                    "reason": "无有效新闻数据"
                }
            
            positive_ratio = positive_count / total
            negative_ratio = negative_count / total
            
            # 确定整体情感
            if positive_ratio > 0.6:
                sentiment = "positive"
                confidence = min(0.9, 0.6 + (positive_ratio - 0.6) * 0.75)
                reason = f"正面新闻占比{positive_ratio:.1%}，市场情绪积极"
            elif negative_ratio > 0.6:
                sentiment = "negative"
                confidence = min(0.9, 0.6 + (negative_ratio - 0.6) * 0.75)
                reason = f"负面新闻占比{negative_ratio:.1%}，市场情绪悲观"
            elif positive_ratio > negative_ratio:
                sentiment = "slightly_positive"
                confidence = 0.5 + (positive_ratio - negative_ratio) * 0.5
                reason = f"正面新闻略多，市场情绪偏积极"
            elif negative_ratio > positive_ratio:
                sentiment = "slightly_negative"
                confidence = 0.5 + (negative_ratio - positive_ratio) * 0.5
                reason = f"负面新闻略多，市场情绪偏悲观"
            else:
                sentiment = "neutral"
                confidence = 0.5
                reason = "正负面新闻平衡，市场情绪中性"
            
            return {
                "sentiment": sentiment,
                "confidence": confidence,
                "reason": reason,
                "statistics": {
                    "positive_ratio": positive_ratio,
                    "negative_ratio": negative_ratio,
                    "neutral_ratio": neutral_count / total
                }
            }
            
        except Exception as e:
            return {
                "sentiment": "neutral",
                "confidence": 0.5,
                "reason": f"计算异常: {e}"
            }

# 全局实例
market_sentiment_analyzer = MarketSentimentAnalyzer()
