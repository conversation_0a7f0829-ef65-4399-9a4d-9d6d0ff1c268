
def safe_parse_jsonp(text_content, callback_name=None):
    """安全的JSONP解析"""
    try:
        if not text_content or text_content.strip() == '':
            return None
            
        # 如果是空的JSON对象
        if text_content.strip() == '{}':
            return {}
            
        # 自动检测callback
        if not callback_name:
            callback_match = re.search(r'^([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(', text_content)
            if callback_match:
                callback_name = callback_match.group(1)
        
        # 移除JSONP包装
        if callback_name and text_content.startswith(callback_name + '('):
            start = len(callback_name) + 1
            end = text_content.rfind(')')
            if end > start:
                json_str = text_content[start:end]
            else:
                json_str = text_content
        else:
            json_str = text_content
        
        return safe_parse_jsonp(text_content, callback)
        
    except Exception as e:
        logger.error(f"JSONP解析失败: {e}")
        return None

import re
import json
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星自动化系统
负责市场信息收集和新闻分析的自动化执行
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

# 导入天枢星核心服务
from .news_collection_service import news_collection_service
from .unified_data_collector import unified_data_collector
from .tianshu_core_service import tianshu_core_service

logger = logging.getLogger(__name__)

class TianshuAutomationSystem:
    """天枢星自动化系统"""
    
    def __init__(self):
        self.system_name = "TianshuAutomationSystem"
        self.version = "1.0.0"
        self.is_active = False
        self.automation_tasks = {}
        self.legendary_memory = None

        # 核心服务引用
        self.news_service = news_collection_service
        self.data_collector = unified_data_collector
        self.core_service = tianshu_core_service

        # 初始化传奇记忆系统
        self._initialize_legendary_memory()

        logger.info(f"天枢星自动化系统 v{self.version} 初始化完成")

    def _initialize_legendary_memory(self):
        """初始化传奇记忆系统"""
        try:
            from backend.core.domain.memory.legendary.interface import legendary_memory_interface
            self.legendary_memory = legendary_memory_interface
            logger.info("✅ 天枢星传奇记忆系统初始化成功")
        except Exception as e:
            logger.warning(f"⚠️ 天枢星传奇记忆系统初始化失败: {e}")
            self.legendary_memory = None
    
    async def execute_market_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行市场分析自动化任务"""
        try:
            stock_code = context.get("stock_code")
            task_type = context.get("task_type", "market_info_collection")
            session_id = context.get("session_id")
            analysis_depth = context.get("analysis_depth", "standard")
            
            logger.info(f"🔍 天枢星开始执行市场分析: {stock_code}")
            
            # 1. 收集股票基础信息
            basic_info = await self._collect_basic_stock_info(stock_code)
            
            # 2. 收集相关新闻
            news_data = await self._collect_stock_news(stock_code)
            
            # 3. 收集市场数据
            market_data = await self._collect_market_data(stock_code)
            
            # 4. 执行情感分析
            sentiment_analysis = await self._analyze_market_sentiment(stock_code, news_data)
            
            # 5. 生成综合分析报告
            analysis_result = {
                "stock_code": stock_code,
                "analysis_type": task_type,
                "session_id": session_id,
                "analysis_depth": analysis_depth,
                "basic_info": basic_info,
                "news_analysis": news_data,
                "market_data": market_data,
                "sentiment_analysis": sentiment_analysis,
                "analysis_time": datetime.now().isoformat(),
                "automation_source": "tianshu_automation_system"
            }
            
            logger.info(f"✅ 天枢星市场分析完成: {stock_code}")
            
            return {
                "success": True,
                "analysis_result": analysis_result,
                "execution_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"天枢星市场分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": datetime.now().isoformat()
            }
    
    async def _collect_basic_stock_info(self, stock_code: str) -> Dict[str, Any]:
        """收集股票基础信息"""
        try:
            # 调用核心服务获取股票基础信息
            basic_info = await self.core_service.get_stock_basic_info(stock_code)
            
            return {
                "stock_code": stock_code,
                "company_name": basic_info.get("company_name", "未知公司"),
                "industry": basic_info.get("industry", "未知行业"),
                "market_cap": basic_info.get("market_cap", 0),
                "pe_ratio": basic_info.get("pe_ratio", 0),
                "pb_ratio": basic_info.get("pb_ratio", 0),
                "collection_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"收集股票基础信息失败: {e}")
            return {
                "stock_code": stock_code,
                "error": str(e),
                "collection_time": datetime.now().isoformat()
            }
    
    async def _collect_stock_news(self, stock_code: str) -> Dict[str, Any]:
        """收集股票相关新闻"""
        try:
            # 调用新闻收集服务
            news_result = await self.news_service.collect_stock_news(stock_code, limit=10)
            
            return {
                "stock_code": stock_code,
                "news_count": len(news_(result or {}).get("news_list", [])),
                "news_list": news_(result or {}).get("news_list", []),
                "collection_time": datetime.now().isoformat(),
                "data_source": "news_collection_service"
            }
            
        except Exception as e:
            logger.error(f"收集股票新闻失败: {e}")
            return {
                "stock_code": stock_code,
                "news_count": 0,
                "news_list": [],
                "error": str(e),
                "collection_time": datetime.now().isoformat()
            }
    
    async def _collect_market_data(self, stock_code: str) -> Dict[str, Any]:
        """收集市场数据"""
        try:
            # 调用数据收集器获取市场数据
            market_data = await self.data_collector.collect_stock_market_data(stock_code)
            
            return {
                "stock_code": stock_code,
                "current_price": market_data.get("current_price", 0),
                "price_change": market_data.get("price_change", 0),
                "price_change_pct": market_data.get("price_change_pct", 0),
                "volume": market_data.get("volume", 0),
                "turnover": market_data.get("turnover", 0),
                "collection_time": datetime.now().isoformat(),
                "data_source": "unified_data_collector"
            }
            
        except Exception as e:
            logger.error(f"收集市场数据失败: {e}")
            return {
                "stock_code": stock_code,
                "error": str(e),
                "collection_time": datetime.now().isoformat()
            }
    
    async def _analyze_market_sentiment(self, stock_code: str, news_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场情感"""
        try:
            news_list = news_data.get("news_list", [])
            
            if not news_list:
                return {
                    "stock_code": stock_code,
                    "sentiment_score": 0.0,
                    "sentiment_label": "中性",
                    "confidence": 0.5,
                    "analysis_time": datetime.now().isoformat()
                }
            
            # 简单的情感分析逻辑
            positive_keywords = ["上涨", "利好", "增长", "盈利", "突破", "买入", "推荐"]
            negative_keywords = ["下跌", "利空", "亏损", "风险", "调整", "卖出", "减持"]
            
            positive_count = 0
            negative_count = 0
            total_count = len(news_list)
            
            for news in news_list:
                title = news.get("title", "")
                content = news.get("content", "")
                text = title + " " + content
                
                for keyword in positive_keywords:
                    if keyword in text:
                        positive_count += 1
                        break
                
                for keyword in negative_keywords:
                    if keyword in text:
                        negative_count += 1
                        break
            
            # 计算情感得分
            if total_count > 0:
                sentiment_score = (positive_count - negative_count) / total_count
            else:
                sentiment_score = 0.0
            
            # 确定情感标签
            if sentiment_score > 0.2:
                sentiment_label = "积极"
            elif sentiment_score < -0.2:
                sentiment_label = "消极"
            else:
                sentiment_label = "中性"
            
            return {
                "stock_code": stock_code,
                "sentiment_score": sentiment_score,
                "sentiment_label": sentiment_label,
                "positive_count": positive_count,
                "negative_count": negative_count,
                "total_news": total_count,
                "confidence": min(0.8, abs(sentiment_score) + 0.3),
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"市场情感分析失败: {e}")
            return {
                "stock_code": stock_code,
                "sentiment_score": 0.0,
                "sentiment_label": "中性",
                "error": str(e),
                "analysis_time": datetime.now().isoformat()
            }
    
    async def start_automation(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """启动自动化任务"""
        try:
            automation_id = f"tianshu_auto_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self.automation_tasks[automation_id] = {
                "config": config,
                "status": "running",
                "start_time": datetime.now().isoformat()
            }
            
            self.is_active = True
            
            logger.info(f"天枢星自动化任务启动: {automation_id}")
            
            return {
                "success": True,
                "automation_id": automation_id,
                "message": "天枢星自动化任务启动成功"
            }
            
        except Exception as e:
            logger.error(f"启动天枢星自动化任务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def stop_automation(self, automation_id: str) -> Dict[str, Any]:
        """停止自动化任务"""
        try:
            if automation_id in self.automation_tasks:
                self.automation_tasks[automation_id]["status"] = "stopped"
                self.automation_tasks[automation_id]["stop_time"] = datetime.now().isoformat()
                
                logger.info(f"天枢星自动化任务停止: {automation_id}")
                
                return {
                    "success": True,
                    "message": "天枢星自动化任务停止成功"
                }
            else:
                return {
                    "success": False,
                    "error": "自动化任务不存在"
                }
                
        except Exception as e:
            logger.error(f"停止天枢星自动化任务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_automation_status(self) -> Dict[str, Any]:
        """获取自动化状态"""
        return {
            "system_name": self.system_name,
            "version": self.version,
            "is_active": self.is_active,
            "active_tasks": len([t for t in self.automation_tasks.values() if t["status"] == "running"]),
            "total_tasks": len(self.automation_tasks),
            "status_time": datetime.now().isoformat()
        }

    async def _call_role_deepseek(self, prompt: str, context_type: str = "analysis", context_data: dict = None) -> dict:
        """调用天枢星专用DeepSeek分析"""
        try:
            from roles.tianshu_star.config.deepseek_config import get_deepseek_config, get_role_setting
            from shared.infrastructure.deepseek_service import deepseek_service

            # 获取角色专用配置
            config = get_deepseek_config()
            role_setting = get_role_setting()

            # 构建角色专用提示词
            role_prompt = f"{role_setting}\n\n请分析：{prompt}"
            if context_data:
                try:
                    context_str = json.dumps(context_data, ensure_ascii=False, default=str)
                    role_prompt += f"\n\n上下文数据：{context_str}"
                except:
                    role_prompt += f"\n\n上下文数据：{str(context_data)}"

            # 调用DeepSeek服务
            messages = [
                {"role": "system", "content": role_setting},
                {"role": "user", "content": prompt}
            ]

            result = await deepseek_service.chat_completion(messages, **config)

            # 确保DeepSeek服务返回值不为None
            if result is None:
                logger.warning("DeepSeek服务返回None，使用默认响应")
                result = {
                    "success": False,
                    "error": "DeepSeek服务返回None",
                    "response": "服务暂时不可用，请稍后重试"
                }

            # 确保result不为None


            if result is None:


                result = {"success": False, "error": "DeepSeek服务返回None"}


            
            return {
                "success": (result or {}).get("success", False),
                "analysis": (result or {}).get("response", ""),
                "role": "tianshu_star",
                "context_type": context_type,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "role": "tianshu_star",
                "timestamp": datetime.now().isoformat()
            }

    async def store_memory(self, content: str, memory_type: str = "analysis", priority: str = "normal", metadata: dict = None) -> dict:
        """存储记忆到传奇记忆系统"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface
            from core.domain.memory.legendary.models import MessageType, MemoryPriority

            # 映射消息类型
            message_type_enum = MessageType.GENERAL
            if memory_type == "analysis":
                message_type_enum = MessageType.MARKET_ANALYSIS
            elif memory_type == "news":
                message_type_enum = MessageType.NEWS_UPDATE
            elif memory_type == "system":
                message_type_enum = MessageType.SYSTEM_NOTIFICATION

            # 映射优先级
            priority_enum = MemoryPriority.NORMAL
            if priority == "high":
                priority_enum = MemoryPriority.HIGH
            elif priority == "low":
                priority_enum = MemoryPriority.LOW

            result = await legendary_memory_interface.add_memory(
                content=content,
                role="天枢星",
                message_type=message_type_enum,
                priority=priority_enum,
                metadata=metadata or {}
            )

            return {"success": result.success, "memory_id": result.memory_id}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def retrieve_memories(self, query: str, limit: int = 10) -> list:
        """从传奇记忆系统检索记忆"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface

            memories = await legendary_memory_interface.search_memories(
                role="天枢星",
                limit=limit
            )

            return memories

        except Exception as e:
            return []

    async def record_performance(self, metric_name: str, value: float, context: dict = None) -> dict:
        """记录绩效到监控系统"""
        try:
            from core.performance.star_performance_monitor import star_performance_monitor

            result = await star_performance_monitor.record_performance(
                star_name="tianshu_star",
                metric_type=metric_name,
                value=value,
                context=context or {}
            )

            return {"success": result, "metric": metric_name, "value": value}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_performance_stats(self) -> dict:
        """获取角色绩效统计"""
        try:
            from core.performance.star_performance_monitor import star_performance_monitor

            return star_performance_monitor.get_star_performance("tianshu_star")

        except Exception as e:
            return {"error": str(e)}

# 全局实例
tianshu_automation_system = TianshuAutomationSystem()
