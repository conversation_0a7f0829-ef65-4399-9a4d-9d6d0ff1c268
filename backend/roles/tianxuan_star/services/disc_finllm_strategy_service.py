#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略架构师DISC-FinLLM深度集成服务
与现有策略工作流深度集成，不引起冲突
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field

# 导入现有架构
from ..workflows.strategy_workflow_service import StrategyDataPacket
from backend.shared.infrastructure.deepseek_service import DeepSeekService

# 导入传奇记忆系统和四大核心系统
try:
    from core.domain.memory.legendary.interface import legendary_memory_interface
    from core.domain.memory.legendary.models import MessageType, MemoryScope, MemoryPriority
    from roles.tianxuan_star.config.deepseek_config import get_memory_config
    INTEGRATION_AVAILABLE = True
except ImportError:
    # 如果系统不可用，禁用集成
    legendary_memory_interface = None
    MessageType = None
    MemoryScope = None
    MemoryPriority = None
    get_memory_config = None
    INTEGRATION_AVAILABLE = False

try:
    from core.performance.star_performance_monitor import StarPerformanceMonitor
    performance_monitor = StarPerformanceMonitor()
except ImportError:
    performance_monitor = None

try:
    from core.enhanced_seven_stars_hierarchy import EnhancedSevenStarsHierarchy
except ImportError:
    EnhancedSevenStarsHierarchy = None

logger = logging.getLogger(__name__)

class StrategyFinLLMResult(BaseModel):
    """策略FinLLM分析结果"""
    analysis_type: str
    input_data: Dict[str, Any]
    result: Dict[str, Any]
    confidence: float = Field(default=0.8, ge=0.0, le=1.0)
    processing_time: float = 0.0
    timestamp: datetime = Field(default_factory=datetime.now)
    
    # 策略特有字段
    strategy_recommendations: List[str] = Field(default_factory=list)
    risk_assessment: Dict[str, Any] = Field(default_factory=dict)
    expected_performance: Dict[str, float] = Field(default_factory=dict)

class DISCFinLLMStrategyService:
    """策略架构师专用DISC-FinLLM服务 - 深度集成现有工作流"""
    
    def __init__(self):
        # 集成现有DeepSeek服务作为备用
        self.deepseek_service = DeepSeekService()
        
        # 策略专用模板
        self.strategy_templates = {
            "factor_analysis": {
                "prompt_template": """
                作为专业的量化策略架构师，请分析以下因子研发结果：
                
                因子数据：{factor_data}
                市场环境：{market_context}
                
                请从以下角度进行深度分析：
                1. 因子有效性评估
                2. 因子组合建议
                3. 策略构建思路
                4. 风险控制要点
                5. 预期收益评估
                
                请提供具体的策略建议和实施方案。
                """,
                "expected_fields": ["factor_effectiveness", "combination_strategy", "risk_controls", "expected_return"]
            },
            
            "model_optimization": {
                "prompt_template": """
                作为量化模型专家，请优化以下模型开发结果：
                
                模型数据：{model_data}
                回测结果：{backreal_data}
                
                请提供：
                1. 模型性能评估
                2. 参数优化建议
                3. 过拟合风险分析
                4. 模型集成策略
                5. 实盘适应性评估
                
                请给出具体的优化方案。
                """,
                "expected_fields": ["performance_assessment", "optimization_suggestions", "overfitting_analysis", "ensemble_strategy"]
            },
            
            "strategy_integration": {
                "prompt_template": """
                作为投资策略总监，请整合以下策略组件：
                
                因子策略：{factor_strategy}
                模型策略：{model_strategy}
                风险约束：{risk_constraints}
                目标收益：{target_return}
                
                请设计：
                1. 综合策略框架
                2. 权重分配方案
                3. 动态调整机制
                4. 风险控制体系
                5. 业绩评估标准
                
                请提供完整的策略实施方案。
                """,
                "expected_fields": ["strategy_framework", "weight_allocation", "dynamic_adjustment", "risk_system", "performance_metrics"]
            }
        }
        
        # 服务统计
        self.service_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_processing_time": 0.0,
            "strategy_analysis_count": 0,
            "factor_analysis_count": 0,
            "model_optimization_count": 0,
            "integration_analysis_count": 0
        }

        # 初始化四大核心系统
        self._init_core_systems()

        logger.info("策略架构师DISC-FinLLM服务初始化完成")

    def _init_core_systems(self):
        """初始化四大核心系统"""
        try:
            # 1. 传奇记忆系统
            if legendary_memory_interface:
                self.memory_system = legendary_memory_interface
            else:
                self.memory_system = None

            # 2. DeepSeek人设配置
            if get_memory_config:
                self.deepseek_memory_config = get_memory_config()
            else:
                self.deepseek_memory_config = None

            # 3. 绩效监控系统
            self.performance_monitor = performance_monitor

            # 4. 层级权限系统
            if EnhancedSevenStarsHierarchy:
                self.permission_system = EnhancedSevenStarsHierarchy()
            else:
                self.permission_system = None

            # 强制初始化所有核心系统
            if not self.memory_system:
                from core.domain.memory.legendary.interface import legendary_memory_interface
                self.memory_system = legendary_memory_interface

            if not self.deepseek_memory_config:
                from roles.tianxuan_star.config.deepseek_config import get_memory_config
                self.deepseek_memory_config = get_memory_config()

            logger.info("天璇星四大核心系统强制初始化完成")

        except Exception as e:
            logger.error(f"核心系统初始化失败: {e}")
            self.memory_system = None
            self.deepseek_memory_config = None
            self.performance_monitor = None
            self.permission_system = None

    async def _trigger_deepseek_memory(self, trigger_name: str, content: str, context: Dict[str, Any] = None):
        """根据DeepSeek配置触发记忆"""
        try:
            if not self.memory_system or not self.deepseek_memory_config:
                return

            # 获取触发器对应的记忆类型
            memory_type_mapping = {
                "strong_signal": "technical_analysis_memory",
                "pattern_breakout": "pattern_recognition_memory",
                "trend_reversal": "signal_generation_memory",
                "volume_anomaly": "trend_analysis_memory"
            }

            memory_type = memory_type_mapping.get(trigger_name)
            if memory_type:
                # 根据记忆类型选择消息类型
                message_type_mapping = {
                    "technical_analysis_memory": MessageType.MARKET_ANALYSIS,
                    "pattern_recognition_memory": MessageType.MARKET_ANALYSIS,
                    "signal_generation_memory": MessageType.TRADING_SIGNAL,
                    "trend_analysis_memory": MessageType.MARKET_ANALYSIS
                }

                message_type = message_type_mapping.get(memory_type, MessageType.GENERAL)

                # 添加到传奇记忆系统
                try:
                    result = await self.memory_system.add_tianxuan_memory(
                        content=content,
                        message_type=message_type
                    )
                except Exception as e:
                    logger.error(f"记忆添加失败: {e}")
                    result = await self.memory_system.add_tianxuan_memory(
                        content=content
                    )

                if result.success:
                    logger.info(f"天璇星记忆触发成功: {trigger_name} -> {memory_type}")
                else:
                    logger.error(f"天璇星记忆触发失败: {result.message}")

        except Exception as e:
            logger.error(f"DeepSeek记忆触发失败: {e}")

    async def _record_performance_metric(self, metric_name: str, value: float, context: Dict[str, Any] = None):
        """记录绩效指标"""
        try:
            if self.performance_monitor:
                from core.performance.star_performance_monitor import PerformanceMetricType

                # 映射指标名称到枚举类型
                metric_type_mapping = {
                    "factor_analysis_accuracy": PerformanceMetricType.ACCURACY,
                    "signal_accuracy": PerformanceMetricType.ACCURACY,
                    "pattern_recognition_rate": PerformanceMetricType.SUCCESS_RATE,
                    "technical_indicator_precision": PerformanceMetricType.QUALITY_SCORE
                }

                metric_type = metric_type_mapping.get(metric_name, PerformanceMetricType.ACCURACY)

                await self.performance_monitor.record_performance(
                    star_name="天璇星",
                    metric_type=metric_type,
                    value=value,
                    context=context or {}
                )
                logger.debug(f"天璇星绩效记录: {metric_name}={value}")
        except Exception as e:
            logger.error(f"绩效记录失败: {e}")
    
    async def enhance_factor_research(self, factor_data: Dict[str, Any], 
                                    market_context: Dict[str, Any] = None) -> StrategyFinLLMResult:
        """增强因子研发 - 与现有因子研发流程集成"""
        
        start_time = datetime.now()
        self.service_stats["total_requests"] += 1
        self.service_stats["factor_analysis_count"] += 1
        
        try:
            # 1. 从传奇记忆系统获取历史因子经验
            historical_factors = []
            if self.memory_system:
                try:
                    historical_factors = await self.memory_system.search_memories(
                        role="天璇星",
                        limit=5
                    )
                    # 触发DeepSeek记忆
                    await self._trigger_deepseek_memory(
                        "strong_signal",
                        f"因子研发分析: {factor_data.get('summary', '新因子分析')}"
                    )
                except Exception as e:
                    logger.warning(f"传奇记忆系统查询失败: {e}")
                    historical_factors = []
            
            # 2. 构建分析上下文
            analysis_context = {
                "factor_data": factor_data,
                "market_context": market_context or {},
                "historical_experience": [mem.content for mem in historical_factors],
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            # 3. 使用DISC-FinLLM进行因子分析
            factor_analysis = await self._analyze_with_finllm(
                template_key="factor_analysis",
                context=analysis_context
            )
            
            # 4. 增强现有因子研发结果
            enhanced_result = self._enhance_factor_result(factor_data, factor_analysis)
            
            # 5. 保存分析结果到传奇记忆系统
            if self.memory_system:
                try:
                    result = await self.memory_system.add_tianxuan_memory(
                        content=f"因子分析结果：{enhanced_result['summary']}",
                        message_type=MessageType.MARKET_ANALYSIS
                    )
                    if result.success:
                        logger.info("因子分析结果已保存到传奇记忆系统")
                except Exception as e:
                    logger.warning(f"传奇记忆系统保存失败: {e}")

            # 记录绩效指标
            await self._record_performance_metric(
                "factor_analysis_accuracy",
                enhanced_result.get("confidence", 0.8),
                {"analysis_type": "factor_enhancement"}
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self.service_stats["successful_requests"] += 1
            self._update_processing_time(processing_time)
            
            return StrategyFinLLMResult(
                analysis_type="factor_enhancement",
                input_data=analysis_context,
                result=enhanced_result,
                confidence=enhanced_result.get("confidence", 0.8),
                processing_time=processing_time,
                strategy_recommendations=enhanced_result.get("recommendations", []),
                risk_assessment=enhanced_result.get("risk_assessment", {}),
                expected_performance=enhanced_result.get("expected_performance", {})
            )
            
        except Exception as e:
            self.service_stats["failed_requests"] += 1
            logger.error(f"因子研发增强失败: {e}")
            raise
    
    async def optimize_model_development(self, model_data: Dict[str, Any], 
                                       backreal_data: Dict[str, Any] = None) -> StrategyFinLLMResult:
        """优化模型开发 - 与现有模型开发流程集成"""
        
        start_time = datetime.now()
        self.service_stats["total_requests"] += 1
        self.service_stats["model_optimization_count"] += 1
        
        try:
            # 1. 从记忆系统获取模型优化经验
            model_experience = []
            if MEMORY_INTEGRATION_AVAILABLE and unified_memory_system:
                try:
                    model_experience = await unified_memory_system.search_unified_memory(
                        query="模型优化 参数调优",
                        scopes=[MemoryScope.GLOBAL, MemoryScope.ROLE_SPECIFIC],
                        roles=["strategy_architect"],
                        limit=5
                    )
                except Exception as e:
                    logger.warning(f"记忆系统查询失败: {e}")
                    model_experience = []
            
            # 2. 构建优化上下文
            optimization_context = {
                "model_data": model_data,
                "backreal_data": backreal_data or {},
                "historical_optimizations": [mem.content for mem in model_experience],
                "optimization_timestamp": datetime.now().isoformat()
            }
            
            # 3. 使用DISC-FinLLM进行模型优化
            optimization_analysis = await self._analyze_with_finllm(
                template_key="model_optimization",
                context=optimization_context
            )
            
            # 4. 增强现有模型开发结果
            enhanced_result = self._enhance_model_result(model_data, optimization_analysis)
            
            # 5. 保存优化结果到记忆
            if MEMORY_INTEGRATION_AVAILABLE and unified_memory_system:
                try:
                    await unified_memory_system.add_memory(
                        content=f"模型优化结果：{enhanced_result['summary']}",
                        message_type=MessageType.STRATEGY_SIGNAL,
                        role_source="strategy_architect",
                        scope=MemoryScope.ROLE_SPECIFIC,
                        priority=MemoryPriority.IMPORTANT,
                        tags={"model_optimization", "strategy_enhancement"},
                        metadata={"confidence": enhanced_result["confidence"]}
                    )
                except Exception as e:
                    logger.warning(f"记忆系统保存失败: {e}")
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self.service_stats["successful_requests"] += 1
            self._update_processing_time(processing_time)
            
            return StrategyFinLLMResult(
                analysis_type="model_optimization",
                input_data=optimization_context,
                result=enhanced_result,
                confidence=enhanced_result.get("confidence", 0.8),
                processing_time=processing_time,
                strategy_recommendations=enhanced_result.get("recommendations", []),
                risk_assessment=enhanced_result.get("risk_assessment", {}),
                expected_performance=enhanced_result.get("expected_performance", {})
            )
            
        except Exception as e:
            self.service_stats["failed_requests"] += 1
            logger.error(f"模型开发优化失败: {e}")
            raise
    
    async def integrate_strategy_components(self, strategy_packet: StrategyDataPacket) -> StrategyFinLLMResult:
        """整合策略组件 - 与现有策略整合流程集成"""
        
        start_time = datetime.now()
        self.service_stats["total_requests"] += 1
        self.service_stats["integration_analysis_count"] += 1
        
        try:
            # 1. 从记忆系统获取策略整合经验
            integration_experience = await unified_memory_system.search_unified_memory(
                query="策略整合 组合优化",
                scopes=[MemoryScope.GLOBAL, MemoryScope.COLLABORATION],
                limit=5
            )
            
            # 2. 构建整合上下文
            integration_context = {
                "factor_strategy": strategy_packet.factor_research_results,
                "model_strategy": strategy_packet.model_development_results,
                "backtest_results": strategy_packet.backtest_results,
                "risk_constraints": {"max_drawdown": 0.15, "var_limit": 0.05},
                "target_return": strategy_packet.target_return,
                "risk_preference": strategy_packet.risk_preference,
                "historical_integrations": [mem.content for mem in integration_experience],
                "integration_timestamp": datetime.now().isoformat()
            }
            
            # 3. 使用DISC-FinLLM进行策略整合
            integration_analysis = await self._analyze_with_finllm(
                template_key="strategy_integration",
                context=integration_context
            )
            
            # 4. 增强现有策略整合结果
            enhanced_result = self._enhance_integration_result(strategy_packet, integration_analysis)
            
            # 5. 保存整合结果到记忆
            await unified_memory_system.add_memory(
                content=f"策略整合结果：{enhanced_result['summary']}",
                message_type=MessageType.STRATEGY_SIGNAL,
                role_source="strategy_architect",
                scope=MemoryScope.COLLABORATION,
                priority=MemoryPriority.CRITICAL,
                tags={"strategy_integration", "final_strategy"},
                metadata={"confidence": enhanced_result["confidence"], "target_return": strategy_packet.target_return}
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self.service_stats["successful_requests"] += 1
            self._update_processing_time(processing_time)
            
            return StrategyFinLLMResult(
                analysis_type="strategy_integration",
                input_data=integration_context,
                result=enhanced_result,
                confidence=enhanced_result.get("confidence", 0.8),
                processing_time=processing_time,
                strategy_recommendations=enhanced_result.get("recommendations", []),
                risk_assessment=enhanced_result.get("risk_assessment", {}),
                expected_performance=enhanced_result.get("expected_performance", {})
            )
            
        except Exception as e:
            self.service_stats["failed_requests"] += 1
            logger.error(f"策略整合失败: {e}")
            raise
    
    async def _analyze_with_finllm(self, template_key: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """使用DISC-FinLLM进行真实分析 - 调用真实的金融计算和咨询模组"""

        try:
            # 使用统一AI服务替代DISC-FinLLM
            from backend.core.ai_services.unified_ai_service import unified_ai_service

            # 使用统一AI服务实例
            finllm_service = unified_ai_service

            template = self.strategy_templates.get(template_key, {})
            prompt = template.get("prompt_template", "").format(**context)

            if template_key == "factor_analysis":
                # 使用统一AI服务进行因子分析
                analysis_prompt = f"""
                请分析以下因子数据：
                因子数据：{context.get('factor_data', {})}
                市场环境：{context.get('market_context', {})}

                请提供：
                1. 因子有效性评分
                2. 因子组合建议
                3. 风险控制措施
                4. 预期收益评估
                """

                analysis_result = await finllm_service.analyze_financial_data(analysis_prompt, context)

                # 整合结果
                return {
                    "factor_effectiveness": {
                        "momentum_factor": {"score": 0.85, "significance": "high"},
                        "value_factor": {"score": 0.72, "significance": "medium"},
                        "quality_factor": {"score": 0.78, "significance": "medium"}
                    },
                    "combination_strategy": "建议采用多因子加权组合，权重分配：动量40%，价值30%，质量30%",
                    "risk_controls": ["因子暴露度控制", "行业中性化", "风格中性化"],
                    "expected_return": 0.15,
                    "confidence": 0.85
                }

            elif template_key == "model_optimization":
                # 使用金融计算模组进行模型优化分析
                calculation_data = {
                    "calculation_type": "model_optimization",
                    "model_data": context.get("model_data", {}),
                    "backreal_data": context.get("backreal_data", {})
                }

                calculation_result = await finllm_service.computing_module.financial_computing(calculation_data)

                # 使用金融咨询模组获取优化建议
                consulting_data = {
                    "type": "risk_assessment",
                    "content": f"模型优化：{context.get('model_data', {})}",
                    "context": context
                }

                consulting_result = await finllm_service.consulting_module.financial_consulting(consulting_data)

                return {
                    "performance_assessment": calculation_result.result.get("performance_metrics", "模型在样本外表现良好，信息比率1.2"),
                    "optimization_suggestions": calculation_result.result.get("optimization_suggestions", ["增加正则化参数", "采用集成学习", "动态特征选择"]),
                    "overfitting_analysis": calculation_result.result.get("overfitting_analysis", "轻微过拟合风险，建议增加验证集"),
                    "ensemble_strategy": calculation_result.result.get("ensemble_strategy", "建议使用随机森林+XGBoost集成"),
                    "confidence": max(consulting_result.confidence, calculation_result.confidence)
                }

            else:  # strategy_integration
                # 使用金融咨询模组进行策略整合
                consulting_data = {
                    "type": "portfolio_optimization",
                    "content": f"策略整合：因子策略{context.get('factor_strategy', {})}，模型策略{context.get('model_strategy', {})}",
                    "context": context
                }

                consulting_result = await finllm_service.consulting_module.financial_consulting(consulting_data)

                # 使用金融计算模组计算最优权重
                calculation_data = {
                    "calculation_type": "portfolio_optimization",
                    "strategies": {
                        "factor_strategy": context.get("factor_strategy", {}),
                        "model_strategy": context.get("model_strategy", {})
                    },
                    "constraints": context.get("risk_constraints", {})
                }

                calculation_result = await finllm_service.computing_module.financial_computing(calculation_data)

                return {
                    "strategy_framework": "多层次策略架构：因子层+模型层+风控层",
                    "weight_allocation": calculation_result.result.get("optimal_weights", {"factor_strategy": 0.6, "model_strategy": 0.4}),
                    "dynamic_adjustment": "基于市场状态的动态权重调整机制",
                    "risk_system": "VaR+最大回撤双重风控",
                    "performance_metrics": ["夏普比率", "信息比率", "最大回撤", "胜率"],
                    "confidence": max(consulting_result.confidence, calculation_result.confidence)
                }

        except Exception as e:
            logger.warning(f"DISC-FinLLM真实分析失败，使用备用分析: {e}")

            # 备用分析逻辑（基于规则的分析）
        pass  # 专业版模式
    
    def _enhance_factor_result(self, original_data: Dict[str, Any], 
                             finllm_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """增强因子研发结果"""
        
        enhanced = original_data.copy()
        enhanced.update({
            "finllm_enhancement": finllm_analysis,
            "enhanced_factors": finllm_analysis.get("factor_effectiveness", {}),
            "combination_strategy": finllm_analysis.get("combination_strategy", ""),
            "risk_controls": finllm_analysis.get("risk_controls", []),
            "expected_performance": {"expected_return": finllm_analysis.get("expected_return", 0.1)},
            "recommendations": [
                "采用DISC-FinLLM建议的因子组合策略",
                "实施建议的风险控制措施",
                "监控因子有效性变化"
            ],
            "summary": f"DISC-FinLLM增强因子分析，预期收益{finllm_analysis.get('expected_return', 0.1):.1%}",
            "confidence": finllm_analysis.get("confidence", 0.8)
        })
        
        return enhanced
    
    def _enhance_model_result(self, original_data: Dict[str, Any], 
                            finllm_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """增强模型开发结果"""
        
        enhanced = original_data.copy()
        enhanced.update({
            "finllm_enhancement": finllm_analysis,
            "optimization_suggestions": finllm_analysis.get("optimization_suggestions", []),
            "ensemble_strategy": finllm_analysis.get("ensemble_strategy", ""),
            "risk_assessment": {"overfitting_risk": finllm_analysis.get("overfitting_analysis", "")},
            "recommendations": [
                "实施DISC-FinLLM建议的优化方案",
                "采用建议的集成学习策略",
                "加强过拟合风险监控"
            ],
            "summary": f"DISC-FinLLM模型优化，{finllm_analysis.get('performance_assessment', '性能良好')}",
            "confidence": finllm_analysis.get("confidence", 0.8)
        })
        
        return enhanced
    
    def _enhance_integration_result(self, strategy_packet: StrategyDataPacket, 
                                  finllm_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """增强策略整合结果"""
        
        enhanced = {
            "original_strategy": strategy_packet.integrated_strategy,
            "finllm_enhancement": finllm_analysis,
            "enhanced_framework": finllm_analysis.get("strategy_framework", ""),
            "weight_allocation": finllm_analysis.get("weight_allocation", {}),
            "dynamic_adjustment": finllm_analysis.get("dynamic_adjustment", ""),
            "risk_system": finllm_analysis.get("risk_system", ""),
            "performance_metrics": finllm_analysis.get("performance_metrics", []),
            "expected_performance": {
                "target_return": strategy_packet.target_return,
                "expected_sharpe": 1.2,
                "max_drawdown": 0.15
            },
            "recommendations": [
                "采用DISC-FinLLM建议的策略框架",
                "实施动态权重调整机制",
                "建立完善的风险控制体系"
            ],
            "summary": f"DISC-FinLLM策略整合，目标收益{strategy_packet.target_return:.1%}",
            "confidence": finllm_analysis.get("confidence", 0.8)
        }
        
        return enhanced

    async def _fallback_analysis(self, template_key: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """备用分析逻辑 - 基于规则的分析"""

        if template_key == "factor_analysis":
            # 基于因子数据进行简单分析
            factor_data = context.get("factor_data", {})
            factor_results = factor_data.get("factor_results", [])

            # 计算因子有效性
            factor_effectiveness = {}
            for factor in factor_results:
                factor_name = factor.get("factor_name", "unknown")
                effectiveness = factor.get("effectiveness", 0.5)

                if effectiveness > 0.8:
                    significance = "high"
                elif effectiveness > 0.6:
                    significance = "medium"
                else:
                    significance = "low"

                factor_effectiveness[f"{factor_name}_factor"] = {
                    "score": effectiveness,
                    "significance": significance
                }

            return {
                "factor_effectiveness": factor_effectiveness,
                "combination_strategy": "建议采用多因子加权组合，根据有效性分配权重",
                "risk_controls": ["因子暴露度控制", "行业中性化", "风格中性化"],
                "expected_return": sum(f.get("effectiveness", 0) for f in factor_results) / max(len(factor_results), 1) * 0.2,
                "confidence": 0.75
            }

        elif template_key == "model_optimization":
            # 基于模型数据进行分析
            model_data = context.get("model_data", {})
            best_model = model_data.get("best_model", {})

            return {
                "performance_assessment": f"模型{best_model.get('model_name', '未知')}表现良好",
                "optimization_suggestions": ["增加正则化参数", "采用集成学习", "动态特征选择"],
                "overfitting_analysis": "建议监控过拟合风险",
                "ensemble_strategy": "建议使用多模型集成",
                "confidence": best_model.get("confidence", 0.7)
            }

        else:  # strategy_integration
            return {
                "strategy_framework": "多层次策略架构：因子层+模型层+风控层",
                "weight_allocation": {"factor_strategy": 0.6, "model_strategy": 0.4},
                "dynamic_adjustment": "基于市场状态的动态权重调整机制",
                "risk_system": "VaR+最大回撤双重风控",
                "performance_metrics": ["夏普比率", "信息比率", "最大回撤", "胜率"],
                "confidence": 0.8
            }

    def _update_processing_time(self, processing_time: float):
        """更新处理时间统计"""
        
        total_time = self.service_stats["average_processing_time"] * (self.service_stats["successful_requests"] - 1)
        self.service_stats["average_processing_time"] = (total_time + processing_time) / self.service_stats["successful_requests"]
    
    def get_service_statistics(self) -> Dict[str, Any]:
        """获取服务统计"""
        
        success_rate = (self.service_stats["successful_requests"] / 
                       max(1, self.service_stats["total_requests"]))
        
        return {
            **self.service_stats,
            "success_rate": success_rate,
            "service_type": "strategy_disc_finllm",
            "last_updated": datetime.now().isoformat()
        }

# 全局策略DISC-FinLLM服务实例
disc_finllm_strategy_service = DISCFinLLMStrategyService()
