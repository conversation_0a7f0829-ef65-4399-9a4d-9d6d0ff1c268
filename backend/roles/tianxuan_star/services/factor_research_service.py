#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天璇星因子研究服务 - 专业完整版本
基于RD-Agent的智能因子挖掘和研究系统
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)

@dataclass
class FactorResearchResult:
    """因子研究结果"""
    factor_name: str
    factor_type: str
    ic_value: float
    rank_ic: float
    ir_ratio: float
    turnover: float
    max_drawdown: float
    sharpe_ratio: float
    annual_return: float
    factor_description: str
    research_timestamp: datetime
    data_quality_score: float

class FactorResearchService:
    """天璇星专业因子研究服务"""

    def __init__(self):
        self.service_name = "FactorResearchService"
        self.version = "2.0.0"
        self.initialized = False

        # 因子库
        self.factor_library = {}
        self.research_history = []

        # 研究配置
        self.research_config = {
            "lookback_period": 252,  # 回看期
            "rebalance_frequency": 20,  # 调仓频率
            "factor_categories": ["技术", "基本面", "量价", "情绪", "宏观"],
            "min_ic_threshold": 0.02,  # 最小IC阈值
            "min_ir_threshold": 0.5,   # 最小IR阈值
        }

        logger.info(f"🔬 天璇星因子研究服务 v{self.version} 初始化完成")

    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            # 初始化RD-Agent连接
            await self._initialize_rd_agent()

            # 加载历史因子库
            await self._load_factor_library()

            # 初始化数据源连接
            await self._initialize_data_sources()

            self.initialized = True
            logger.info(" 天璇星因子研究服务初始化完成")
            return True

        except Exception as e:
            logger.error(f" 天璇星因子研究服务初始化失败: {e}")
            return False

    async def research_new_factor(self, factor_definition: Dict[str, Any]) -> FactorResearchResult:
        """研究新因子"""
        try:
            factor_name = factor_definition.get("name", f"factor_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            factor_type = factor_definition.get("type", "技术")

            logger.info(f"🔬 开始研究新因子: {factor_name} ({factor_type})")

            # 1. 获取历史数据
            historical_data = await self._get_historical_data(factor_definition)

            # 2. 计算因子值
            factor_values = await self._calculate_factor_values(factor_definition, historical_data)

            # 3. 因子有效性检验
            effectiveness_metrics = await self._test_factor_effectiveness(factor_values, historical_data)

            # 4. 回测分析
            backtest_results = await self._run_factor_backtest(factor_values, historical_data)

            # 5. 创建研究结果
            research_result = FactorResearchResult(
                factor_name=factor_name,
                factor_type=factor_type,
                ic_value=effectiveness_metrics.get("ic", 0.0),
                rank_ic=effectiveness_metrics.get("rank_ic", 0.0),
                ir_ratio=effectiveness_metrics.get("ir", 0.0),
                turnover=backtest_results.get("turnover", 0.0),
                max_drawdown=backtest_results.get("max_drawdown", 0.0),
                sharpe_ratio=backtest_results.get("sharpe_ratio", 0.0),
                annual_return=backtest_results.get("annual_return", 0.0),
                factor_description=factor_definition.get("description", ""),
                research_timestamp=datetime.now(),
                data_quality_score=effectiveness_metrics.get("data_quality", 0.0)
            )

            # 6. 保存到因子库
            await self._save_factor_to_library(research_result, factor_definition)

            logger.info(f" 因子研究完成: {factor_name}, IC={research_result.ic_value:.4f}")
            return research_result

        except Exception as e:
            logger.error(f" 因子研究失败: {e}")
            raise Exception(f"因子研究服务异常: {e}")

    async def _initialize_rd_agent(self):
        """初始化RD-Agent连接"""
        try:
            # 连接RD-Agent服务
            from backend.roles.intelligence_officer.services.rd_agent_service import rd_agent_service

            # 验证RD-Agent可用性
            rd_status = await rd_agent_service.get_service_status()
            if rd_status.get("status") == "active":
                logger.info(" RD-Agent连接成功")
            else:
                logger.warning("⚠️ RD-Agent服务不可用，使用本地因子计算")

        except Exception as e:
            logger.warning(f"⚠️ RD-Agent初始化失败: {e}")

    async def _load_factor_library(self):
        """加载历史因子库"""
        try:
            # 从数据库或文件加载历史因子
            # 这里可以实现具体的加载逻辑
            self.factor_library = {
                "momentum_20": {"type": "技术", "formula": "close.pct_change(20)", "ic": 0.045},
                "rsi_14": {"type": "技术", "formula": "ta.RSI(close, 14)", "ic": 0.032},
                "pe_ratio": {"type": "基本面", "formula": "price / eps", "ic": -0.028},
                "volume_ratio": {"type": "量价", "formula": "volume / volume.rolling(20).mean()", "ic": 0.021}
            }

            logger.info(f"📚 加载因子库: {len(self.factor_library)}个历史因子")

        except Exception as e:
            logger.warning(f"⚠️ 因子库加载失败: {e}")
            self.factor_library = {}

    async def _initialize_data_sources(self):
        """初始化数据源连接"""
        try:
            # 连接天枢星数据API
            from backend.roles.tianshu_star.services.stock_data_api import stock_data_api

            # 测试数据连接
            test_request = {
                "requesting_role": "tianxuan",
                "stock_codes": ["000001"],
                "start_date": (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d'),
                "end_date": datetime.now().strftime('%Y-%m-%d')
            }

            # 这里可以添加实际的测试逻辑
            logger.info(" 天枢星数据源连接成功")

        except Exception as e:
            logger.warning(f"⚠️ 数据源初始化失败: {e}")

    async def _get_historical_data(self, factor_definition: Dict[str, Any]) -> pd.DataFrame:
        """获取历史数据"""
        try:
            # 使用瑶光星的历史数据管理器获取真实历史数据
            from backend.roles.yaoguang_star.core.historical_data_manager import HistoricalDataManager

            # 构建数据请求
            stock_codes = factor_definition.get("stock_codes", ["000001", "000002", "600000"])
            lookback_days = factor_definition.get("lookback_days", self.research_config["lookback_period"])

            # 创建历史数据管理器
            data_manager = HistoricalDataManager()

            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=lookback_days)

            # 获取所有股票的历史数据
            all_data = []
            for stock_code in stock_codes:
                try:
                    # 清理股票代码格式
                    clean_code = stock_code.split('.')[0] if '.' in stock_code else stock_code

                    # 获取历史数据
                    stock_data = await data_manager.get_historical_data(
                        stock_code=clean_code,
                        data_type="daily",
                        start_date=start_date.strftime('%Y-%m-%d'),
                        end_date=end_date.strftime('%Y-%m-%d')
                    )

                    if not stock_data.empty:
                        # 重命名列以匹配期望格式
                        stock_data = stock_data.reset_index()
                        stock_data['stock_code'] = clean_code
                        stock_data['trade_date'] = stock_data['date'].dt.strftime('%Y-%m-%d')
                        stock_data['open_price'] = stock_data['open']
                        stock_data['high_price'] = stock_data['high']
                        stock_data['low_price'] = stock_data['low']
                        stock_data['close_price'] = stock_data['close']

                        # 确保volume列存在
                        if 'volume' not in stock_data.columns:
                            stock_data['volume'] = 1000000  # 默认成交量

                        # 选择需要的列
                        required_columns = ['stock_code', 'trade_date', 'open_price', 'high_price', 'low_price', 'close_price', 'volume']
                        available_columns = [col for col in required_columns if col in stock_data.columns]
                        stock_data = stock_data[available_columns]

                        all_data.append(stock_data)
                        logger.info(f"获取历史数据成功: {clean_code} ({len(stock_data)}条记录)")
                    else:
                        logger.warning(f"股票 {clean_code} 无历史数据")

                except Exception as e:
                    logger.warning(f"获取股票 {stock_code} 历史数据失败: {e}")
                    continue

            if all_data:
                # 合并所有数据
                combined_data = pd.concat(all_data, ignore_index=True)
                logger.info(f"历史数据获取完成: {len(combined_data)}条记录")
                return combined_data
            else:
                logger.error("所有股票历史数据获取失败，无法进行因子研发")
                raise ValueError("因子研发需要真实历史数据")

        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            raise ValueError("因子研发需要真实历史数据")

    async def _get_real_historical_data(self, stock_codes: List[str], days: int) -> pd.DataFrame:
        """获取真实历史数据"""
        try:
            # 使用瑶光星历史数据管理器
            from roles.yaoguang_star.services.historical_data_manager import historical_data_manager

            all_data = []
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            for stock_code in stock_codes:
                try:
                    stock_data = await historical_data_manager.get_stock_data(
                        stock_code=stock_code,
                        start_date=start_date.strftime('%Y-%m-%d'),
                        end_date=end_date.strftime('%Y-%m-%d')
                    )

                    if stock_data and len(stock_data) > 0:
                        # 转换为标准格式
                        for record in stock_data:
                            all_data.append({
                                "stock_code": stock_code,
                                "trade_date": record.get("date", ""),
                                "open_price": record.get("open", 0),
                                "high_price": record.get("high", 0),
                                "low_price": record.get("low", 0),
                                "close_price": record.get("close", 0),
                                "volume": record.get("volume", 0),
                                "change_percent": record.get("change_pct", 0)
                            })
                except Exception as e:
                    logger.warning(f"获取{stock_code}历史数据失败: {e}")

            if not all_data:
                raise ValueError("无法获取任何真实历史数据")

            return pd.DataFrame(all_data)

        except Exception as e:
            logger.error(f"获取真实历史数据失败: {e}")
            raise ValueError("因子研发需要真实历史数据")

    async def _calculate_factor_values(self, factor_definition: Dict[str, Any], data: pd.DataFrame) -> pd.DataFrame:
        """计算因子值"""
        try:
            formula = factor_definition.get("formula", "close_price")
            factor_type = factor_definition.get("type", "技术")

            logger.info(f"🧮 计算因子值: {formula}")

            # 按股票分组计算
            factor_values = []

            for stock_code in data['stock_code'].unique():
                stock_data = data[data['stock_code'] == stock_code].copy()
                stock_data = stock_data.sort_values('trade_date')

                # 根据因子类型计算
                if factor_type == "技术":
                    factor_value = self._calculate_technical_factor(stock_data, formula)
                elif factor_type == "基本面":
                    factor_value = self._calculate_fundamental_factor(stock_data, formula)
                elif factor_type == "量价":
                    factor_value = self._calculate_volume_price_factor(stock_data, formula)
                else:
                    factor_value = stock_data['close_price'].pct_change(20)  # 默认动量因子

                # 添加到结果
                for i, value in enumerate(factor_value):
                    if not np.isnan(value):
                        factor_values.append({
                            "stock_code": stock_code,
                            "trade_date": stock_data.iloc[i]['trade_date'],
                            "factor_value": value,
                            "close_price": stock_data.iloc[i]['close_price']
                        })

            return pd.DataFrame(factor_values)

        except Exception as e:
            logger.error(f" 计算因子值失败: {e}")
            return pd.DataFrame()

    def _calculate_technical_factor(self, data: pd.DataFrame, formula: str) -> pd.Series:
        """计算技术因子"""
        try:
            if "momentum" in formula:
                period = int(formula.split("_")[1]) if "_" in formula else 20
                return data['close_price'].pct_change(period)
            elif "rsi" in formula:
                delta = data['close_price'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                return 100 - (100 / (1 + rs))
            else:
                # 默认返回价格变化率
                return data['close_price'].pct_change()

        except Exception as e:
            logger.warning(f"⚠️ 技术因子计算失败: {e}")
            return pd.Series([0] * len(data))

    def _calculate_fundamental_factor(self, data: pd.DataFrame, formula: str) -> pd.Series:
        """计算基本面因子"""
        try:
            # 这里可以添加基本面因子的计算逻辑
            # 暂时返回简单的价格相关指标
            if "pe" in formula:
                return data['close_price'] / 1.0  # 假设EPS为1
            else:
                return data['close_price'].rolling(20).mean() / data['close_price']

        except Exception as e:
            logger.warning(f"⚠️ 基本面因子计算失败: {e}")
            return pd.Series([1] * len(data))

    def _calculate_volume_price_factor(self, data: pd.DataFrame, formula: str) -> pd.Series:
        """计算量价因子"""
        try:
            if "volume_ratio" in formula:
                return data['volume'] / data['volume'].rolling(20).mean()
            elif "vwap" in formula:
                # 成交量加权平均价格
                return (data['close_price'] * data['volume']).rolling(20).sum() / data['volume'].rolling(20).sum()
            else:
                return data['volume'].pct_change()

        except Exception as e:
            logger.warning(f"⚠️ 量价因子计算失败: {e}")
            return pd.Series([1] * len(data))

    async def _test_factor_effectiveness(self, factor_values: pd.DataFrame, historical_data: pd.DataFrame) -> Dict[str, float]:
        """测试因子有效性"""
        try:
            if factor_values.empty:
                return {"ic": 0.0, "rank_ic": 0.0, "ir": 0.0, "data_quality": 0.0}

            # 计算未来收益率
            merged_data = factor_values.merge(historical_data, on=['stock_code', 'trade_date'])
            merged_data = merged_data.sort_values(['stock_code', 'trade_date'])

            # 计算下期收益率
            merged_data['future_return'] = merged_data.groupby('stock_code')['close_price'].pct_change().shift(-1)

            # 去除缺失值
            valid_data = merged_data.dropna()

            if len(valid_data) < 10:
                return {"ic": 0.0, "rank_ic": 0.0, "ir": 0.0, "data_quality": 0.0}

            # 计算IC (Information Coefficient)
            ic = valid_data['factor_value'].corr(valid_data['future_return'])

            # 计算Rank IC
            rank_ic = valid_data['factor_value'].rank().corr(valid_data['future_return'].rank())

            # 计算IR (Information Ratio)
            ic_series = valid_data.groupby('trade_date').apply(
                lambda x: x['factor_value'].corr(x['future_return']) if len(x) > 1 else 0
            )
            ir = ic_series.mean() / ic_series.std() if ic_series.std() > 0 else 0

            # 数据质量评分
            data_quality = min(1.0, len(valid_data) / 1000)

            return {
                "ic": ic if not np.isnan(ic) else 0.0,
                "rank_ic": rank_ic if not np.isnan(rank_ic) else 0.0,
                "ir": ir if not np.isnan(ir) else 0.0,
                "data_quality": data_quality
            }

        except Exception as e:
            logger.error(f" 因子有效性测试失败: {e}")
            return {"ic": 0.0, "rank_ic": 0.0, "ir": 0.0, "data_quality": 0.0}

    async def _run_factor_backtest(self, factor_values: pd.DataFrame, historical_data: pd.DataFrame) -> Dict[str, float]:
        """运行因子回测"""
        try:
            if factor_values.empty:
                return {"turnover": 0.0, "max_drawdown": 0.0, "sharpe_ratio": 0.0, "annual_return": 0.0}

            merged_data = factor_values.merge(historical_data, on=['stock_code', 'trade_date'])
            merged_data = merged_data.sort_values('trade_date')

            # 按因子值排序，构建投资组合
            portfolio_returns = []

            for date in merged_data['trade_date'].unique():
                date_data = merged_data[merged_data['trade_date'] == date]
                if len(date_data) > 0:
                    # 选择因子值最高的前20%股票
                    top_stocks = date_data.nlargest(max(1, len(date_data) // 5), 'factor_value')
                    avg_return = top_stocks['close_price'].pct_change().mean()
                    portfolio_returns.append(avg_return if not np.isnan(avg_return) else 0)

            if not portfolio_returns:
                return {"turnover": 0.0, "max_drawdown": 0.0, "sharpe_ratio": 0.0, "annual_return": 0.0}

            returns_series = pd.Series(portfolio_returns)

            # 计算回测指标
            annual_return = returns_series.mean() * 252
            volatility = returns_series.std() * np.sqrt(252)
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0

            # 计算最大回撤
            cumulative_returns = (1 + returns_series).cumprod()
            running_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = abs(drawdown.min())

            turnover = 0.5  # 假设换手率

            return {
                "turnover": turnover,
                "max_drawdown": max_drawdown,
                "sharpe_ratio": sharpe_ratio,
                "annual_return": annual_return
            }

        except Exception as e:
            logger.error(f" 因子回测失败: {e}")
            return {"turnover": 0.0, "max_drawdown": 0.0, "sharpe_ratio": 0.0, "annual_return": 0.0}

    async def _save_factor_to_library(self, research_result: FactorResearchResult, factor_definition: Dict[str, Any]):
        """保存因子到因子库"""
        try:
            factor_info = {
                "type": research_result.factor_type,
                "formula": factor_definition.get("formula", ""),
                "ic": research_result.ic_value,
                "rank_ic": research_result.rank_ic,
                "ir": research_result.ir_ratio,
                "sharpe_ratio": research_result.sharpe_ratio,
                "annual_return": research_result.annual_return,
                "research_date": research_result.research_timestamp.isoformat(),
                "description": research_result.factor_description
            }

            self.factor_library[research_result.factor_name] = factor_info
            self.research_history.append(research_result)

            logger.info(f"💾 因子已保存到库: {research_result.factor_name}")

        except Exception as e:
            logger.error(f" 保存因子失败: {e}")

    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "service_name": self.service_name,
            "version": self.version,
            "initialized": self.initialized,
            "factor_count": len(self.factor_library),
            "research_count": len(self.research_history),
            "timestamp": datetime.now().isoformat()
        }

    async def list_factors(self) -> Dict[str, Any]:
        """列出所有因子"""
        return {
            "factor_library": self.factor_library,
            "total_count": len(self.factor_library),
            "categories": list(set(f["type"] for f in self.factor_library.values())),
            "timestamp": datetime.now().isoformat()
        }

# 创建全局实例
factor_research_service = FactorResearchService()

logger.info("🔬 天璇星因子研究服务模块加载完成（专业版本）")
