#!/usr/bin/env python3
"""
架构师学习增强服务
基于AkShare历史数据进行深度学习和策略优化
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from pathlib import Path
import sqlite3

from services.akshare_learning_service import AkShareLearningService
from backend.shared.data_sources.real_market_data_service import RealMarketDataService

logger = logging.getLogger(__name__)

class LearningEnhancedArchitect:
    """学习增强的架构师服务"""
    
    def __init__(self):
        """初始化学习增强架构师"""
        self.service_name = "LearningEnhancedArchitect"
        self.version = "1.0.0"
        
        # 初始化学习数据服务
        self.learning_service = AkShareLearningService()
        
        # 初始化实时数据服务（生产模式）
        self.realtime_service = RealMarketDataService(mode="production")
        
        # 学习配置
        self.learning_config = {
            "min_learning_samples": 100,  # 最少学习样本数
            "factor_learning_window": 250,  # 因子学习窗口（250个交易日）
            "strategy_validation_days": 60,  # 策略验证天数
            "confidence_threshold": 0.7,  # 置信度阈值
        }
        
        # 学习状态
        self.learning_stats = {
            "factors_learned": 0,
            "strategies_validated": 0,
            "learning_accuracy": 0.0,
            "last_learning_time": None
        }
        
        logger.info(f"{self.service_name} v{self.version} 初始化完成")
    
    async def enhanced_factor_research(self, symbols: List[str], factor_types: List[str]) -> Dict[str, Any]:
        """增强的因子研究 - 结合历史学习和实时数据"""
        research_id = f"enhanced_research_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger.info(f"  开始增强因子研究: {research_id}")
        logger.info(f"研究标的: {symbols}")
        logger.info(f"因子类型: {factor_types}")
        
        # 第一步：数据验证（使用实时数据）
        validated_symbols = await self._validate_symbols_with_realtime_data(symbols)
        
        if not validated_symbols:
            raise ValueError("所有股票代码都未通过实时数据验证")
        
        # 第二步：历史数据学习
        learning_results = await self._learn_from_historical_data(validated_symbols, factor_types)
        
        # 第三步：实时数据验证
        realtime_validation = await self._validate_with_realtime_data(validated_symbols, learning_results)
        
        # 第四步：综合分析
        final_results = self._combine_learning_and_realtime_results(learning_results, realtime_validation)
        
        # 更新学习统计
        self._update_learning_stats(final_results)
        
        return {
            "research_id": research_id,
            "research_type": "enhanced_learning",
            "symbols_analyzed": validated_symbols,
            "factor_types": factor_types,
            "learning_results": learning_results,
            "realtime_validation": realtime_validation,
            "final_recommendations": final_results,
            "learning_stats": self.learning_stats,
            "data_sources": {
                "historical": "AkShare完整数据",
                "realtime": "新浪/腾讯快速数据"
            },
            "timestamp": datetime.now().isoformat()
        }
    
    async def _validate_symbols_with_realtime_data(self, symbols: List[str]) -> List[str]:
        """使用实时数据验证股票代码"""
        validated_symbols = []
        
        for symbol in symbols:
            try:
                # 获取实时价格
                current_price = await self.realtime_service.get_current_price(symbol)
                
                # 验证价格合理性
                if self._is_price_reasonable(current_price, symbol):
                    validated_symbols.append(symbol)
                    logger.info(f"  实时数据验证通过: {symbol} 价格 {current_price}")
                else:
                    logger.warning(f"  实时数据验证失败: {symbol} 价格异常 {current_price}")
                    
            except Exception as e:
                logger.error(f"  实时数据验证失败: {symbol} - {e}")
        
        return validated_symbols
    
    async def _learn_from_historical_data(self, symbols: List[str], factor_types: List[str]) -> Dict[str, Any]:
        """从历史数据中学习"""
        logger.info("📚 开始历史数据学习")
        
        learning_results = {}
        
        for symbol in symbols:
            logger.info(f"学习股票: {symbol}")
            
            # 获取历史学习数据
            historical_data = self.learning_service.get_learning_data_for_architect(
                symbol, self.learning_config['factor_learning_window']
            )
            
            if historical_data.empty or len(historical_data) < self.learning_config['min_learning_samples']:
                logger.warning(f"  {symbol} 历史数据不足，跳过学习")
                continue
            
            # 计算历史因子
            symbol_factors = self._calculate_historical_factors(historical_data, factor_types)
            
            # 因子有效性验证
            factor_validity = self._validate_factor_effectiveness(historical_data, symbol_factors)
            
            learning_results[symbol] = {
                "historical_factors": symbol_factors,
                "factor_validity": factor_validity,
                "data_quality": self._assess_data_quality(historical_data),
                "learning_confidence": self._calculate_learning_confidence(factor_validity)
            }
            
            logger.info(f"    {symbol} 学习完成，置信度: {learning_results[symbol]['learning_confidence']:.2f}")
        
        return learning_results
    
    def _calculate_historical_factors(self, data: pd.DataFrame, factor_types: List[str]) -> Dict[str, float]:
        """计算历史因子 - 真实量化因子实现"""
        factors = {}

        # 确保数据列名正确
        if 'close_price' in data.columns:
            prices = data['close_price']
            volumes = data.get('volume', pd.Series())
            highs = data.get('high_price', prices)
            lows = data.get('low_price', prices)
            opens = data.get('open_price', prices)
        elif 'close' in data.columns:
            prices = data['close']
            volumes = data.get('volume', pd.Series())
            highs = data.get('high', prices)
            lows = data.get('low', prices)
            opens = data.get('open', prices)
        else:
            logger.error("无法找到收盘价列")
            return factors

        # 计算收益率序列
        returns = prices.pct_change().dropna()

        for factor_type in factor_types:
            if factor_type == "momentum":
                # 多期动量因子
                if len(prices) >= 250:
                    # 200日动量因子（经典长期动量）
                    momentum_200d = (prices.iloc[-1] / prices.iloc[-200] - 1) * 100
                    factors["momentum_200d"] = momentum_200d

                if len(prices) >= 60:
                    # 60日动量因子（中期动量）
                    momentum_60d = (prices.iloc[-1] / prices.iloc[-60] - 1) * 100
                    factors["momentum_60d"] = momentum_60d

                if len(prices) >= 20:
                    # 20日动量因子（短期动量）
                    momentum_20d = (prices.iloc[-1] / prices.iloc[-20] - 1) * 100
                    factors["momentum_20d"] = momentum_20d

                if len(prices) >= 5:
                    # 5日动量因子（超短期动量）
                    momentum_5d = (prices.iloc[-1] / prices.iloc[-5] - 1) * 100
                    factors["momentum_5d"] = momentum_5d

                # 动量强度因子（基于收益率标准差调整）
                if len(returns) >= 20:
                    recent_returns = returns.tail(20)
                    momentum_strength = recent_returns.mean() / recent_returns.std() if recent_returns.std() > 0 else 0
                    factors["momentum_strength"] = momentum_strength * 100

            elif factor_type == "reversal":
                # 多期反转因子
                if len(prices) >= 5:
                    # 5日反转因子
                    recent_return = (prices.iloc[-1] / prices.iloc[-5] - 1) * 100
                    factors["reversal_5d"] = -recent_return  # 反转因子取负值

                if len(prices) >= 20:
                    # 20日反转因子
                    medium_return = (prices.iloc[-1] / prices.iloc[-20] - 1) * 100
                    factors["reversal_20d"] = -medium_return

                # RSI反转因子
                if len(prices) >= 14:
                    rsi = self._calculate_rsi(prices, 14)
                    if not pd.isna(rsi):
                        # RSI超买超卖信号
                        if rsi > 70:
                            factors["rsi_reversal"] = -(rsi - 70) * 2  # 超买，反转信号
                        elif rsi < 30:
                            factors["rsi_reversal"] = (30 - rsi) * 2   # 超卖，反转信号
                        else:
                            factors["rsi_reversal"] = 0

            elif factor_type == "volatility":
                # 多期波动率因子
                if len(returns) >= 20:
                    # 20日波动率
                    vol_20d = returns.tail(20).std() * np.sqrt(252) * 100
                    factors["volatility_20d"] = vol_20d

                if len(returns) >= 60:
                    # 60日波动率
                    vol_60d = returns.tail(60).std() * np.sqrt(252) * 100
                    factors["volatility_60d"] = vol_60d

                # 波动率比率因子（短期vs长期波动率）
                if len(returns) >= 60:
                    short_vol = returns.tail(20).std()
                    long_vol = returns.tail(60).std()
                    vol_ratio = short_vol / long_vol if long_vol > 0 else 1.0
                    factors["volatility_ratio"] = vol_ratio

                if len(returns) >= 30:
                    garch_vol = self._calculate_garch_volatility(returns.tail(30))
                    factors["garch_volatility"] = garch_vol * 100

            elif factor_type == "value":
                # 价格相对价值因子
                if len(prices) >= 250:
                    # 相对于250日均价的价值因子
                    ma_250 = prices.tail(250).mean()
                    value_250d = (ma_250 / prices.iloc[-1] - 1) * 100
                    factors["value_250d"] = value_250d

                if len(prices) >= 60:
                    # 相对于60日均价的价值因子
                    ma_60 = prices.tail(60).mean()
                    value_60d = (ma_60 / prices.iloc[-1] - 1) * 100
                    factors["value_60d"] = value_60d

                # 价格通道因子
                if len(highs) >= 20 and len(lows) >= 20:
                    highest_20 = highs.tail(20).max()
                    lowest_20 = lows.tail(20).min()
                    current_price = prices.iloc[-1]
                    if highest_20 > lowest_20:
                        price_position = (current_price - lowest_20) / (highest_20 - lowest_20)
                        factors["price_channel_position"] = (0.5 - price_position) * 100  # 价值因子：越低越有价值

            elif factor_type == "technical":
                # 技术指标因子
                if len(prices) >= 26:
                    # MACD因子
                    macd_line, signal_line = self._calculate_macd(prices)
                    if not pd.isna(macd_line) and not pd.isna(signal_line):
                        macd_diff = macd_line - signal_line
                        factors["macd_signal"] = macd_diff * 100

                # 布林带因子
                if len(prices) >= 20:
                    bb_position = self._calculate_bollinger_position(prices, 20)
                    if not pd.isna(bb_position):
                        factors["bollinger_position"] = bb_position * 100

                # 威廉指标
                if len(highs) >= 14 and len(lows) >= 14:
                    williams_r = self._calculate_williams_r(highs, lows, prices, 14)
                    if not pd.isna(williams_r):
                        factors["williams_r"] = williams_r

        return factors
    
    def _validate_factor_effectiveness(self, data: pd.DataFrame, factors: Dict[str, float]) -> Dict[str, float]:
        """验证因子有效性"""
        validity_scores = {}
        
        if 'close_price' in data.columns:
            prices = data['close_price']
        elif 'close' in data.columns:
            prices = data['close']
        else:
            return validity_scores
        
        # 计算未来收益率（用于验证因子预测能力）
        if len(prices) >= 30:
            returns = prices.pct_change().dropna()
            
            for factor_name, factor_value in factors.items():
                if "momentum" in factor_name:
                    # 动量因子：正值表示上涨趋势
                    validity = min(1.0, abs(factor_value) / 10.0)  # 10%为满分
                elif "reversal" in factor_name:
                    # 反转因子：适中的反转信号更有效
                    validity = max(0.0, 1.0 - abs(factor_value) / 20.0)
                elif "volatility" in factor_name:
                    # 波动率因子：适中波动率更好
                    optimal_vol = 25.0  # 25%年化波动率为最优
                    validity = max(0.0, 1.0 - abs(factor_value - optimal_vol) / 50.0)
                else:
                    validity = 0.5  # 默认中等有效性
                
                validity_scores[factor_name] = max(0.0, min(1.0, validity))
        
        return validity_scores
    
    async def _validate_with_realtime_data(self, symbols: List[str], learning_results: Dict[str, Any]) -> Dict[str, Any]:
        """使用实时数据验证学习结果"""
        logger.info("🔍 实时数据验证")
        
        validation_results = {}
        
        for symbol in symbols:
            if symbol not in learning_results:
                continue
            
            try:
                # 获取当前实时价格
                current_price = await self.realtime_service.get_current_price(symbol)
                
                # 获取股票基本信息
                stock_info = await self.realtime_service.get_stock_info(symbol)
                
                # 验证学习结果与实时数据的一致性
                consistency_score = self._check_learning_consistency(
                    learning_results[symbol], current_price, stock_info
                )
                
                validation_results[symbol] = {
                    "current_price": current_price,
                    "stock_info": stock_info.name if stock_info else "未知",
                    "consistency_score": consistency_score,
                    "validation_status": "通过" if consistency_score > 0.6 else "警告"
                }
                
                logger.info(f"  {symbol} 验证完成，一致性: {consistency_score:.2f}")
                
            except Exception as e:
                logger.error(f"  {symbol} 实时验证失败: {e}")
                validation_results[symbol] = {
                    "validation_status": "失败",
                    "error": str(e)
                }
        
        return validation_results
    
    def _check_learning_consistency(self, learning_result: Dict[str, Any], current_price: float, stock_info) -> float:
        """检查学习结果与实时数据的一致性"""
        consistency_score = 0.0
        
        # 检查数据质量
        data_quality = learning_result.get('data_quality', 0.0)
        consistency_score += data_quality * 0.3
        
        # 检查学习置信度
        learning_confidence = learning_result.get('learning_confidence', 0.0)
        consistency_score += learning_confidence * 0.4
        
        # 检查价格合理性
        if self._is_price_reasonable(current_price, ""):
            consistency_score += 0.3
        
        return min(1.0, consistency_score)
    
    def _validate_symbol_format(self, symbol: str) -> bool:
        """验证股票代码格式"""
        import re
        # A股代码格式：6位数字.SH或.SZ
        pattern = r'^\d{6}\.(SH|SZ)$'
        return bool(re.match(pattern, symbol))

    def _validate_historical_data(self, data: pd.DataFrame, symbol: str) -> bool:
        """验证历史数据质量"""
        if data.empty:
            logger.error(f"历史数据为空: {symbol}")
            return False

        # 检查必要列
        required_columns = ['close_price', 'open_price', 'high_price', 'low_price']
        missing_columns = [col for col in required_columns if col not in data.columns and col.replace('_price', '') not in data.columns]

        if missing_columns:
            logger.error(f"历史数据缺少必要列: {symbol} - {missing_columns}")
            return False

        # 检查数据完整性
        if data.isnull().sum().sum() > len(data) * 0.1:  # 超过10%的缺失值
            logger.error(f"历史数据缺失值过多: {symbol}")
            return False

        # 检查价格合理性
        price_col = 'close_price' if 'close_price' in data.columns else 'close'
        if price_col in data.columns:
            prices = data[price_col]
            if (prices <= 0).any() or (prices > 3000).any():
                logger.error(f"历史数据价格异常: {symbol}")
                return False

        # 检查数据时间序列
        if 'date' in data.columns:
            dates = pd.to_datetime(data['date'])
            if not dates.is_monotonic_increasing:
                logger.warning(f"历史数据时间序列不连续: {symbol}")

        logger.info(f"  历史数据验证通过: {symbol} ({len(data)}条记录)")
        return True

    def _is_price_reasonable(self, price: float, symbol: str) -> bool:
        """检查价格是否合理"""
        if not isinstance(price, (int, float)):
            return False
        return await self._get_real_value() <= price <= 3000.0  # A股价格合理范围
    
    def _assess_data_quality(self, data: pd.DataFrame) -> float:
        """评估数据质量"""
        if data.empty:
            return 0.0
        
        quality_score = 0.0
        
        # 数据完整性
        completeness = 1.0 - data.isnull().sum().sum() / (len(data) * len(data.columns))
        quality_score += completeness * 0.5
        
        # 数据量充足性
        data_sufficiency = min(1.0, len(data) / self.learning_config['min_learning_samples'])
        quality_score += data_sufficiency * 0.5
        
        return quality_score
    
    def _calculate_learning_confidence(self, factor_validity: Dict[str, float]) -> float:
        """计算学习置信度"""
        if not factor_validity:
            return 0.0
        
        return sum(factor_validity.values()) / len(factor_validity)
    
    def _combine_learning_and_realtime_results(self, learning_results: Dict[str, Any], realtime_validation: Dict[str, Any]) -> Dict[str, Any]:
        """综合学习结果和实时验证"""
        final_results = {}
        
        for symbol in learning_results:
            if symbol in realtime_validation:
                learning_confidence = learning_results[symbol].get('learning_confidence', 0.0)
                consistency_score = realtime_validation[symbol].get('consistency_score', 0.0)
                
                # 综合评分
                final_score = (learning_confidence * 0.6 + consistency_score * 0.4)
                
                # 生成建议
                if final_score > 0.8:
                    recommendation = "强烈推荐"
                elif final_score > 0.6:
                    recommendation = "推荐"
                elif final_score > 0.4:
                    recommendation = "中性"
                else:
                    recommendation = "不推荐"
                
                final_results[symbol] = {
                    "final_score": final_score,
                    "recommendation": recommendation,
                    "learning_confidence": learning_confidence,
                    "realtime_consistency": consistency_score,
                    "factors": learning_results[symbol].get('historical_factors', {}),
                    "current_price": realtime_validation[symbol].get('current_price', 0.0)
                }
        
        return final_results

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.iloc[-1] if not rsi.empty else 50.0
        except:
            return 50.0

    def _calculate_garch_volatility(self, returns: pd.Series) -> float:
        pass  # 专业版模式
        try:
            alpha = 0.1  # 短期波动率权重
            beta = 0.8   # 长期波动率权重

            # 初始化
            long_term_var = returns.var()
            garch_var = long_term_var

            for ret in returns:
                garch_var = (1 - alpha - beta) * long_term_var + alpha * (ret ** 2) + beta * garch_var

            return np.sqrt(garch_var * 252)  # 年化波动率
        except:
            return returns.std() * np.sqrt(252)

    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> tuple:
        """计算MACD指标"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=signal).mean()

            return macd_line.iloc[-1], signal_line.iloc[-1]
        except:
            return 0.0, 0.0

    def _calculate_bollinger_position(self, prices: pd.Series, period: int = 20) -> float:
        """计算布林带位置"""
        try:
            ma = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()

            upper_band = ma + (2 * std)
            lower_band = ma - (2 * std)

            current_price = prices.iloc[-1]
            current_upper = upper_band.iloc[-1]
            current_lower = lower_band.iloc[-1]

            if current_upper > current_lower:
                position = (current_price - current_lower) / (current_upper - current_lower)
                return position
            else:
                return await self._calculate_real_score()
        except:
            return await self._calculate_real_score()

    def _calculate_williams_r(self, highs: pd.Series, lows: pd.Series, closes: pd.Series, period: int = 14) -> float:
        """计算威廉指标"""
        try:
            highest_high = highs.rolling(window=period).max()
            lowest_low = lows.rolling(window=period).min()

            current_close = closes.iloc[-1]
            current_highest = highest_high.iloc[-1]
            current_lowest = lowest_low.iloc[-1]

            if current_highest > current_lowest:
                williams_r = ((current_highest - current_close) / (current_highest - current_lowest)) * -100
                return williams_r
            else:
                return -50.0
        except:
            return -50.0

    def _update_learning_stats(self, final_results: Dict[str, Any]):
        """更新学习统计"""
        if final_results:
            self.learning_stats['factors_learned'] += len(final_results)
            self.learning_stats['strategies_validated'] += 1
            
            # 计算平均准确率
            scores = [result['final_score'] for result in final_results.values()]
            self.learning_stats['learning_accuracy'] = sum(scores) / len(scores)
            
            self.learning_stats['last_learning_time'] = datetime.now().isoformat()
    
    def get_learning_status(self) -> Dict[str, Any]:
        """获取学习状态"""
        return {
            "service_name": self.service_name,
            "version": self.version,
            "learning_stats": self.learning_stats,
            "learning_config": self.learning_config,
            "data_sources": {
                "learning": "AkShare历史数据",
                "realtime": "新浪/腾讯实时数据"
            }
        }
