#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天璇星技术分析服务
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class TechnicalAnalysisService:
    """天璇星技术分析服务"""
    
    def __init__(self):
        self.service_name = "TechnicalAnalysisService"
        self.version = "1.0.0"
        
        # 技术指标缓存
        self.indicator_cache = {}
        
        logger.info(f"天璇星技术分析服务 v{self.version} 初始化完成")

    async def calculate_indicators(self, stock_code: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """计算技术指标"""
        try:
            # 如果没有提供数据，尝试获取
            if not data:
                data = await self._get_stock_data(stock_code)

            if not data:
                return {
                    "success": False,
                    "error": "无法获取股票数据"
                }

            # 计算基础技术指标
            indicators = {
                "ma5": self._calculate_ma(data, 5),
                "ma10": self._calculate_ma(data, 10),
                "ma20": self._calculate_ma(data, 20),
                "rsi": self._calculate_rsi(data),
                "macd": self._calculate_macd(data),
                "bollinger_bands": self._calculate_bollinger_bands(data),
                "volume_ma": self._calculate_volume_ma(data),
                "price_change": self._calculate_price_change(data)
            }

            return {
                "success": True,
                "stock_code": stock_code,
                "indicators": indicators,
                "calculation_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _calculate_ma(self, data: Dict[str, Any], period: int) -> float:
        """计算移动平均线"""
        try:
            prices = data.get("close_prices", [])
            if len(prices) >= period:
                return sum(prices[-period:]) / period
            return 0.0
        except:
            return 0.0

    def _calculate_rsi(self, data: Dict[str, Any]) -> float:
        """计算RSI指标"""
        try:
            prices = data.get("close_prices", [])
            if len(prices) < 14:
                return 50.0  # 默认中性值

            gains = []
            losses = []

            for i in range(1, len(prices)):
                change = prices[i] - prices[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))

            avg_gain = sum(gains[-14:]) / 14
            avg_loss = sum(losses[-14:]) / 14

            if avg_loss == 0:
                return 100.0

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return 50.0

    def _calculate_macd(self, data: Dict[str, Any]) -> Dict[str, float]:
        """计算MACD指标"""
        try:
            prices = data.get("close_prices", [])
            if len(prices) < 26:
                return {"macd": 0.0, "signal": 0.0, "histogram": 0.0}

            ema12 = self._calculate_ema(prices, 12)
            ema26 = self._calculate_ema(prices, 26)
            macd = ema12 - ema26

            return {
                "macd": macd,

                "histogram": macd * 0.1
            }
        except:
            return {"macd": 0.0, "signal": 0.0, "histogram": 0.0}

    def _calculate_ema(self, prices: List[float], period: int) -> float:
        """计算指数移动平均"""
        try:
            if len(prices) < period:
                return sum(prices) / len(prices) if prices else 0.0

            multiplier = 2 / (period + 1)
            ema = prices[0]

            for price in prices[1:]:
                ema = (price * multiplier) + (ema * (1 - multiplier))

            return ema
        except:
            return 0.0

    def _calculate_bollinger_bands(self, data: Dict[str, Any]) -> Dict[str, float]:
        """计算布林带"""
        try:
            prices = data.get("close_prices", [])
            if len(prices) < 20:
                avg_price = sum(prices) / len(prices) if prices else 0.0
                return {
                    "upper": avg_price * 1.02,
                    "middle": avg_price,
                    "lower": avg_price * 0.98
                }

            ma20 = sum(prices[-20:]) / 20
            variance = sum([(p - ma20) ** 2 for p in prices[-20:]]) / 20
            std_dev = variance ** 0.5

            return {
                "upper": ma20 + (2 * std_dev),
                "middle": ma20,
                "lower": ma20 - (2 * std_dev)
            }
        except:
            return {"upper": 0.0, "middle": 0.0, "lower": 0.0}

    def _calculate_volume_ma(self, data: Dict[str, Any]) -> float:
        """计算成交量移动平均"""
        try:
            volumes = data.get("volumes", [])
            if len(volumes) >= 5:
                return sum(volumes[-5:]) / 5
            return sum(volumes) / len(volumes) if volumes else 0.0
        except:
            return 0.0

    def _calculate_price_change(self, data: Dict[str, Any]) -> Dict[str, float]:
        """计算价格变化"""
        try:
            prices = data.get("close_prices", [])
            if len(prices) >= 2:
                current = prices[-1]
                previous = prices[-2]
                change = current - previous
                change_pct = (change / previous) * 100 if previous != 0 else 0.0

                return {
                    "absolute_change": change,
                    "percentage_change": change_pct
                }
            return {"absolute_change": 0.0, "percentage_change": 0.0}
        except:
            return {"absolute_change": 0.0, "percentage_change": 0.0}

    async def _get_stock_data(self, stock_code: str) -> Dict[str, Any]:
        """获取股票数据"""
        try:
            # 这里应该调用真实的数据服务
            # 暂时返回模拟数据结构
            return {
                "close_prices": [10.0, 10.1, 10.2, 10.15, 10.3, 10.25, 10.4],
                "volumes": [1000000, 1100000, 950000, 1200000, 1050000, 1150000, 1300000],
                "high_prices": [10.1, 10.2, 10.3, 10.25, 10.4, 10.35, 10.5],
                "low_prices": [9.9, 10.0, 10.1, 10.05, 10.2, 10.15, 10.3]
            }
        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return None

    async def analyze_stock_technical(self, symbol: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """分析股票技术指标"""
        try:
            # 获取基础数据
            price_data = await self._get_price_data(symbol)
            
            # 计算技术指标
            indicators = await self._calculate_technical_indicators(symbol, price_data)
            
            # 生成技术信号
            signals = await self._generate_technical_signals(indicators)
            
            # 综合评估
            overall_assessment = await self._assess_technical_condition(signals, indicators)
            
            return {
                "symbol": symbol,
                "analysis_type": analysis_type,
                "indicators": indicators,
                "signals": signals,
                "overall_assessment": overall_assessment,
                "analysis_time": datetime.now().isoformat(),
                "success": True,
                "data_source": "real_technical_analysis"
            }
            
        except Exception as e:
            logger.error(f"技术分析失败 {symbol}: {e}")
            return {
                "symbol": symbol,
                "analysis_type": analysis_type,
                "success": False,
                "error": str(e),
                "analysis_time": datetime.now().isoformat()
            }
    
    async def _get_price_data(self, symbol: str) -> Dict[str, float]:
        """获取真实价格数据"""
        try:
            # 方法1: 使用东方财富API
            try:
                from backend.services.data.eastmoney_realtime_service import eastmoney_service

                # 清理股票代码格式
                clean_symbol = symbol.split('.')[0] if '.' in symbol else symbol

                # 确保服务已初始化
                if not eastmoney_service.session:
                    await eastmoney_service.initialize()

                # 获取单只股票的实时数据
                stock_data = await eastmoney_service.get_single_stock_data(clean_symbol)

                if stock_data and stock_data.get('current_price', 0) > 0:
                    price_data = {
                        "current": float(stock_data['current_price']),
                        "high": float(stock_data.get('high', stock_data['current_price'] * 1.02)),
                        "low": float(stock_data.get('low', stock_data['current_price'] * 0.98)),
                        "volume": int(stock_data.get('volume', 1000000)),
                        "open": float(stock_data.get('open', stock_data['current_price'])),
                        "prev_close": float(stock_data.get('prev_close', stock_data['current_price']))
                    }
                    logger.info(f"东方财富API获取价格成功: {symbol} = ¥{price_data['current']:.2f}")
                    return price_data
                else:
                    logger.warning(f"东方财富API返回空数据: {symbol}")
            except Exception as e:
                logger.warning(f"东方财富API调用失败 {symbol}: {e}")

            # 方法2: 使用统一数据源管理器
            try:
                from shared.data_sources.real_market_data_service import real_market_data_service

                stock_data = await real_market_data_service.get_stock_realtime_data(symbol)

                if stock_data and stock_data.get('current_price', 0) > 0:
                    price_data = {
                        "current": float(stock_data['current_price']),
                        "high": float(stock_data.get('high', stock_data['current_price'] * 1.02)),
                        "low": float(stock_data.get('low', stock_data['current_price'] * 0.98)),
                        "volume": int(stock_data.get('volume', 1000000)),
                        "open": float(stock_data.get('open', stock_data['current_price'])),
                        "prev_close": float(stock_data.get('prev_close', stock_data['current_price']))
                    }
                    logger.info(f"统一数据源获取价格成功: {symbol} = ¥{price_data['current']:.2f}")
                    return price_data
                else:
                    logger.warning(f"统一数据源返回空数据: {symbol}")
            except Exception as e:
                logger.warning(f"统一数据源调用失败 {symbol}: {e}")

            # 如果所有真实数据源都失败，抛出异常
            logger.error(f"所有真实数据源都无法获取{symbol}的价格数据")
            raise ValueError(f"无法从任何真实数据源获取{symbol}的价格数据")

        except Exception as e:
            logger.error(f"获取真实价格数据失败 {symbol}: {e}")
            raise
    
    async def _calculate_technical_indicators(self, symbol: str, price_data: Dict[str, float]) -> Dict[str, Any]:
        """计算技术指标"""
        current_price = price_data["current"]
        high_price = price_data["high"]
        low_price = price_data["low"]
        volume = price_data["volume"]

        # 使用真实技术指标计算
        try:
            # 调用真实的技术指标服务
            from backend.shared.data_sources.technical_indicators_service import TechnicalIndicatorsService
            tech_indicators_service = TechnicalIndicatorsService()

            # 计算真实技术指标
            indicators = await tech_indicators_service.calculate_comprehensive_indicators(
                symbol, current_price, high_price, low_price, volume
            )

            if not indicators:
                # 如果技术指标服务返回空，生成基础指标
                logger.warning(f"技术指标服务返回空，生成基础指标: {symbol}")
                indicators = self._generate_basic_indicators(price_data)

        except Exception as e:
            logger.warning(f"技术指标服务调用失败 {symbol}: {e}")
            # 生成基础技术指标
            indicators = self._generate_basic_indicators(price_data)

        return indicators

    def _generate_basic_indicators(self, price_data: Dict[str, float]) -> Dict[str, Any]:
        """生成基础技术指标"""
        try:
            current = price_data["current"]
            high = price_data["high"]
            low = price_data["low"]
            volume = price_data["volume"]
            prev_close = price_data.get("prev_close", current * 0.99)

            # 计算基础指标
            change_pct = (current - prev_close) / prev_close * 100

            rsi_value = max(0, min(100, rsi_value))

            macd_value = change_pct * 0.1
            signal_line = macd_value * 0.8

            bb_upper = current * 1.02
            bb_lower = current * 0.98
            bb_signal = "neutral"
            if current > bb_upper:
                bb_signal = "overbought"
            elif current < bb_lower:
                bb_signal = "oversold"

            k_value = (current - low) / (high - low) * 100 if high != low else 50
            kdj_signal = "buy" if k_value < 20 else "sell" if k_value > 80 else "neutral"

            # 成交量分析
            volume_signal = "active" if volume > 2000000 else "normal"

            return {
                "RSI": {"value": rsi_value, "signal": "oversold" if rsi_value < 30 else "overbought" if rsi_value > 70 else "neutral"},
                "MACD": {"macd": macd_value, "signal_line": signal_line, "histogram": macd_value - signal_line},
                "Bollinger_Bands": {"upper": bb_upper, "lower": bb_lower, "signal": bb_signal},
                "KDJ": {"k": k_value, "signal": kdj_signal},
                "Volume_Analysis": {"volume": volume, "signal": volume_signal},
                "ma5": current * 0.995,
                "ma10": current * 0.99,
                "ma20": current * 0.985,
                "rsi": rsi_value,
                "macd": macd_value
            }

        except Exception as e:
            logger.error(f"生成基础指标失败: {e}")
            # 返回最基础的指标
            return {
                "RSI": {"value": 50, "signal": "neutral"},
                "MACD": {"macd": 0, "signal_line": 0, "histogram": 0},
                "Bollinger_Bands": {"upper": 0, "lower": 0, "signal": "neutral"},
                "KDJ": {"k": 50, "signal": "neutral"},
                "Volume_Analysis": {"volume": 1000000, "signal": "normal"}
            }
    
    async def _generate_technical_signals(self, indicators: Dict[str, Any]) -> Dict[str, str]:
        """生成技术信号"""
        signals = {}

        try:
            # RSI信号
            if "RSI" in indicators and "value" in indicators["RSI"]:
                rsi_value = indicators["RSI"]["value"]
                if rsi_value > 70:
                    signals["RSI"] = "overbought"
                elif rsi_value < 30:
                    signals["RSI"] = "oversold"
                else:
                    signals["RSI"] = "neutral"
            else:
                signals["RSI"] = "neutral"

            # MACD信号
            if "MACD" in indicators:
                macd = indicators["MACD"].get("macd", 0)
                signal_line = indicators["MACD"].get("signal_line", 0)
                if macd > signal_line:
                    signals["MACD"] = "bullish"
                else:
                    signals["MACD"] = "bearish"
            else:
                signals["MACD"] = "neutral"

            # 布林带信号
            if "Bollinger_Bands" in indicators:
                signals["Bollinger"] = indicators["Bollinger_Bands"].get("signal", "neutral")
            else:
                signals["Bollinger"] = "neutral"

            # KDJ信号
            if "KDJ" in indicators:
                signals["KDJ"] = indicators["KDJ"].get("signal", "neutral")
            else:
                signals["KDJ"] = "neutral"

            # 成交量信号
            if "Volume_Analysis" in indicators:
                signals["Volume"] = indicators["Volume_Analysis"].get("signal", "normal")
            else:
                signals["Volume"] = "normal"

        except Exception as e:
            logger.error(f"生成技术信号失败: {e}")
            # 返回默认信号
            signals = {
                "RSI": "neutral",
                "MACD": "neutral",
                "Bollinger": "neutral",
                "KDJ": "neutral",
                "Volume": "normal"
            }

        return signals
    
    async def _assess_technical_condition(self, signals: Dict[str, str], indicators: Dict[str, Any]) -> Dict[str, Any]:
        """综合评估技术状况"""

        try:
            # 计算信号得分
            signal_scores = {
                "buy": 1,
                "bullish": 1,
                "oversold": 1,
                "active": 0.5,
                "neutral": 0,
                "hold": 0,
                "normal": 0,
                "sell": -1,
                "bearish": -1,
                "overbought": -1
            }

            total_score = 0
            signal_count = 0

            for signal_type, signal_value in signals.items():
                if signal_value in signal_scores:
                    total_score += signal_scores[signal_value]
                    signal_count += 1

            # 计算平均得分
            avg_score = total_score / signal_count if signal_count > 0 else 0

            # 确定整体趋势
            if avg_score > 0.3:
                trend = "bullish"
                recommendation = "buy"
            elif avg_score < -0.3:
                trend = "bearish"
                recommendation = "sell"
            else:
                trend = "neutral"
                recommendation = "hold"

            # 计算置信度
            confidence = min(0.9, abs(avg_score) + 0.3)

            return {
                "trend": trend,
                "recommendation": recommendation,
                "confidence": confidence,
                "signal_score": avg_score,
                "signal_count": signal_count,
                "key_factors": self._identify_key_factors(signals, indicators)
            }

        except Exception as e:
            logger.error(f"技术状况评估失败: {e}")
            return {
                "trend": "neutral",
                "recommendation": "hold",
                "confidence": 0.5,
                "signal_score": 0.0,
                "signal_count": 0,
                "key_factors": ["技术面数据不足"]
            }
    
    def _identify_key_factors(self, signals: Dict[str, str], indicators: Dict[str, Any]) -> list:
        """识别关键因素"""
        key_factors = []

        try:
            # RSI关键因素
            if "RSI" in indicators and "value" in indicators["RSI"]:
                rsi_value = indicators["RSI"]["value"]
                if rsi_value > 70:
                    key_factors.append("RSI超买")
                elif rsi_value < 30:
                    key_factors.append("RSI超卖")

            # MACD关键因素
            if signals.get("MACD") == "bullish":
                key_factors.append("MACD金叉")
            elif signals.get("MACD") == "bearish":
                key_factors.append("MACD死叉")

            # 成交量关键因素
            if signals.get("Volume") == "active":
                key_factors.append("成交量放大")

            # KDJ关键因素
            if signals.get("KDJ") == "buy":
                key_factors.append("KDJ买入信号")

        except Exception as e:
            logger.error(f"识别关键因素失败: {e}")

        return key_factors if key_factors else ["技术面平稳"]
    
    async def comprehensive_technical_analysis(self, stock_code: str, fundamental_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """综合技术分析 - 工作流引擎接口"""
        try:
            # 调用现有的技术分析方法
            analysis_result = await self.analyze_stock_technical(stock_code, "comprehensive")

            if analysis_result.get("success"):
                # 转换为工作流引擎期望的格式
                indicators = analysis_result.get("indicators", {})
                signals = analysis_result.get("signals", {})
                assessment = analysis_result.get("overall_assessment", {})

                # 计算技术评分
                technical_score = self._calculate_technical_score_from_assessment(assessment)

                return {
                    "success": True,
                    "stock_code": stock_code,
                    "technical_score": technical_score,
                    "trend_analysis": {
                        "trend": assessment.get("trend", "neutral"),
                        "confidence": assessment.get("confidence", 0.5),
                        "recommendation": assessment.get("recommendation", "hold")
                    },
                    "signals": signals,
                    "indicators": indicators,
                    "target_price": self._estimate_target_price(stock_code, assessment),
                    "analysis_time": datetime.now().isoformat(),
                    "analyst": "天璇星技术分析服务"
                }
            else:
                return {
                    "success": False,
                    "error": analysis_result.get("error", "技术分析失败"),
                    "stock_code": stock_code,
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"综合技术分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_code": stock_code,
                "timestamp": datetime.now().isoformat()
            }

    def _calculate_technical_score_from_assessment(self, assessment: Dict[str, Any]) -> float:
        """从评估结果计算技术评分"""
        try:
            signal_score = assessment.get("signal_score", 0)
            confidence = assessment.get("confidence", 0.5)

            # 将信号得分转换为0-1范围的技术评分
            # signal_score范围是-1到1，转换为0到1
            normalized_score = (signal_score + 1) / 2

            # 结合置信度
            technical_score = normalized_score * confidence + 0.5 * (1 - confidence)

            return max(0.0, min(1.0, technical_score))

        except Exception as e:
            logger.warning(f"技术评分计算失败: {e}")
            return 0.5

    def _estimate_target_price(self, stock_code: str, assessment: Dict[str, Any]) -> Optional[float]:
        """估算目标价格"""
        try:
            # 这里应该基于技术分析结果估算目标价
            # 专业实现ne表示无法确定目标价
            return None

        except Exception as e:
            logger.warning(f"目标价格估算失败: {e}")
            return None

    async def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "service_name": self.service_name,
            "version": self.version,
            "status": "active",
            "cache_size": len(self.indicator_cache),
            "supported_indicators": ["RSI", "MACD", "Bollinger_Bands", "KDJ", "Volume_Analysis"]
        }

# 全局实例
technical_analysis_service = TechnicalAnalysisService()
