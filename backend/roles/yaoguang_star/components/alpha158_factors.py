#!/usr/bin/env python3
"""
Alpha158因子实现
基于真实市场数据的158个经典量化因子
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class Alpha158FactorCalculator:
    """Alpha158因子计算器"""

    def __init__(self):
        self.factor_names = self._get_alpha158_factor_names()

    def _get_alpha158_factor_names(self) -> List[str]:
        """获取Alpha158因子名称列表"""
        return [
            # 价格因子 (1-20)
            "CLOSE", "OPEN", "HIGH", "LOW", "VWAP",
            "RETURN_1", "RETURN_5", "RETURN_10", "RETURN_20", "RETURN_60",
            "MA_5", "MA_10", "MA_20", "MA_60", "MA_120",
            "STD_5", "STD_10", "STD_20", "STD_60", "BETA_60",

            # 成交量因子 (21-40)
            "VOLUME", "VOLUME_RATIO", "TURNOVER", "VOLUME_MA_5", "VOLUME_MA_10",
            "VOLUME_STD_5", "VOLUME_STD_10", "VOLUME_PRICE_CORR", "VOLUME_RETURN_CORR",
            "VOLUME_TREND_5", "VOLUME_TREND_10", "VOLUME_MOMENTUM", "VOLUME_REVERSAL",
            "VOLUME_SPIKE", "VOLUME_DROUGHT", "VOLUME_CONSISTENCY", "VOLUME_ACCELERATION",
            "VOLUME_DECELERATION", "VOLUME_VOLATILITY", "VOLUME_EFFICIENCY",

            # 技术指标因子 (41-80)
            "RSI_6", "RSI_12", "RSI_24", "MACD", "MACD_SIGNAL", "MACD_HIST",
            "BOLLINGER_UPPER", "BOLLINGER_LOWER", "BOLLINGER_POSITION", "BOLLINGER_WIDTH",
            "KDJ_K", "KDJ_D", "KDJ_J", "CCI", "WILLIAMS_R", "STOCH_RSI",
            "ADX", "DI_PLUS", "DI_MINUS", "AROON_UP", "AROON_DOWN", "AROON_OSC",
            "MFI", "OBV", "CMF", "EMV", "FORCE_INDEX", "EASE_OF_MOVEMENT",
            "VOLUME_PRICE_TREND", "ACCUMULATION_DISTRIBUTION", "CHAIKIN_OSC",
            "TRIX", "ULTIMATE_OSC", "COMMODITY_CHANNEL", "MOMENTUM_10", "MOMENTUM_20",
            "ROC_5", "ROC_10", "ROC_20", "PRICE_CHANNEL_HIGH", "PRICE_CHANNEL_LOW",

            # 波动率因子 (81-100)
            "VOLATILITY_5", "VOLATILITY_10", "VOLATILITY_20", "VOLATILITY_60",
            "GARCH_VOLATILITY", "REALIZED_VOLATILITY", "PARKINSON_VOLATILITY",
            "GARMAN_KLASS_VOLATILITY", "ROGERS_SATCHELL_VOLATILITY", "YANG_ZHANG_VOLATILITY",
            "VOLATILITY_RATIO", "VOLATILITY_TREND", "VOLATILITY_MEAN_REVERSION",
            "VOLATILITY_CLUSTERING", "VOLATILITY_ASYMMETRY", "VOLATILITY_PERSISTENCE",
            "VOLATILITY_JUMP", "VOLATILITY_REGIME", "VOLATILITY_SMILE", "VOLATILITY_SURFACE",

            # 动量因子 (101-120)
            "MOMENTUM_1", "MOMENTUM_5", "MOMENTUM_20", "MOMENTUM_60", "MOMENTUM_120",
            "REVERSAL_1", "REVERSAL_5", "REVERSAL_20", "ACCELERATION", "DECELERATION",
            "TREND_STRENGTH", "TREND_CONSISTENCY", "TREND_DURATION", "TREND_REVERSAL",
            "MOMENTUM_DIVERGENCE", "MOMENTUM_CONVERGENCE", "MOMENTUM_OSCILLATION",
            "MOMENTUM_PERSISTENCE", "MOMENTUM_DECAY", "MOMENTUM_AMPLIFICATION",

            # 相对强度因子 (121-140)
            "RELATIVE_STRENGTH_5", "RELATIVE_STRENGTH_10", "RELATIVE_STRENGTH_20",
            "RELATIVE_STRENGTH_60", "SECTOR_RELATIVE_STRENGTH", "MARKET_RELATIVE_STRENGTH",
            "PEER_RELATIVE_STRENGTH", "HISTORICAL_RELATIVE_STRENGTH", "CROSS_SECTIONAL_RANK",
            "PERCENTILE_RANK", "Z_SCORE", "MODIFIED_Z_SCORE", "ROBUST_Z_SCORE",
            "RELATIVE_VOLATILITY", "RELATIVE_VOLUME", "RELATIVE_TURNOVER",
            "RELATIVE_MOMENTUM", "RELATIVE_REVERSAL", "RELATIVE_TREND", "RELATIVE_EFFICIENCY",

            # 市场微观结构因子 (141-158)
            "BID_ASK_SPREAD", "MARKET_IMPACT", "PRICE_IMPROVEMENT", "EXECUTION_SHORTFALL",
            "IMPLEMENTATION_SHORTFALL", "ARRIVAL_PRICE", "VWAP_DEVIATION", "TWAP_DEVIATION",
            "PARTICIPATION_RATE", "MARKET_SHARE", "ORDER_FLOW_IMBALANCE", "TRADE_IMBALANCE",
            "QUOTE_IMBALANCE", "DEPTH_IMBALANCE", "LIQUIDITY_RATIO", "AMIHUD_ILLIQUIDITY",
            "PASTOR_STAMBAUGH_LIQUIDITY", "EFFECTIVE_SPREAD"
        ]

    async def calculate_all_factors(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """计算所有Alpha158因子"""
        try:
            factors = {}

            # 价格因子
            factors.update(await self._calculate_price_factors(data))

            # 成交量因子
            factors.update(await self._calculate_volume_factors(data))

            # 技术指标因子
            factors.update(await self._calculate_technical_factors(data))

            # 波动率因子
            factors.update(await self._calculate_volatility_factors(data))

            # 动量因子
            factors.update(await self._calculate_momentum_factors(data))

            logger.info(f"成功计算 {len(factors)} 个Alpha158因子")
            return factors

        except Exception as e:
            logger.error(f"Alpha158因子计算失败: {e}")
            return {}

    async def _calculate_price_factors(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """计算价格相关因子"""
        factors = {}

        try:
            close = data['close'].values
            open_price = data['open'].values
            high = data['high'].values
            low = data['low'].values
            volume = data['volume'].values

            # 基础价格因子
            factors['CLOSE'] = close
            factors['OPEN'] = open_price
            factors['HIGH'] = high
            factors['LOW'] = low

            # VWAP计算
            factors['VWAP'] = (close * volume).cumsum() / volume.cumsum()

            # 收益率因子
            if len(close) > 1:
                returns = np.zeros_like(close)
                returns[1:] = (close[1:] - close[:-1]) / close[:-1]
                factors['RETURN_1'] = returns
            else:
                factors['RETURN_1'] = np.zeros_like(close)
            factors['RETURN_5'] = self._calculate_return(close, 5)
            factors['RETURN_10'] = self._calculate_return(close, 10)
            factors['RETURN_20'] = self._calculate_return(close, 20)
            factors['RETURN_60'] = self._calculate_return(close, 60)

            # 移动平均因子
            factors['MA_5'] = self._calculate_ma(close, 5)
            factors['MA_10'] = self._calculate_ma(close, 10)
            factors['MA_20'] = self._calculate_ma(close, 20)
            factors['MA_60'] = self._calculate_ma(close, 60)
            factors['MA_120'] = self._calculate_ma(close, 120)

            # 标准差因子
            factors['STD_5'] = self._calculate_rolling_std(close, 5)
            factors['STD_10'] = self._calculate_rolling_std(close, 10)
            factors['STD_20'] = self._calculate_rolling_std(close, 20)
            factors['STD_60'] = self._calculate_rolling_std(close, 60)

            return factors

        except Exception as e:
            logger.error(f"价格因子计算失败: {e}")
            return {}

    async def _calculate_volume_factors(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """计算成交量相关因子"""
        factors = {}

        try:
            close = data['close'].values
            volume = data['volume'].values

            # 基础成交量因子
            factors['VOLUME'] = volume

            # 成交量比率
            volume_ma_5 = self._calculate_ma(volume, 5)
            factors['VOLUME_RATIO'] = volume / volume_ma_5

            # 换手率（需要流通股本数据，这里用成交量/价格估算）

            # 成交量移动平均
            factors['VOLUME_MA_5'] = volume_ma_5
            factors['VOLUME_MA_10'] = self._calculate_ma(volume, 10)

            # 成交量标准差
            factors['VOLUME_STD_5'] = self._calculate_rolling_std(volume, 5)
            factors['VOLUME_STD_10'] = self._calculate_rolling_std(volume, 10)

            # 成交量价格相关性
            factors['VOLUME_PRICE_CORR'] = self._calculate_rolling_correlation(volume, close, 20)

            # 成交量收益率相关性
            returns = self._calculate_return(close, 1)
            factors['VOLUME_RETURN_CORR'] = self._calculate_rolling_correlation(volume[1:], returns[1:], 20)

            # 成交量趋势
            factors['VOLUME_TREND_5'] = self._calculate_trend(volume, 5)
            factors['VOLUME_TREND_10'] = self._calculate_trend(volume, 10)

            # 成交量动量
            factors['VOLUME_MOMENTUM'] = self._calculate_return(volume, 5)

            # 成交量反转
            factors['VOLUME_REVERSAL'] = -self._calculate_return(volume, 1)

            return factors

        except Exception as e:
            logger.error(f"成交量因子计算失败: {e}")
            return {}

    def _calculate_rolling_correlation(self, x: np.ndarray, y: np.ndarray, period: int) -> np.ndarray:
        """计算滚动相关性"""
        if len(x) != len(y) or len(x) < period:
            return np.zeros(max(len(x), len(y)))

        corr = np.zeros(len(x))
        for i in range(period-1, len(x)):
            start_idx = i - period + 1
            x_window = x[start_idx:i+1]
            y_window = y[start_idx:i+1]

            if np.std(x_window) > 0 and np.std(y_window) > 0:
                corr[i] = np.corrcoef(x_window, y_window)[0, 1]

        return corr

    def _calculate_trend(self, values: np.ndarray, period: int) -> np.ndarray:
        """计算趋势"""
        if len(values) < period:
            return np.zeros_like(values)

        trend = np.zeros_like(values)
        for i in range(period-1, len(values)):
            start_idx = i - period + 1
            window = values[start_idx:i+1]
            x = np.arange(len(window))
            slope = np.polyfit(x, window, 1)[0]
            trend[i] = slope

        return trend

    async def _calculate_technical_factors(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """计算技术指标因子"""
        factors = {}

        try:
            close = data['close'].values
            high = data['high'].values
            low = data['low'].values
            volume = data['volume'].values

            # RSI指标
            factors['RSI_6'] = self._calculate_rsi(close, 6)
            factors['RSI_12'] = self._calculate_rsi(close, 12)
            factors['RSI_24'] = self._calculate_rsi(close, 24)

            # MACD指标
            macd, signal, histogram = self._calculate_macd(close)
            factors['MACD'] = macd
            factors['MACD_SIGNAL'] = signal
            factors['MACD_HIST'] = histogram

            # 布林带
            upper, lower, position, width = self._calculate_bollinger_bands(close)
            factors['BOLLINGER_UPPER'] = upper
            factors['BOLLINGER_LOWER'] = lower
            factors['BOLLINGER_POSITION'] = position
            factors['BOLLINGER_WIDTH'] = width

            # KDJ指标
            k, d, j = self._calculate_kdj(high, low, close)
            factors['KDJ_K'] = k
            factors['KDJ_D'] = d
            factors['KDJ_J'] = j

            # CCI指标
            factors['CCI'] = self._calculate_cci(high, low, close)

            # Williams %R
            factors['WILLIAMS_R'] = self._calculate_williams_r(high, low, close)

            # Stochastic RSI
            factors['STOCH_RSI'] = self._calculate_stochastic_rsi(close)

            # ADX指标
            adx, di_plus, di_minus = self._calculate_adx(high, low, close)
            factors['ADX'] = adx
            factors['DI_PLUS'] = di_plus
            factors['DI_MINUS'] = di_minus

            # Aroon指标
            aroon_up, aroon_down, aroon_osc = self._calculate_aroon(high, low)
            factors['AROON_UP'] = aroon_up
            factors['AROON_DOWN'] = aroon_down
            factors['AROON_OSC'] = aroon_osc

            # MFI指标
            factors['MFI'] = self._calculate_mfi(high, low, close, volume)

            # OBV指标
            factors['OBV'] = self._calculate_obv(close, volume)

            # CMF指标
            factors['CMF'] = self._calculate_cmf(high, low, close, volume)

            # 更多高级技术指标
            factors['TRIX'] = self._calculate_trix(close)
            factors['ULTIMATE_OSC'] = self._calculate_ultimate_oscillator(high, low, close)
            factors['COMMODITY_CHANNEL'] = self._calculate_commodity_channel_index(high, low, close)
            factors['MASS_INDEX'] = self._calculate_mass_index(high, low)
            factors['CHAIKIN_OSC'] = self._calculate_chaikin_oscillator(high, low, close, volume)
            factors['PRICE_OSC'] = self._calculate_price_oscillator(close)
            factors['VOLUME_OSC'] = self._calculate_volume_oscillator(volume)

            return factors

        except Exception as e:
            logger.error(f"技术指标因子计算失败: {e}")
            return {}

    def _calculate_return(self, prices: np.ndarray, period: int) -> np.ndarray:
        """计算指定周期的收益率"""
        if len(prices) <= period:
            return np.zeros_like(prices)

        returns = np.zeros_like(prices)
        returns[period:] = (prices[period:] - prices[:-period]) / prices[:-period]
        return returns

    def _calculate_ma(self, prices: np.ndarray, period: int) -> np.ndarray:
        """计算移动平均"""
        if len(prices) < period:
            return np.full_like(prices, prices.mean())

        ma = np.zeros_like(prices)
        for i in range(len(prices)):
            start_idx = max(0, i - period + 1)
            ma[i] = prices[start_idx:i+1].mean()
        return ma

    def _calculate_rolling_std(self, prices: np.ndarray, period: int) -> np.ndarray:
        """计算滚动标准差"""
        if len(prices) < period:
            return np.full_like(prices, prices.std())

        std = np.zeros_like(prices)
        for i in range(len(prices)):
            start_idx = max(0, i - period + 1)
            std[i] = prices[start_idx:i+1].std()
        return std

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> np.ndarray:
        """计算RSI指标"""
        if len(prices) < period + 1:
            return np.full_like(prices, 50.0)

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        rsi = np.zeros_like(prices)

        # 计算初始平均增益和损失
        avg_gain = gains[:period].mean()
        avg_loss = losses[:period].mean()

        for i in range(period, len(prices)):
            if i == period:
                rsi[i] = 100 - (100 / (1 + avg_gain / avg_loss)) if avg_loss != 0 else 100
            else:
                avg_gain = (avg_gain * (period - 1) + gains[i-1]) / period
                avg_loss = (avg_loss * (period - 1) + losses[i-1]) / period
                rsi[i] = 100 - (100 / (1 + avg_gain / avg_loss)) if avg_loss != 0 else 100

        return rsi

    def _calculate_macd(self, prices: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9):
        """计算MACD指标"""
        if len(prices) < slow:
            return np.zeros_like(prices), np.zeros_like(prices), np.zeros_like(prices)

        # 计算EMA
        ema_fast = self._calculate_ema(prices, fast)
        ema_slow = self._calculate_ema(prices, slow)

        # MACD线
        macd_line = ema_fast - ema_slow

        # 信号线
        signal_line = self._calculate_ema(macd_line, signal)

        # 柱状图
        histogram = macd_line - signal_line

        return macd_line, signal_line, histogram

    def _calculate_ema(self, prices: np.ndarray, period: int) -> np.ndarray:
        """计算指数移动平均"""
        if len(prices) == 0:
            return np.array([])

        alpha = 2.0 / (period + 1)
        ema = np.zeros_like(prices)
        ema[0] = prices[0]

        for i in range(1, len(prices)):
            ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]

        return ema

    def _calculate_bollinger_bands(self, prices: np.ndarray, period: int = 20, std_dev: float = 2.0):
        """计算布林带"""
        ma = self._calculate_ma(prices, period)
        std = self._calculate_rolling_std(prices, period)

        upper = ma + (std * std_dev)
        lower = ma - (std * std_dev)

        # 布林带位置
        with np.errstate(divide='ignore', invalid='ignore'):
            position = (prices - lower) / (upper - lower)
            position = np.where((upper == lower) | np.isnan(position) | np.isinf(position), 0.5, position)

        # 布林带宽度
        width = (upper - lower) / ma

        return upper, lower, position, width

    def _calculate_kdj(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 9):
        """计算KDJ指标"""
        if len(close) < period:
            return np.full_like(close, 50.0), np.full_like(close, 50.0), np.full_like(close, 50.0)

        k = np.zeros_like(close)
        d = np.zeros_like(close)
        j = np.zeros_like(close)

        for i in range(period-1, len(close)):
            start_idx = i - period + 1
            highest = high[start_idx:i+1].max()
            lowest = low[start_idx:i+1].min()

            if highest == lowest:
                rsv = 50.0
            else:
                rsv = (close[i] - lowest) / (highest - lowest) * 100

            if i == period - 1:
                k[i] = rsv
                d[i] = rsv
            else:
                k[i] = (2 * k[i-1] + rsv) / 3
                d[i] = (2 * d[i-1] + k[i]) / 3

            j[i] = 3 * k[i] - 2 * d[i]

        return k, d, j

    def _calculate_cci(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 20) -> np.ndarray:
        """计算CCI指标"""
        if len(close) < period:
            return np.zeros_like(close)

        typical_price = (high + low + close) / 3
        ma_tp = self._calculate_ma(typical_price, period)

        cci = np.zeros_like(close)

        for i in range(period-1, len(close)):
            start_idx = i - period + 1
            mean_deviation = np.abs(typical_price[start_idx:i+1] - ma_tp[i]).mean()

            if mean_deviation != 0:
                cci[i] = (typical_price[i] - ma_tp[i]) / (0.015 * mean_deviation)

        return cci

    def _calculate_williams_r(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """计算Williams %R指标"""
        if len(close) < period:
            return np.full_like(close, -50.0)

        wr = np.zeros_like(close)

        for i in range(period-1, len(close)):
            start_idx = i - period + 1
            highest = high[start_idx:i+1].max()
            lowest = low[start_idx:i+1].min()

            if highest == lowest:
                wr[i] = -50.0
            else:
                wr[i] = (highest - close[i]) / (highest - lowest) * -100

        return wr

    def _calculate_stochastic_rsi(self, prices: np.ndarray, period: int = 14) -> np.ndarray:
        """计算Stochastic RSI"""
        rsi = self._calculate_rsi(prices, period)

        stoch_rsi = np.zeros_like(rsi)

        for i in range(period-1, len(rsi)):
            start_idx = i - period + 1
            rsi_window = rsi[start_idx:i+1]
            rsi_min = rsi_window.min()
            rsi_max = rsi_window.max()

            if rsi_max == rsi_min:
                stoch_rsi[i] = 0.5
            else:
                stoch_rsi[i] = (rsi[i] - rsi_min) / (rsi_max - rsi_min)

        return stoch_rsi

    def _calculate_adx(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14):
        """计算ADX指标"""
        if len(close) < period + 1:
            return np.zeros_like(close), np.zeros_like(close), np.zeros_like(close)

        # 计算真实范围TR
        tr = np.zeros(len(close))
        for i in range(1, len(close)):
            tr[i] = max(
                high[i] - low[i],
                abs(high[i] - close[i-1]),
                abs(low[i] - close[i-1])
            )

        # 计算方向移动
        dm_plus = np.zeros(len(close))
        dm_minus = np.zeros(len(close))

        for i in range(1, len(close)):
            up_move = high[i] - high[i-1]
            down_move = low[i-1] - low[i]

            if up_move > down_move and up_move > 0:
                dm_plus[i] = up_move
            if down_move > up_move and down_move > 0:
                dm_minus[i] = down_move

        # 计算平滑的TR和DM
        atr = self._calculate_ema(tr, period)
        adm_plus = self._calculate_ema(dm_plus, period)
        adm_minus = self._calculate_ema(dm_minus, period)

        # 计算DI
        with np.errstate(divide='ignore', invalid='ignore'):
            di_plus = np.where(atr != 0, 100 * adm_plus / atr, 0)
            di_minus = np.where(atr != 0, 100 * adm_minus / atr, 0)

        # 计算ADX
        dx = 100 * np.abs(di_plus - di_minus) / (di_plus + di_minus)
        dx = np.where(di_plus + di_minus == 0, 0, dx)
        adx = self._calculate_ema(dx, period)

        return adx, di_plus, di_minus

    def _calculate_aroon(self, high: np.ndarray, low: np.ndarray, period: int = 25):
        """计算Aroon指标"""
        if len(high) < period:
            return np.zeros_like(high), np.zeros_like(high), np.zeros_like(high)

        aroon_up = np.zeros_like(high)
        aroon_down = np.zeros_like(high)

        for i in range(period-1, len(high)):
            start_idx = i - period + 1

            # 找到最高价和最低价的位置
            high_idx = np.argmax(high[start_idx:i+1]) + start_idx
            low_idx = np.argmin(low[start_idx:i+1]) + start_idx

            # 计算Aroon Up和Aroon Down
            aroon_up[i] = ((period - (i - high_idx)) / period) * 100
            aroon_down[i] = ((period - (i - low_idx)) / period) * 100

        aroon_osc = aroon_up - aroon_down

        return aroon_up, aroon_down, aroon_osc

    def _calculate_mfi(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, volume: np.ndarray, period: int = 14) -> np.ndarray:
        """计算资金流量指标MFI"""
        if len(close) < period + 1:
            return np.full_like(close, 50.0)

        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume

        mfi = np.zeros_like(close)

        for i in range(period, len(close)):
            positive_flow = 0
            negative_flow = 0

            for j in range(i - period + 1, i + 1):
                if j > 0:
                    if typical_price[j] > typical_price[j-1]:
                        positive_flow += money_flow[j]
                    elif typical_price[j] < typical_price[j-1]:
                        negative_flow += money_flow[j]

            if negative_flow == 0:
                mfi[i] = 100
            else:
                money_ratio = positive_flow / negative_flow
                mfi[i] = 100 - (100 / (1 + money_ratio))

        return mfi

    def _calculate_obv(self, close: np.ndarray, volume: np.ndarray) -> np.ndarray:
        """计算能量潮指标OBV"""
        if len(close) < 2:
            return np.zeros_like(close)

        obv = np.zeros_like(close)
        obv[0] = volume[0]

        for i in range(1, len(close)):
            if close[i] > close[i-1]:
                obv[i] = obv[i-1] + volume[i]
            elif close[i] < close[i-1]:
                obv[i] = obv[i-1] - volume[i]
            else:
                obv[i] = obv[i-1]

        return obv

    def _calculate_cmf(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, volume: np.ndarray, period: int = 20) -> np.ndarray:
        """计算蔡金资金流量指标CMF"""
        if len(close) < period:
            return np.zeros_like(close)

        # 计算资金流量乘数
        mf_multiplier = np.zeros_like(close)
        for i in range(len(close)):
            if high[i] != low[i]:
                mf_multiplier[i] = ((close[i] - low[i]) - (high[i] - close[i])) / (high[i] - low[i])

        # 计算资金流量量
        mf_volume = mf_multiplier * volume

        # 计算CMF
        cmf = np.zeros_like(close)
        for i in range(period-1, len(close)):
            start_idx = i - period + 1
            sum_mf_volume = mf_volume[start_idx:i+1].sum()
            sum_volume = volume[start_idx:i+1].sum()

            if sum_volume != 0:
                cmf[i] = sum_mf_volume / sum_volume

        return cmf

    async def _calculate_volatility_factors(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """计算波动率因子"""
        factors = {}

        try:
            close = data['close'].values
            high = data['high'].values
            low = data['low'].values

            # 基础波动率因子
            factors['VOLATILITY_5'] = self._calculate_rolling_std(close, 5) / self._calculate_ma(close, 5)
            factors['VOLATILITY_10'] = self._calculate_rolling_std(close, 10) / self._calculate_ma(close, 10)
            factors['VOLATILITY_20'] = self._calculate_rolling_std(close, 20) / self._calculate_ma(close, 20)
            factors['VOLATILITY_60'] = self._calculate_rolling_std(close, 60) / self._calculate_ma(close, 60)

            # Parkinson波动率
            factors['PARKINSON_VOLATILITY'] = self._calculate_parkinson_volatility(high, low)

            # Garman-Klass波动率
            factors['GARMAN_KLASS_VOLATILITY'] = self._calculate_garman_klass_volatility(high, low, close)

            return factors

        except Exception as e:
            logger.error(f"波动率因子计算失败: {e}")
            return {}

    async def _calculate_momentum_factors(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """计算动量因子"""
        factors = {}

        try:
            close = data['close'].values

            # 动量因子
            factors['MOMENTUM_1'] = self._calculate_return(close, 1)
            factors['MOMENTUM_5'] = self._calculate_return(close, 5)
            factors['MOMENTUM_20'] = self._calculate_return(close, 20)
            factors['MOMENTUM_60'] = self._calculate_return(close, 60)
            factors['MOMENTUM_120'] = self._calculate_return(close, 120)

            # 反转因子
            factors['REVERSAL_1'] = -self._calculate_return(close, 1)
            factors['REVERSAL_5'] = -self._calculate_return(close, 5)
            factors['REVERSAL_20'] = -self._calculate_return(close, 20)

            # 趋势强度
            factors['TREND_STRENGTH'] = self._calculate_trend_strength(close)

            return factors

        except Exception as e:
            logger.error(f"动量因子计算失败: {e}")
            return {}

    def _calculate_parkinson_volatility(self, high: np.ndarray, low: np.ndarray, period: int = 20) -> np.ndarray:
        """计算Parkinson波动率"""
        if len(high) < period:
            return np.zeros_like(high)

        log_hl = np.log(high / low)
        parkinson_vol = np.zeros_like(high)

        for i in range(period-1, len(high)):
            start_idx = i - period + 1
            parkinson_vol[i] = np.sqrt(np.mean(log_hl[start_idx:i+1]**2) / (4 * np.log(2)))

        return parkinson_vol

    def _calculate_garman_klass_volatility(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 20) -> np.ndarray:
        """计算Garman-Klass波动率"""
        if len(high) < period:
            return np.zeros_like(high)

        log_hl = np.log(high / low)
        log_cc = np.log(close[1:] / close[:-1])

        gk_vol = np.zeros_like(high)

        for i in range(period, len(high)):
            start_idx = i - period + 1
            hl_component = 0.5 * np.mean(log_hl[start_idx:i+1]**2)
            cc_component = (2 * np.log(2) - 1) * np.mean(log_cc[start_idx-1:i]**2)
            gk_vol[i] = np.sqrt(hl_component - cc_component)

        return gk_vol

    def _calculate_trend_strength(self, prices: np.ndarray, period: int = 20) -> np.ndarray:
        """计算趋势强度"""
        if len(prices) < period:
            return np.zeros_like(prices)

        trend_strength = np.zeros_like(prices)

        for i in range(period-1, len(prices)):
            start_idx = i - period + 1
            price_window = prices[start_idx:i+1]

            # 计算线性回归斜率
            x = np.arange(len(price_window))
            slope = np.polyfit(x, price_window, 1)[0]

            # 计算R²
            y_pred = np.polyval([slope, price_window[0]], x)
            ss_res = np.sum((price_window - y_pred) ** 2)
            ss_tot = np.sum((price_window - np.mean(price_window)) ** 2)

            if ss_tot != 0:
                r_squared = 1 - (ss_res / ss_tot)
                trend_strength[i] = slope * r_squared  # 趋势强度 = 斜率 * 拟合度

        return trend_strength

    def _calculate_trix(self, prices: np.ndarray, period: int = 14) -> np.ndarray:
        """计算TRIX指标"""
        if len(prices) < period * 3:
            return np.zeros_like(prices)

        # 三重指数平滑
        ema1 = self._calculate_ema(prices, period)
        ema2 = self._calculate_ema(ema1, period)
        ema3 = self._calculate_ema(ema2, period)

        # 计算TRIX
        trix = np.zeros_like(prices)
        for i in range(1, len(ema3)):
            if ema3[i-1] != 0:
                trix[i] = (ema3[i] - ema3[i-1]) / ema3[i-1] * 10000

        return trix

    def _calculate_ultimate_oscillator(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> np.ndarray:
        """计算终极振荡器"""
        if len(close) < 28:
            return np.zeros_like(close)

        # 计算买压
        bp = close - np.minimum(low, np.roll(close, 1))
        bp[0] = 0

        # 计算真实范围
        tr = np.maximum(high - low,
                       np.maximum(np.abs(high - np.roll(close, 1)),
                                 np.abs(low - np.roll(close, 1))))
        tr[0] = high[0] - low[0]

        # 计算不同周期的平均值
        avg7_bp = self._calculate_ma(bp, 7)
        avg14_bp = self._calculate_ma(bp, 14)
        avg28_bp = self._calculate_ma(bp, 28)

        avg7_tr = self._calculate_ma(tr, 7)
        avg14_tr = self._calculate_ma(tr, 14)
        avg28_tr = self._calculate_ma(tr, 28)

        # 计算终极振荡器
        uo = np.zeros_like(close)
        for i in range(28, len(close)):
            if avg7_tr[i] != 0 and avg14_tr[i] != 0 and avg28_tr[i] != 0:
                uo[i] = 100 * ((4 * avg7_bp[i] / avg7_tr[i]) +
                              (2 * avg14_bp[i] / avg14_tr[i]) +
                              (avg28_bp[i] / avg28_tr[i])) / 7

        return uo

    def _calculate_commodity_channel_index(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 20) -> np.ndarray:
        """计算商品通道指数CCI"""
        if len(close) < period:
            return np.zeros_like(close)

        typical_price = (high + low + close) / 3
        ma_tp = self._calculate_ma(typical_price, period)

        cci = np.zeros_like(close)

        for i in range(period-1, len(close)):
            start_idx = i - period + 1
            mean_deviation = np.abs(typical_price[start_idx:i+1] - ma_tp[i]).mean()

            if mean_deviation != 0:
                cci[i] = (typical_price[i] - ma_tp[i]) / (0.015 * mean_deviation)

        return cci

    def _calculate_mass_index(self, high: np.ndarray, low: np.ndarray, period: int = 25) -> np.ndarray:
        """计算质量指数"""
        if len(high) < period:
            return np.zeros_like(high)

        # 计算高低价差的EMA
        hl_diff = high - low
        ema9 = self._calculate_ema(hl_diff, 9)
        ema9_ema9 = self._calculate_ema(ema9, 9)

        # 计算质量指数
        mass_index = np.zeros_like(high)

        for i in range(period-1, len(high)):
            if ema9_ema9[i] != 0:
                ratio_sum = 0
                for j in range(i - period + 1, i + 1):
                    if ema9_ema9[j] != 0:
                        ratio_sum += ema9[j] / ema9_ema9[j]
                mass_index[i] = ratio_sum

        return mass_index

    def _calculate_chaikin_oscillator(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, volume: np.ndarray) -> np.ndarray:
        """计算蔡金振荡器"""
        if len(close) < 10:
            return np.zeros_like(close)

        # 计算累积/分布线
        ad_line = np.zeros_like(close)

        for i in range(len(close)):
            if high[i] != low[i]:
                clv = ((close[i] - low[i]) - (high[i] - close[i])) / (high[i] - low[i])
                ad_line[i] = ad_line[i-1] + clv * volume[i] if i > 0 else clv * volume[i]

        # 计算蔡金振荡器
        ema3 = self._calculate_ema(ad_line, 3)
        ema10 = self._calculate_ema(ad_line, 10)

        return ema3 - ema10

    def _calculate_price_oscillator(self, prices: np.ndarray, fast: int = 12, slow: int = 26) -> np.ndarray:
        """计算价格振荡器"""
        if len(prices) < slow:
            return np.zeros_like(prices)

        ema_fast = self._calculate_ema(prices, fast)
        ema_slow = self._calculate_ema(prices, slow)

        return ((ema_fast - ema_slow) / ema_slow) * 100

    def _calculate_volume_oscillator(self, volume: np.ndarray, fast: int = 5, slow: int = 10) -> np.ndarray:
        """计算成交量振荡器"""
        if len(volume) < slow:
            return np.zeros_like(volume)

        ema_fast = self._calculate_ema(volume.astype(float), fast)
        ema_slow = self._calculate_ema(volume.astype(float), slow)

        return ((ema_fast - ema_slow) / ema_slow) * 100

# 全局Alpha158因子计算器实例
alpha158_calculator = Alpha158FactorCalculator()

__all__ = ['Alpha158FactorCalculator', 'alpha158_calculator']
