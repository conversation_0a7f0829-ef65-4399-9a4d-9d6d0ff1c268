#!/usr/bin/env python3
"""
基本面因子实现
包含财务指标、估值指标、成长性指标等
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)

class FundamentalFactorCalculator:
    """基本面因子计算器"""
    
    def __init__(self):
        self.factor_categories = {
            "valuation": ["PE", "PB", "PS", "PCF", "EV_EBITDA", "PEG"],
            "profitability": ["ROE", "ROA", "ROIC", "GROSS_MARGIN", "OPERATING_MARGIN", "NET_MARGIN"],
            "growth": ["REVENUE_GROWTH", "PROFIT_GROWTH", "EPS_GROWTH", "BOOK_VALUE_GROWTH"],
            "leverage": ["DEBT_TO_EQUITY", "DEBT_TO_ASSETS", "INTEREST_COVERAGE", "CURRENT_RATIO"],
            "efficiency": ["ASSET_TURNOVER", "INVENTORY_TURNOVER", "RECEIVABLES_TURNOVER", "WORKING_CAPITAL_TURNOVER"],
            "quality": ["ACCRUALS", "EARNINGS_QUALITY", "CASH_CONVERSION", "PIOTROSKI_SCORE"]
        }
    
    async def calculate_valuation_factors(self, financial_data: Dict[str, Any]) -> Dict[str, float]:
        """计算估值因子"""
        try:
            factors = {}
            
            # 获取基础数据
            market_cap = financial_data.get('market_cap', 0)
            shares_outstanding = financial_data.get('shares_outstanding', 1)
            price = financial_data.get('price', 0)
            
            # 财务数据
            net_income = financial_data.get('net_income', 0)
            book_value = financial_data.get('book_value', 0)
            revenue = financial_data.get('revenue', 0)
            cash_flow = financial_data.get('operating_cash_flow', 0)
            ebitda = financial_data.get('ebitda', 0)
            enterprise_value = financial_data.get('enterprise_value', market_cap)
            
            # 计算每股指标
            eps = net_income / shares_outstanding if shares_outstanding > 0 else 0
            book_value_per_share = book_value / shares_outstanding if shares_outstanding > 0 else 0
            revenue_per_share = revenue / shares_outstanding if shares_outstanding > 0 else 0
            cash_flow_per_share = cash_flow / shares_outstanding if shares_outstanding > 0 else 0
            
            # 估值比率
            factors['PE'] = price / eps if eps > 0 else np.inf
            factors['PB'] = price / book_value_per_share if book_value_per_share > 0 else np.inf
            factors['PS'] = price / revenue_per_share if revenue_per_share > 0 else np.inf
            factors['PCF'] = price / cash_flow_per_share if cash_flow_per_share > 0 else np.inf
            factors['EV_EBITDA'] = enterprise_value / ebitda if ebitda > 0 else np.inf
            
            # PEG比率
            earnings_growth = financial_data.get('earnings_growth', 0)
            factors['PEG'] = factors['PE'] / (earnings_growth * 100) if earnings_growth > 0 else np.inf
            
            return factors
            
        except Exception as e:
            logger.error(f"估值因子计算失败: {e}")
            return {}
    
    async def calculate_profitability_factors(self, financial_data: Dict[str, Any]) -> Dict[str, float]:
        """计算盈利能力因子"""
        try:
            factors = {}
            
            # 获取基础数据
            net_income = financial_data.get('net_income', 0)
            revenue = financial_data.get('revenue', 1)
            total_assets = financial_data.get('total_assets', 1)
            shareholders_equity = financial_data.get('shareholders_equity', 1)
            invested_capital = financial_data.get('invested_capital', total_assets)
            gross_profit = financial_data.get('gross_profit', 0)
            operating_income = financial_data.get('operating_income', 0)
            cost_of_goods_sold = financial_data.get('cost_of_goods_sold', 0)
            
            # 盈利能力指标
            factors['ROE'] = net_income / shareholders_equity if shareholders_equity > 0 else 0
            factors['ROA'] = net_income / total_assets if total_assets > 0 else 0
            factors['ROIC'] = net_income / invested_capital if invested_capital > 0 else 0
            factors['GROSS_MARGIN'] = gross_profit / revenue if revenue > 0 else 0
            factors['OPERATING_MARGIN'] = operating_income / revenue if revenue > 0 else 0
            factors['NET_MARGIN'] = net_income / revenue if revenue > 0 else 0
            
            return factors
            
        except Exception as e:
            logger.error(f"盈利能力因子计算失败: {e}")
            return {}
    
    async def calculate_growth_factors(self, financial_data: Dict[str, Any], historical_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算成长性因子"""
        try:
            factors = {}
            
            if len(historical_data) < 2:
                return {
                    'REVENUE_GROWTH': 0,
                    'PROFIT_GROWTH': 0,
                    'EPS_GROWTH': 0,
                    'BOOK_VALUE_GROWTH': 0
                }
            
            # 当前年度数据
            current = financial_data
            previous = historical_data[-2] if len(historical_data) >= 2 else historical_data[-1]
            
            # 计算增长率
            factors['REVENUE_GROWTH'] = self._calculate_growth_rate(
                current.get('revenue', 0), 
                previous.get('revenue', 1)
            )
            
            factors['PROFIT_GROWTH'] = self._calculate_growth_rate(
                current.get('net_income', 0), 
                previous.get('net_income', 1)
            )
            
            factors['EPS_GROWTH'] = self._calculate_growth_rate(
                current.get('eps', 0), 
                previous.get('eps', 1)
            )
            
            factors['BOOK_VALUE_GROWTH'] = self._calculate_growth_rate(
                current.get('book_value', 0), 
                previous.get('book_value', 1)
            )
            
            return factors
            
        except Exception as e:
            logger.error(f"成长性因子计算失败: {e}")
            return {}
    
    async def calculate_leverage_factors(self, financial_data: Dict[str, Any]) -> Dict[str, float]:
        """计算杠杆因子"""
        try:
            factors = {}
            
            # 获取基础数据
            total_debt = financial_data.get('total_debt', 0)
            shareholders_equity = financial_data.get('shareholders_equity', 1)
            total_assets = financial_data.get('total_assets', 1)
            interest_expense = financial_data.get('interest_expense', 0)
            ebit = financial_data.get('ebit', 1)
            current_assets = financial_data.get('current_assets', 0)
            current_liabilities = financial_data.get('current_liabilities', 1)
            
            # 杠杆指标
            factors['DEBT_TO_EQUITY'] = total_debt / shareholders_equity if shareholders_equity > 0 else np.inf
            factors['DEBT_TO_ASSETS'] = total_debt / total_assets if total_assets > 0 else 0
            factors['INTEREST_COVERAGE'] = ebit / interest_expense if interest_expense > 0 else np.inf
            factors['CURRENT_RATIO'] = current_assets / current_liabilities if current_liabilities > 0 else np.inf
            
            return factors
            
        except Exception as e:
            logger.error(f"杠杆因子计算失败: {e}")
            return {}
    
    async def calculate_efficiency_factors(self, financial_data: Dict[str, Any]) -> Dict[str, float]:
        """计算效率因子"""
        try:
            factors = {}
            
            # 获取基础数据
            revenue = financial_data.get('revenue', 0)
            total_assets = financial_data.get('total_assets', 1)
            inventory = financial_data.get('inventory', 1)
            accounts_receivable = financial_data.get('accounts_receivable', 1)
            working_capital = financial_data.get('working_capital', 1)
            cost_of_goods_sold = financial_data.get('cost_of_goods_sold', 0)
            
            # 效率指标
            factors['ASSET_TURNOVER'] = revenue / total_assets if total_assets > 0 else 0
            factors['INVENTORY_TURNOVER'] = cost_of_goods_sold / inventory if inventory > 0 else 0
            factors['RECEIVABLES_TURNOVER'] = revenue / accounts_receivable if accounts_receivable > 0 else 0
            factors['WORKING_CAPITAL_TURNOVER'] = revenue / working_capital if working_capital > 0 else 0
            
            return factors
            
        except Exception as e:
            logger.error(f"效率因子计算失败: {e}")
            return {}
    
    async def calculate_quality_factors(self, financial_data: Dict[str, Any], cash_flow_data: Dict[str, Any]) -> Dict[str, float]:
        """计算质量因子"""
        try:
            factors = {}
            
            # 获取基础数据
            net_income = financial_data.get('net_income', 0)
            operating_cash_flow = cash_flow_data.get('operating_cash_flow', 0)
            total_assets = financial_data.get('total_assets', 1)
            
            # 应计项目
            accruals = (net_income - operating_cash_flow) / total_assets if total_assets > 0 else 0
            factors['ACCRUALS'] = accruals
            
            # 盈利质量
            factors['EARNINGS_QUALITY'] = operating_cash_flow / net_income if net_income > 0 else 0
            
            # 现金转换
            factors['CASH_CONVERSION'] = operating_cash_flow / net_income if net_income > 0 else 0
            
            factors['PIOTROSKI_SCORE'] = await self._calculate_piotroski_score(financial_data)
            
            return factors
            
        except Exception as e:
            logger.error(f"质量因子计算失败: {e}")
            return {}
    
    def _calculate_growth_rate(self, current: float, previous: float) -> float:
        """计算增长率"""
        if previous == 0:
            return 0
        return (current - previous) / abs(previous)
    
    async def _calculate_piotroski_score(self, financial_data: Dict[str, Any]) -> int:
        pass  # 专业版模式
        try:
            score = 0
            
            # 盈利能力（4分）
            if financial_data.get('net_income', 0) > 0:
                score += 1
            if financial_data.get('operating_cash_flow', 0) > 0:
                score += 1
            if financial_data.get('roa_current', 0) > financial_data.get('roa_previous', 0):
                score += 1
            if financial_data.get('operating_cash_flow', 0) > financial_data.get('net_income', 0):
                score += 1
            
            # 杠杆、流动性和资金来源（3分）
            if financial_data.get('debt_ratio_current', 1) < financial_data.get('debt_ratio_previous', 1):
                score += 1
            if financial_data.get('current_ratio_current', 0) > financial_data.get('current_ratio_previous', 0):
                score += 1
            if financial_data.get('shares_outstanding_current', 1) <= financial_data.get('shares_outstanding_previous', 1):
                score += 1
            
            # 运营效率（2分）
            if financial_data.get('gross_margin_current', 0) > financial_data.get('gross_margin_previous', 0):
                score += 1
            if financial_data.get('asset_turnover_current', 0) > financial_data.get('asset_turnover_previous', 0):
                score += 1
            
            return score
            
        except Exception as e:
            logger.error(f"Piotroski评分计算失败: {e}")
            return 0

# 全局基本面因子计算器实例
fundamental_calculator = FundamentalFactorCalculator()

__all__ = ['FundamentalFactorCalculator', 'fundamental_calculator']
