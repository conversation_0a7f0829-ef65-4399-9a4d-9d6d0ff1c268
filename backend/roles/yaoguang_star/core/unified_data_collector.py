from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星统一数据收集器
整合所有数据源，提供统一的数据接口
"""

import asyncio
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import pandas as pd

logger = logging.getLogger(__name__)

class UnifiedDataCollector:
    """统一数据收集器"""
    
    def __init__(self):
        self.db_path = get_database_path("stock_database")
        self.data_sources = {
            "eastmoney": "东方财富API",
            "local_db": "本地数据库",
            "historical": "历史数据收集器"
        }
        
        logger.info("📊 瑶光星统一数据收集器初始化完成")
    
    async def get_unified_stock_data(self, stock_code: str, days: int = 30) -> Dict[str, Any]:
        """获取统一的股票数据"""
        
        try:
            # 优先从本地数据库获取
            local_data = await self._get_local_data(stock_code, days)
            
            if local_data and len(local_data) >= days * 0.8:  # 80%的数据完整性
                logger.info(f"✅ 从本地数据库获取 {stock_code} 数据: {len(local_data)}条")
                return {
                    "success": True,
                    "data_source": "local_db",
                    "stock_code": stock_code,
                    "data": local_data,
                    "record_count": len(local_data)
                }
            
            # 如果本地数据不足，尝试从东方财富API获取
            logger.info(f"本地数据不足，尝试从东方财富API获取 {stock_code} 数据")
            eastmoney_data = await self._get_eastmoney_data(stock_code, days)
            
            if eastmoney_data:
                # 保存到本地数据库
                await self._save_to_local_db(stock_code, eastmoney_data)
                
                return {
                    "success": True,
                    "data_source": "eastmoney",
                    "stock_code": stock_code,
                    "data": eastmoney_data,
                    "record_count": len(eastmoney_data)
                }
            
            # 如果都失败，返回错误
            return {
                "success": False,
                "error": f"无法获取 {stock_code} 的数据",
                "stock_code": stock_code
            }
            
        except Exception as e:
            logger.error(f"获取统一股票数据失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_code": stock_code
            }
    
    async def get_batch_stock_data(self, stock_codes: List[str], days: int = 30) -> Dict[str, Any]:
        """批量获取股票数据"""
        
        results = {}
        successful_count = 0
        failed_count = 0
        
        for stock_code in stock_codes:
            try:
                data_result = await self.get_unified_stock_data(stock_code, days)
                results[stock_code] = data_result
                
                if data_result["success"]:
                    successful_count += 1
                else:
                    failed_count += 1
                    
            except Exception as e:
                logger.error(f"批量获取 {stock_code} 数据失败: {e}")
                results[stock_code] = {
                    "success": False,
                    "error": str(e),
                    "stock_code": stock_code
                }
                failed_count += 1
        
        return {
            "success": True,
            "total_requested": len(stock_codes),
            "successful_count": successful_count,
            "failed_count": failed_count,
            "success_rate": successful_count / len(stock_codes) if stock_codes else 0,
            "results": results,
            "collection_time": datetime.now().isoformat()
        }
    
    async def _get_local_data(self, stock_code: str, days: int) -> List[Dict[str, Any]]:
        """从本地数据库获取数据"""
        
        try:
            if not Path(self.db_path).exists():
                logger.warning(f"本地数据库不存在: {self.db_path}")
                return []
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查询最近N天的数据
            query = """
                SELECT trade_date, open_price, high_price, low_price, close_price, 
                       volume, amount, change_percent, turnover_rate
                FROM daily_data 
                WHERE stock_code = ? 
                AND trade_date >= date('now', '-{} days')
                ORDER BY trade_date DESC
                LIMIT ?
            """.format(days)
            
            cursor.execute(query, (stock_code, days))
            rows = cursor.fetchall()
            conn.close()
            
            if not rows:
                return []
            
            # 转换为字典格式
            data = []
            for row in rows:
                data.append({
                    "date": row[0],
                    "open": float(row[1]) if row[1] else 0,
                    "high": float(row[2]) if row[2] else 0,
                    "low": float(row[3]) if row[3] else 0,
                    "close": float(row[4]) if row[4] else 0,
                    "volume": int(row[5]) if row[5] else 0,
                    "amount": float(row[6]) if row[6] else 0,
                    "change_percent": float(row[7]) if row[7] else 0,
                    "turnover_rate": float(row[8]) if row[8] else 0
                })
            
            return data
            
        except Exception as e:
            logger.error(f"从本地数据库获取数据失败: {e}")
            return []
    
    async def _get_eastmoney_data(self, stock_code: str, days: int) -> List[Dict[str, Any]]:
        """从东方财富API获取数据"""
        
        try:
            # 集成现有的数据管理服务
            from ..services.data_management_service import data_management_service
            
            # 获取历史数据
            result = await data_management_service.get_historical_data(stock_code, days)
            
            if result and isinstance(result, list):
                return result
            else:
                logger.warning(f"东方财富API返回数据格式异常: {type(result)}")
                return []
                
        except Exception as e:
            logger.error(f"从东方财富API获取数据失败: {e}")
            return []
    
    async def _save_to_local_db(self, stock_code: str, data: List[Dict[str, Any]]):
        """保存数据到本地数据库"""
        
        try:
            if not data:
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 确保表存在
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open_price REAL,
                    high_price REAL,
                    low_price REAL,
                    close_price REAL,
                    volume INTEGER,
                    amount REAL,
                    change_percent REAL,
                    turnover_rate REAL,
                    data_source TEXT DEFAULT 'unified_collector',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, trade_date)
                )
            """)
            
            # 插入数据
            saved_count = 0
            for record in data:
                try:
                    cursor.execute("""
                        INSERT OR REPLACE INTO daily_data 
                        (stock_code, trade_date, open_price, high_price, low_price, close_price,
                         volume, amount, change_percent, turnover_rate, data_source, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        stock_code,
                        record.get("date"),
                        record.get("open"),
                        record.get("high"),
                        record.get("low"),
                        record.get("close"),
                        record.get("volume"),
                        record.get("amount"),
                        record.get("change_percent"),
                        record.get("turnover_rate"),
                        "unified_collector",
                        datetime.now().isoformat()
                    ))
                    saved_count += 1
                except Exception as e:
                    logger.error(f"保存单条记录失败: {e}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ 保存 {stock_code} 数据到本地数据库: {saved_count}条")
            
        except Exception as e:
            logger.error(f"保存数据到本地数据库失败: {e}")
    
    async def get_data_quality_report(self) -> Dict[str, Any]:
        """获取数据质量报告"""
        
        try:
            if not Path(self.db_path).exists():
                return {
                    "success": False,
                    "error": "本地数据库不存在"
                }
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计数据
            cursor.execute("SELECT COUNT(*) FROM daily_data")
            total_records = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data")
            unique_stocks = cursor.fetchone()[0]
            
            cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM daily_data")
            date_range = cursor.fetchone()
            
            # 数据源统计
            cursor.execute("""
                SELECT data_source, COUNT(*) 
                FROM daily_data 
                GROUP BY data_source
            """)
            source_stats = dict(cursor.fetchall())
            
            # 最近更新统计
            cursor.execute("""
                SELECT COUNT(*) 
                FROM daily_data 
                WHERE updated_at >= datetime('now', '-1 day')
            """)
            recent_updates = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                "success": True,
                "total_records": total_records,
                "unique_stocks": unique_stocks,
                "date_range": {
                    "start": date_range[0],
                    "end": date_range[1]
                },
                "data_sources": source_stats,
                "recent_updates": recent_updates,
                "data_quality_score": self._calculate_quality_score(
                    total_records, unique_stocks, recent_updates
                ),
                "report_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"生成数据质量报告失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _calculate_quality_score(self, total_records: int, unique_stocks: int, recent_updates: int) -> float:
        """计算数据质量评分"""
        
        # 基础评分
        base_score = 0
        
        # 数据量评分 (40%)
        if total_records >= 1000000:  # 100万条记录
            base_score += 40
        elif total_records >= 100000:  # 10万条记录
            base_score += 30
        elif total_records >= 10000:   # 1万条记录
            base_score += 20
        else:
            base_score += 10
        
        # 股票覆盖度评分 (30%)
        if unique_stocks >= 5000:      # 5000只股票
            base_score += 30
        elif unique_stocks >= 1000:    # 1000只股票
            base_score += 20
        elif unique_stocks >= 100:     # 100只股票
            base_score += 10
        else:
            base_score += 5
        
        # 数据新鲜度评分 (30%)
        if recent_updates >= 1000:     # 最近1天更新1000条
            base_score += 30
        elif recent_updates >= 100:    # 最近1天更新100条
            base_score += 20
        elif recent_updates >= 10:     # 最近1天更新10条
            base_score += 10
        else:
            base_score += 5
        
        return min(100, base_score)
    
    async def sync_with_historical_collector(self) -> Dict[str, Any]:
        """与历史数据收集器同步"""
        
        try:
            # 检查历史数据收集器的进度
            quality_report = await self.get_data_quality_report()
            
            if not quality_report["success"]:
                return {
                    "success": False,
                    "error": "无法获取数据质量报告"
                }
            
            # 检查是否需要启动历史数据收集
            total_records = quality_report["total_records"]
            unique_stocks = quality_report["unique_stocks"]
            
            if total_records < 1000000 or unique_stocks < 3000:
                logger.info("数据量不足，建议启动历史数据收集器")
                
                return {
                    "success": True,
                    "sync_status": "data_insufficient",
                    "recommendation": "启动历史数据收集器",
                    "current_stats": {
                        "total_records": total_records,
                        "unique_stocks": unique_stocks
                    },
                    "target_stats": {
                        "total_records": 12000000,  # 目标1200万条
                        "unique_stocks": 5151       # 目标5151只股票
                    }
                }
            else:
                return {
                    "success": True,
                    "sync_status": "data_sufficient",
                    "message": "数据量充足，无需额外收集",
                    "current_stats": {
                        "total_records": total_records,
                        "unique_stocks": unique_stocks
                    }
                }
                
        except Exception as e:
            logger.error(f"与历史数据收集器同步失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# 全局实例
unified_data_collector = UnifiedDataCollector()
