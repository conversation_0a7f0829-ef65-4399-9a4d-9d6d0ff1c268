from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星每日股票数据更新服务
每天下午5点自动更新全部A股当天数据
"""

import asyncio
import logging
import schedule
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import threading

logger = logging.getLogger(__name__)

class DailyStockUpdateService:
    """每日股票数据更新服务"""
    
    def __init__(self):
        self.service_name = "瑶光星每日股票更新服务"
        self.version = "1.0.0"
        
        # 更新配置
        self.update_config = {
            "update_time": "17:00",  # 下午5点
            "enabled": True,
            "retry_attempts": 3,
            "retry_delay": 300,  # 5分钟重试间隔
            "weekend_skip": True,  # 跳过周末
            "holiday_skip": True   # 跳过节假日
        }
        
        # 更新状态
        self.last_update_time = None
        self.update_history = []
        self.is_updating = False
        
        # 调度器线程
        self.scheduler_thread = None
        self.scheduler_running = False
        
        logger.info(f"{self.service_name} v{self.version} 初始化完成")
    
    def start_scheduler(self):
        """启动定时调度器"""
        try:
            if self.scheduler_running:
                logger.warning("调度器已在运行中")
                return
            
            # 设置定时任务
            schedule.clear()
            schedule.every().day.at(self.update_config["update_time"]).do(self._scheduled_update)
            
            # 启动调度器线程
            self.scheduler_running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            logger.info(f"✅ 定时调度器已启动，每天 {self.update_config['update_time']} 自动更新股票数据")
            
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
    
    def stop_scheduler(self):
        """停止定时调度器"""
        try:
            self.scheduler_running = False
            schedule.clear()
            
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=5)
            
            logger.info("✅ 定时调度器已停止")
            
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
    
    def _run_scheduler(self):
        """运行调度器"""
        while self.scheduler_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"调度器运行错误: {e}")
                time.sleep(60)
    
    def _scheduled_update(self):
        """定时更新任务"""
        try:
            # 检查是否需要跳过
            if self._should_skip_update():
                logger.info("⏭️ 跳过今日更新（周末或节假日）")
                return
            
            # 异步执行更新
            asyncio.create_task(self.execute_daily_update())
            
        except Exception as e:
            logger.error(f"定时更新任务失败: {e}")
    
    def _should_skip_update(self) -> bool:
        """检查是否应该跳过更新"""
        now = datetime.now()
        
        # 检查是否为周末
        if self.update_config["weekend_skip"] and now.weekday() >= 5:  # 周六=5, 周日=6
            return True
        
        holidays = [
            "01-01",  # 元旦
            "02-11", "02-12", "02-13", "02-14", "02-15", "02-16", "02-17",  # 春节
            "04-05",  # 清明节
            "05-01",  # 劳动节
            "06-22",  # 端午节
            "09-29",  # 中秋节
            "10-01", "10-02", "10-03", "10-04", "10-05", "10-06", "10-07"   # 国庆节
        ]
        
        if self.update_config["holiday_skip"]:
            current_date = now.strftime("%m-%d")
            if current_date in holidays:
                return True
        
        return False
    
    async def execute_daily_update(self) -> Dict[str, Any]:
        """执行每日股票数据更新"""
        try:
            if self.is_updating:
                return {
                    "success": False,
                    "message": "更新已在进行中",
                    "timestamp": datetime.now().isoformat()
                }
            
            self.is_updating = True
            update_start_time = datetime.now()
            
            logger.info("🚀 开始每日股票数据更新")
            
            # 导入瑶光星数据管理服务
            from .data_management_service import data_management_service
            
            # 获取所有A股列表
            stock_list = await self._get_all_a_stocks()
            
            if not stock_list:
                raise Exception("获取A股列表失败")
            
            logger.info(f"📊 准备更新 {len(stock_list)} 只A股的当日数据")
            
            # 批量更新实时数据
            update_result = await data_management_service.batch_get_realtime_data(
                stock_codes=[stock["code"] for stock in stock_list]
            )
            
            # 保存更新结果
            update_record = {
                "update_date": update_start_time.strftime("%Y-%m-%d"),
                "update_time": update_start_time.isoformat(),
                "total_stocks": len(stock_list),
                "successful_updates": update_result.get("successful_count", 0),
                "failed_updates": update_result.get("failed_count", 0),
                "update_duration": (datetime.now() - update_start_time).total_seconds(),
                "data_source": "东方财富API",
                "update_type": "daily_scheduled"
            }
            
            self.update_history.append(update_record)
            self.last_update_time = update_start_time.isoformat()
            
            # 限制历史记录数量
            if len(self.update_history) > 30:  # 保留最近30天
                self.update_history = self.update_history[-30:]
            
            self.is_updating = False
            
            logger.info(f"✅ 每日股票数据更新完成")
            logger.info(f"📊 成功更新: {update_record['successful_updates']} 只")
            logger.info(f"❌ 失败更新: {update_record['failed_updates']} 只")
            logger.info(f"⏱️ 耗时: {update_record['update_duration']:.2f} 秒")
            
            return {
                "success": True,
                "message": "每日股票数据更新完成",
                "update_record": update_record,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.is_updating = False
            logger.error(f"每日股票数据更新失败: {e}")
            
            # 记录失败
            error_record = {
                "update_date": datetime.now().strftime("%Y-%m-%d"),
                "update_time": datetime.now().isoformat(),
                "error": str(e),
                "update_type": "daily_scheduled_failed"
            }
            self.update_history.append(error_record)
            
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _get_all_a_stocks(self) -> List[Dict[str, Any]]:
        """获取所有A股列表"""
        try:
            import sqlite3
            
            db_path = get_database_path("stock_database")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询所有A股
            cursor.execute("SELECT stock_code, stock_name, exchange FROM stock_info")
            rows = cursor.fetchall()
            
            stock_list = []
            for row in rows:
                stock_code = str(row[0])
                stock_name = str(row[1])
                exchange = str(row[2])
                
                # 转换股票代码格式
                if '.' not in stock_code:
                    if stock_code.startswith(('000', '002', '300')):
                        stock_code = f"{stock_code}.XSHE"
                    elif stock_code.startswith(('600', '601', '603', '605', '688')):
                        stock_code = f"{stock_code}.XSHG"
                
                stock_list.append({
                    "code": stock_code,
                    "name": stock_name,
                    "exchange": exchange
                })
            
            conn.close()
            
            logger.info(f"📊 获取到 {len(stock_list)} 只A股")
            return stock_list
            
        except Exception as e:
            logger.error(f"获取A股列表失败: {e}")
            return []
    
    def get_update_status(self) -> Dict[str, Any]:
        """获取更新状态"""
        return {
            "service_name": self.service_name,
            "version": self.version,
            "scheduler_running": self.scheduler_running,
            "is_updating": self.is_updating,
            "last_update_time": self.last_update_time,
            "update_config": self.update_config,
            "update_history_count": len(self.update_history),
            "next_update_time": self._get_next_update_time()
        }
    
    def _get_next_update_time(self) -> Optional[str]:
        """获取下次更新时间"""
        try:
            if not self.scheduler_running:
                return None
            
            # 计算下次更新时间
            now = datetime.now()
            today_update = datetime.combine(now.date(), datetime.strptime(self.update_config["update_time"], "%H:%M").time())
            
            if now < today_update:
                next_update = today_update
            else:
                next_update = today_update + timedelta(days=1)
            
            # 跳过周末
            while next_update.weekday() >= 5:
                next_update += timedelta(days=1)
            
            return next_update.isoformat()
            
        except Exception as e:
            logger.error(f"计算下次更新时间失败: {e}")
            return None
    
    def get_update_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取更新历史"""
        return self.update_history[-limit:] if self.update_history else []

# 创建全局实例
daily_stock_update_service = DailyStockUpdateService()
