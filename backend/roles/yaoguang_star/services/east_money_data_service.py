from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星东方财富数据服务
提供10年历史数据下载和实时数据获取功能
"""

import asyncio
import logging
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import requests
import json
import time

logger = logging.getLogger(__name__)

class EastMoneyDataService:
    """东方财富数据服务"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://quote.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*'
        })
        
        # 数据库路径
        self.db_path = get_database_path("stock_database")
        
        # 东方财富API配置
        self.base_url = "https://push2his.eastmoney.com/api/qt/stock/kline/get"
        self.realtime_url = "https://push2.eastmoney.com/api/qt/ulist/get"
        
    async def download_stock_historical_data(self, stock_code: str, start_date: str, 
                                           end_date: str, frequency: str = "daily") -> Dict[str, Any]:
        """下载股票历史数据"""
        try:
            logger.info(f"📈 开始下载 {stock_code} 的历史数据 ({start_date} 到 {end_date})")
            
            # 转换股票代码格式
            secid = self._convert_stock_code(stock_code)
            
            # 构建请求参数
            params = {
                'cb': 'jQuery112409953340710234_1640995200000',
                'secid': secid,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'fields1': 'f1,f2,f3,f4,f5,f6',
                'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                'klt': '101',  # 日线
                'fqt': '1',    # 前复权
                'beg': start_date.replace('-', ''),
                'end': end_date.replace('-', ''),
                '_': str(int(time.time() * 1000))
            }
            
            # 发送请求
            response = self.session.get(self.base_url, params=params, timeout=30)
            
            if response.status_code == 200:
                # 解析JSONP响应
                content = response.text
                if content.startswith('jQuery'):
                    # 提取JSON部分
                    start_idx = content.find('(') + 1
                    end_idx = content.rfind(')')
                    json_str = content[start_idx:end_idx]
                    data = json.loads(json_str)
                else:
                    data = response.json()
                
                # 解析数据
                if data.get('data') and data['data'].get('klines'):
                    klines = data['data']['klines']
                    
                    # 转换为DataFrame
                    df_data = []
                    for kline in klines:
                        parts = kline.split(',')
                        if len(parts) >= 11:
                            df_data.append({
                                'date': parts[0],
                                'open': float(parts[1]),
                                'close': float(parts[2]),
                                'high': float(parts[3]),
                                'low': float(parts[4]),
                                'volume': int(parts[5]),
                                'turnover': float(parts[6]),
                                'amplitude': float(parts[7]) if parts[7] else 0.0,
                                'change_pct': float(parts[8]) if parts[8] else 0.0,
                                'change_amount': float(parts[9]) if parts[9] else 0.0,
                                'turnover_rate': float(parts[10]) if parts[10] else 0.0
                            })
                    
                    if df_data:
                        df = pd.DataFrame(df_data)
                        df['date'] = pd.to_datetime(df['date'])
                        df.set_index('date', inplace=True)
                        
                        # 保存到数据库
                        await self._save_to_database(stock_code, df)
                        
                        logger.info(f"✅ {stock_code} 历史数据下载成功: {len(df)} 条记录")
                        
                        return {
                            "success": True,
                            "stock_code": stock_code,
                            "data": df,
                            "records_count": len(df),
                            "date_range": f"{start_date} 到 {end_date}",
                            "data_source": "东方财富API"
                        }
                    else:
                        logger.warning(f"⚠️ {stock_code} 无有效数据")
                        return {
                            "success": False,
                            "stock_code": stock_code,
                            "error": "无有效历史数据",
                            "data": pd.DataFrame()
                        }
                else:
                    logger.warning(f"⚠️ {stock_code} API返回数据格式异常")
                    return {
                        "success": False,
                        "stock_code": stock_code,
                        "error": "API返回数据格式异常",
                        "data": pd.DataFrame()
                    }
            else:
                logger.error(f"❌ {stock_code} HTTP请求失败: {response.status_code}")
                return {
                    "success": False,
                    "stock_code": stock_code,
                    "error": f"HTTP请求失败: {response.status_code}",
                    "data": pd.DataFrame()
                }
                
        except Exception as e:
            logger.error(f"❌ {stock_code} 历史数据下载失败: {e}")
            return {
                "success": False,
                "stock_code": stock_code,
                "error": str(e),
                "data": pd.DataFrame()
            }
    
    def _convert_stock_code(self, stock_code: str) -> str:
        """转换股票代码格式"""
        # 移除后缀
        if '.' in stock_code:
            code = stock_code.split('.')[0]
        else:
            code = stock_code
        
        # 根据代码前缀确定市场
        if code.startswith(('000', '002', '300')):
            return f"0.{code}"  # 深交所
        elif code.startswith(('600', '601', '603', '605', '688')):
            return f"1.{code}"  # 上交所
        else:
            return f"1.{code}"  # 默认上交所
    
    async def _save_to_database(self, stock_code: str, df: pd.DataFrame):
        """保存数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 创建表（如果不存在）
            table_name = f"stock_{stock_code.replace('.', '_')}"
            
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                date TEXT PRIMARY KEY,
                open REAL,
                close REAL,
                high REAL,
                low REAL,
                volume INTEGER,
                turnover REAL,
                amplitude REAL,
                change_pct REAL,
                change_amount REAL,
                turnover_rate REAL,
                created_time TEXT DEFAULT CURRENT_TIMESTAMP
            )
            """
            
            conn.execute(create_table_sql)
            
            # 插入数据
            for index, row in df.iterrows():
                insert_sql = f"""
                INSERT OR REPLACE INTO {table_name} 
                (date, open, close, high, low, volume, turnover, amplitude, change_pct, change_amount, turnover_rate)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                conn.execute(insert_sql, (
                    index.strftime('%Y-%m-%d'),
                    row['open'], row['close'], row['high'], row['low'],
                    row['volume'], row['turnover'], row['amplitude'],
                    row['change_pct'], row['change_amount'], row['turnover_rate']
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"💾 {stock_code} 数据已保存到数据库")
            
        except Exception as e:
            logger.error(f"❌ {stock_code} 数据库保存失败: {e}")
    
    async def get_realtime_data(self, stock_codes: List[str]) -> Dict[str, Any]:
        """获取实时数据"""
        try:
            # 转换股票代码
            secids = [self._convert_stock_code(code) for code in stock_codes]
            secids_str = ','.join(secids)
            
            params = {
                'cb': 'jQuery112409953340710234_1640995200000',
                'pn': '1',
                'pz': str(len(stock_codes)),
                'po': '1',
                'np': '1',
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': '2',
                'invt': '2',
                'fid': 'f3',
                'fs': secids_str,
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152',
                '_': str(int(time.time() * 1000))
            }
            
            response = self.session.get(self.realtime_url, params=params, timeout=10)
            
            if response.status_code == 200:
                # 解析JSONP响应
                content = response.text
                if content.startswith('jQuery'):
                    start_idx = content.find('(') + 1
                    end_idx = content.rfind(')')
                    json_str = content[start_idx:end_idx]
                    data = json.loads(json_str)
                else:
                    data = response.json()
                
                if data.get('data') and data['data'].get('diff'):
                    results = []
                    for item in data['data']['diff']:
                        results.append({
                            'code': item.get('f12', ''),
                            'name': item.get('f14', ''),
                            'price': item.get('f2', 0),
                            'change_pct': item.get('f3', 0),
                            'change_amount': item.get('f4', 0),
                            'volume': item.get('f5', 0),
                            'turnover': item.get('f6', 0),
                            'high': item.get('f15', 0),
                            'low': item.get('f16', 0),
                            'open': item.get('f17', 0),
                            'prev_close': item.get('f18', 0)
                        })
                    
                    return {
                        "success": True,
                        "data": results,
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    return {
                        "success": False,
                        "error": "API返回数据格式异常"
                    }
            else:
                return {
                    "success": False,
                    "error": f"HTTP请求失败: {response.status_code}"
                }
                
        except Exception as e:
            logger.error(f"❌ 实时数据获取失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# 创建全局实例
east_money_data_service = EastMoneyDataService()
