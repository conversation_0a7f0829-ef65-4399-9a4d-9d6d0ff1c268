#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强数据源服务
集成多种数据源：历史数据、基本面数据、市场情绪数据
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np
import sqlite3
from pathlib import Path
import json
import aiohttp

logger = logging.getLogger(__name__)

class EnhancedDataSourceService:
    """增强数据源服务"""
    
    def __init__(self):
        self.db_path = Path("data/complete_a_stock_library/complete_a_stock_data.db")
        self.sentiment_cache = {}
        self.fundamental_cache = {}
        
        # 初始化数据源
        self._init_data_sources()
        
        logger.info("增强数据源服务初始化完成")
    
    def _init_data_sources(self):
        """初始化数据源"""
        try:
            # 确保数据库表结构完整
            self._ensure_database_schema()
            
            # 初始化外部数据源连接
            self._init_external_sources()
            
        except Exception as e:
            logger.error(f"数据源初始化失败: {e}")
    
    def _ensure_database_schema(self):
        """确保数据库表结构完整"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建基本面数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS fundamental_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    report_date TEXT NOT NULL,
                    pe_ratio REAL,
                    pb_ratio REAL,
                    roe REAL,
                    revenue_growth REAL,
                    profit_growth REAL,
                    debt_ratio REAL,
                    current_ratio REAL,
                    market_cap REAL,
                    data_source TEXT DEFAULT 'manual',
                    created_time TEXT,
                    UNIQUE(stock_code, report_date)
                )
            ''')
            
            # 创建市场情绪数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_sentiment (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    sentiment_date TEXT NOT NULL,
                    sentiment_score REAL,
                    news_sentiment REAL,
                    social_sentiment REAL,
                    analyst_rating REAL,
                    institutional_activity REAL,
                    retail_activity REAL,
                    data_source TEXT DEFAULT 'aggregated',
                    created_time TEXT,
                    UNIQUE(stock_code, sentiment_date)
                )
            ''')
            
            # 创建技术指标数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS technical_indicators (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    indicator_date TEXT NOT NULL,
                    rsi_14 REAL,
                    macd_signal REAL,
                    bollinger_position REAL,
                    ma_alignment_score REAL,
                    volume_ratio REAL,
                    momentum_score REAL,
                    support_resistance_score REAL,
                    data_source TEXT DEFAULT 'calculated',
                    created_time TEXT,
                    UNIQUE(stock_code, indicator_date)
                )
            ''')
            
            # 创建行业数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS industry_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    industry_name TEXT NOT NULL,
                    data_date TEXT NOT NULL,
                    industry_pe REAL,
                    industry_pb REAL,
                    industry_growth REAL,
                    policy_impact_score REAL,
                    market_attention_score REAL,
                    capital_flow_score REAL,
                    data_source TEXT DEFAULT 'aggregated',
                    created_time TEXT,
                    UNIQUE(industry_name, data_date)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("数据库表结构检查完成")
            
        except Exception as e:
            logger.error(f"数据库表结构检查失败: {e}")
    
    def _init_external_sources(self):
        """初始化外部数据源"""
        try:
            # 这里可以初始化各种外部数据源的连接
            # 例如：财经API、新闻API、社交媒体API等
            
            self.external_sources = {
                "eastmoney": {"status": "available", "priority": 1},
                "tushare": {"status": "available", "priority": 2},
                "akshare": {"status": "available", "priority": 3},
                "news_api": {"status": "limited", "priority": 4},
                "social_api": {"status": "limited", "priority": 5}
            }
            
            logger.info("外部数据源初始化完成")
            
        except Exception as e:
            logger.error(f"外部数据源初始化失败: {e}")
    
    async def get_comprehensive_stock_data(self, stock_code: str, 
                                         start_date: str, end_date: str) -> Dict[str, Any]:
        """获取股票的综合数据"""
        
        try:
            comprehensive_data = {
                "stock_code": stock_code,
                "data_period": f"{start_date} ~ {end_date}",
                "historical_data": {},
                "fundamental_data": {},
                "technical_indicators": {},
                "market_sentiment": {},
                "industry_data": {},
                "data_quality": {}
            }
            
            # 1. 获取历史价格数据
            historical_data = await self._get_historical_price_data(stock_code, start_date, end_date)
            comprehensive_data["historical_data"] = historical_data
            
            # 2. 获取基本面数据
            fundamental_data = await self._get_fundamental_data(stock_code, start_date, end_date)
            comprehensive_data["fundamental_data"] = fundamental_data
            
            # 3. 计算技术指标
            technical_data = await self._calculate_technical_indicators(stock_code, historical_data)
            comprehensive_data["technical_indicators"] = technical_data
            
            # 4. 获取市场情绪数据
            sentiment_data = await self._get_market_sentiment_data(stock_code, start_date, end_date)
            comprehensive_data["market_sentiment"] = sentiment_data
            
            # 5. 获取行业数据
            industry_data = await self._get_industry_data(stock_code, start_date, end_date)
            comprehensive_data["industry_data"] = industry_data
            
            # 6. 评估数据质量
            data_quality = self._assess_data_quality(comprehensive_data)
            comprehensive_data["data_quality"] = data_quality
            
            return {
                "success": True,
                "data": comprehensive_data,
                "collection_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取综合股票数据失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_code": stock_code
            }
    
    async def _get_historical_price_data(self, stock_code: str, 
                                       start_date: str, end_date: str) -> Dict[str, Any]:
        """获取历史价格数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = '''
                SELECT trade_date, open_price, high_price, low_price, close_price, 
                       volume, amount, change_percent
                FROM daily_data 
                WHERE stock_code = ? AND trade_date BETWEEN ? AND ?
                ORDER BY trade_date
            '''
            
            df = pd.read_sql_query(query, conn, params=(stock_code, start_date, end_date))
            conn.close()
            
            if df.empty:
                return {"status": "no_data", "records": 0}
            
            # 计算基础统计指标
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df.set_index('trade_date', inplace=True)
            
            stats = {
                "records": len(df),
                "date_range": f"{df.index.min().strftime('%Y-%m-%d')} ~ {df.index.max().strftime('%Y-%m-%d')}",
                "price_range": {
                    "min": float(df['low_price'].min()),
                    "max": float(df['high_price'].max()),
                    "latest": float(df['close_price'].iloc[-1])
                },
                "volume_stats": {
                    "avg_volume": float(df['volume'].mean()),
                    "max_volume": float(df['volume'].max()),
                    "latest_volume": float(df['volume'].iloc[-1])
                },
                "return_stats": {
                    "total_return": float((df['close_price'].iloc[-1] / df['close_price'].iloc[0] - 1) * 100),
                    "volatility": float(df['close_price'].pct_change().std() * np.sqrt(252) * 100),
                    "max_single_day_gain": float(df['change_percent'].max()),
                    "max_single_day_loss": float(df['change_percent'].min())
                }
            }
            
            return {
                "status": "success",
                "data": stats,
                "raw_data": df.to_dict('records')[-30:]  # 只返回最近30天的详细数据
            }
            
        except Exception as e:
            logger.error(f"获取历史价格数据失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def _get_fundamental_data(self, stock_code: str, 
                                  start_date: str, end_date: str) -> Dict[str, Any]:
        """获取基本面数据"""
        try:
            # 首先尝试从数据库获取
            conn = sqlite3.connect(self.db_path)
            
            query = '''
                SELECT report_date, pe_ratio, pb_ratio, roe, revenue_growth, 
                       profit_growth, debt_ratio, current_ratio, market_cap
                FROM fundamental_data 
                WHERE stock_code = ? AND report_date BETWEEN ? AND ?
                ORDER BY report_date DESC
            '''
            
            df = pd.read_sql_query(query, conn, params=(stock_code, start_date, end_date))
            conn.close()
            
            if not df.empty:
                latest_data = df.iloc[0].to_dict()
                return {
                    "status": "success",
                    "source": "database",
                    "latest_data": latest_data,
                    "historical_records": len(df)
                }
            
            # 如果数据库没有数据，尝试生成基础的基本面数据
            estimated_data = await self._estimate_fundamental_data(stock_code)
            
            return {
                "status": "estimated",
                "source": "estimation",
                "latest_data": estimated_data,
                "note": "基于行业平均值估算"
            }
            
        except Exception as e:
            logger.error(f"获取基本面数据失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def _estimate_fundamental_data(self, stock_code: str) -> Dict[str, Any]:
        """估算基本面数据"""
        try:
            # 基于股票代码和行业的简单估算
            # 这里使用行业平均值作为估算基础
            
            industry_averages = {
                "银行": {"pe_ratio": 6.5, "pb_ratio": 0.8, "roe": 12.0},
                "地产": {"pe_ratio": 8.0, "pb_ratio": 1.2, "roe": 10.0},
                "科技": {"pe_ratio": 35.0, "pb_ratio": 4.5, "roe": 15.0},
                "消费": {"pe_ratio": 25.0, "pb_ratio": 3.0, "roe": 18.0},
                "医药": {"pe_ratio": 40.0, "pb_ratio": 5.0, "roe": 12.0},
                "制造": {"pe_ratio": 15.0, "pb_ratio": 1.8, "roe": 8.0},
                "默认": {"pe_ratio": 20.0, "pb_ratio": 2.5, "roe": 10.0}
            }
            
            # 简单的行业判断逻辑
            if stock_code.startswith("00000") or stock_code.startswith("60000"):
                industry = "银行"
            elif stock_code.startswith("00000"):
                industry = "地产"
            elif stock_code.startswith("30"):
                industry = "科技"
            else:
                industry = "默认"
            
            base_data = industry_averages.get(industry, industry_averages["默认"])
            
            # 添加一些随机变动
            estimated_data = {
                "pe_ratio": base_data["pe_ratio"] * (0.8 + np.random.random() * 0.4),
                "pb_ratio": base_data["pb_ratio"] * (0.8 + np.random.random() * 0.4),
                "roe": base_data["roe"] * (0.8 + np.random.random() * 0.4),
                "revenue_growth": np.random.normal(8.0, 5.0),  # 平均8%增长，标准差5%
                "profit_growth": np.random.normal(10.0, 8.0),  # 平均10%增长，标准差8%
                "debt_ratio": np.random.uniform(0.3, 0.7),     # 负债率30%-70%
                "current_ratio": np.random.uniform(1.0, 2.5),  # 流动比率1.0-2.5
                "market_cap": np.random.uniform(50, 5000),     # 市值50亿-5000亿
                "estimation_date": datetime.now().strftime('%Y-%m-%d'),
                "industry": industry
            }
            
            return estimated_data
            
        except Exception as e:
            logger.error(f"估算基本面数据失败: {e}")
            return {}
    
    async def _calculate_technical_indicators(self, stock_code: str, 
                                            historical_data: Dict) -> Dict[str, Any]:
        """计算技术指标"""
        try:
            if historical_data.get("status") != "success":
                return {"status": "no_base_data"}
            
            raw_data = historical_data.get("raw_data", [])
            if not raw_data:
                return {"status": "insufficient_data"}
            
            # 转换为DataFrame进行计算
            df = pd.DataFrame(raw_data)
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df.set_index('trade_date', inplace=True)
            df = df.sort_index()
            
            indicators = {}
            
            # RSI计算
            if len(df) >= 14:
                delta = df['close_price'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                indicators["rsi_14"] = float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else 50
            
            # 移动平均线
            if len(df) >= 20:
                df['ma5'] = df['close_price'].rolling(5).mean()
                df['ma20'] = df['close_price'].rolling(20).mean()
                
                # MA排列评分
                current_price = df['close_price'].iloc[-1]
                ma5 = df['ma5'].iloc[-1]
                ma20 = df['ma20'].iloc[-1]
                
                if current_price > ma5 > ma20:
                    ma_score = 1.0  # 多头排列
                elif current_price < ma5 < ma20:
                    ma_score = 0.0  # 空头排列
                else:
                    ma_score = 0.5  # 震荡
                
                indicators["ma_alignment_score"] = ma_score
            
            # 成交量比率
            if len(df) >= 10:
                recent_volume = df['volume'].iloc[-5:].mean()
                historical_volume = df['volume'].iloc[-20:-5].mean()
                volume_ratio = recent_volume / historical_volume if historical_volume > 0 else 1
                indicators["volume_ratio"] = float(volume_ratio)
            
            # 动量评分
            if len(df) >= 10:
                price_momentum = (df['close_price'].iloc[-1] / df['close_price'].iloc[-10] - 1) * 100
                indicators["momentum_score"] = float(price_momentum)
            
            return {
                "status": "success",
                "indicators": indicators,
                "calculation_date": datetime.now().strftime('%Y-%m-%d')
            }
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {"status": "error", "error": str(e)}

    async def _get_market_sentiment_data(self, stock_code: str,
                                       start_date: str, end_date: str) -> Dict[str, Any]:
        """获取市场情绪数据"""
        try:
            sentiment_data = {
                "status": "estimated",
                "source": "simulation",
                "sentiment_score": np.random.uniform(0.3, 0.7),  # 0-1之间的情绪分数
                "news_sentiment": np.random.uniform(0.4, 0.6),
                "social_sentiment": np.random.uniform(0.3, 0.8),
                "analyst_rating": np.random.uniform(0.5, 0.9),
                "institutional_activity": np.random.uniform(0.2, 0.8),
                "retail_activity": np.random.uniform(0.4, 0.9),
                "data_date": end_date,
                "note": "基于模拟数据生成的市场情绪指标"
            }

            return sentiment_data

        except Exception as e:
            logger.error(f"获取市场情绪数据失败: {e}")
            return {"status": "error", "error": str(e)}

    async def _get_industry_data(self, stock_code: str,
                               start_date: str, end_date: str) -> Dict[str, Any]:
        """获取行业数据"""
        try:
            industry_data = {
                "status": "estimated",
                "source": "simulation",
                "industry_pe": np.random.uniform(15, 35),
                "industry_pb": np.random.uniform(1.5, 4.0),
                "industry_growth": np.random.uniform(5, 15),
                "policy_impact_score": np.random.uniform(0.3, 0.8),
                "market_attention_score": np.random.uniform(0.4, 0.9),
                "capital_flow_score": np.random.uniform(0.2, 0.7),
                "data_date": end_date,
                "note": "基于模拟数据生成的行业指标"
            }

            return industry_data

        except Exception as e:
            logger.error(f"获取行业数据失败: {e}")
            return {"status": "error", "error": str(e)}

    def _assess_data_quality(self, comprehensive_data: Dict) -> Dict[str, Any]:
        """评估数据质量"""
        try:
            quality_scores = {}

            # 评估历史数据质量
            historical = comprehensive_data.get("historical_data", {})
            if historical.get("status") == "success":
                records = historical.get("data", {}).get("records", 0)
                quality_scores["historical_data"] = min(records / 252, 1.0)  # 252个交易日为满分
            else:
                quality_scores["historical_data"] = 0

            # 评估基本面数据质量
            fundamental = comprehensive_data.get("fundamental_data", {})
            if fundamental.get("status") in ["success", "estimated"]:
                quality_scores["fundamental_data"] = 0.8 if fundamental.get("status") == "success" else 0.5
            else:
                quality_scores["fundamental_data"] = 0

            # 评估技术指标质量
            technical = comprehensive_data.get("technical_indicators", {})
            if technical.get("status") == "success":
                indicator_count = len(technical.get("indicators", {}))
                quality_scores["technical_indicators"] = min(indicator_count / 5, 1.0)  # 5个指标为满分
            else:
                quality_scores["technical_indicators"] = 0

            # 评估市场情绪数据质量
            sentiment = comprehensive_data.get("market_sentiment", {})
            quality_scores["market_sentiment"] = 0.5 if sentiment.get("status") == "estimated" else 0

            # 评估行业数据质量
            industry = comprehensive_data.get("industry_data", {})
            quality_scores["industry_data"] = 0.5 if industry.get("status") == "estimated" else 0

            # 计算总体质量分数
            overall_quality = sum(quality_scores.values()) / len(quality_scores)

            return {
                "individual_scores": quality_scores,
                "overall_quality": overall_quality,
                "quality_level": "优秀" if overall_quality >= 0.8 else
                               "良好" if overall_quality >= 0.6 else
                               "一般" if overall_quality >= 0.4 else "较差",
                "assessment_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"评估数据质量失败: {e}")
            return {
                "individual_scores": {},
                "overall_quality": 0,
                "quality_level": "评估失败",
                "error": str(e)
            }

# 全局实例
enhanced_data_source_service = EnhancedDataSourceService()
