from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星学习优化服务
负责系统学习、优化和知识管理
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class LearningRecord:
    """学习记录"""
    record_id: str
    learning_type: str
    content: Dict[str, Any]
    performance_impact: float
    confidence: float
    created_time: datetime

class LearningOptimizationService:
    """学习优化服务"""
    
    def __init__(self):
        self.service_name = "瑶光星学习优化服务"
        self.version = "Professional v2.0"
        
        # 学习记录存储
        self.learning_records = {}
        self.record_counter = 0
        
        # 学习配置
        self.learning_config = {
            "learning_rate": 0.1,
            "memory_retention": 0.9,
            "optimization_threshold": 0.05,
            "max_records": 10000
        }
        
        # 性能指标
        self.performance_metrics = {
            "prediction_accuracy": 0.0,
            "strategy_success_rate": 0.0,
            "risk_control_effectiveness": 0.0,
            "overall_performance": 0.0
        }
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")

    async def optimize_stock_learning(self, stock_code: str, learning_context: Dict[str, Any]) -> Dict[str, Any]:
        """优化股票学习（真实实现）"""
        try:
            logger.info(f"🎓 开始优化股票学习: {stock_code}")

            # 从本地股票库获取历史数据
            historical_data = await self._get_local_historical_data(stock_code)

            if not historical_data:
                logger.warning(f"无法获取 {stock_code} 的历史数据")
                return {
                    "success": False,
                    "error": "无历史数据可用于学习优化"
                }

            # 分析学习上下文
            optimization_type = learning_context.get("optimization_type", "general")
            test_purpose = learning_context.get("test_purpose", "")

            # 执行真实的学习优化
            optimization_result = await self._perform_real_optimization(
                stock_code, historical_data, learning_context
            )

            # 创建学习记录
            learning_record = await self._create_learning_record(
                "stock_optimization",
                {
                    "stock_code": stock_code,
                    "optimization_type": optimization_type,
                    "data_points": len(historical_data),
                    "context": learning_context
                },
                optimization_result.get("performance_impact", 0.0)
            )

            logger.info(f"✅ {stock_code} 学习优化完成")

            return {
                "success": True,
                "stock_code": stock_code,
                "learning_result": {
                    "record_id": learning_record.record_id,
                    "performance_impact": learning_record.performance_impact,
                    "confidence": learning_record.confidence,
                    "created_time": learning_record.created_time.isoformat()
                },
                "optimization_suggestions": optimization_result.get("suggestions", []),
                "historical_analysis": optimization_result.get("historical_analysis", {}),
                "data_source": "本地股票库",
                "data_points": len(historical_data)
            }

        except Exception as e:
            logger.error(f"股票学习优化失败 {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_code": stock_code
            }

    async def _perform_real_optimization(self, stock_code: str, historical_data: List[Dict],
                                       learning_context: Dict[str, Any]) -> Dict[str, Any]:
        """执行真实的优化分析"""
        try:
            # 分析历史价格模式
            prices = [float(record.get('close_price', 0)) for record in historical_data if record.get('close_price')]
            volumes = [int(record.get('volume', 0)) for record in historical_data if record.get('volume')]

            if not prices:
                return {"performance_impact": 0.0, "suggestions": ["无有效价格数据"]}

            # 计算技术指标
            price_trend = self._analyze_price_trend(prices)
            volatility = self._calculate_volatility(prices)
            volume_pattern = self._analyze_volume_pattern(volumes) if volumes else {}

            # 生成优化建议
            suggestions = []
            performance_impact = 0.0

            # 基于价格趋势的建议
            if price_trend.get("trend") == "上涨":
                suggestions.append("价格呈上涨趋势，建议关注突破点位")
                performance_impact += 0.1
            elif price_trend.get("trend") == "下跌":
                suggestions.append("价格呈下跌趋势，建议等待反弹信号")
                performance_impact -= 0.05

            # 基于波动率的建议
            if volatility > 0.05:
                suggestions.append("波动率较高，建议加强风险控制")
                performance_impact -= 0.02
            elif volatility < 0.02:
                suggestions.append("波动率较低，可适当增加仓位")
                performance_impact += 0.03

            # 基于成交量的建议
            if volume_pattern.get("trend") == "放量":
                suggestions.append("成交量放大，关注资金流向")
                performance_impact += 0.05

            return {
                "performance_impact": performance_impact,
                "suggestions": suggestions,
                "historical_analysis": {
                    "price_trend": price_trend,
                    "volatility": volatility,
                    "volume_pattern": volume_pattern,
                    "data_quality": "良好" if len(prices) > 30 else "一般"
                }
            }

        except Exception as e:
            logger.error(f"真实优化分析失败: {e}")
            return {"performance_impact": 0.0, "suggestions": ["优化分析异常"]}

    def _analyze_price_trend(self, prices: List[float]) -> Dict[str, Any]:
        """分析价格趋势"""
        try:
            if len(prices) < 5:
                return {"trend": "数据不足", "strength": 0.0}

            # 计算短期和长期均价
            short_avg = sum(prices[:5]) / 5
            long_avg = sum(prices) / len(prices)

            # 判断趋势
            if short_avg > long_avg * 1.02:
                trend = "上涨"
                strength = (short_avg - long_avg) / long_avg
            elif short_avg < long_avg * 0.98:
                trend = "下跌"
                strength = (long_avg - short_avg) / long_avg
            else:
                trend = "横盘"
                strength = 0.0

            return {
                "trend": trend,
                "strength": min(strength, 1.0),
                "short_avg": short_avg,
                "long_avg": long_avg
            }

        except Exception as e:
            logger.error(f"价格趋势分析失败: {e}")
            return {"trend": "分析失败", "strength": 0.0}

    def _calculate_volatility(self, prices: List[float]) -> float:
        """计算波动率"""
        try:
            if len(prices) < 2:
                return 0.0

            # 计算收益率
            returns = []
            for i in range(1, len(prices)):
                if prices[i-1] > 0:
                    ret = (prices[i] - prices[i-1]) / prices[i-1]
                    returns.append(ret)

            if not returns:
                return 0.0

            # 计算标准差（波动率）
            mean_return = sum(returns) / len(returns)
            variance = sum((ret - mean_return) ** 2 for ret in returns) / len(returns)
            volatility = variance ** 0.5

            # 年化波动率（假设252个交易日）
            return volatility * (252 ** 0.5)

        except Exception as e:
            logger.error(f"计算波动率失败: {e}")
            return 0.2

    def _analyze_volume_pattern(self, volumes: List[int]) -> Dict[str, Any]:
        """分析成交量模式"""
        try:
            if len(volumes) < 5:
                return {"trend": "数据不足", "avg_volume": 0}

            # 计算平均成交量
            recent_avg = sum(volumes[:5]) / 5
            total_avg = sum(volumes) / len(volumes)

            # 判断成交量趋势
            if recent_avg > total_avg * 1.2:
                trend = "放量"
            elif recent_avg < total_avg * 0.8:
                trend = "缩量"
            else:
                trend = "正常"

            return {
                "trend": trend,
                "recent_avg": recent_avg,
                "total_avg": total_avg,
                "volume_ratio": recent_avg / total_avg if total_avg > 0 else 1.0
            }

        except Exception as e:
            logger.error(f"成交量模式分析失败: {e}")
            return {"trend": "分析失败", "avg_volume": 0}
    
    async def learn_from_workflow_result(self, workflow_result: Dict[str, Any]) -> Dict[str, Any]:
        """从工作流结果中学习（使用本地股票库数据）"""
        try:
            stock_code = workflow_result.get("stock_code")
            workflow_type = workflow_result.get("workflow_type", "comprehensive")

            # 从本地股票库获取历史数据进行学习
            historical_data = await self._get_local_historical_data(stock_code)

            # 提取学习要素（结合本地数据）
            learning_elements = self._extract_learning_elements(workflow_result, historical_data)

            # 评估结果质量（基于历史数据验证）
            result_quality = self._evaluate_result_quality(workflow_result, historical_data)

            # 创建学习记录
            learning_record = await self._create_learning_record(
                "workflow_result", learning_elements, result_quality
            )

            # 更新性能指标
            await self._update_performance_metrics(workflow_result, result_quality)

            # 基于本地数据生成优化建议
            optimization_suggestions = self._generate_optimization_suggestions(
                learning_elements, result_quality, historical_data
            )

            # 学习历史模式
            pattern_insights = await self._learn_historical_patterns(stock_code, historical_data)

            return {
                "success": True,
                "stock_code": stock_code,
                "learning_result": {
                    "record_id": learning_record.record_id,
                    "learning_type": learning_record.learning_type,
                    "performance_impact": learning_record.performance_impact,
                    "confidence": learning_record.confidence,
                    "created_time": learning_record.created_time.isoformat()
                },
                "optimization_suggestions": optimization_suggestions,
                "pattern_insights": pattern_insights,
                "performance_update": self.performance_metrics,
                "data_source": "本地股票库",
                "historical_records": len(historical_data),
                "learner": "瑶光星学习优化服务"
            }

        except Exception as e:
            logger.error(f"工作流结果学习失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _extract_learning_elements(self, workflow_result: Dict[str, Any]) -> Dict[str, Any]:
        """提取学习要素"""
        try:
            elements = {}
            
            # 提取决策相关信息
            if "decision" in workflow_result:
                decision = workflow_result["decision"]
                elements["decision_confidence"] = decision.get("confidence", 0.5)
                elements["decision_action"] = decision.get("action", "hold")
                elements["decision_reasoning"] = decision.get("reasoning", [])
            
            # 提取分析结果
            if "intelligence_analysis" in workflow_result:
                intelligence = workflow_result["intelligence_analysis"]
                elements["intelligence_sentiment"] = intelligence.get("sentiment_score", 0.5)
                elements["intelligence_confidence"] = intelligence.get("confidence", 0.5)
            
            if "technical_analysis" in workflow_result:
                technical = workflow_result["technical_analysis"]
                elements["technical_score"] = technical.get("technical_score", 0.5)
                elements["technical_signals"] = technical.get("signals", [])
            
            if "risk_assessment" in workflow_result:
                risk = workflow_result["risk_assessment"]
                elements["risk_level"] = risk.get("risk_level", "medium")
                elements["risk_score"] = risk.get("risk_score", 0.5)
            
            # 提取执行结果
            if "execution_result" in workflow_result:
                execution = workflow_result["execution_result"]
                elements["execution_success"] = execution.get("success", False)
                elements["execution_error"] = execution.get("error")
            
            return elements
            
        except Exception as e:
            logger.warning(f"学习要素提取失败: {e}")
            return {}
    
    def _evaluate_result_quality(self, workflow_result: Dict[str, Any]) -> float:
        """评估结果质量"""
        try:
            quality_score = 0.0
            weight_sum = 0.0
            
            # 评估决策质量
            if "decision" in workflow_result:
                decision = workflow_result["decision"]
                decision_confidence = decision.get("confidence", 0.5)
                quality_score += decision_confidence * 0.3
                weight_sum += 0.3
            
            # 评估分析质量
            if "intelligence_analysis" in workflow_result:
                intelligence = workflow_result["intelligence_analysis"]
                intelligence_confidence = intelligence.get("confidence", 0.5)
                quality_score += intelligence_confidence * 0.2
                weight_sum += 0.2
            
            if "technical_analysis" in workflow_result:
                technical = workflow_result["technical_analysis"]
                # 假设技术分析质量基于信号数量和一致性
                signals = technical.get("signals", [])
                signal_quality = min(len(signals) / 5, 1.0)  # 最多5个信号
                quality_score += signal_quality * 0.2
                weight_sum += 0.2
            
            # 评估风险控制质量
            if "risk_assessment" in workflow_result:
                risk = workflow_result["risk_assessment"]
                risk_confidence = risk.get("confidence", 0.5)
                quality_score += risk_confidence * 0.2
                weight_sum += 0.2
            
            # 评估执行质量
            if "execution_result" in workflow_result:
                execution = workflow_result["execution_result"]
                execution_success = 1.0 if execution.get("success", False) else 0.0
                quality_score += execution_success * 0.1
                weight_sum += 0.1
            
            # 计算最终质量评分
            final_quality = quality_score / weight_sum if weight_sum > 0 else 0.5
            
            return max(0.0, min(1.0, final_quality))
            
        except Exception as e:
            logger.warning(f"结果质量评估失败: {e}")
            return 0.5
    
    async def _create_learning_record(self, learning_type: str, content: Dict[str, Any], 
                                    performance_impact: float) -> LearningRecord:
        """创建学习记录"""
        try:
            self.record_counter += 1
            record_id = f"LR_{datetime.now().strftime('%Y%m%d')}_{self.record_counter:06d}"
            
            # 计算置信度
            confidence = self._calculate_learning_confidence(content, performance_impact)
            
            record = LearningRecord(
                record_id=record_id,
                learning_type=learning_type,
                content=content,
                performance_impact=performance_impact,
                confidence=confidence,
                created_time=datetime.now()
            )
            
            # 存储记录
            self.learning_records[record_id] = record
            
            # 清理旧记录
            await self._cleanup_old_records()
            
            logger.info(f"创建学习记录: {record_id}, 影响: {performance_impact:.3f}")
            
            return record
            
        except Exception as e:
            logger.error(f"创建学习记录失败: {e}")
            raise
    
    def _calculate_learning_confidence(self, content: Dict[str, Any], performance_impact: float) -> float:
        """计算学习置信度"""
        try:
            # 基于内容完整性和性能影响计算置信度
            content_completeness = len(content) / 10  # 假设10个要素为完整
            impact_significance = abs(performance_impact)
            
            confidence = (content_completeness * 0.6 + impact_significance * 0.4)
            
            return max(0.0, min(1.0, confidence))
            
        except Exception as e:
            logger.warning(f"学习置信度计算失败: {e}")
            return 0.5
    
    async def _update_performance_metrics(self, workflow_result: Dict[str, Any], quality: float):
        """更新性能指标"""
        try:
            learning_rate = self.learning_config["learning_rate"]
            
            # 更新预测准确性
            if "decision" in workflow_result:
                decision_confidence = workflow_result["decision"].get("confidence", 0.5)
                self.performance_metrics["prediction_accuracy"] = (
                    self.performance_metrics["prediction_accuracy"] * (1 - learning_rate) +
                    decision_confidence * learning_rate
                )
            
            # 更新策略成功率
            if "execution_result" in workflow_result:
                execution_success = 1.0 if workflow_result["execution_result"].get("success", False) else 0.0
                self.performance_metrics["strategy_success_rate"] = (
                    self.performance_metrics["strategy_success_rate"] * (1 - learning_rate) +
                    execution_success * learning_rate
                )
            
            # 更新风险控制有效性
            if "risk_assessment" in workflow_result:
                risk_confidence = workflow_result["risk_assessment"].get("confidence", 0.5)
                self.performance_metrics["risk_control_effectiveness"] = (
                    self.performance_metrics["risk_control_effectiveness"] * (1 - learning_rate) +
                    risk_confidence * learning_rate
                )
            
            # 更新整体性能
            self.performance_metrics["overall_performance"] = (
                self.performance_metrics["prediction_accuracy"] * 0.3 +
                self.performance_metrics["strategy_success_rate"] * 0.4 +
                self.performance_metrics["risk_control_effectiveness"] * 0.3
            )
            
            logger.info(f"性能指标更新完成，整体性能: {self.performance_metrics['overall_performance']:.3f}")
            
        except Exception as e:
            logger.warning(f"性能指标更新失败: {e}")
    
    def _generate_optimization_suggestions(self, learning_elements: Dict[str, Any], 
                                         quality: float) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        try:
            # 基于质量评分生成建议
            if quality < 0.6:
                suggestions.append("建议加强分析深度，提高决策质量")
            
            # 基于决策置信度
            decision_confidence = learning_elements.get("decision_confidence", 0.5)
            if decision_confidence < 0.7:
                suggestions.append("建议增强决策模型，提高决策置信度")
            
            # 基于风险评估
            risk_score = learning_elements.get("risk_score", 0.5)
            if risk_score > 0.7:
                suggestions.append("建议加强风险控制措施")
            
            # 基于执行结果
            execution_success = learning_elements.get("execution_success", False)
            if not execution_success:
                suggestions.append("建议优化交易执行策略")
            
            # 基于技术分析
            technical_score = learning_elements.get("technical_score", 0.5)
            if technical_score < 0.5:
                suggestions.append("建议改进技术分析方法")
            
            if not suggestions:
                suggestions.append("当前表现良好，继续保持")
            
            return suggestions[:5]  # 最多返回5个建议
            
        except Exception as e:
            logger.warning(f"优化建议生成失败: {e}")
            return ["建议进行系统检查和优化"]
    
    async def _cleanup_old_records(self):
        """清理旧记录"""
        try:
            max_records = self.learning_config["max_records"]
            
            if len(self.learning_records) > max_records:
                # 按时间排序，删除最旧的记录
                sorted_records = sorted(
                    self.learning_records.items(),
                    key=lambda x: x[1].created_time
                )
                
                records_to_delete = len(self.learning_records) - max_records
                for i in range(records_to_delete):
                    record_id = sorted_records[i][0]
                    del self.learning_records[record_id]
                
                logger.info(f"清理了{records_to_delete}条旧学习记录")
                
        except Exception as e:
            logger.warning(f"清理旧记录失败: {e}")
    
    def get_learning_summary(self) -> Dict[str, Any]:
        """获取学习总结"""
        try:
            total_records = len(self.learning_records)
            
            if total_records == 0:
                return {
                    "total_records": 0,
                    "performance_metrics": self.performance_metrics,
                    "learning_status": "初始状态"
                }
            
            # 计算平均性能影响
            avg_performance_impact = sum(
                record.performance_impact for record in self.learning_records.values()
            ) / total_records
            
            # 计算平均置信度
            avg_confidence = sum(
                record.confidence for record in self.learning_records.values()
            ) / total_records
            
            # 最近学习活动
            recent_records = sorted(
                self.learning_records.values(),
                key=lambda x: x.created_time,
                reverse=True
            )[:5]
            
            return {
                "total_records": total_records,
                "avg_performance_impact": round(avg_performance_impact, 3),
                "avg_confidence": round(avg_confidence, 3),
                "performance_metrics": {
                    k: round(v, 3) for k, v in self.performance_metrics.items()
                },
                "recent_learning": [
                    {
                        "record_id": record.record_id,
                        "learning_type": record.learning_type,
                        "performance_impact": round(record.performance_impact, 3),
                        "created_time": record.created_time.isoformat()
                    }
                    for record in recent_records
                ],
                "learning_status": "活跃学习中"
            }
            
        except Exception as e:
            logger.error(f"获取学习总结失败: {e}")
            return {
                "error": str(e),
                "learning_status": "错误状态"
            }
    
    def get_optimization_recommendations(self) -> List[str]:
        """获取优化建议"""
        try:
            recommendations = []
            
            # 基于整体性能
            overall_performance = self.performance_metrics["overall_performance"]
            if overall_performance < 0.6:
                recommendations.append("系统整体性能偏低，建议全面优化")
            elif overall_performance < 0.8:
                recommendations.append("系统性能良好，建议针对性优化")
            else:
                recommendations.append("系统性能优秀，保持当前策略")
            
            # 基于具体指标
            if self.performance_metrics["prediction_accuracy"] < 0.7:
                recommendations.append("预测准确性需要提升")
            
            if self.performance_metrics["strategy_success_rate"] < 0.6:
                recommendations.append("策略成功率偏低，需要调整策略")
            
            if self.performance_metrics["risk_control_effectiveness"] < 0.8:
                recommendations.append("风险控制需要加强")
            
            return recommendations

        except Exception as e:
            logger.error(f"获取优化建议失败: {e}")
            return ["建议进行系统检查"]

    async def _get_local_historical_data(self, stock_code: str) -> List[Dict]:
        """从本地股票库获取历史数据"""
        try:
            import sqlite3
            from pathlib import Path

            # 本地股票库路径 - 使用统一的数据库路径
            db_path = Path(get_database_path("stock_database"))

            if not db_path.exists():
                logger.warning(f"本地股票库不存在: {db_path}")
                return []

            # 转换股票代码格式 - 支持多种格式
            def clean_stock_code(code: str) -> str:
                """清理股票代码，移除交易所后缀"""
                if '.' in code:
                    return code.split('.')[0]
                return code

            clean_code = clean_stock_code(stock_code)

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 尝试多种股票代码格式查询
            possible_codes = [
                clean_code,           # 000521
                stock_code,           # 原始格式
                f"{clean_code}.XSHE", # 000521.XSHE
                f"{clean_code}.XSHG"  # 000521.XSHG
            ]

            historical_data = []
            for code in possible_codes:
                cursor.execute('''
                    SELECT * FROM daily_data
                    WHERE stock_code = ? OR stock_code LIKE ?
                    ORDER BY trade_date DESC
                    LIMIT 90
                ''', (code, f"{clean_code}%"))

                columns = [description[0] for description in cursor.description]
                rows = cursor.fetchall()

                if rows:
                    # 转换为字典列表
                    for row in rows:
                        record = dict(zip(columns, row))
                        historical_data.append(record)
                    break  # 找到数据就退出循环

            conn.close()

            logger.info(f"从本地库获取 {stock_code} (清理后: {clean_code}) 历史数据: {len(historical_data)} 条")
            return historical_data

        except Exception as e:
            logger.error(f"获取本地历史数据失败: {e}")
            return []

    async def _learn_historical_patterns(self, stock_code: str, historical_data: List[Dict]) -> Dict[str, Any]:
        """学习历史模式"""
        try:
            if len(historical_data) < 10:
                return {"pattern_count": 0, "insights": []}

            insights = []

            # 分析价格趋势
            prices = [float(record.get('close_price', 0)) for record in historical_data]
            if len(prices) >= 5:
                recent_trend = "上涨" if prices[0] > prices[4] else "下跌"
                avg_price = sum(prices) / len(prices)
                volatility = self._calculate_volatility(prices)

                insights.append(f"近期趋势: {recent_trend}")
                insights.append(f"平均价格: {avg_price:.2f}")
                insights.append(f"波动率: {volatility:.3f}")

            # 分析成交量模式
            volumes = [int(record.get('volume', 0)) for record in historical_data]
            if volumes:
                avg_volume = sum(volumes) / len(volumes)
                insights.append(f"平均成交量: {avg_volume:.0f}")

            # 分析支撑阻力位
            support_resistance = self._find_support_resistance(prices)
            insights.extend(support_resistance)

            return {
                "pattern_count": len(insights),
                "insights": insights,
                "data_period": f"{len(historical_data)}天",
                "analysis_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"历史模式学习失败: {e}")
            return {"pattern_count": 0, "insights": []}

# 全局学习优化服务实例
learning_optimization_service = LearningOptimizationService()

__all__ = ["learning_optimization_service", "LearningOptimizationService", "LearningRecord"]
