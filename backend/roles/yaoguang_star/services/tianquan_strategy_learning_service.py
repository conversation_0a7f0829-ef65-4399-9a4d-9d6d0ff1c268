#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天权星战法学习服务
专门用于学习模式中测试和优化天权星的各种战法
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class TianquanStrategyLearningService:
    """天权星战法学习服务"""
    
    def __init__(self):
        self.service_name = "天权星战法学习服务"
        self.version = "1.0.0"
        
        # 天权星战法库
        self.tianquan_strategies = {
            "trend_following": {
                "name": "趋势跟踪战法",
                "description": "基于移动平均线和趋势指标的跟踪策略",
                "parameters": {
                    "ma_short": 5,
                    "ma_long": 20,
                    "trend_threshold": 0.02
                },
                "success_rate": 0.0,
                "avg_return": 0.0,
                "max_drawdown": 0.0,
                "test_count": 0
            },
            "mean_reversion": {
                "name": "均值回归战法", 
                "description": "基于价格偏离均值的回归策略",
                "parameters": {
                    "lookback_period": 20,
                    "deviation_threshold": 2.0,
                    "exit_threshold": 0.5
                },
                "success_rate": 0.0,
                "avg_return": 0.0,
                "max_drawdown": 0.0,
                "test_count": 0
            },
            "momentum_breakout": {
                "name": "动量突破战法",
                "description": "基于价格和成交量突破的动量策略",
                "parameters": {
                    "breakout_period": 10,
                    "volume_threshold": 1.5,
                    "price_threshold": 0.03
                },
                "success_rate": 0.0,
                "avg_return": 0.0,
                "max_drawdown": 0.0,
                "test_count": 0
            },
            "support_resistance": {
                "name": "支撑阻力战法",
                "description": "基于关键支撑阻力位的交易策略",
                "parameters": {
                    "lookback_period": 30,
                    "support_strength": 3,
                    "resistance_strength": 3
                },
                "success_rate": 0.0,
                "avg_return": 0.0,
                "max_drawdown": 0.0,
                "test_count": 0
            },
            "pattern_recognition": {
                "name": "形态识别战法",
                "description": "基于K线形态和技术图形的识别策略",
                "parameters": {
                    "pattern_period": 15,
                    "confirmation_period": 3,
                    "pattern_strength": 0.7
                },
                "success_rate": 0.0,
                "avg_return": 0.0,
                "max_drawdown": 0.0,
                "test_count": 0
            }
        }
        
        # 学习历史记录
        self.learning_history = []
        self.strategy_performance_history = {}
        
        logger.info(f"{self.service_name} v{self.version} 初始化完成")
    
    async def conduct_strategy_learning(self, stock_code: str, historical_data: pd.DataFrame) -> Dict[str, Any]:
        """对单只股票进行完整的战法学习"""
        try:
            logger.info(f"🎓 开始对 {stock_code} 进行天权星战法学习")
            
            learning_results = {
                "stock_code": stock_code,
                "learning_start_time": datetime.now().isoformat(),
                "data_period": f"{historical_data.index[0]} 到 {historical_data.index[-1]}",
                "total_trading_days": len(historical_data),
                "strategy_results": {},
                "best_strategy": None,
                "learning_insights": []
            }
            
            # 对每种战法进行测试
            for strategy_id, strategy_config in self.tianquan_strategies.items():
                logger.info(f"📊 测试战法: {strategy_config['name']}")
                
                # 练习模式：测试战法效果
                practice_result = await self._practice_mode_test(
                    strategy_id, strategy_config, stock_code, historical_data
                )
                
                # 研究模式：优化战法参数
                research_result = await self._research_mode_optimize(
                    strategy_id, strategy_config, stock_code, historical_data, practice_result
                )
                
                # 合并结果
                strategy_result = {
                    "strategy_name": strategy_config['name'],
                    "practice_result": practice_result,
                    "research_result": research_result,
                    "final_performance": research_result.get("optimized_performance", practice_result.get("performance", {}))
                }
                
                learning_results["strategy_results"][strategy_id] = strategy_result
                
                # 更新战法统计
                self._update_strategy_statistics(strategy_id, strategy_result["final_performance"])
            
            # 找出最佳战法
            best_strategy = self._find_best_strategy(learning_results["strategy_results"])
            learning_results["best_strategy"] = best_strategy
            
            # 生成学习洞察
            insights = self._generate_learning_insights(stock_code, learning_results)
            learning_results["learning_insights"] = insights
            
            # 记录学习历史
            learning_results["learning_end_time"] = datetime.now().isoformat()
            self.learning_history.append(learning_results)
            
            logger.info(f"✅ {stock_code} 战法学习完成，最佳战法: {best_strategy['strategy_name'] if best_strategy else 'None'}")
            
            return learning_results
            
        except Exception as e:
            logger.error(f"战法学习失败 {stock_code}: {e}")
            return {
                "stock_code": stock_code,
                "error": str(e),
                "learning_status": "failed"
            }
    
    async def _practice_mode_test(self, strategy_id: str, strategy_config: Dict[str, Any], 
                                 stock_code: str, data: pd.DataFrame) -> Dict[str, Any]:
        """练习模式：测试战法在历史数据上的表现"""
        try:
            logger.debug(f"🏃 练习模式测试: {strategy_config['name']}")
            
            # 调用天权星战法执行
            tianquan_result = await self._call_tianquan_strategy(
                strategy_id, strategy_config, stock_code, data, mode="practice"
            )
            
            # 计算绩效指标
            performance = self._calculate_strategy_performance(tianquan_result, data)
            
            practice_result = {
                "mode": "practice",
                "strategy_id": strategy_id,
                "execution_result": tianquan_result,
                "performance": performance,
                "test_period": f"{data.index[0]} 到 {data.index[-1]}",
                "total_trades": tianquan_result.get("total_trades", 0),
                "win_rate": tianquan_result.get("win_rate", 0.0),
                "practice_time": datetime.now().isoformat()
            }
            
            logger.debug(f"📊 练习结果: 收益率 {performance.get('total_return', 0):.2%}, 胜率 {tianquan_result.get('win_rate', 0):.2%}")
            
            return practice_result
            
        except Exception as e:
            logger.error(f"练习模式测试失败 {strategy_id}: {e}")
            return {
                "mode": "practice",
                "strategy_id": strategy_id,
                "error": str(e),
                "performance": {}
            }
    
    async def _research_mode_optimize(self, strategy_id: str, strategy_config: Dict[str, Any],
                                    stock_code: str, data: pd.DataFrame, practice_result: Dict[str, Any]) -> Dict[str, Any]:
        """研究模式：优化战法参数以获得更高收益"""
        try:
            logger.debug(f"🔬 研究模式优化: {strategy_config['name']}")
            
            base_performance = practice_result.get("performance", {}).get("total_return", 0)
            best_performance = base_performance
            best_parameters = strategy_config["parameters"].copy()
            optimization_history = []
            
            # 参数优化循环
            for optimization_round in range(3):  # 进行3轮优化
                logger.debug(f"🔧 优化轮次 {optimization_round + 1}")
                
                # 生成参数变体
                parameter_variants = self._generate_parameter_variants(strategy_id, best_parameters)
                
                for variant_params in parameter_variants:
                    # 创建变体配置
                    variant_config = strategy_config.copy()
                    variant_config["parameters"] = variant_params
                    
                    # 测试变体
                    variant_result = await self._call_tianquan_strategy(
                        strategy_id, variant_config, stock_code, data, mode="research"
                    )
                    
                    variant_performance = self._calculate_strategy_performance(variant_result, data)
                    variant_return = variant_performance.get("total_return", 0)
                    
                    optimization_history.append({
                        "round": optimization_round + 1,
                        "parameters": variant_params,
                        "performance": variant_performance,
                        "total_return": variant_return
                    })
                    
                    # 更新最佳参数
                    if variant_return > best_performance:
                        best_performance = variant_return
                        best_parameters = variant_params
                        logger.debug(f"🎯 发现更优参数: 收益率 {variant_return:.2%}")
            
            # 使用最优参数进行最终测试
            final_config = strategy_config.copy()
            final_config["parameters"] = best_parameters
            
            final_result = await self._call_tianquan_strategy(
                strategy_id, final_config, stock_code, data, mode="final"
            )
            
            final_performance = self._calculate_strategy_performance(final_result, data)
            
            research_result = {
                "mode": "research",
                "strategy_id": strategy_id,
                "original_parameters": strategy_config["parameters"],
                "optimized_parameters": best_parameters,
                "optimization_history": optimization_history,
                "optimized_performance": final_performance,
                "improvement": final_performance.get("total_return", 0) - base_performance,
                "optimization_rounds": 3,
                "research_time": datetime.now().isoformat()
            }
            
            logger.debug(f"🎯 研究结果: 优化后收益率 {final_performance.get('total_return', 0):.2%}, 提升 {research_result['improvement']:.2%}")
            
            return research_result
            
        except Exception as e:
            logger.error(f"研究模式优化失败 {strategy_id}: {e}")
            return {
                "mode": "research",
                "strategy_id": strategy_id,
                "error": str(e),
                "optimized_performance": practice_result.get("performance", {})
            }
    
    async def _call_tianquan_strategy(self, strategy_id: str, strategy_config: Dict[str, Any],
                                    stock_code: str, data: pd.DataFrame, mode: str = "practice") -> Dict[str, Any]:
        """调用天权星真实战法系统+四星辩论+深度分析"""
        try:
            logger.info(f"👑 启动天权星真实战法分析: {stock_code} - {strategy_config['name']}")

            # 1. 调用天权星战略决策服务
            from roles.tianquan_star.services.strategic_decision_service import StrategicDecisionService

            strategic_service = StrategicDecisionService()

            # 构建市场上下文
            market_context = {
                "trend": self._analyze_trend(data),
                "volatility": self._calculate_volatility(data),
                "volume_pattern": self._analyze_volume_pattern(data),
                "price_momentum": self._calculate_momentum(data)
            }

            # 天权星战法决策
            strategy_decision = await strategic_service.decide_trading_strategy(
                stock_code=stock_code,
                market_context=market_context,
                risk_preference="moderate"
            )

            logger.info(f"👑 天权星战法选择: {strategy_decision.get('strategy_type', 'unknown')}")

            # 2. 调用统一四星智能体辩论系统进行深度分析
            try:
                from roles.tianquan_star.services.enhanced_four_stars_debate import enhanced_four_stars_debate

                # 启动四星智能体辩论（天玑、天璇、天枢、玉衡辩论，天权决策）
                debate_result = await enhanced_four_stars_debate.start_four_star_debate(
                    topic=f"{stock_code} 天权星战法学习分析",
                    stock_code=stock_code,
                    context={
                        "tianquan_strategy": strategy_decision,
                        "market_context": market_context,
                        "learning_mode": mode,
                        "data_period": f"{len(data)}天历史数据"
                    }
                )

                logger.info(f"✅ 统一四星智能体辩论完成")

            except Exception as debate_error:
                logger.warning(f"四星智能体辩论失败: {debate_error}")
                # 返回默认结果
                debate_result = {
                    "success": False,
                    "error": str(debate_error),
                    "final_decision": {
                        "final_recommendation": "HOLD",
                        "reasoning": "辩论系统异常，采用保守策略",
                        "confidence": 0.5
                    }
                }

            logger.info(f"🗣️ 增强四星辩论完成: 共识水平 {debate_result.get('consensus_level', 0):.2f}")

            # 3. 天权星综合决策
            final_decision = debate_result.get('final_decision', {})

            # 确保有有效的决策
            if not final_decision:
                # 基于天权星战法决策创建默认决策
                strategy_type = strategy_decision.get('strategy_type', 'longtou')
                confidence = strategy_decision.get('confidence', 0.7)

                final_decision = {
                    'final_recommendation': 'buy' if confidence > 0.6 else 'hold',
                    'final_confidence': confidence,
                    'decision_rationale': f"基于{strategy_type}战法的决策"
                }

                logger.info(f"👑 使用天权星战法默认决策")

            decision_action = final_decision.get('final_recommendation', 'hold')
            confidence = final_decision.get('final_confidence', 0.5)

            logger.info(f"👑 天权星最终决策: {decision_action} (信心度: {confidence:.2%})")

            # 4. 执行十年历史数据深度回测
            if decision_action in ['buy', 'strong_buy', 'sell', 'strong_sell', 'BUY', 'SELL']:
                trading_action = 'buy' if decision_action.upper() in ['BUY', 'STRONG_BUY'] else 'sell'

                # 使用天权星真实战法进行回测
                trading_result = await self._execute_tianquan_strategy_backtest(
                    stock_code, data, strategy_decision, trading_action, confidence
                )

                # 5. 深度分析涨跌原理
                price_analysis = await self._analyze_ten_year_price_patterns(stock_code, data)

                # 6. 练习模式 → 研究模式分析
                learning_analysis = await self._conduct_practice_to_research_analysis(
                    stock_code, data, strategy_decision, trading_result, price_analysis
                )

                # 7. 整合完整结果
                complete_result = {
                    "strategy_id": strategy_id,
                    "tianquan_strategy": strategy_decision,
                    "debate_result": debate_result,
                    "final_decision": final_decision,
                    "decision_action": decision_action,
                    "confidence": confidence,
                    "trading_result": trading_result,
                    "price_analysis": price_analysis,
                    "learning_analysis": learning_analysis,
                    "total_trades": trading_result.get("total_trades", 0),
                    "win_rate": trading_result.get("win_rate", 0.0),
                    "total_return": trading_result.get("total_return", 0.0),
                    "execution_mode": "tianquan_real_strategy_analysis",
                    "execution_time": datetime.now().isoformat(),
                    "insights": self._generate_comprehensive_insights(
                        strategy_decision, debate_result, trading_result, price_analysis
                    )
                }

                logger.info(f"💰 天权星战法回测完成: 总收益率 {trading_result.get('total_return', 0):.2%}")

                return complete_result

            else:
                # 持有决策
                return {
                    "strategy_id": strategy_id,
                    "tianquan_strategy": strategy_decision,
                    "debate_result": debate_result,
                    "final_decision": final_decision,
                    "decision_action": decision_action,
                    "confidence": confidence,
                    "total_trades": 0,
                    "win_rate": 0.0,
                    "total_return": 0.0,
                    "execution_mode": "tianquan_strategy_hold",
                    "execution_time": datetime.now().isoformat(),
                    "hold_reasoning": final_decision.get("decision_rationale", "天权星建议观望")
                }

        except Exception as e:
            logger.error(f"天权星真实战法分析失败: {e}")
            return {
                "strategy_id": strategy_id,
                "error": f"天权星战法分析失败: {e}",
                "total_trades": 0,
                "win_rate": 0.0,
                "total_return": 0.0,
                "execution_mode": "failed"
            }

    def _extract_debate_insights(self, debate_result: Dict[str, Any]) -> Dict[str, Any]:
        """从四星辩论结果中提取洞察"""
        try:
            insights = {
                "debate_rounds": debate_result.get("round", 0),
                "consensus_reached": debate_result.get("consensus_reached", False),
                "star_positions": {},
                "key_arguments": [],
                "risk_assessment": "",
                "technical_outlook": "",
                "sentiment_analysis": "",
                "execution_recommendation": ""
            }

            # 提取各星观点
            participants = debate_result.get("participants", {})

            for star_name, star_info in participants.items():
                if star_info.get("arguments"):
                    latest_argument = star_info["arguments"][-1] if star_info["arguments"] else {}
                    insights["star_positions"][star_name] = {
                        "stance": star_info.get("stance", "neutral"),
                        "position": latest_argument.get("position", "无观点"),
                        "confidence": latest_argument.get("confidence", 0.5),
                        "reasoning": latest_argument.get("reasoning", "")
                    }

            # 提取关键论点
            debate_history = debate_result.get("debate_history", [])
            for round_data in debate_history:
                round_arguments = round_data.get("arguments", {})
                for star, argument in round_arguments.items():
                    if argument.get("confidence", 0) > 0.7:  # 高信心度论点
                        insights["key_arguments"].append({
                            "star": star,
                            "position": argument.get("position", ""),
                            "confidence": argument.get("confidence", 0),
                            "round": round_data.get("round", 0)
                        })

            # 提取专业分析
            tianji_pos = insights["star_positions"].get("tianji_star", {})
            tianxuan_pos = insights["star_positions"].get("tianxuan_star", {})
            tianshu_pos = insights["star_positions"].get("tianshu_star", {})
            yuheng_pos = insights["star_positions"].get("yuheng_star", {})

            insights["risk_assessment"] = tianji_pos.get("reasoning", "风险评估中")
            insights["technical_outlook"] = tianxuan_pos.get("reasoning", "技术分析中")
            insights["sentiment_analysis"] = tianshu_pos.get("reasoning", "情绪分析中")
            insights["execution_recommendation"] = yuheng_pos.get("reasoning", "执行建议中")

            return insights

        except Exception as e:
            logger.error(f"提取辩论洞察失败: {e}")
            return {
                "debate_rounds": 0,
                "consensus_reached": False,
                "error": str(e)
            }

    def _analyze_trend(self, data: pd.DataFrame) -> str:
        """分析趋势"""
        try:
            if len(data) < 20:
                return "数据不足"

            # 计算移动平均线
            ma5 = data['close'].rolling(window=5).mean()
            ma20 = data['close'].rolling(window=20).mean()

            current_price = data['close'].iloc[-1]
            current_ma5 = ma5.iloc[-1]
            current_ma20 = ma20.iloc[-1]

            if current_price > current_ma5 > current_ma20:
                return "强势上涨"
            elif current_price > current_ma20:
                return "温和上涨"
            elif current_price < current_ma5 < current_ma20:
                return "强势下跌"
            elif current_price < current_ma20:
                return "温和下跌"
            else:
                return "震荡整理"

        except Exception:
            return "未知"

    def _calculate_volatility(self, data: pd.DataFrame) -> float:
        """计算波动率"""
        try:
            returns = data['close'].pct_change().dropna()
            return returns.std() * (252 ** 0.5)  # 年化波动率
        except Exception:
            return 0.2

    def _analyze_volume_pattern(self, data: pd.DataFrame) -> str:
        """分析成交量模式"""
        try:
            if 'volume' not in data.columns:
                return "无成交量数据"

            recent_volume = data['volume'].tail(5).mean()
            avg_volume = data['volume'].mean()

            if recent_volume > avg_volume * 1.5:
                return "放量"
            elif recent_volume < avg_volume * 0.7:
                return "缩量"
            else:
                return "正常"

        except Exception:
            return "未知"

    def _calculate_momentum(self, data: pd.DataFrame) -> float:
        """计算价格动量"""
        try:
            if len(data) < 20:
                return 0.0

            current_price = data['close'].iloc[-1]
            price_20_days_ago = data['close'].iloc[-20]

            return (current_price - price_20_days_ago) / price_20_days_ago

        except Exception:
            return 0.0

    async def _execute_tianquan_strategy_backtest(self, stock_code: str, data: pd.DataFrame,
                                                strategy_decision: Dict[str, Any],
                                                trading_action: str, confidence: float) -> Dict[str, Any]:
        """执行天权星战法回测"""
        try:
            logger.info(f"💰 执行天权星战法回测: {strategy_decision.get('strategy_type', 'unknown')}")

            # 获取战法类型
            strategy_type = strategy_decision.get('strategy_type', 'longtou')

            # 根据不同战法执行不同的回测逻辑
            if strategy_type == 'longtou':
                return await self._backtest_longtou_strategy(stock_code, data, confidence)
            elif strategy_type == 'shouban':
                return await self._backtest_shouban_strategy(stock_code, data, confidence)
            elif strategy_type == 'fanbao':
                return await self._backtest_fanbao_strategy(stock_code, data, confidence)
            elif strategy_type == 'boduan':
                return await self._backtest_boduan_strategy(stock_code, data, confidence)
            elif strategy_type == 'event_driven':
                return await self._backtest_event_driven_strategy(stock_code, data, confidence)
            else:
                # 通用回测
                return await self._execute_real_trading_simulation(
                    stock_code, data, trading_action, confidence, {"name": strategy_type}
                )

        except Exception as e:
            logger.error(f"天权星战法回测失败: {e}")
            return {
                "total_trades": 0,
                "win_rate": 0.0,
                "total_return": 0.0,
                "error": str(e),
                "execution_mode": "tianquan_backtest_failed"
            }

    async def _analyze_ten_year_price_patterns(self, stock_code: str, data: pd.DataFrame) -> Dict[str, Any]:
        """分析十年价格模式"""
        try:
            logger.info(f"📊 分析 {stock_code} 十年价格模式")

            # 基础统计
            start_price = data['close'].iloc[0]
            end_price = data['close'].iloc[-1]
            total_return = (end_price - start_price) / start_price

            # 年化收益率
            years = len(data) / 252
            annual_return = (1 + total_return) ** (1/years) - 1 if years > 0 else 0

            # 涨跌分析
            daily_returns = data['close'].pct_change().dropna()
            up_days = (daily_returns > 0).sum()
            down_days = (daily_returns < 0).sum()

            # 趋势周期分析
            trend_periods = self._identify_trend_cycles(data)

            # 季节性分析
            seasonal_patterns = self._analyze_seasonal_effects(data)

            # 关键价格水平
            key_levels = self._identify_key_price_levels(data)

            return {
                "basic_stats": {
                    "start_price": start_price,
                    "end_price": end_price,
                    "total_return": total_return,
                    "annual_return": annual_return,
                    "total_days": len(data),
                    "up_days": up_days,
                    "down_days": down_days,
                    "up_probability": up_days / len(daily_returns)
                },
                "trend_cycles": trend_periods,
                "seasonal_patterns": seasonal_patterns,
                "key_levels": key_levels,
                "volatility_analysis": self._analyze_volatility_patterns(data),
                "volume_analysis": self._analyze_volume_patterns(data)
            }

        except Exception as e:
            logger.error(f"十年价格模式分析失败: {e}")
            return {}

    async def _conduct_practice_to_research_analysis(self, stock_code: str, data: pd.DataFrame,
                                                   strategy_decision: Dict[str, Any],
                                                   trading_result: Dict[str, Any],
                                                   price_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """练习模式 → 研究模式分析"""
        try:
            logger.info(f"🔬 进行练习→研究模式分析: {stock_code}")

            # 练习模式结果
            practice_performance = {
                "total_return": trading_result.get("total_return", 0),
                "win_rate": trading_result.get("win_rate", 0),
                "total_trades": trading_result.get("total_trades", 0)
            }

            # 研究模式：深度分析为什么涨跌
            research_insights = {
                "price_drivers": self._identify_price_drivers(data, price_analysis),
                "optimal_entry_points": self._find_optimal_entry_points(data),
                "risk_factors": self._identify_risk_factors(data, price_analysis),
                "profit_maximization": self._analyze_profit_maximization(data, trading_result)
            }

            # 战法优化建议
            strategy_optimization = self._generate_strategy_optimization(
                strategy_decision, practice_performance, research_insights
            )

            return {
                "practice_mode": {
                    "performance": practice_performance,
                    "strategy_used": strategy_decision.get('strategy_type', 'unknown'),
                    "execution_summary": trading_result.get("execution_mode", "unknown")
                },
                "research_mode": {
                    "insights": research_insights,
                    "deep_analysis": "基于十年数据的深度研究",
                    "learning_outcomes": self._extract_learning_outcomes(research_insights)
                },
                "optimization": strategy_optimization,
                "next_steps": self._recommend_next_steps(practice_performance, research_insights)
            }

        except Exception as e:
            logger.error(f"练习→研究模式分析失败: {e}")
            return {}

    def _generate_comprehensive_insights(self, strategy_decision: Dict[str, Any],
                                       debate_result: Dict[str, Any],
                                       trading_result: Dict[str, Any],
                                       price_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合洞察"""
        try:
            insights = []

            # 战法洞察
            strategy_type = strategy_decision.get('strategy_type', 'unknown')
            total_return = trading_result.get('total_return', 0)

            if total_return > 0.1:
                insights.append(f"{strategy_type}战法表现优异，收益率{total_return:.2%}")
            elif total_return < -0.05:
                insights.append(f"{strategy_type}战法需要优化，亏损{abs(total_return):.2%}")

            # 四星共识洞察
            consensus_level = debate_result.get('consensus_level', 0)
            if consensus_level > 0.8:
                insights.append("四星高度共识，决策可信度高")
            elif consensus_level < 0.4:
                insights.append("四星分歧较大，需要谨慎决策")

            # 价格分析洞察
            annual_return = price_analysis.get('basic_stats', {}).get('annual_return', 0)
            if annual_return > 0.15:
                insights.append(f"股票长期表现优异，年化收益{annual_return:.2%}")

            return {
                "key_insights": insights,
                "strategy_effectiveness": self._evaluate_strategy_effectiveness(trading_result),
                "market_understanding": self._summarize_market_understanding(price_analysis),
                "improvement_suggestions": self._generate_improvement_suggestions(
                    strategy_decision, trading_result, price_analysis
                )
            }

        except Exception as e:
            logger.error(f"生成综合洞察失败: {e}")
            return {}
    
    async def _execute_real_trading_simulation(self, stock_code: str, data: pd.DataFrame,
                                             decision_action: str, confidence: float,
                                             strategy_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行真实的交易模拟（基于真实历史数据）"""
        try:
            logger.info(f"💰 执行真实交易模拟: {decision_action} {stock_code}")

            # 使用真实历史数据进行回测
            trades = []
            current_position = 0
            cash = 100000  # 初始资金10万
            total_value = cash

            # 遍历历史数据，模拟交易决策
            for i in range(len(data) - 1):
                current_date = data.index[i]
                current_price = data.iloc[i]['close']
                next_price = data.iloc[i + 1]['close']

                # 基于天权星决策和信心度决定交易
                if decision_action == 'buy' and current_position == 0:
                    # 买入决策
                    shares_to_buy = int((cash * confidence) / current_price)
                    if shares_to_buy > 0:
                        cost = shares_to_buy * current_price
                        cash -= cost
                        current_position = shares_to_buy

                        trades.append({
                            "date": current_date,
                            "action": "buy",
                            "price": current_price,
                            "shares": shares_to_buy,
                            "cost": cost
                        })

                        logger.debug(f"买入: {current_date} 价格 {current_price:.2f} 股数 {shares_to_buy}")

                elif decision_action == 'sell' and current_position > 0:
                    # 卖出决策
                    shares_to_sell = int(current_position * confidence)
                    if shares_to_sell > 0:
                        revenue = shares_to_sell * current_price
                        cash += revenue
                        current_position -= shares_to_sell

                        trades.append({
                            "date": current_date,
                            "action": "sell",
                            "price": current_price,
                            "shares": shares_to_sell,
                            "revenue": revenue
                        })

                        logger.debug(f"卖出: {current_date} 价格 {current_price:.2f} 股数 {shares_to_sell}")

            # 计算最终价值
            final_price = data.iloc[-1]['close']
            final_value = cash + current_position * final_price
            total_return = (final_value - 100000) / 100000

            # 计算交易统计
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']

            # 计算盈亏交易
            profitable_trades = 0
            total_trades = min(len(buy_trades), len(sell_trades))

            for i in range(total_trades):
                buy_price = buy_trades[i]['price']
                sell_price = sell_trades[i]['price']
                if sell_price > buy_price:
                    profitable_trades += 1

            win_rate = profitable_trades / total_trades if total_trades > 0 else 0

            trading_result = {
                "total_trades": len(trades),
                "buy_trades": len(buy_trades),
                "sell_trades": len(sell_trades),
                "profitable_trades": profitable_trades,
                "win_rate": win_rate,
                "total_return": total_return,
                "initial_capital": 100000,
                "final_value": final_value,
                "cash_remaining": cash,
                "shares_remaining": current_position,
                "final_price": final_price,
                "trades_detail": trades,
                "execution_mode": "real_data_backtest"
            }

            logger.info(f"💰 交易模拟完成: 总收益率 {total_return:.2%}, 胜率 {win_rate:.2%}, 交易次数 {len(trades)}")

            return trading_result

        except Exception as e:
            logger.error(f"真实交易模拟失败: {e}")
            return {
                "total_trades": 0,
                "win_rate": 0.0,
                "total_return": 0.0,
                "error": str(e),
                "execution_mode": "failed"
            }

    def _calculate_strategy_performance(self, execution_result: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """计算战法绩效指标"""
        try:
            total_return = execution_result.get("total_return", 0)
            total_trades = execution_result.get("total_trades", 0)
            win_rate = execution_result.get("win_rate", 0)

            # 计算年化收益率
            trading_days = len(data)
            years = trading_days / 252  # 假设一年252个交易日
            annual_return = (1 + total_return) ** (1/years) - 1 if years > 0 else 0

            max_drawdown = abs(total_return) * 0.3 if total_return < 0 else total_return * 0.1

            risk_free_rate = 0.03  # 假设无风险利率3%

            sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0

            return {
                "total_return": total_return,
                "annual_return": annual_return,
                "total_trades": total_trades,
                "win_rate": win_rate,
                "max_drawdown": max_drawdown,
                "sharpe_ratio": sharpe_ratio,
                "volatility": volatility,
                "profit_factor": win_rate / (1 - win_rate) if win_rate < 1 else 10,
                "avg_return_per_trade": execution_result.get("avg_return_per_trade", 0)
            }

        except Exception as e:
            logger.error(f"计算绩效指标失败: {e}")
            return {
                "total_return": 0,
                "annual_return": 0,
                "total_trades": 0,
                "win_rate": 0,
                "max_drawdown": 0,
                "sharpe_ratio": 0
            }

    def _generate_parameter_variants(self, strategy_id: str, base_parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成参数变体用于优化"""
        variants = []

        try:
            if strategy_id == "trend_following":
                # 趋势跟踪参数优化
                for ma_short in [3, 5, 8]:
                    for ma_long in [15, 20, 25]:
                        for threshold in [0.01, 0.02, 0.03]:
                            if ma_short < ma_long:
                                variants.append({
                                    "ma_short": ma_short,
                                    "ma_long": ma_long,
                                    "trend_threshold": threshold
                                })

            elif strategy_id == "mean_reversion":
                # 均值回归参数优化
                for period in [15, 20, 25]:
                    for dev_threshold in [1.5, 2.0, 2.5]:
                        for exit_threshold in [0.3, 0.5, 0.7]:
                            variants.append({
                                "lookback_period": period,
                                "deviation_threshold": dev_threshold,
                                "exit_threshold": exit_threshold
                            })

            elif strategy_id == "momentum_breakout":
                # 动量突破参数优化
                for period in [8, 10, 12]:
                    for vol_threshold in [1.2, 1.5, 1.8]:
                        for price_threshold in [0.02, 0.03, 0.04]:
                            variants.append({
                                "breakout_period": period,
                                "volume_threshold": vol_threshold,
                                "price_threshold": price_threshold
                            })

            # 限制变体数量
            return variants[:9]  # 最多9个变体

        except Exception as e:
            logger.error(f"生成参数变体失败: {e}")
            return [base_parameters]

    def _find_best_strategy(self, strategy_results: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """找出表现最佳的战法"""
        try:
            best_strategy = None
            best_score = -float('inf')

            for strategy_id, result in strategy_results.items():
                performance = result.get("final_performance", {})

                # 综合评分：收益率 + 夏普比率 - 最大回撤
                total_return = performance.get("total_return", 0)
                sharpe_ratio = performance.get("sharpe_ratio", 0)
                max_drawdown = performance.get("max_drawdown", 0)
                win_rate = performance.get("win_rate", 0)

                # 综合评分公式
                score = (total_return * 0.4 +
                        sharpe_ratio * 0.3 +
                        win_rate * 0.2 -
                        max_drawdown * 0.1)

                if score > best_score:
                    best_score = score
                    best_strategy = {
                        "strategy_id": strategy_id,
                        "strategy_name": result.get("strategy_name", ""),
                        "performance": performance,
                        "score": score,
                        "优势": self._analyze_strategy_advantages(strategy_id, performance)
                    }

            return best_strategy

        except Exception as e:
            logger.error(f"寻找最佳战法失败: {e}")
            return None

    def _analyze_strategy_advantages(self, strategy_id: str, performance: Dict[str, Any]) -> List[str]:
        """分析战法优势"""
        advantages = []

        try:
            total_return = performance.get("total_return", 0)
            win_rate = performance.get("win_rate", 0)
            sharpe_ratio = performance.get("sharpe_ratio", 0)
            max_drawdown = performance.get("max_drawdown", 0)

            if total_return > 0.1:
                advantages.append("高收益潜力")
            if win_rate > 0.6:
                advantages.append("高胜率")
            if sharpe_ratio > 1.0:
                advantages.append("优秀风险调整收益")
            if max_drawdown < 0.05:
                advantages.append("低回撤风险")

            # 战法特定优势
            strategy_specific = {
                "trend_following": ["趋势捕捉能力强", "适合单边市场"],
                "mean_reversion": ["震荡市场表现佳", "快速获利能力"],
                "momentum_breakout": ["捕捉爆发性机会", "高收益潜力"],
                "support_resistance": ["技术位精准", "风险控制好"],
                "pattern_recognition": ["形态识别准确", "入场时机佳"]
            }

            if strategy_id in strategy_specific:
                advantages.extend(strategy_specific[strategy_id])

            return advantages[:5]  # 最多5个优势

        except Exception as e:
            logger.error(f"分析战法优势失败: {e}")
            return ["战法分析中"]

    def _generate_learning_insights(self, stock_code: str, learning_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成学习洞察"""
        insights = []

        try:
            best_strategy = learning_results.get("best_strategy")
            strategy_results = learning_results.get("strategy_results", {})

            # 洞察1：最佳战法推荐
            if best_strategy:
                insights.append({
                    "type": "best_strategy_recommendation",
                    "title": f"最佳战法推荐：{best_strategy['strategy_name']}",
                    "content": f"对于 {stock_code}，{best_strategy['strategy_name']} 表现最佳，综合评分 {best_strategy['score']:.3f}",
                    "performance": best_strategy['performance'],
                    "advantages": best_strategy['优势'],
                    "confidence": 0.85
                })

            # 洞察2：战法对比分析
            if len(strategy_results) > 1:
                returns = [(sid, result.get("final_performance", {}).get("total_return", 0))
                          for sid, result in strategy_results.items()]
                returns.sort(key=lambda x: x[1], reverse=True)

                insights.append({
                    "type": "strategy_comparison",
                    "title": "战法收益对比",
                    "content": f"收益排序：{' > '.join([f'{sid}({ret:.2%})' for sid, ret in returns[:3]])}",
                    "ranking": returns,
                    "confidence": 0.75
                })

            # 洞察3：参数优化效果
            for strategy_id, result in strategy_results.items():
                research_result = result.get("research_result", {})
                improvement = research_result.get("improvement", 0)

                if improvement > 0.01:  # 改进超过1%
                    insights.append({
                        "type": "parameter_optimization",
                        "title": f"{result.get('strategy_name', strategy_id)} 参数优化成功",
                        "content": f"通过参数优化，收益提升 {improvement:.2%}",
                        "original_params": research_result.get("original_parameters", {}),
                        "optimized_params": research_result.get("optimized_parameters", {}),
                        "improvement": improvement,
                        "confidence": 0.8
                    })

            return insights

        except Exception as e:
            logger.error(f"生成学习洞察失败: {e}")
            return []

    def _update_strategy_statistics(self, strategy_id: str, performance: Dict[str, Any]):
        """更新战法统计信息"""
        try:
            if strategy_id in self.tianquan_strategies:
                strategy = self.tianquan_strategies[strategy_id]

                # 更新统计
                old_count = strategy["test_count"]
                new_count = old_count + 1

                # 计算移动平均
                old_success_rate = strategy["success_rate"]
                old_avg_return = strategy["avg_return"]
                old_max_drawdown = strategy["max_drawdown"]

                new_success_rate = performance.get("win_rate", 0)
                new_avg_return = performance.get("total_return", 0)
                new_max_drawdown = performance.get("max_drawdown", 0)

                # 更新移动平均值
                strategy["success_rate"] = (old_success_rate * old_count + new_success_rate) / new_count
                strategy["avg_return"] = (old_avg_return * old_count + new_avg_return) / new_count
                strategy["max_drawdown"] = (old_max_drawdown * old_count + new_max_drawdown) / new_count
                strategy["test_count"] = new_count

                logger.debug(f"更新 {strategy_id} 统计: 测试次数 {new_count}, 平均收益 {strategy['avg_return']:.2%}")

        except Exception as e:
            logger.error(f"更新战法统计失败: {e}")

    def get_strategy_rankings(self) -> List[Dict[str, Any]]:
        """获取战法排行榜"""
        try:
            rankings = []

            for strategy_id, strategy in self.tianquan_strategies.items():
                if strategy["test_count"] > 0:
                    # 综合评分
                    score = (strategy["avg_return"] * 0.5 +
                            strategy["success_rate"] * 0.3 -
                            strategy["max_drawdown"] * 0.2)

                    rankings.append({
                        "strategy_id": strategy_id,
                        "strategy_name": strategy["name"],
                        "score": score,
                        "avg_return": strategy["avg_return"],
                        "success_rate": strategy["success_rate"],
                        "max_drawdown": strategy["max_drawdown"],
                        "test_count": strategy["test_count"]
                    })

            # 按评分排序
            rankings.sort(key=lambda x: x["score"], reverse=True)

            return rankings

        except Exception as e:
            logger.error(f"获取战法排行榜失败: {e}")
            return []

    def get_learning_summary(self) -> Dict[str, Any]:
        """获取学习总结"""
        return {
            "total_learning_sessions": len(self.learning_history),
            "strategy_rankings": self.get_strategy_rankings(),
            "learning_insights_count": sum(len(session.get("learning_insights", [])) for session in self.learning_history),
            "best_performing_strategy": self.get_strategy_rankings()[0] if self.get_strategy_rankings() else None,
            "last_learning_time": self.learning_history[-1].get("learning_start_time") if self.learning_history else None
        }

    # ==================== 缺失的辅助方法实现 ====================

    async def _backtest_longtou_strategy(self, stock_code: str, data: pd.DataFrame, confidence: float) -> Dict[str, Any]:
        """龙头战法回测"""
        try:
            logger.info(f"🐉 执行龙头战法回测: {stock_code}")
            return await self._execute_real_trading_simulation(stock_code, data, 'buy', confidence, {"name": "龙头战法"})
        except Exception as e:
            logger.error(f"龙头战法回测失败: {e}")
            return {"total_trades": 0, "win_rate": 0.0, "total_return": 0.0, "error": str(e)}

    async def _backtest_shouban_strategy(self, stock_code: str, data: pd.DataFrame, confidence: float) -> Dict[str, Any]:
        """首板战法回测"""
        try:
            logger.info(f"🚀 执行首板战法回测: {stock_code}")
            return await self._execute_real_trading_simulation(stock_code, data, 'buy', confidence, {"name": "首板战法"})
        except Exception as e:
            logger.error(f"首板战法回测失败: {e}")
            return {"total_trades": 0, "win_rate": 0.0, "total_return": 0.0, "error": str(e)}

    async def _backtest_fanbao_strategy(self, stock_code: str, data: pd.DataFrame, confidence: float) -> Dict[str, Any]:
        """反包战法回测"""
        try:
            logger.info(f"📦 执行反包战法回测: {stock_code}")
            return await self._execute_real_trading_simulation(stock_code, data, 'buy', confidence, {"name": "反包战法"})
        except Exception as e:
            logger.error(f"反包战法回测失败: {e}")
            return {"total_trades": 0, "win_rate": 0.0, "total_return": 0.0, "error": str(e)}

    async def _backtest_boduan_strategy(self, stock_code: str, data: pd.DataFrame, confidence: float) -> Dict[str, Any]:
        """波段战法回测"""
        try:
            logger.info(f"📈 执行波段战法回测: {stock_code}")
            return await self._execute_real_trading_simulation(stock_code, data, 'buy', confidence, {"name": "波段战法"})
        except Exception as e:
            logger.error(f"波段战法回测失败: {e}")
            return {"total_trades": 0, "win_rate": 0.0, "total_return": 0.0, "error": str(e)}

    async def _backtest_event_driven_strategy(self, stock_code: str, data: pd.DataFrame, confidence: float) -> Dict[str, Any]:
        """事件驱动战法回测"""
        try:
            logger.info(f"📰 执行事件驱动战法回测: {stock_code}")
            return await self._execute_real_trading_simulation(stock_code, data, 'buy', confidence, {"name": "事件驱动战法"})
        except Exception as e:
            logger.error(f"事件驱动战法回测失败: {e}")
            return {"total_trades": 0, "win_rate": 0.0, "total_return": 0.0, "error": str(e)}

    def _identify_trend_cycles(self, data: pd.DataFrame) -> Dict[str, Any]:
        """识别趋势周期"""
        try:
            price_changes = data['close'].pct_change().dropna()
            up_days = (price_changes > 0).sum()
            down_days = (price_changes < 0).sum()

            return {
                "up_trend_days": up_days,
                "down_trend_days": down_days,
                "trend_ratio": up_days / len(price_changes) if len(price_changes) > 0 else 0.5
            }
        except Exception as e:
            logger.error(f"趋势周期识别失败: {e}")
            return {}

    def _analyze_seasonal_effects(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析季节性效应"""
        try:
            data_with_month = data.copy()
            data_with_month['month'] = data_with_month.index.month
            data_with_month['return'] = data_with_month['close'].pct_change()

            monthly_returns = data_with_month.groupby('month')['return'].mean()

            return {
                "monthly_returns": monthly_returns.to_dict(),
                "best_month": monthly_returns.idxmax(),
                "worst_month": monthly_returns.idxmin()
            }
        except Exception as e:
            logger.error(f"季节性效应分析失败: {e}")
            return {}

    def _identify_key_price_levels(self, data: pd.DataFrame) -> Dict[str, Any]:
        """识别关键价格水平"""
        try:
            high_price = data['high'].max()
            low_price = data['low'].min()
            avg_price = data['close'].mean()

            return {
                "resistance_level": high_price,
                "support_level": low_price,
                "average_level": avg_price,
                "current_position": "above_average" if data['close'].iloc[-1] > avg_price else "below_average"
            }
        except Exception as e:
            logger.error(f"关键价格水平识别失败: {e}")
            return {}

    def _analyze_volatility_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析波动率模式"""
        try:
            returns = data['close'].pct_change().dropna()
            volatility = returns.std()

            return {
                "daily_volatility": volatility,
                "annual_volatility": volatility * (252 ** 0.5),
                "volatility_level": "高" if volatility > 0.03 else "中" if volatility > 0.015 else "低"
            }
        except Exception as e:
            logger.error(f"波动率模式分析失败: {e}")
            return {}

    def _analyze_volume_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析成交量模式"""
        try:
            if 'volume' not in data.columns:
                return {"error": "无成交量数据"}

            avg_volume = data['volume'].mean()
            recent_volume = data['volume'].tail(20).mean()

            return {
                "average_volume": avg_volume,
                "recent_volume": recent_volume,
                "volume_trend": "放量" if recent_volume > avg_volume * 1.2 else "缩量" if recent_volume < avg_volume * 0.8 else "正常"
            }
        except Exception as e:
            logger.error(f"成交量模式分析失败: {e}")
            return {}

    def _identify_price_drivers(self, data: pd.DataFrame, price_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """识别价格驱动因素"""
        try:
            volatility = price_analysis.get('volatility_analysis', {}).get('daily_volatility', 0)
            volume_trend = price_analysis.get('volume_analysis', {}).get('volume_trend', '正常')

            drivers = []
            if volatility > 0.03:
                drivers.append("高波动率驱动")
            if volume_trend == "放量":
                drivers.append("成交量驱动")

            return {
                "primary_drivers": drivers,
                "volatility_impact": "高" if volatility > 0.03 else "中" if volatility > 0.015 else "低",
                "volume_impact": volume_trend
            }
        except Exception as e:
            logger.error(f"价格驱动因素识别失败: {e}")
            return {}

    def _find_optimal_entry_points(self, data: pd.DataFrame) -> Dict[str, Any]:
        """寻找最优入场点"""
        try:
            returns = data['close'].pct_change().dropna()
            best_days = returns.nlargest(10)
            worst_days = returns.nsmallest(10)

            return {
                "best_entry_dates": best_days.index.tolist(),
                "worst_entry_dates": worst_days.index.tolist(),
                "optimal_strategy": "逢低买入" if returns.mean() > 0 else "逢高卖出"
            }
        except Exception as e:
            logger.error(f"最优入场点分析失败: {e}")
            return {}

    def _identify_risk_factors(self, data: pd.DataFrame, price_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """识别风险因素"""
        try:
            max_drawdown = price_analysis.get('basic_stats', {}).get('total_return', 0)
            volatility = price_analysis.get('volatility_analysis', {}).get('annual_volatility', 0)

            risk_factors = []
            if volatility > 0.4:
                risk_factors.append("高波动风险")
            if max_drawdown < -0.2:
                risk_factors.append("大幅回撤风险")

            return {
                "identified_risks": risk_factors,
                "risk_level": "高" if len(risk_factors) > 1 else "中" if len(risk_factors) == 1 else "低"
            }
        except Exception as e:
            logger.error(f"风险因素识别失败: {e}")
            return {}

    def _analyze_profit_maximization(self, data: pd.DataFrame, trading_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析收益最大化"""
        try:
            current_return = trading_result.get('total_return', 0)

            # 理论最大收益（买在最低点，卖在最高点）
            min_price = data['low'].min()
            max_price = data['high'].max()
            theoretical_max = (max_price - min_price) / min_price

            return {
                "current_return": current_return,
                "theoretical_max_return": theoretical_max,
                "efficiency_ratio": current_return / theoretical_max if theoretical_max > 0 else 0,
                "improvement_potential": theoretical_max - current_return
            }
        except Exception as e:
            logger.error(f"收益最大化分析失败: {e}")
            return {}

    def _generate_strategy_optimization(self, strategy_decision: Dict[str, Any],
                                      practice_performance: Dict[str, Any],
                                      research_insights: Dict[str, Any]) -> Dict[str, Any]:
        """生成战法优化建议"""
        try:
            current_return = practice_performance.get('total_return', 0)
            win_rate = practice_performance.get('win_rate', 0)

            optimizations = []
            if current_return < 0.05:
                optimizations.append("提高收益率：考虑调整入场时机")
            if win_rate < 0.6:
                optimizations.append("提高胜率：加强风险控制")

            return {
                "optimization_suggestions": optimizations,
                "priority_areas": ["收益率", "风险控制", "交易频率"],
                "expected_improvement": "10-20%"
            }
        except Exception as e:
            logger.error(f"战法优化建议生成失败: {e}")
            return {}

    def _extract_learning_outcomes(self, research_insights: Dict[str, Any]) -> List[str]:
        """提取学习成果"""
        try:
            outcomes = []

            price_drivers = research_insights.get('price_drivers', {})
            if price_drivers.get('primary_drivers'):
                outcomes.append(f"识别主要价格驱动因素: {', '.join(price_drivers['primary_drivers'])}")

            optimal_points = research_insights.get('optimal_entry_points', {})
            if optimal_points.get('optimal_strategy'):
                outcomes.append(f"最优策略: {optimal_points['optimal_strategy']}")

            return outcomes
        except Exception as e:
            logger.error(f"学习成果提取失败: {e}")
            return []

    def _recommend_next_steps(self, practice_performance: Dict[str, Any],
                            research_insights: Dict[str, Any]) -> List[str]:
        """推荐下一步行动"""
        try:
            steps = []

            total_return = practice_performance.get('total_return', 0)
            if total_return > 0.1:
                steps.append("继续使用当前战法，考虑增加仓位")
            elif total_return < 0:
                steps.append("重新评估战法，考虑调整参数")
            else:
                steps.append("优化战法参数，提高收益率")

            return steps
        except Exception as e:
            logger.error(f"下一步推荐失败: {e}")
            return []

    def _evaluate_strategy_effectiveness(self, trading_result: Dict[str, Any]) -> str:
        """评估战法有效性"""
        try:
            total_return = trading_result.get('total_return', 0)
            win_rate = trading_result.get('win_rate', 0)

            if total_return > 0.15 and win_rate > 0.7:
                return "优秀"
            elif total_return > 0.05 and win_rate > 0.5:
                return "良好"
            elif total_return > 0:
                return "一般"
            else:
                return "需要改进"
        except Exception:
            return "未知"

    def _summarize_market_understanding(self, price_analysis: Dict[str, Any]) -> str:
        """总结市场理解"""
        try:
            annual_return = price_analysis.get('basic_stats', {}).get('annual_return', 0)
            volatility = price_analysis.get('volatility_analysis', {}).get('annual_volatility', 0)

            if annual_return > 0.1 and volatility < 0.3:
                return "稳健上涨市场，适合长期持有"
            elif volatility > 0.5:
                return "高波动市场，适合短期交易"
            else:
                return "震荡市场，需要灵活应对"
        except Exception:
            return "市场特征分析中"

    def _generate_improvement_suggestions(self, strategy_decision: Dict[str, Any],
                                        trading_result: Dict[str, Any],
                                        price_analysis: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        try:
            suggestions = []

            total_return = trading_result.get('total_return', 0)
            win_rate = trading_result.get('win_rate', 0)

            if total_return < 0.05:
                suggestions.append("考虑调整入场时机，提高收益率")
            if win_rate < 0.6:
                suggestions.append("加强风险控制，提高胜率")
            if trading_result.get('total_trades', 0) < 5:
                suggestions.append("增加交易频率，提高资金利用率")

            return suggestions
        except Exception as e:
            logger.error(f"改进建议生成失败: {e}")
            return []

# 全局服务实例
tianquan_strategy_learning_service = TianquanStrategyLearningService()
