from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
告警服务
提供系统告警和通知功能
"""

import asyncio
import logging
import json
import smtplib
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import sqlite3
import os

logger = logging.getLogger(__name__)

@dataclass
class Alert:
    """告警信息"""
    alert_id: str
    alert_type: str
    severity: str  # critical, high, medium, low
    title: str
    message: str
    source: str
    timestamp: str
    resolved: bool = False
    acknowledged: bool = False

@dataclass
class NotificationChannel:
    """通知渠道"""
    channel_type: str  # email, webhook, websocket
    config: Dict[str, Any]
    enabled: bool = True

class AlertService:
    """告警服务"""
    
    def __init__(self):
        self.db_path = "backend/data/alerts.db"
        self.is_connected = False
        self.notification_channels = []
        self.alert_rules = {
            "risk_violation": {"severity": "high", "notify": True},
            "system_error": {"severity": "critical", "notify": True},
            "market_alert": {"severity": "medium", "notify": True},
            "performance_warning": {"severity": "low", "notify": False}
        }
        self.active_alerts = []
        
    async def initialize(self):
        """初始化告警服务"""
        try:
            # 确保数据目录存在
            os.makedirs("data", exist_ok=True)
            
            # 初始化数据库
            await self._init_database()
            
            # 加载通知渠道配置
            await self._load_notification_channels()
            
            # 加载活跃告警
            await self._load_active_alerts()
            
            self.is_connected = True
            logger.info("告警服务初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"告警服务初始化失败: {e}")
            self.is_connected = False
            return False
    
    async def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建告警表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                alert_id TEXT UNIQUE NOT NULL,
                alert_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                source TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                resolved INTEGER DEFAULT 0,
                acknowledged INTEGER DEFAULT 0
            )
        ''')
        
        # 创建通知渠道表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notification_channels (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                channel_type TEXT NOT NULL,
                config TEXT NOT NULL,
                enabled INTEGER DEFAULT 1
            )
        ''')
        
        conn.commit()
        conn.close()
    
    async def _load_notification_channels(self):
        """加载通知渠道配置"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT channel_type, config, enabled FROM notification_channels
            ''')
            
            rows = cursor.fetchall()
            self.notification_channels = [
                NotificationChannel(
                    channel_type=row[0],
                    config=json.loads(row[1]),
                    enabled=bool(row[2])
                )
                for row in rows
            ]
            
            conn.close()
            
            # 如果没有配置，添加默认配置
            if not self.notification_channels:
                await self._add_default_channels()
            
            logger.info(f"加载了 {len(self.notification_channels)} 个通知渠道")
            
        except Exception as e:
            logger.error(f"加载通知渠道失败: {e}")
    
    async def _add_default_channels(self):
        """添加默认通知渠道"""
        default_channels = [
            {
                "channel_type": "websocket",
                "config": {"target": "all_clients"},
                "enabled": True
            },
            {
                "channel_type": "email",
                "config": {
                    "smtp_server": "smtp.gmail.com",
                    "smtp_port": 587,
                    "username": "",
                    "password": "",
                    "recipients": []
                },
                "enabled": False  # 默认禁用，需要配置
            }
        ]
        
        for channel_config in default_channels:
            await self._save_notification_channel(
                channel_config["channel_type"],
                channel_config["config"],
                channel_config["enabled"]
            )
    
    async def _save_notification_channel(self, channel_type: str, config: Dict[str, Any], enabled: bool):
        """保存通知渠道"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO notification_channels (channel_type, config, enabled)
                VALUES (?, ?, ?)
            ''', (channel_type, json.dumps(config), int(enabled)))
            
            conn.commit()
            conn.close()
            
            # 添加到内存
            self.notification_channels.append(
                NotificationChannel(
                    channel_type=channel_type,
                    config=config,
                    enabled=enabled
                )
            )
            
        except Exception as e:
            logger.error(f"保存通知渠道失败: {e}")
    
    async def _load_active_alerts(self):
        """加载活跃告警"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT alert_id, alert_type, severity, title, message, source, timestamp, resolved, acknowledged
                FROM alerts 
                WHERE resolved = 0
                ORDER BY timestamp DESC
            ''')
            
            rows = cursor.fetchall()
            self.active_alerts = [
                Alert(
                    alert_id=row[0],
                    alert_type=row[1],
                    severity=row[2],
                    title=row[3],
                    message=row[4],
                    source=row[5],
                    timestamp=row[6],
                    resolved=bool(row[7]),
                    acknowledged=bool(row[8])
                )
                for row in rows
            ]
            
            conn.close()
            logger.info(f"加载了 {len(self.active_alerts)} 个活跃告警")
            
        except Exception as e:
            logger.error(f"加载活跃告警失败: {e}")
    
    async def create_alert(self, alert_type: str, title: str, message: str, source: str, severity: Optional[str] = None) -> str:
        """创建告警"""
        try:
            # 确定告警严重程度
            if not severity:
                severity = self.alert_rules.get(alert_type, {}).get("severity", "medium")
            
            # 创建告警对象
            alert = Alert(
                alert_id=f"alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{alert_type}",
                alert_type=alert_type,
                severity=severity,
                title=title,
                message=message,
                source=source,
                timestamp=datetime.now().isoformat()
            )
            
            # 保存到数据库
            await self._save_alert(alert)
            
            # 添加到活跃告警列表
            self.active_alerts.append(alert)
            
            # 发送通知
            should_notify = self.alert_rules.get(alert_type, {}).get("notify", True)
            if should_notify:
                await self._send_notifications(alert)
            
            logger.info(f"创建告警: {alert.alert_id} - {alert.title}")
            return alert.alert_id
            
        except Exception as e:
            logger.error(f"创建告警失败: {e}")
            return ""
    
    async def _save_alert(self, alert: Alert):
        """保存告警到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO alerts 
                (alert_id, alert_type, severity, title, message, source, timestamp, resolved, acknowledged)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                alert.alert_id,
                alert.alert_type,
                alert.severity,
                alert.title,
                alert.message,
                alert.source,
                alert.timestamp,
                int(alert.resolved),
                int(alert.acknowledged)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"保存告警失败: {e}")
    
    async def _send_notifications(self, alert: Alert):
        """发送通知"""
        for channel in self.notification_channels:
            if not channel.enabled:
                continue
                
            try:
                if channel.channel_type == "websocket":
                    await self._send_websocket_notification(alert, channel.config)
                elif channel.channel_type == "email":
                    await self._send_email_notification(alert, channel.config)
                elif channel.channel_type == "webhook":
                    await self._send_webhook_notification(alert, channel.config)
                    
            except Exception as e:
                logger.error(f"发送{channel.channel_type}通知失败: {e}")
    
    async def _send_websocket_notification(self, alert: Alert, config: Dict[str, Any]):
        """发送WebSocket通知"""
        try:
            # 这里应该集成WebSocket管理器
            from services.websocket_manager import get_websocket_manager, WebSocketMessage, MessageType
            
            ws_manager = await get_websocket_manager()
            message = WebSocketMessage(
                type=MessageType.ALERT,
                data={
                    "alert_id": alert.alert_id,
                    "alert_type": alert.alert_type,
                    "severity": alert.severity,
                    "title": alert.title,
                    "message": alert.message,
                    "source": alert.source,
                    "timestamp": alert.timestamp
                }
            )
            
            await ws_manager.broadcast(message)
            logger.info(f"WebSocket通知已发送: {alert.alert_id}")
            
        except Exception as e:
            logger.error(f"WebSocket通知发送失败: {e}")
    
    async def _send_email_notification(self, alert: Alert, config: Dict[str, Any]):
        """发送邮件通知"""
        try:
            if not config.get("username") or not config.get("recipients"):
                return  # 邮件未配置
            
            # 创建邮件内容
            msg = MIMEMultipart()
            msg['From'] = config["username"]
            msg['To'] = ", ".join(config["recipients"])
            msg['Subject'] = f"[{alert.severity.upper()}] {alert.title}"

            body = f"""
告警详情:
- 告警ID: {alert.alert_id}
- 告警类型: {alert.alert_type}
- 严重程度: {alert.severity}
- 来源: {alert.source}
- 时间: {alert.timestamp}
- 消息: {alert.message}
            """

            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            server = smtplib.SMTP(config["smtp_server"], config["smtp_port"])
            server.starttls()
            server.login(config["username"], config["password"])
            server.send_message(msg)
            server.quit()
            
            logger.info(f"邮件通知已发送: {alert.alert_id}")
            
        except Exception as e:
            logger.error(f"邮件通知发送失败: {e}")
    
    async def _send_webhook_notification(self, alert: Alert, config: Dict[str, Any]):
        """发送Webhook通知"""
        try:
            import aiohttp
            
            payload = {
                "alert_id": alert.alert_id,
                "alert_type": alert.alert_type,
                "severity": alert.severity,
                "title": alert.title,
                "message": alert.message,
                "source": alert.source,
                "timestamp": alert.timestamp
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(config["url"], json=payload) as response:
                    if response.status == 200:
                        logger.info(f"Webhook通知已发送: {alert.alert_id}")
                    else:
                        logger.error(f"Webhook通知发送失败: {response.status}")
                        
        except Exception as e:
            logger.error(f"Webhook通知发送失败: {e}")
    
    async def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        return [
            {
                "alert_id": alert.alert_id,
                "alert_type": alert.alert_type,
                "severity": alert.severity,
                "title": alert.title,
                "message": alert.message,
                "source": alert.source,
                "timestamp": alert.timestamp,
                "resolved": alert.resolved,
                "acknowledged": alert.acknowledged
            }
            for alert in self.active_alerts
        ]
    
    async def acknowledge_alert(self, alert_id: str) -> bool:
        """确认告警"""
        try:
            # 更新内存中的告警状态
            for alert in self.active_alerts:
                if alert.alert_id == alert_id:
                    alert.acknowledged = True
                    break
            
            # 更新数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE alerts SET acknowledged = 1 WHERE alert_id = ?
            ''', (alert_id,))
            
            conn.commit()
            conn.close()
            
            logger.info(f"告警已确认: {alert_id}")
            return True
            
        except Exception as e:
            logger.error(f"确认告警失败: {e}")
            return False
    
    async def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        try:
            # 更新内存中的告警状态
            for alert in self.active_alerts:
                if alert.alert_id == alert_id:
                    alert.resolved = True
                    break
            
            # 更新数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE alerts SET resolved = 1 WHERE alert_id = ?
            ''', (alert_id,))
            
            conn.commit()
            conn.close()
            
            # 从活跃告警列表中移除
            self.active_alerts = [alert for alert in self.active_alerts if alert.alert_id != alert_id]
            
            logger.info(f"告警已解决: {alert_id}")
            return True
            
        except Exception as e:
            logger.error(f"解决告警失败: {e}")
            return False
    
    async def get_alert_statistics(self) -> Dict[str, Any]:
        """获取告警统计"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 总告警数
            cursor.execute('SELECT COUNT(*) FROM alerts')
            total_alerts = cursor.fetchone()[0]
            
            # 活跃告警数
            cursor.execute('SELECT COUNT(*) FROM alerts WHERE resolved = 0')
            active_alerts = cursor.fetchone()[0]
            
            # 按严重程度统计
            cursor.execute('''
                SELECT severity, COUNT(*) FROM alerts 
                WHERE resolved = 0 
                GROUP BY severity
            ''')
            severity_stats = dict(cursor.fetchall())
            
            # 按类型统计
            cursor.execute('''
                SELECT alert_type, COUNT(*) FROM alerts 
                WHERE resolved = 0 
                GROUP BY alert_type
            ''')
            type_stats = dict(cursor.fetchall())
            
            conn.close()
            
            return {
                "total_alerts": total_alerts,
                "active_alerts": active_alerts,
                "resolved_alerts": total_alerts - active_alerts,
                "severity_distribution": severity_stats,
                "type_distribution": type_stats,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取告警统计失败: {e}")
            return {}

# 全局服务实例
alert_service = AlertService()
