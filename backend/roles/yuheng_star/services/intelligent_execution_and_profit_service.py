from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玉衡星智能执行建议和收益统计服务
提供智能执行建议、成本优化、收益计算、年月周日统计等功能
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import json
import sqlite3
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class ExecutionAction(Enum):
    """执行动作"""
    BUY = "买入"
    SELL = "卖出"
    HOLD = "持有"
    REDUCE = "减仓"
    ADD = "加仓"

class ExecutionTiming(Enum):
    """执行时机"""
    IMMEDIATE = "立即执行"
    MARKET_OPEN = "开盘执行"
    MARKET_CLOSE = "收盘执行"
    INTRADAY_OPTIMAL = "盘中最优"
    NEXT_DAY = "次日执行"

@dataclass
class ExecutionRecommendation:
    """执行建议"""
    recommendation_id: str
    stock_code: str
    action: ExecutionAction
    timing: ExecutionTiming
    target_price: float
    quantity: int
    confidence: float
    reason: str
    expected_cost: float
    expected_return: float
    risk_level: str
    timestamp: datetime

@dataclass
class TradeRecord:
    """交易记录"""
    trade_id: str
    stock_code: str
    stock_name: str
    action: ExecutionAction
    quantity: int
    price: float
    total_amount: float
    commission: float
    timestamp: datetime
    strategy_name: str
    is_simulated: bool = True

@dataclass
class ProfitStatistics:
    """收益统计"""
    period: str
    start_date: datetime
    end_date: datetime
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_profit: float
    total_commission: float
    net_profit: float
    max_profit: float
    max_loss: float
    sharpe_ratio: float
    max_drawdown: float

class IntelligentExecutionAndProfitService:
    """玉衡星智能执行建议和收益统计服务"""
    
    def __init__(self):
        self.trade_records = []
        self.execution_recommendations = []
        self.portfolio_positions = {}  # 当前持仓
        
        # 初始化数据库
        self._init_database()
        
        # 执行参数配置
        self.execution_config = {
            "commission_rate": 0.0003,  # 佣金费率
            "min_commission": 5.0,  # 最低佣金
            "slippage_rate": 0.001,  # 滑点率
            "market_impact_factor": 0.0005,  # 市场冲击因子
            "optimal_execution_threshold": 10000,  # 最优执行阈值
            "risk_free_rate": 0.03  # 无风险利率
        }
        
        logger.info("玉衡星智能执行建议和收益统计服务初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        try:
            self.db_connection = sqlite3.connect("backend/data/trading_execution.db", check_same_thread=False)
            cursor = self.db_connection.cursor()
            
            # 创建交易记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trade_records (
                    trade_id TEXT PRIMARY KEY,
                    stock_code TEXT NOT NULL,
                    stock_name TEXT NOT NULL,
                    action TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    price REAL NOT NULL,
                    total_amount REAL NOT NULL,
                    commission REAL NOT NULL,
                    timestamp TEXT NOT NULL,
                    strategy_name TEXT NOT NULL,
                    is_simulated BOOLEAN DEFAULT TRUE,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建持仓记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS portfolio_positions (
                    position_id TEXT PRIMARY KEY,
                    stock_code TEXT NOT NULL,
                    stock_name TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    avg_cost REAL NOT NULL,
                    current_price REAL NOT NULL,
                    market_value REAL NOT NULL,
                    unrealized_pnl REAL NOT NULL,
                    last_updated TEXT NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建执行建议表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS execution_recommendations (
                    recommendation_id TEXT PRIMARY KEY,
                    stock_code TEXT NOT NULL,
                    action TEXT NOT NULL,
                    timing TEXT NOT NULL,
                    target_price REAL NOT NULL,
                    quantity INTEGER NOT NULL,
                    confidence REAL NOT NULL,
                    reason TEXT NOT NULL,
                    expected_cost REAL NOT NULL,
                    expected_return REAL NOT NULL,
                    risk_level TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    is_executed BOOLEAN DEFAULT FALSE,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建收益统计表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS profit_statistics (
                    stat_id TEXT PRIMARY KEY,
                    period TEXT NOT NULL,
                    start_date TEXT NOT NULL,
                    end_date TEXT NOT NULL,
                    total_trades INTEGER NOT NULL,
                    winning_trades INTEGER NOT NULL,
                    losing_trades INTEGER NOT NULL,
                    win_rate REAL NOT NULL,
                    total_profit REAL NOT NULL,
                    total_commission REAL NOT NULL,
                    net_profit REAL NOT NULL,
                    max_profit REAL NOT NULL,
                    max_loss REAL NOT NULL,
                    sharpe_ratio REAL NOT NULL,
                    max_drawdown REAL NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            self.db_connection.commit()
            logger.info("交易执行数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            self.db_connection = None
    
    async def generate_execution_recommendation(
        self, 
        stock_code: str, 
        strategy_signal: Dict[str, Any],
        market_data: Dict[str, Any] = None
    ) -> ExecutionRecommendation:
        """生成智能执行建议"""
        
        try:
            logger.info(f"玉衡星为{stock_code}生成智能执行建议...")
            
            # 分析市场条件
            market_condition = await self._analyze_market_condition(stock_code, market_data)
            
            # 分析策略信号
            signal_analysis = self._analyze_strategy_signal(strategy_signal)
            
            # 计算最优执行参数
            execution_params = await self._calculate_optimal_execution(
                stock_code, signal_analysis, market_condition
            )
            
            # 生成执行建议
            recommendation = ExecutionRecommendation(
                recommendation_id=f"exec_rec_{int(datetime.now().timestamp())}",
                stock_code=stock_code,
                action=execution_params["action"],
                timing=execution_params["timing"],
                target_price=execution_params["target_price"],
                quantity=execution_params["quantity"],
                confidence=execution_params["confidence"],
                reason=execution_params["reason"],
                expected_cost=execution_params["expected_cost"],
                expected_return=execution_params["expected_return"],
                risk_level=execution_params["risk_level"],
                timestamp=datetime.now()
            )
            
            # 保存建议
            await self._save_execution_recommendation(recommendation)
            self.execution_recommendations.append(recommendation)
            
            logger.info(f"玉衡星执行建议生成完成: {recommendation.action.value} {stock_code}")
            return recommendation
            
        except Exception as e:
            logger.error(f"生成执行建议失败: {e}")
            # 返回默认建议
            return ExecutionRecommendation(
                recommendation_id=f"exec_rec_error_{int(datetime.now().timestamp())}",
                stock_code=stock_code,
                action=ExecutionAction.HOLD,
                timing=ExecutionTiming.IMMEDIATE,
                target_price = get_realistic_price("symbol", base=0.0),
                quantity=0,
                confidence=0.3,
                reason=f"分析失败: {str(e)}",
                expected_cost=0.0,
                expected_return_rate = get_realistic_return("symbol", base=0.0),
                risk_level="高",
                timestamp=datetime.now()
            )
    
    async def _analyze_market_condition(self, stock_code: str, market_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """分析市场条件"""
        try:
            if not market_data:
                # 获取实时市场数据 - 使用真实数据集成服务
                from .real_data_integration_service import real_data_service
                market_data_result = await real_data_service.get_real_market_data(stock_code)
                if market_data_result.get("success"):
                    market_data = market_data_result["data"]
                else:
                    # 使用默认数据
                    market_data = {
                        "current_price": 10.0,
                        "volume": 100000,
                        "bid_price": 9.99,
                        "ask_price": 10.01,
                        "high": 10.5,
                        "low": 9.5
                    }
            
            # 分析流动性
            liquidity_score = self._calculate_liquidity_score(market_data)
            
            # 分析波动率
            volatility_score = self._calculate_volatility_score(market_data)
            
            # 分析市场情绪
            sentiment_score = self._calculate_market_sentiment(market_data)
            
            return {
                "liquidity_score": liquidity_score,
                "volatility_score": volatility_score,
                "sentiment_score": sentiment_score,
                "market_status": self._determine_market_status(liquidity_score, volatility_score),
                "optimal_execution_window": self._calculate_optimal_window(market_data)
            }
            
        except Exception as e:
            logger.error(f"分析市场条件失败: {e}")
            return {
                "liquidity_score": 0.5,
                "volatility_score": 0.5,
                "sentiment_score": 0.5,
                "market_status": "normal",
                "optimal_execution_window": "intraday"
            }
    
    def _calculate_liquidity_score(self, market_data: Any) -> float:
        """计算流动性评分"""
        try:
            if hasattr(market_data, 'volume') and hasattr(market_data, 'turnover'):
                volume = float(market_data.volume) if market_data.volume else 0
                turnover = float(market_data.turnover) if market_data.turnover else 0
                
                # 基于成交量和换手率计算流动性
                if volume > 1000000 and turnover > 0.01:  # 100万股且换手率>1%
                    return 0.8
                elif volume > 500000 and turnover > 0.005:  # 50万股且换手率>0.5%
                    return 0.6
                else:
                    return 0.4
            
        except Exception as e:
            logger.error(f"计算流动性评分失败: {e}")
        
        return 0.5
    
    def _calculate_volatility_score(self, market_data: Any) -> float:
        """计算波动率评分"""
        try:
            if hasattr(market_data, 'change_percent'):
                change_percent = abs(float(market_data.change_percent)) if market_data.change_percent else 0
                
                # 基于当日涨跌幅评估波动率
                if change_percent > 5:
                    return 0.9  # 高波动
                elif change_percent > 3:
                    return 0.7  # 中等波动
                elif change_percent > 1:
                    return 0.5  # 正常波动
                else:
                    return 0.3  # 低波动
            
        except Exception as e:
            logger.error(f"计算波动率评分失败: {e}")
        
        return 0.5
    
    def _calculate_market_sentiment(self, market_data: Any) -> float:
        """计算市场情绪"""
        try:
            if hasattr(market_data, 'change_percent'):
                change_percent = float(market_data.change_percent) if market_data.change_percent else 0
                
                # 基于涨跌幅判断情绪
                if change_percent > 2:
                    return self._calculate_positive_score()
                elif change_percent > 0:
                    return 0.6  # 偏积极
                elif change_percent > -2:
                    return 0.4  # 偏消极
                else:
                    return 0.2  # 消极
            
        except Exception as e:
            logger.error(f"计算市场情绪失败: {e}")
        
        return 0.5
    
    def _determine_market_status(self, liquidity_score: float, volatility_score: float) -> str:
        """确定市场状态"""
        if liquidity_score > 0.7 and volatility_score < 0.5:
            return "optimal"  # 最优
        elif liquidity_score > 0.5 and volatility_score < 0.7:
            return "good"  # 良好
        elif liquidity_score > 0.3:
            return "normal"  # 正常
        else:
            return "poor"  # 较差
    
    def _calculate_optimal_window(self, market_data: Any) -> str:
        """计算最优执行窗口"""
        current_time = datetime.now().time()
        
        # 开盘前30分钟和收盘前30分钟通常流动性较好
        if current_time < datetime.strptime("10:00", "%H:%M").time():
            return "morning_optimal"
        elif current_time > datetime.strptime("14:30", "%H:%M").time():
            return "afternoon_optimal"
        else:
            return "intraday_normal"
    
    def _analyze_strategy_signal(self, strategy_signal: Dict[str, Any]) -> Dict[str, Any]:
        """分析策略信号"""
        try:
            signal_strength = strategy_signal.get("signal_strength", 50)
            confidence = strategy_signal.get("confidence", 0.5)
            recommendation = strategy_signal.get("recommendation", "hold")
            
            # 转换为执行动作
            if recommendation.lower() in ["buy", "买入"]:
                action = ExecutionAction.BUY
            elif recommendation.lower() in ["sell", "卖出"]:
                action = ExecutionAction.SELL
            elif recommendation.lower() in ["reduce", "减仓"]:
                action = ExecutionAction.REDUCE
            elif recommendation.lower() in ["add", "加仓"]:
                action = ExecutionAction.ADD
            else:
                action = ExecutionAction.HOLD
            
            # 计算建议仓位
            if signal_strength > 80:
                position_ratio = 0.8
            elif signal_strength > 60:
                position_ratio = 0.6
            elif signal_strength > 40:
                position_ratio = 0.4
            else:
                position_ratio = 0.2
            
            return {
                "action": action,
                "signal_strength": signal_strength,
                "confidence": confidence,
                "position_ratio": position_ratio,
                "urgency": "high" if signal_strength > 80 else "medium" if signal_strength > 60 else "low"
            }
            
        except Exception as e:
            logger.error(f"分析策略信号失败: {e}")
            return {
                "action": ExecutionAction.HOLD,
                "signal_strength": 50,
                "confidence": 0.5,
                "position_ratio": 0.3,
                "urgency": "low"
            }
    
    async def _calculate_optimal_execution(
        self, 
        stock_code: str, 
        signal_analysis: Dict[str, Any], 
        market_condition: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算最优执行参数"""
        
        try:
            # 获取当前价格 - 使用真实数据集成服务
            from .real_data_integration_service import real_data_service
            price_data_result = await real_data_service.get_real_stock_price(stock_code)
            if price_data_result.get("success"):
                current_price = float(price_data_result["data"]["current_price"])
            else:
                current_price = 10.0  # 默认价格
            
            # 计算目标价格（考虑滑点）
            action = signal_analysis["action"]
            if action == ExecutionAction.BUY:
                target_price = current_price * (1 + self.execution_config["slippage_rate"])
            elif action == ExecutionAction.SELL:
                target_price = current_price * (1 - self.execution_config["slippage_rate"])
            else:
                target_price = current_price
            
            # 计算交易数量
            position_ratio = signal_analysis["position_ratio"]
            available_capital = 100000  # 假设可用资金10万
            quantity = int((available_capital * position_ratio) / target_price / 100) * 100  # 整手
            
            # 计算预期成本
            total_amount = quantity * target_price
            commission = max(total_amount * self.execution_config["commission_rate"], self.execution_config["min_commission"])
            market_impact = total_amount * self.execution_config["market_impact_factor"]
            expected_cost = commission + market_impact
            
            # 计算预期收益
            expected_return = self._calculate_expected_return(signal_analysis, market_condition, total_amount)
            
            # 确定执行时机
            timing = self._determine_execution_timing(signal_analysis, market_condition)
            
            # 生成执行原因
            reason = self._generate_execution_reason(signal_analysis, market_condition, action)
            
            # 评估风险等级
            risk_level = self._assess_risk_level(signal_analysis, market_condition)
            
            return {
                "action": action,
                "timing": timing,
                "target_price": target_price,
                "quantity": quantity,
                "confidence": signal_analysis["confidence"],
                "reason": reason,
                "expected_cost": expected_cost,
                "expected_return": expected_return,
                "risk_level": risk_level
            }
            
        except Exception as e:
            logger.error(f"计算最优执行参数失败: {e}")
            return {
                "action": ExecutionAction.HOLD,
                "timing": ExecutionTiming.IMMEDIATE,
                "target_price": 10.0,
                "quantity": 0,
                "confidence": 0.3,
                "reason": f"计算失败: {str(e)}",
                "expected_cost": 0.0,
                "expected_return": 0.0,
                "risk_level": "高"
            }
    
    def _calculate_expected_return(self, signal_analysis: Dict[str, Any], market_condition: Dict[str, Any], total_amount: float) -> float:
        """计算预期收益"""
        try:
            signal_strength = signal_analysis["signal_strength"]
            confidence = signal_analysis["confidence"]
            
            # 基于信号强度和置信度计算预期收益率
            base_return_rate = (signal_strength - 50) / 100 * 0.1  # 基础收益率
            confidence_adjustment = confidence * 0.05  # 置信度调整
            market_adjustment = (market_condition["sentiment_score"] - 0.5) * 0.02  # 市场情绪调整
            
            expected_return_rate = base_return_rate + confidence_adjustment + market_adjustment
            expected_return = total_amount * expected_return_rate
            
            return expected_return
            
        except Exception as e:
            logger.error(f"计算预期收益失败: {e}")
            return 0.0
    
    def _determine_execution_timing(self, signal_analysis: Dict[str, Any], market_condition: Dict[str, Any]) -> ExecutionTiming:
        """确定执行时机"""
        urgency = signal_analysis["urgency"]
        market_status = market_condition["market_status"]
        optimal_window = market_condition["optimal_execution_window"]
        
        if urgency == "high":
            return ExecutionTiming.IMMEDIATE
        elif market_status == "optimal" and optimal_window in ["morning_optimal", "afternoon_optimal"]:
            return ExecutionTiming.INTRADAY_OPTIMAL
        elif market_status in ["good", "normal"]:
            return ExecutionTiming.MARKET_OPEN
        else:
            return ExecutionTiming.NEXT_DAY
    
    def _generate_execution_reason(self, signal_analysis: Dict[str, Any], market_condition: Dict[str, Any], action: ExecutionAction) -> str:
        """生成执行原因"""
        signal_strength = signal_analysis["signal_strength"]
        confidence = signal_analysis["confidence"]
        market_status = market_condition["market_status"]
        
        reason_parts = []
        
        if action == ExecutionAction.BUY:
            reason_parts.append(f"策略信号强度{signal_strength}分，建议买入")
        elif action == ExecutionAction.SELL:
            reason_parts.append(f"策略信号强度{signal_strength}分，建议卖出")
        
        reason_parts.append(f"置信度{confidence:.2f}")
        reason_parts.append(f"市场状态{market_status}")
        
        if market_condition["liquidity_score"] > 0.7:
            reason_parts.append("流动性良好")
        
        if market_condition["volatility_score"] > 0.7:
            reason_parts.append("注意高波动风险")
        
        return "，".join(reason_parts)
    
    def _assess_risk_level(self, signal_analysis: Dict[str, Any], market_condition: Dict[str, Any]) -> str:
        """评估风险等级"""
        confidence = signal_analysis["confidence"]
        volatility = market_condition["volatility_score"]
        liquidity = market_condition["liquidity_score"]
        
        risk_score = (1 - confidence) * 0.4 + volatility * 0.4 + (1 - liquidity) * 0.2
        
        if risk_score > 0.7:
            return "高"
        elif risk_score > 0.5:
            return "中"
        else:
            return "低"

    async def execute_trade(
        self,
        stock_code: str,
        stock_name: str,
        action: ExecutionAction,
        quantity: int,
        price: float,
        strategy_name: str
    ) -> TradeRecord:
        """执行交易（模拟）"""

        try:
            # 计算交易金额和佣金
            total_amount = quantity * price
            commission = max(total_amount * self.execution_config["commission_rate"], self.execution_config["min_commission"])

            # 创建交易记录
            trade_record = TradeRecord(
                trade_id=f"trade_{int(datetime.now().timestamp())}",
                stock_code=stock_code,
                stock_name=stock_name,
                action=action,
                quantity=quantity,
                price=price,
                total_amount=total_amount,
                commission=commission,
                timestamp=datetime.now(),
                strategy_name=strategy_name,
                is_simulated=True
            )

            # 更新持仓
            await self._update_portfolio_position(trade_record)

            # 保存交易记录
            await self._save_trade_record(trade_record)
            self.trade_records.append(trade_record)

            logger.info(f"玉衡星执行交易: {action.value} {stock_code} {quantity}股 @{price:.2f}")
            return trade_record

        except Exception as e:
            logger.error(f"执行交易失败: {e}")
            raise

    async def _update_portfolio_position(self, trade_record: TradeRecord):
        """更新持仓"""
        try:
            stock_code = trade_record.stock_code

            if stock_code not in self.portfolio_positions:
                self.portfolio_positions[stock_code] = {
                    "quantity": 0,
                    "avg_cost": 0.0,
                    "total_cost": 0.0
                }

            position = self.portfolio_positions[stock_code]

            if trade_record.action in [ExecutionAction.BUY, ExecutionAction.ADD]:
                # 买入或加仓
                new_total_cost = position["total_cost"] + trade_record.total_amount + trade_record.commission
                new_quantity = position["quantity"] + trade_record.quantity

                if new_quantity > 0:
                    position["avg_cost"] = new_total_cost / new_quantity
                    position["quantity"] = new_quantity
                    position["total_cost"] = new_total_cost

            elif trade_record.action in [ExecutionAction.SELL, ExecutionAction.REDUCE]:
                # 卖出或减仓
                position["quantity"] = max(0, position["quantity"] - trade_record.quantity)

                if position["quantity"] == 0:
                    position["avg_cost"] = 0.0
                    position["total_cost"] = 0.0

            # 保存持仓到数据库
            await self._save_portfolio_position(stock_code, position, trade_record.price)

        except Exception as e:
            logger.error(f"更新持仓失败: {e}")

    async def _save_trade_record(self, trade_record: TradeRecord):
        """保存交易记录"""
        if not self.db_connection:
            return

        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                INSERT INTO trade_records
                (trade_id, stock_code, stock_name, action, quantity, price,
                 total_amount, commission, timestamp, strategy_name, is_simulated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                trade_record.trade_id,
                trade_record.stock_code,
                trade_record.stock_name,
                trade_record.action.value,
                trade_record.quantity,
                trade_record.price,
                trade_record.total_amount,
                trade_record.commission,
                trade_record.timestamp.isoformat(),
                trade_record.strategy_name,
                trade_record.is_simulated
            ))

            self.db_connection.commit()

        except Exception as e:
            logger.error(f"保存交易记录失败: {e}")

    async def _save_portfolio_position(self, stock_code: str, position: Dict[str, Any], current_price: float):
        """保存持仓记录"""
        if not self.db_connection:
            return

        try:
            cursor = self.db_connection.cursor()

            market_value = position["quantity"] * current_price
            unrealized_pnl = market_value - position["total_cost"]

            cursor.execute("""
                INSERT OR REPLACE INTO portfolio_positions
                (position_id, stock_code, stock_name, quantity, avg_cost,
                 current_price, market_value, unrealized_pnl, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                f"pos_{stock_code}",
                stock_code,
                "股票名称",  # 这里应该从数据源获取
                position["quantity"],
                position["avg_cost"],
                current_price,
                market_value,
                unrealized_pnl,
                datetime.now().isoformat()
            ))

            self.db_connection.commit()

        except Exception as e:
            logger.error(f"保存持仓记录失败: {e}")

    async def _save_execution_recommendation(self, recommendation: ExecutionRecommendation):
        """保存执行建议"""
        if not self.db_connection:
            return

        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                INSERT INTO execution_recommendations
                (recommendation_id, stock_code, action, timing, target_price,
                 quantity, confidence, reason, expected_cost, expected_return,
                 risk_level, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                recommendation.recommendation_id,
                recommendation.stock_code,
                recommendation.action.value,
                recommendation.timing.value,
                recommendation.target_price,
                recommendation.quantity,
                recommendation.confidence,
                recommendation.reason,
                recommendation.expected_cost,
                recommendation.expected_return,
                recommendation.risk_level,
                recommendation.timestamp.isoformat()
            ))

            self.db_connection.commit()

        except Exception as e:
            logger.error(f"保存执行建议失败: {e}")

    async def calculate_profit_statistics(self, period: str = "all") -> ProfitStatistics:
        """计算收益统计"""

        try:
            # 确定时间范围
            end_date = datetime.now()
            if period == "day":
                start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
            elif period == "week":
                start_date = end_date - timedelta(days=7)
            elif period == "month":
                start_date = end_date - timedelta(days=30)
            elif period == "year":
                start_date = end_date - timedelta(days=365)
            else:
                start_date = datetime(2020, 1, 1)  # 全部时间

            # 获取时间范围内的交易记录
            period_trades = [
                trade for trade in self.trade_records
                if start_date <= trade.timestamp <= end_date
            ]

            if not period_trades:
                return ProfitStatistics(
                    period=period,
                    start_date=start_date,
                    end_date=end_date,
                    total_trades=0,
                    winning_trades=0,
                    losing_trades=0,
                    win_rate=0.0,
                    total_profit=0.0,
                    total_commission=0.0,
                    net_profit=0.0,
                    max_profit=0.0,
                    max_loss=0.0,
                    sharpe_ratio=0.0,
                    max_drawdown=0.0
                )

            # 计算基础统计
            total_trades = len(period_trades)
            total_commission = sum(trade.commission for trade in period_trades)

            # 计算盈亏
            profits = []
            for trade in period_trades:
                if trade.action in [ExecutionAction.SELL, ExecutionAction.REDUCE]:
                    # 卖出交易，计算盈亏
                    profit = self._calculate_trade_profit(trade)
                    profits.append(profit)

            if profits:
                total_profit = sum(profits)
                winning_trades = len([p for p in profits if p > 0])
                losing_trades = len([p for p in profits if p < 0])
                win_rate = winning_trades / len(profits) * 100
                max_profit = max(profits)
                max_loss = min(profits)
            else:
                total_profit = 0.0
                winning_trades = 0
                losing_trades = 0
                win_rate = 0.0
                max_profit = 0.0
                max_loss = 0.0

            net_profit = total_profit - total_commission

            # 计算夏普比率
            sharpe_ratio = self._calculate_sharpe_ratio(profits)

            # 计算最大回撤
            max_drawdown = self._calculate_max_drawdown(profits)

            statistics = ProfitStatistics(
                period=period,
                start_date=start_date,
                end_date=end_date,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_profit=total_profit,
                total_commission=total_commission,
                net_profit=net_profit,
                max_profit=max_profit,
                max_loss=max_loss,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown
            )

            # 保存统计结果
            await self._save_profit_statistics(statistics)

            return statistics

        except Exception as e:
            logger.error(f"计算收益统计失败: {e}")
            return ProfitStatistics(
                period=period,
                start_date=datetime.now(),
                end_date=datetime.now(),
                total_trades=0,
                winning_trades=0,
                losing_trades=0,
                win_rate=0.0,
                total_profit=0.0,
                total_commission=0.0,
                net_profit=0.0,
                max_profit=0.0,
                max_loss=0.0,
                sharpe_ratio=0.0,
                max_drawdown=0.0
            )

    def _calculate_trade_profit(self, trade: TradeRecord) -> float:
        """计算单笔交易盈亏"""
        try:
            if trade.action in [ExecutionAction.SELL, ExecutionAction.REDUCE]:
                pass
                profit = (trade.price - avg_cost) * trade.quantity - trade.commission
                return profit

        except Exception as e:
            logger.error(f"计算交易盈亏失败: {e}")

        return 0.0

    def _calculate_sharpe_ratio(self, profits: List[float]) -> float:
        """计算夏普比率"""
        try:
            if len(profits) < 2:
                return 0.0

            returns = np.array(profits)
            mean_return = np.mean(returns)
            std_return = np.std(returns)

            if std_return == 0:
                return 0.0

            # 年化夏普比率
            risk_free_rate = self.execution_config["risk_free_rate"]
            sharpe = (mean_return - risk_free_rate) / std_return * np.sqrt(252)  # 假设252个交易日

            return float(sharpe)

        except Exception as e:
            logger.error(f"计算夏普比率失败: {e}")
            return 0.0

    def _calculate_max_drawdown(self, profits: List[float]) -> float:
        """计算最大回撤"""
        try:
            if not profits:
                return 0.0

            cumulative = np.cumsum(profits)
            running_max = np.maximum.accumulate(cumulative)
            drawdown = (cumulative - running_max) / running_max
            max_drawdown = np.min(drawdown)

            return float(abs(max_drawdown)) if max_drawdown < 0 else 0.0

        except Exception as e:
            logger.error(f"计算最大回撤失败: {e}")
            return 0.0

    async def _save_profit_statistics(self, statistics: ProfitStatistics):
        """保存收益统计"""
        if not self.db_connection:
            return

        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                INSERT INTO profit_statistics
                (stat_id, period, start_date, end_date, total_trades, winning_trades,
                 losing_trades, win_rate, total_profit, total_commission, net_profit,
                 max_profit, max_loss, sharpe_ratio, max_drawdown)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                f"stat_{statistics.period}_{int(datetime.now().timestamp())}",
                statistics.period,
                statistics.start_date.isoformat(),
                statistics.end_date.isoformat(),
                statistics.total_trades,
                statistics.winning_trades,
                statistics.losing_trades,
                statistics.win_rate,
                statistics.total_profit,
                statistics.total_commission,
                statistics.net_profit,
                statistics.max_profit,
                statistics.max_loss,
                statistics.sharpe_ratio,
                statistics.max_drawdown
            ))

            self.db_connection.commit()

        except Exception as e:
            logger.error(f"保存收益统计失败: {e}")

    async def get_portfolio_summary(self) -> Dict[str, Any]:
        """获取投资组合摘要"""
        try:
            total_market_value = 0.0
            total_cost = 0.0
            total_unrealized_pnl = 0.0
            position_count = 0

            for stock_code, position in self.portfolio_positions.items():
                if position["quantity"] > 0:
                    # 获取当前价格 - 使用真实数据集成服务
                    from .real_data_integration_service import real_data_service
                    try:
                        price_data_result = await real_data_service.get_real_stock_price(stock_code)
                        if price_data_result.get("success"):
                            current_price = float(price_data_result["data"]["current_price"])
                        else:
                            current_price = position["avg_cost"]  # 使用成本价作为默认值
                    except Exception as e:
                        logger.warning(f"获取{stock_code}价格失败: {e}")
                        current_price = position["avg_cost"]

                    market_value = position["quantity"] * current_price
                    unrealized_pnl = market_value - position["total_cost"]

                    total_market_value += market_value
                    total_cost += position["total_cost"]
                    total_unrealized_pnl += unrealized_pnl
                    position_count += 1

            # 计算收益率
            total_return_rate = (total_unrealized_pnl / total_cost * 100) if total_cost > 0 else 0.0

            return {
                "position_count": position_count,
                "total_market_value": total_market_value,
                "total_cost": total_cost,
                "total_unrealized_pnl": total_unrealized_pnl,
                "total_return_rate": total_return_rate,
                "positions": self.portfolio_positions,
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取投资组合摘要失败: {e}")
            return {
                "position_count": 0,
                "total_market_value": 0.0,
                "total_cost": 0.0,
                "total_unrealized_pnl": 0.0,
                "total_return_rate": 0.0,
                "positions": {},
                "last_updated": datetime.now().isoformat()
            }

    async def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """获取综合统计报告"""
        try:
            # 计算各时间段的收益统计
            daily_stats = await self.calculate_profit_statistics("day")
            weekly_stats = await self.calculate_profit_statistics("week")
            monthly_stats = await self.calculate_profit_statistics("month")
            yearly_stats = await self.calculate_profit_statistics("year")
            all_time_stats = await self.calculate_profit_statistics("all")

            # 获取投资组合摘要
            portfolio_summary = await self.get_portfolio_summary()

            # 获取最近的执行建议
            recent_recommendations = self.execution_recommendations[-10:] if self.execution_recommendations else []

            return {
                "statistics": {
                    "daily": {
                        "total_trades": daily_stats.total_trades,
                        "net_profit": daily_stats.net_profit,
                        "win_rate": daily_stats.win_rate
                    },
                    "weekly": {
                        "total_trades": weekly_stats.total_trades,
                        "net_profit": weekly_stats.net_profit,
                        "win_rate": weekly_stats.win_rate
                    },
                    "monthly": {
                        "total_trades": monthly_stats.total_trades,
                        "net_profit": monthly_stats.net_profit,
                        "win_rate": monthly_stats.win_rate
                    },
                    "yearly": {
                        "total_trades": yearly_stats.total_trades,
                        "net_profit": yearly_stats.net_profit,
                        "win_rate": yearly_stats.win_rate,
                        "sharpe_ratio": yearly_stats.sharpe_ratio,
                        "max_drawdown": yearly_stats.max_drawdown
                    },
                    "all_time": {
                        "total_trades": all_time_stats.total_trades,
                        "net_profit": all_time_stats.net_profit,
                        "win_rate": all_time_stats.win_rate,
                        "sharpe_ratio": all_time_stats.sharpe_ratio,
                        "max_drawdown": all_time_stats.max_drawdown
                    }
                },
                "portfolio": portfolio_summary,
                "recent_recommendations": [
                    {
                        "stock_code": rec.stock_code,
                        "action": rec.action.value,
                        "target_price": rec.target_price,
                        "confidence": rec.confidence,
                        "reason": rec.reason,
                        "timestamp": rec.timestamp.isoformat()
                    }
                    for rec in recent_recommendations
                ],
                "performance_metrics": {
                    "total_return": all_time_stats.net_profit,
                    "win_rate": all_time_stats.win_rate,
                    "profit_factor": abs(all_time_stats.max_profit / all_time_stats.max_loss) if all_time_stats.max_loss != 0 else 0,
                    "avg_trade_profit": all_time_stats.net_profit / all_time_stats.total_trades if all_time_stats.total_trades > 0 else 0,
                    "sharpe_ratio": all_time_stats.sharpe_ratio,
                    "max_drawdown": all_time_stats.max_drawdown
                },
                "generated_at": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取综合统计失败: {e}")
            return {
                "statistics": {},
                "portfolio": {},
                "recent_recommendations": [],
                "performance_metrics": {},
                "error": str(e),
                "generated_at": datetime.now().isoformat()
            }

# 全局实例
intelligent_execution_service = IntelligentExecutionAndProfitService()
