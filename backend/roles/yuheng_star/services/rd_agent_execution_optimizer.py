from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD-Agent执行优化引擎 - 天机星-交易执行官
实现基于RD-Agent的交易执行优化、策略学习和自适应改进
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pydantic import BaseModel, Field
from enum import Enum
import sqlite3
import os
from pathlib import Path
import json
import uuid
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class ExecutionOptimizationType(Enum):
    """执行优化类型"""
    TIMING_OPTIMIZATION = "timing_optimization"
    COST_MINIMIZATION = "cost_minimization"
    SLIPPAGE_REDUCTION = "slippage_reduction"
    FILL_RATE_IMPROVEMENT = "fill_rate_improvement"
    STRATEGY_ADAPTATION = "strategy_adaptation"

class LearningSignal(Enum):
    """学习信号"""
    EXECUTION_SUCCESS = "execution_success"
    EXECUTION_FAILURE = "execution_failure"
    COST_OVERRUN = "cost_overrun"
    SLIPPAGE_EXCESSIVE = "slippage_excessive"
    TIMING_SUBOPTIMAL = "timing_suboptimal"

@dataclass
class ExecutionExperience:
    """执行经验"""
    experience_id: str
    order_characteristics: Dict[str, Any]
    market_conditions: Dict[str, Any]
    execution_strategy: str
    execution_result: Dict[str, Any]
    performance_metrics: Dict[str, float]
    learning_signals: List[LearningSignal]
    timestamp: datetime

class ExecutionStrategyLearner:
    """执行策略学习器"""

    def __init__(self):
        self.experience_buffer = []
        self.strategy_performance = {}
        self.adaptation_rules = {}
        self.learning_rate = 0.1

    async def learn_from_execution(self, experience: ExecutionExperience) -> Dict[str, Any]:
        """从执行经验中学习"""

        try:
            # 添加经验到缓冲区
            self.experience_buffer.append(experience)

            # 更新策略性能
            await self._update_strategy_performance(experience)

            # 提取学习信号
            learning_insights = await self._extract_learning_insights(experience)

            # 更新适应规则
            await self._update_adaptation_rules(experience, learning_insights)

            # 生成优化建议
            optimization_suggestions = await self._generate_optimization_suggestions(experience)

            return {
                "learning_insights": learning_insights,
                "optimization_suggestions": optimization_suggestions,
                "strategy_performance_update": self.strategy_performance.get(experience.execution_strategy, {}),
                "adaptation_rules_updated": len(self.adaptation_rules)
            }

        except Exception as e:
            logger.error(f"执行学习失败: {e}")
            raise

    async def _update_strategy_performance(self, experience: ExecutionExperience):
        """更新策略性能"""

        strategy = experience.execution_strategy
        metrics = experience.performance_metrics

        if strategy not in self.strategy_performance:
            self.strategy_performance[strategy] = {
                "execution_count": 0,
                "avg_cost": 0.0,
                "avg_slippage": 0.0,
                "fill_rate": 0.0,
                "success_rate": 0.0
            }

        perf = self.strategy_performance[strategy]
        count = perf["execution_count"]

        # 使用指数移动平均更新性能指标
        alpha = self.learning_rate

        perf["avg_cost"] = (1 - alpha) * perf["avg_cost"] + alpha * metrics.get("cost_bps", 0)
        perf["avg_slippage"] = (1 - alpha) * perf["avg_slippage"] + alpha * metrics.get("slippage_bps", 0)
        perf["fill_rate"] = (1 - alpha) * perf["fill_rate"] + alpha * metrics.get("fill_rate", 0)

        # 判断执行是否成功
        success = len([s for s in experience.learning_signals if s in [
            LearningSignal.EXECUTION_SUCCESS
        ]]) > 0

        perf["success_rate"] = (1 - alpha) * perf["success_rate"] + alpha * (1.0 if success else 0.0)
        perf["execution_count"] += 1

    async def _extract_learning_insights(self, experience: ExecutionExperience) -> Dict[str, Any]:
        """提取学习洞察"""

        insights = {
            "market_condition_impact": {},
            "order_size_impact": {},
            "timing_impact": {},
            "strategy_effectiveness": {}
        }

        # 分析市场条件影响
        market_conditions = experience.market_conditions
        performance = experience.performance_metrics

        # 流动性影响
        liquidity_level = market_conditions.get("liquidity_level", "medium")
        insights["market_condition_impact"]["liquidity"] = {
            "level": liquidity_level,
            "cost_impact": performance.get("cost_bps", 0),
            "slippage_impact": performance.get("slippage_bps", 0)
        }

        # 波动率影响
        volatility = market_conditions.get("volatility", 0.02)
        insights["market_condition_impact"]["volatility"] = {
            "level": volatility,
            "timing_cost": performance.get("timing_cost", 0)
        }

        # 订单规模影响
        order_size = experience.order_characteristics.get("quantity", 0)
        market_depth = market_conditions.get("depth", 1000000)
        size_ratio = order_size / market_depth

        insights["order_size_impact"] = {
            "size_ratio": size_ratio,
            "market_impact": performance.get("market_impact", 0),
            "fill_difficulty": 1.0 - performance.get("fill_rate", 1.0)
        }

        # 时机影响
        execution_hour = experience.timestamp.hour
        insights["timing_impact"] = {
            "execution_hour": execution_hour,
            "is_peak_hour": 9 <= execution_hour <= 10 or 14 <= execution_hour <= 15,
            "timing_cost": performance.get("timing_cost", 0)
        }

        # 策略有效性
        strategy = experience.execution_strategy
        insights["strategy_effectiveness"] = {
            "strategy": strategy,
            "overall_performance": self._calculate_strategy_score(performance),
            "relative_performance": self._compare_strategy_performance(strategy)
        }

        return insights

    def _calculate_strategy_score(self, performance: Dict[str, float]) -> float:
        """计算策略评分"""

        # 综合评分：成本、滑点、成交率
        cost_score = max(0, 1 - performance.get("cost_bps", 0) / 50)  # 50bps为基准
        slippage_score = max(0, 1 - performance.get("slippage_bps", 0) / 20)  # 20bps为基准
        fill_score = performance.get("fill_rate", 0)

        overall_score = (cost_score * 0.4 + slippage_score * 0.3 + fill_score * 0.3)
        return overall_score

    def _compare_strategy_performance(self, strategy: str) -> Dict[str, float]:
        """比较策略性能"""

        if strategy not in self.strategy_performance:
            return {"relative_rank": 0.5, "performance_percentile": 50}

        current_perf = self.strategy_performance[strategy]
        all_strategies = list(self.strategy_performance.values())

        if len(all_strategies) <= 1:
            return {"relative_rank": 1.0, "performance_percentile": 100}

        # 计算相对排名
        current_score = self._calculate_strategy_score({
            "cost_bps": current_perf["avg_cost"],
            "slippage_bps": current_perf["avg_slippage"],
            "fill_rate": current_perf["fill_rate"]
        })

        better_strategies = sum(1 for perf in all_strategies
                              if self._calculate_strategy_score({
                                  "cost_bps": perf["avg_cost"],
                                  "slippage_bps": perf["avg_slippage"],
                                  "fill_rate": perf["fill_rate"]
                              }) > current_score)

        relative_rank = 1 - (better_strategies / len(all_strategies))
        performance_percentile = relative_rank * 100

        return {
            "relative_rank": relative_rank,
            "performance_percentile": performance_percentile
        }

    async def _update_adaptation_rules(self, experience: ExecutionExperience, insights: Dict):
        """更新适应规则"""

        # 基于学习信号更新规则
        for signal in experience.learning_signals:
            rule_key = f"{signal.value}_{experience.execution_strategy}"

            if rule_key not in self.adaptation_rules:
                self.adaptation_rules[rule_key] = {
                    "trigger_count": 0,
                    "conditions": {},
                    "adaptations": []
                }

            rule = self.adaptation_rules[rule_key]
            rule["trigger_count"] += 1

            # 更新触发条件
            if signal == LearningSignal.COST_OVERRUN:
                rule["conditions"]["high_cost_threshold"] = experience.performance_metrics.get("cost_bps", 0)
                rule["adaptations"].append("reduce_participation_rate")
            elif signal == LearningSignal.SLIPPAGE_EXCESSIVE:
                rule["conditions"]["high_slippage_threshold"] = experience.performance_metrics.get("slippage_bps", 0)
                rule["adaptations"].append("extend_execution_time")
            elif signal == LearningSignal.TIMING_SUBOPTIMAL:
                rule["conditions"]["suboptimal_timing_hour"] = experience.timestamp.hour
                rule["adaptations"].append("avoid_low_liquidity_periods")

    async def _generate_optimization_suggestions(self, experience: ExecutionExperience) -> List[str]:
        """生成优化建议"""

        suggestions = []
        performance = experience.performance_metrics

        # 基于成本的建议
        if performance.get("cost_bps", 0) > 30:
            suggestions.append("成本过高，建议使用TWAP或VWAP策略降低市场冲击")

        # 基于滑点的建议
        if performance.get("slippage_bps", 0) > 15:
            suggestions.append("滑点过大，建议延长执行时间或降低参与率")

        # 基于成交率的建议
        if performance.get("fill_rate", 1.0) < 0.8:
            suggestions.append("成交率偏低，建议调整价格策略或增加执行时间")

        # 基于市场条件的建议
        liquidity_level = experience.market_conditions.get("liquidity_level", "medium")
        if liquidity_level == "low_liquidity":
            suggestions.append("当前流动性较低，建议避免大额订单或选择流动性更好的时段")

        # 基于时机的建议
        execution_hour = experience.timestamp.hour
        if execution_hour in [12, 13]:  # 午间时段
            suggestions.append("午间时段流动性较低，建议避免在此时段执行大额交易")

        return suggestions

class RDAgentExecutionOptimizer:
    """RD-Agent执行优化引擎"""

    def __init__(self):
        self.strategy_learner = ExecutionStrategyLearner()
        self.optimization_history = []
        self.active_optimizations = {}

        # 初始化数据库
        self.db_path = "backend/data/yaoguang_distribution.db"
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self._init_database()

        logger.info("RD-Agent执行优化引擎初始化完成")

    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建执行经验表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS execution_experiences (
                experience_id TEXT PRIMARY KEY,
                order_characteristics TEXT,
                market_conditions TEXT,
                execution_strategy TEXT,
                execution_result TEXT,
                performance_metrics TEXT,
                learning_signals TEXT,
                timestamp TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建优化建议表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS optimization_suggestions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                experience_id TEXT,
                suggestion_type TEXT,
                suggestion_content TEXT,
                confidence REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

    async def optimize_execution_strategy(
        self,
        order_characteristics: Dict[str, Any],
        market_conditions: Dict[str, Any],
        current_strategy: str
    ) -> Dict[str, Any]:
        """优化执行策略"""

        try:
            logger.info(f"开始优化执行策略: {current_strategy}")

            # 分析当前市场条件
            market_analysis = await self._analyze_market_conditions(market_conditions)

            # 评估订单特征
            order_analysis = await self._analyze_order_characteristics(order_characteristics)

            # 基于历史经验推荐策略
            strategy_recommendation = await self._recommend_optimal_strategy(
                order_analysis, market_analysis
            )

            # 生成执行参数优化
            parameter_optimization = await self._optimize_execution_parameters(
                strategy_recommendation, order_characteristics, market_conditions
            )

            # 预测执行结果
            execution_forecast = await self._forecast_execution_performance(
                strategy_recommendation, parameter_optimization, market_conditions
            )

            optimization_result = {
                "recommended_strategy": strategy_recommendation,
                "optimized_parameters": parameter_optimization,
                "execution_forecast": execution_forecast,
                "market_analysis": market_analysis,
                "order_analysis": order_analysis,
                "confidence": execution_forecast.get("confidence", 0.8)
            }

            # 保存优化结果
            await self._save_optimization_result(optimization_result)

            logger.info(f"执行策略优化完成: {strategy_recommendation['strategy']}")
            return optimization_result

        except Exception as e:
            logger.error(f"执行策略优化失败: {e}")
            raise

    async def _analyze_market_conditions(self, market_conditions: Dict) -> Dict[str, Any]:
        """分析市场条件"""

        analysis = {
            "liquidity_assessment": {},
            "volatility_assessment": {},
            "timing_assessment": {},
            "overall_favorability": 0.0
        }

        # 流动性评估
        liquidity_level = market_conditions.get("liquidity_level", "medium")
        depth = market_conditions.get("depth", 1000000)
        volume = market_conditions.get("volume", 50000)

        liquidity_score = {
            "high_liquidity": 0.9,
            "medium_liquidity": 0.7,
            "low_liquidity": 0.4
        }.get(liquidity_level, 0.5)

        analysis["liquidity_assessment"] = {
            "level": liquidity_level,
            "depth": depth,
            "volume": volume,
            "score": liquidity_score
        }

        # 波动率评估
        volatility = market_conditions.get("volatility", 0.02)
        volatility_score = max(0.1, 1.0 - volatility * 20)  # 波动率越高，评分越低

        analysis["volatility_assessment"] = {
            "volatility": volatility,
            "score": volatility_score,
            "risk_level": "high" if volatility > 0.03 else "medium" if volatility > 0.015 else "low"
        }

        # 时机评估
        current_hour = datetime.now().hour
        timing_score = self._calculate_timing_score(current_hour)

        analysis["timing_assessment"] = {
            "current_hour": current_hour,
            "score": timing_score,
            "is_optimal": timing_score > 0.8
        }

        # 综合评估
        analysis["overall_favorability"] = (
            liquidity_score * 0.4 +
            volatility_score * 0.3 +
            timing_score * 0.3
        )

        return analysis

    def _calculate_timing_score(self, hour: int) -> float:
        """计算时机评分"""

        # 基于历史数据的时段评分
        timing_scores = {
            9: 0.9,   # 开盘高流动性
            10: 0.8,  # 开盘后
            11: 0.6,  # 上午
            12: 0.4,  # 午间
            13: 0.4,  # 午间
            14: 0.8,  # 下午开始
            15: 0.9   # 收盘前
        }

        return timing_scores.get(hour, 0.5)

    async def _analyze_order_characteristics(self, order_characteristics: Dict) -> Dict[str, Any]:
        """分析订单特征"""

        quantity = order_characteristics.get("quantity", 0)
        urgency = order_characteristics.get("urgency", 0.5)
        risk_tolerance = order_characteristics.get("risk_tolerance", 0.5)

        analysis = {
            "size_category": self._categorize_order_size(quantity),
            "urgency_level": self._categorize_urgency(urgency),
            "risk_profile": self._categorize_risk_tolerance(risk_tolerance),
            "complexity_score": self._calculate_order_complexity(order_characteristics)
        }

        return analysis

    def _categorize_order_size(self, quantity: int) -> str:
        """分类订单规模"""
        if quantity < 10000:
            return "small"
        elif quantity < 100000:
            return "medium"
        elif quantity < 1000000:
            return "large"
        else:
            return "block"

    def _categorize_urgency(self, urgency: float) -> str:
        """分类紧急程度"""
        if urgency < 0.3:
            return "low"
        elif urgency < 0.7:
            return "medium"
        else:
            return "high"

    def _categorize_risk_tolerance(self, risk_tolerance: float) -> str:
        """分类风险容忍度"""
        if risk_tolerance < 0.3:
            return "conservative"
        elif risk_tolerance < 0.7:
            return "moderate"
        else:
            return "aggressive"

    def _calculate_order_complexity(self, order_characteristics: Dict) -> float:
        """计算订单复杂度"""

        complexity_factors = [
            order_characteristics.get("has_constraints", False),
            order_characteristics.get("multi_leg", False),
            order_characteristics.get("conditional", False)
        ]

        complexity_score = sum(complexity_factors) / len(complexity_factors)
        return complexity_score

    async def _recommend_optimal_strategy(self, order_analysis: Dict, market_analysis: Dict) -> Dict[str, Any]:
        """推荐最优策略"""

        # 基于订单和市场分析推荐策略
        size_category = order_analysis["size_category"]
        urgency_level = order_analysis["urgency_level"]
        risk_profile = order_analysis["risk_profile"]

        liquidity_score = market_analysis["liquidity_assessment"]["score"]
        volatility_score = market_analysis["volatility_assessment"]["score"]
        timing_score = market_analysis["timing_assessment"]["score"]

        # 策略评分矩阵
        strategy_scores = {
            "market": 0.0,
            "twap": 0.0,
            "vwap": 0.0,
            "pov": 0.0,
            "implementation_shortfall": 0.0
        }

        # 基于订单规模的策略偏好
        if size_category == "small":
            strategy_scores["market"] += 0.8
            strategy_scores["twap"] += 0.3
        elif size_category == "medium":
            strategy_scores["twap"] += 0.7
            strategy_scores["vwap"] += 0.8
            strategy_scores["pov"] += 0.6
        elif size_category == "large":
            strategy_scores["vwap"] += 0.9
            strategy_scores["twap"] += 0.8
            strategy_scores["implementation_shortfall"] += 0.7
        else:  # block
            strategy_scores["implementation_shortfall"] += 0.9
            strategy_scores["vwap"] += 0.6

        # 基于紧急程度的策略偏好
        if urgency_level == "high":
            strategy_scores["market"] += 0.6
            strategy_scores["implementation_shortfall"] += 0.4
        elif urgency_level == "medium":
            strategy_scores["twap"] += 0.5
            strategy_scores["pov"] += 0.6
        else:  # low
            strategy_scores["vwap"] += 0.5
            strategy_scores["twap"] += 0.6

        # 基于市场条件的策略偏好
        if liquidity_score > 0.8:
            strategy_scores["market"] += 0.3
            strategy_scores["pov"] += 0.4
        elif liquidity_score < 0.5:
            strategy_scores["twap"] += 0.4
            strategy_scores["implementation_shortfall"] += 0.3

        if volatility_score > 0.8:
            strategy_scores["twap"] += 0.3
            strategy_scores["vwap"] += 0.4

        # 选择最高评分的策略
        best_strategy = max(strategy_scores, key=strategy_scores.get)
        best_score = strategy_scores[best_strategy]

        return {
            "strategy": best_strategy,
            "confidence": min(0.95, best_score),
            "strategy_scores": strategy_scores,
            "reasoning": f"基于{size_category}订单规模、{urgency_level}紧急程度和当前市场条件推荐"
        }

    async def _optimize_execution_parameters(
        self,
        strategy_recommendation: Dict,
        order_characteristics: Dict,
        market_conditions: Dict
    ) -> Dict[str, Any]:
        """优化执行参数"""

        strategy = strategy_recommendation["strategy"]

        # 基础参数
        base_params = {
            "participation_rate": 0.1,
            "time_horizon": 60,
            "urgency_factor": 0.5,
            "max_deviation": 0.02
        }

        # 基于策略调整参数
        if strategy == "market":
            optimized_params = {
                "participation_rate": 1.0,  # 立即执行
                "time_horizon": 1,
                "urgency_factor": 1.0,
                "max_deviation": 0.05
            }
        elif strategy == "twap":
            optimized_params = {
                "participation_rate": 0.15,
                "time_horizon": 90,
                "urgency_factor": 0.3,
                "max_deviation": 0.015
            }
        elif strategy == "vwap":
            optimized_params = {
                "participation_rate": 0.2,
                "time_horizon": 120,
                "urgency_factor": 0.4,
                "max_deviation": 0.02
            }
        elif strategy == "pov":
            optimized_params = {
                "participation_rate": 0.12,
                "time_horizon": 75,
                "urgency_factor": 0.6,
                "max_deviation": 0.025
            }
        else:  # implementation_shortfall
            optimized_params = {
                "participation_rate": 0.08,
                "time_horizon": 150,
                "urgency_factor": order_characteristics.get("urgency", 0.5),
                "max_deviation": 0.01
            }

        # 基于市场条件微调
        liquidity_level = market_conditions.get("liquidity_level", "medium")
        if liquidity_level == "low_liquidity":
            optimized_params["participation_rate"] *= 0.7
            optimized_params["time_horizon"] *= 1.3
        elif liquidity_level == "high_liquidity":
            optimized_params["participation_rate"] *= 1.2
            optimized_params["time_horizon"] *= 0.8

        return {
            "optimized_parameters": optimized_params,
            "parameter_adjustments": {
                "liquidity_adjustment": liquidity_level,
                "volatility_adjustment": market_conditions.get("volatility", 0.02)
            }
        }

    async def _forecast_execution_performance(
        self,
        strategy_recommendation: Dict,
        parameter_optimization: Dict,
        market_conditions: Dict
    ) -> Dict[str, Any]:
        """预测执行表现"""

        strategy = strategy_recommendation["strategy"]
        params = parameter_optimization["optimized_parameters"]

        # 基于策略和参数预测性能
        base_cost_bps = {
            "market": 25.0,
            "twap": 15.0,
            "vwap": 12.0,
            "pov": 18.0,
            "implementation_shortfall": 10.0
        }.get(strategy, 15.0)

        # 基于市场条件调整
        liquidity_adjustment = {
            "high_liquidity": 0.8,
            "medium_liquidity": 1.0,
            "low_liquidity": 1.4
        }.get(market_conditions.get("liquidity_level", "medium"), 1.0)

        volatility = market_conditions.get("volatility", 0.02)
        volatility_adjustment = 1.0 + (volatility - 0.02) * 10

        # 预测成本
        predicted_cost_bps = base_cost_bps * liquidity_adjustment * volatility_adjustment

        # 预测成交率
        predicted_fill_rate = {
            "market": 0.95,
            "twap": 0.90,
            "vwap": 0.92,
            "pov": 0.88,
            "implementation_shortfall": 0.85
        }.get(strategy, 0.90)

        # 预测执行时间
        predicted_duration = params["time_horizon"]

        # 综合置信度
        strategy_confidence = strategy_recommendation["confidence"]
        market_favorability = (liquidity_adjustment + (2.0 - volatility_adjustment)) / 2
        overall_confidence = (strategy_confidence + market_favorability) / 2

        return {
            "predicted_cost_bps": predicted_cost_bps,
            "predicted_fill_rate": predicted_fill_rate,
            "predicted_duration": predicted_duration,
            "confidence": min(0.95, max(0.1, overall_confidence)),
            "risk_factors": {
                "liquidity_risk": 1.0 - liquidity_adjustment,
                "volatility_risk": volatility_adjustment - 1.0,
                "execution_risk": 1.0 - predicted_fill_rate
            }
        }

    async def _save_optimization_result(self, optimization_result: Dict):
        """保存优化结果"""

        try:
            self.optimization_history.append({
                "timestamp": datetime.now(),
                "result": optimization_result
            })

            # 保持历史记录在合理范围内
            if len(self.optimization_history) > 1000:
                self.optimization_history = self.optimization_history[-500:]

        except Exception as e:
            logger.error(f"保存优化结果失败: {e}")