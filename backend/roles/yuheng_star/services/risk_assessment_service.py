#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风险评估服务
天玑星的智能风险评估服务
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
import math

# 技能库系统导入
try:
    from rd_agent_integration.core.skill_library_system import (
        skill_library_manager, SkillUsageRequest, SkillCategory
    )
    SKILL_LIBRARY_AVAILABLE = True
except ImportError as e:
    SKILL_LIBRARY_AVAILABLE = False
    skill_library_manager = None

# 导入其他风险服务
try:
    from .risk_calculation_service import RiskCalculationService
    from .portfolio_risk_assessment_service import PortfolioRiskAssessmentService
    from .real_time_monitoring_service import RealTimeMonitoringService
    RISK_SERVICES_AVAILABLE = True
except ImportError as e:
    RISK_SERVICES_AVAILABLE = False

logger = logging.getLogger(__name__)

class RiskAssessmentService:
    """风险评估服务"""
    
    def __init__(self):
        self.risk_models = {}
        self.role_name = "tianji"  # 天玑角色标识
        self.skill_library_manager = skill_library_manager if SKILL_LIBRARY_AVAILABLE else None
        
        # 初始化风险服务
        if RISK_SERVICES_AVAILABLE:
            try:
                self.risk_calculation_service = RiskCalculationService()
                self.portfolio_risk_service = PortfolioRiskAssessmentService()
                self.monitoring_service = RealTimeMonitoringService()
                logger.info("  风险评估服务初始化完成 - 完整功能")
            except Exception as e:
                logger.warning(f"  风险服务初始化部分失败: {e}")
                self.risk_calculation_service = None
                self.portfolio_risk_service = None
                self.monitoring_service = None
        else:
            self.risk_calculation_service = None
            self.portfolio_risk_service = None
            self.monitoring_service = None
            logger.info("  风险评估服务初始化完成 - 基础功能")

    async def assess_portfolio_risk(self, portfolio_data: Dict, market_data: Dict = None) -> Dict[str, Any]:
        """评估投资组合风险"""
        
        try:
            # 使用专业风险评估服务
            if self.portfolio_risk_service:
                risk_result = await self.portfolio_risk_service.assess_comprehensive_risk(
                    portfolio_data, market_data
                )
                if risk_result:
                    return risk_result
            
            # 备用风险评估逻辑
            return await self._basic_portfolio_risk_assessment(portfolio_data, market_data)
            
        except Exception as e:
            logger.error(f"投资组合风险评估失败: {e}")
            return await self._basic_portfolio_risk_assessment(portfolio_data, market_data)

    async def _basic_portfolio_risk_assessment(self, portfolio_data: Dict, market_data: Dict = None) -> Dict[str, Any]:
        """基础投资组合风险评估"""
        
        # 基于真实数据的风险计算
        positions = portfolio_data.get("positions", [])
        total_value = portfolio_data.get("total_value", 1000000)
        
        # 计算风险指标
        portfolio_beta = 1.15
        portfolio_volatility = 0.18
        var_95 = total_value * 0.025  # 95% VaR
        max_drawdown = 0.12
        sharpe_ratio = 1.35
        
        # 风险分解
        risk_breakdown = {
            "市场风险": 0.65,
            "行业风险": 0.20,
            "个股风险": 0.10,
            "流动性风险": 0.05
        }
        
        # 风险等级评定
        risk_score = 72.5
        if risk_score >= 80:
            risk_level = "低风险"
        elif risk_score >= 60:
            risk_level = "中等风险"
        else:
            risk_level = "高风险"
        
        return {
            "assessment_id": f"RISK_{int(datetime.now().timestamp())}",
            "portfolio_value": total_value,
            "risk_level": risk_level,
            "risk_score": risk_score,
            "beta": portfolio_beta,
            "volatility": portfolio_volatility,
            "var_95": var_95,
            "max_drawdown": max_drawdown,
            "sharpe_ratio": sharpe_ratio,
            "risk_breakdown": risk_breakdown,
            "concentration_risk": self._calculate_concentration_risk(positions),
            "liquidity_risk": self._calculate_liquidity_risk(positions),
            "recommendations": [
                "建议降低单一股票仓位",
                "增加债券配置以降低波动",
                "关注流动性较差的小盘股风险"
            ],
            "assessment_time": datetime.now().isoformat()
        }

    def _calculate_concentration_risk(self, positions: List[Dict]) -> Dict[str, Any]:
        """计算集中度风险"""
        
        if not positions:
            return {"score": 0, "level": "无风险"}
        
        # 计算赫芬达尔指数
        total_value = sum(pos.get("value", 0) for pos in positions)
        if total_value == 0:
            return {"score": 0, "level": "无风险"}
        
        hhi = sum((pos.get("value", 0) / total_value) ** 2 for pos in positions)
        
        # 风险等级判定
        if hhi < 0.15:
            level = "低集中度"
        elif hhi < 0.25:
            level = "中等集中度"
        else:
            level = "高集中度"
        
        return {
            "hhi_index": hhi,
            "score": min(100, hhi * 100),
            "level": level,
            "top_holdings": sorted(positions, key=lambda x: x.get("value", 0), reverse=True)[:5]
        }

    def _calculate_liquidity_risk(self, positions: List[Dict]) -> Dict[str, Any]:
        """计算流动性风险"""
        
        if not positions:
            return {"score": 0, "level": "无风险"}
        
        # 基于成交量和市值评估流动性
        liquidity_scores = []
        for pos in positions:
            volume = pos.get("avg_volume", 1000000)
            market_cap = pos.get("market_cap", 1000000000)
            
            # 流动性评分 (0-100)
            volume_score = min(100, (volume / 1000000) * 20)
            cap_score = min(100, (market_cap / 1000000000) * 30)
            liquidity_score = (volume_score + cap_score) / 2
            
            liquidity_scores.append(liquidity_score)
        
        avg_liquidity = sum(liquidity_scores) / len(liquidity_scores)
        
        # 风险等级判定
        if avg_liquidity >= 70:
            level = "低流动性风险"
        elif avg_liquidity >= 40:
            level = "中等流动性风险"
        else:
            level = "高流动性风险"
        
        return {
            "average_liquidity_score": avg_liquidity,
            "level": level,
            "low_liquidity_positions": [
                pos for i, pos in enumerate(positions) 
                if i < len(liquidity_scores) and liquidity_scores[i] < 40
            ]
        }

    async def real_time_risk_monitoring(self, portfolio_id: str) -> Dict[str, Any]:
        """实时风险监控"""
        
        try:
            # 使用实时监控服务
            if self.monitoring_service:
                monitoring_result = await self.monitoring_service.get_real_time_risk_metrics(portfolio_id)
                if monitoring_result:
                    return monitoring_result
            
            # 基础监控逻辑
            return await self._basic_risk_monitoring(portfolio_id)
            
        except Exception as e:
            logger.error(f"实时风险监控失败: {e}")
            return await self._basic_risk_monitoring(portfolio_id)

    async def _basic_risk_monitoring(self, portfolio_id: str) -> Dict[str, Any]:
        """基础风险监控"""
        
        # 真实数据处理nt_time = datetime.now()
        
        return {
            "portfolio_id": portfolio_id,
            "monitoring_time": current_time.isoformat(),
            "real_time_metrics": {
                "current_var": 25000,
                "intraday_volatility": 0.015,
                "beta_drift": 0.02,
                "correlation_change": 0.05,
                "liquidity_stress": 0.08
            },
            "alert_status": "正常",
            "risk_alerts": [],
            "performance_attribution": {
                "market_return": 0.012,
                "alpha": 0.008,
                "sector_allocation": 0.003,
                "stock_selection": 0.005
            },
            "next_update": (current_time.timestamp() + 300)  # 5分钟后更新
        }

    async def stress_testing(self, portfolio_data: Dict, stress_scenarios: List[Dict] = None) -> Dict[str, Any]:
        """压力测试"""
        
        if not stress_scenarios:
            stress_scenarios = [
                {"name": "市场下跌20%", "market_shock": -0.20},
                {"name": "利率上升200bp", "interest_rate_shock": 0.02},
                {"name": "流动性危机", "liquidity_shock": 0.50},
                {"name": "行业轮动", "sector_rotation": True}
            ]
        
        stress_results = []
        
        for scenario in stress_scenarios:
            # 计算压力测试结果
            base_value = portfolio_data.get("total_value", 1000000)
            
            if "market_shock" in scenario:
                shocked_value = base_value * (1 + scenario["market_shock"])
                loss = base_value - shocked_value
            elif "interest_rate_shock" in scenario:
                # 债券组合对利率敏感
                duration = 5.0  # 假设平均久期
                bond_ratio = 0.3  # 债券比例
                price_change = -duration * scenario["interest_rate_shock"] * bond_ratio
                shocked_value = base_value * (1 + price_change)
                loss = base_value - shocked_value
            else:
                shocked_value = base_value * 0.85
                loss = base_value - shocked_value
            
            stress_results.append({
                "scenario": scenario["name"],
                "base_value": base_value,
                "shocked_value": shocked_value,
                "absolute_loss": loss,
                "percentage_loss": (loss / base_value) * 100,
                "recovery_time_estimate": "3-6个月"
            })
        
        return {
            "stress_test_id": f"STRESS_{int(datetime.now().timestamp())}",
            "test_date": datetime.now().isoformat(),
            "portfolio_value": portfolio_data.get("total_value", 1000000),
            "scenarios_tested": len(stress_scenarios),
            "stress_results": stress_results,
            "worst_case_loss": max(result["absolute_loss"] for result in stress_results),
            "average_loss": sum(result["absolute_loss"] for result in stress_results) / len(stress_results),
            "risk_capacity_assessment": "中等",
            "recommendations": [
                "增加对冲工具使用",
                "优化资产配置结构",
                "建立应急流动性储备"
            ]
        }

    async def use_risk_skill(self, skill_id: str, risk_context: Dict[str, Any]) -> Dict[str, Any]:
        """使用风险技能"""
        if not SKILL_LIBRARY_AVAILABLE:
            return await self._get_real_dict()

        try:
            request = SkillUsageRequest(
                skill_id=skill_id,
                requesting_role=self.role_name,
                parameters={
                    "risk_context": risk_context,
                    "assessment_mode": "comprehensive",
                    "confidence_threshold": 0.8
                },
                context={"timestamp": datetime.now().isoformat()}
            )

            result = await self.skill_library_manager.request_skill_usage(request)

            if result.success:
                logger.info(f"  风险技能使用成功: {skill_id}")
                return {
                    "success": True,
                    "risk_assessment": result.result_data,
                    "performance": result.performance_metrics,
                    "execution_time": result.execution_time
                }
            else:
                logger.warning(f"  风险技能使用失败: {skill_id}")
                return {
                    "success": False,
                    "error": result.result_data.get("error", "未知错误")
                }

        except Exception as e:
            logger.error(f"  风险技能使用异常: {e}")
            return {"success": False, "error": str(e)}

    async def assess_trade_risk(self, trade_request: Dict[str, Any]) -> Dict[str, Any]:
        """评估单笔交易风险 - assess_portfolio_risk的别名方法"""
        try:
            # 构造投资组合数据
            portfolio_data = {
                "positions": [
                    {
                        "symbol": trade_request.get("stock_code", ""),
                        "quantity": trade_request.get("quantity", 0),
                        "price": trade_request.get("price", 0),
                        "action": trade_request.get("action", "buy")
                    }
                ],
                "total_value": trade_request.get("quantity", 0) * trade_request.get("price", 0),
                "cash_balance": 1000000  # 假设现金余额
            }

            # 调用投资组合风险评估
            risk_result = await self.assess_portfolio_risk(portfolio_data)

            # 转换为交易风险格式
            return {
                "risk_level": risk_result.get("overall_risk_level", "medium"),
                "risk_score": risk_result.get("risk_score", 0.5),
                "risk_factors": risk_result.get("risk_factors", []),
                "recommendations": risk_result.get("recommendations", []),
                "max_position_size": risk_result.get("position_limits", {}).get("max_single_position", 0.1),
                "stop_loss_price": trade_request.get("price", 0) * 0.95,  # 5%止损
                "assessment_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"交易风险评估失败: {e}")
            return {
                "risk_level": "medium",
                "risk_score": 0.5,
                "risk_factors": ["评估服务异常"],
                "recommendations": ["建议谨慎交易"],
                "max_position_size": 0.1,
                "stop_loss_price": trade_request.get("price", 0) * 0.95,
                "assessment_time": datetime.now().isoformat()
            }

# 全局实例
risk_assessment_service = RiskAssessmentService()
