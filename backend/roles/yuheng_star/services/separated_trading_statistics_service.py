from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分离的交易统计服务 - 区分虚拟交易和真实交易
"""

import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, Any, List, Optional
import sqlite3
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class TradingMode:
    """交易模式枚举"""
    VIRTUAL = "virtual"    # 虚拟交易 (学习模式)
    REAL = "real"         # 真实交易 (自动化模式)

@dataclass
class SeparatedTradingStatistics:
    """分离的交易统计数据"""
    mode: str  # virtual 或 real
    period: str  # daily, weekly, monthly, yearly
    start_date: str
    end_date: str
    
    # 基础统计
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # 收益统计
    total_return: float
    total_commission: float
    net_profit: float
    annual_return: float
    
    # 风险指标
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    
    # 交易指标
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    profit_factor: float
    
    # 数据源标识
    data_source: str  # "historical_db" 或 "realtime_api"

class SeparatedTradingStatisticsService:
    """分离的交易统计服务"""
    
    def __init__(self):
        self.service_name = "SeparatedTradingStatisticsService"
        self.version = "1.0.0"
        
        # 初始化数据库
        self.db_path = Path("backend/data/separated_trading_statistics.db")
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
        
        logger.info("分离交易统计服务初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # 虚拟交易记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS virtual_trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT UNIQUE NOT NULL,
                    stock_code TEXT NOT NULL,
                    side TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    price REAL NOT NULL,
                    commission REAL DEFAULT 0,
                    pnl REAL DEFAULT 0,
                    trade_date TEXT NOT NULL,
                    data_source TEXT DEFAULT 'historical_db',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 真实交易记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS real_trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT UNIQUE NOT NULL,
                    stock_code TEXT NOT NULL,
                    side TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    price REAL NOT NULL,
                    commission REAL DEFAULT 0,
                    pnl REAL DEFAULT 0,
                    trade_date TEXT NOT NULL,
                    data_source TEXT DEFAULT 'realtime_api',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 统计汇总表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_statistics_summary (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    mode TEXT NOT NULL,
                    period TEXT NOT NULL,
                    start_date TEXT NOT NULL,
                    end_date TEXT NOT NULL,
                    total_trades INTEGER DEFAULT 0,
                    winning_trades INTEGER DEFAULT 0,
                    win_rate REAL DEFAULT 0,
                    total_return REAL DEFAULT 0,
                    net_profit REAL DEFAULT 0,
                    sharpe_ratio REAL DEFAULT 0,
                    max_drawdown REAL DEFAULT 0,
                    data_source TEXT,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("分离交易统计数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
    
    async def record_virtual_trade(self, trade_data: Dict[str, Any]) -> bool:
        """记录虚拟交易"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO virtual_trades
                (trade_id, stock_code, side, quantity, price, commission, pnl, trade_date, data_source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade_data.get("trade_id"),
                trade_data.get("stock_code"),
                trade_data.get("side"),
                trade_data.get("quantity"),
                trade_data.get("price"),
                trade_data.get("commission", 0),
                trade_data.get("pnl", 0),
                trade_data.get("trade_date", datetime.now().strftime('%Y-%m-%d')),
                "historical_db"
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"虚拟交易记录成功: {trade_data.get('trade_id')}")
            return True
            
        except Exception as e:
            logger.error(f"虚拟交易记录失败: {e}")
            return False
    
    async def record_real_trade(self, trade_data: Dict[str, Any]) -> bool:
        """记录真实交易"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO real_trades
                (trade_id, stock_code, side, quantity, price, commission, pnl, trade_date, data_source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade_data.get("trade_id"),
                trade_data.get("stock_code"),
                trade_data.get("side"),
                trade_data.get("quantity"),
                trade_data.get("price"),
                trade_data.get("commission", 0),
                trade_data.get("pnl", 0),
                trade_data.get("trade_date", datetime.now().strftime('%Y-%m-%d')),
                "realtime_api"
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"真实交易记录成功: {trade_data.get('trade_id')}")
            return True
            
        except Exception as e:
            logger.error(f"真实交易记录失败: {e}")
            return False
    
    async def get_virtual_statistics(self, period: str = "daily") -> SeparatedTradingStatistics:
        """获取虚拟交易统计"""
        return await self._calculate_statistics(TradingMode.VIRTUAL, period)
    
    async def get_real_statistics(self, period: str = "daily") -> SeparatedTradingStatistics:
        """获取真实交易统计"""
        return await self._calculate_statistics(TradingMode.REAL, period)
    
    async def _calculate_statistics(self, mode: str, period: str) -> SeparatedTradingStatistics:
        """计算统计数据"""
        try:
            # 确定日期范围
            end_date = datetime.now()
            if period == "daily":
                start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
            elif period == "weekly":
                start_date = end_date - timedelta(days=7)
            elif period == "monthly":
                start_date = end_date - timedelta(days=30)
            else:  # yearly
                start_date = end_date - timedelta(days=365)
            
            # 获取交易数据
            table_name = "virtual_trades" if mode == TradingMode.VIRTUAL else "real_trades"
            trades_data = await self._get_trades_data(table_name, start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d'))
            
            if not trades_data:
                return self._get_empty_statistics(mode, period, start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d'))
            
            # 计算统计指标
            total_trades = len(trades_data)
            winning_trades = len([t for t in trades_data if t['pnl'] > 0])
            losing_trades = len([t for t in trades_data if t['pnl'] < 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 收益计算
            returns = [t['pnl'] for t in trades_data]
            total_return = sum(returns)
            total_commission = sum([t['commission'] for t in trades_data])
            net_profit = total_return - total_commission

            sharpe_ratio = net_profit / volatility if volatility > 0 else 0

            # 交易指标
            winning_returns = [r for r in returns if r > 0]
            losing_returns = [r for r in returns if r < 0]
            
            avg_win = sum(winning_returns) / len(winning_returns) if winning_returns else 0
            avg_loss = sum(losing_returns) / len(losing_returns) if losing_returns else 0
            largest_win = max(returns) if returns else 0
            largest_loss = min(returns) if returns else 0
            
            total_wins = sum(winning_returns) if winning_returns else 0
            total_losses = abs(sum(losing_returns)) if losing_returns else 0
            profit_factor = total_wins / total_losses if total_losses > 0 else 0
            
            data_source = "historical_db" if mode == TradingMode.VIRTUAL else "realtime_api"
            
            return SeparatedTradingStatistics(
                mode=mode,
                period=period,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d'),
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_return=total_return,
                total_commission=total_commission,
                net_profit=net_profit,

                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                avg_win=avg_win,
                avg_loss=avg_loss,
                largest_win=largest_win,
                largest_loss=largest_loss,
                profit_factor=profit_factor,
                data_source=data_source
            )
            
        except Exception as e:
            logger.error(f"统计计算失败: {e}")
            return self._get_empty_statistics(mode, period, "", "")
    
    async def _get_trades_data(self, table_name: str, start_date: str, end_date: str) -> List[Dict]:
        """获取交易数据"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            cursor.execute(f'''
                SELECT * FROM {table_name}
                WHERE trade_date >= ? AND trade_date <= ?
                ORDER BY trade_date DESC
            ''', (start_date, end_date))
            
            columns = [description[0] for description in cursor.description]
            rows = cursor.fetchall()
            
            conn.close()
            
            trades = []
            for row in rows:
                trade = dict(zip(columns, row))
                trades.append(trade)
            
            return trades
            
        except Exception as e:
            logger.error(f"获取交易数据失败: {e}")
            return []
    
    def _get_empty_statistics(self, mode: str, period: str, start_date: str, end_date: str) -> SeparatedTradingStatistics:
        """获取空统计数据"""
        data_source = "historical_db" if mode == TradingMode.VIRTUAL else "realtime_api"
        
        return SeparatedTradingStatistics(
            mode=mode,
            period=period,
            start_date=start_date,
            end_date=end_date,
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            win_rate=0,
            total_return=0,
            total_commission=0,
            net_profit=0,
            annual_return=0,
            volatility=0,
            sharpe_ratio=0,
            max_drawdown=0,
            avg_win=0,
            avg_loss=0,
            largest_win=0,
            largest_loss=0,
            profit_factor=0,
            data_source=data_source
        )

# 全局实例
separated_trading_statistics_service = SeparatedTradingStatisticsService()

logger.info("分离交易统计服务模块加载完成")
