from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析A股数据收集状态
"""

import sqlite3

def analyze_collection_status():
    """分析收集状态"""
    db_path = get_database_path('stock_database')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    print('=== A股数据收集状态分析 ===')

    # 统计总体情况
    cursor.execute('SELECT COUNT(*) FROM stock_info')
    total_stocks = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source = 'eastmoney_annual'")
    collected_stocks = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source = 'eastmoney_annual'")
    collected_records = cursor.fetchone()[0]

    print(f'总股票数: {total_stocks}')
    print(f'已收集股票: {collected_stocks}')
    print(f'已收集记录: {collected_records}')
    print(f'成功率: {collected_stocks/total_stocks*100:.1f}%')

    # 检查数据分布
    cursor.execute("""
        SELECT stock_code, COUNT(*) as record_count 
        FROM daily_data 
        WHERE data_source = 'eastmoney_annual'
        GROUP BY stock_code 
        ORDER BY record_count DESC 
        LIMIT 10
    """)

    print('\n=== 记录数最多的10只股票 ===')
    for row in cursor.fetchall():
        print(f'{row[0]}: {row[1]} 条记录')

    # 检查记录数少的股票
    cursor.execute("""
        SELECT stock_code, COUNT(*) as record_count 
        FROM daily_data 
        WHERE data_source = 'eastmoney_annual'
        GROUP BY stock_code 
        HAVING record_count < 200
        ORDER BY record_count ASC 
        LIMIT 10
    """)

    print('\n=== 记录数少于200的股票 ===')
    for row in cursor.fetchall():
        print(f'{row[0]}: {row[1]} 条记录')

    # 检查没有数据的股票
    cursor.execute("""
        SELECT stock_code, stock_name 
        FROM stock_info 
        WHERE stock_code NOT IN (
            SELECT DISTINCT stock_code FROM daily_data WHERE data_source = 'eastmoney_annual'
        )
        LIMIT 10
    """)

    print('\n=== 没有数据的股票示例 ===')
    for row in cursor.fetchall():
        print(f'{row[0]} ({row[1]})')

    # 分析失败原因
    print('\n=== 可能的失败原因分析 ===')
    
    # 检查新股（可能没有足够历史数据）
    cursor.execute("""
        SELECT stock_code, stock_name 
        FROM stock_info 
        WHERE stock_code NOT IN (
            SELECT DISTINCT stock_code FROM daily_data WHERE data_source = 'eastmoney_annual'
        )
        AND (stock_code LIKE '30%' OR stock_code LIKE '68%')
        LIMIT 5
    """)
    
    new_stocks = cursor.fetchall()
    if new_stocks:
        print('可能的新股（科创板/创业板）:')
        for row in new_stocks:
            print(f'  {row[0]} ({row[1]})')
    
    # 检查停牌股票的特征
    cursor.execute("""
        SELECT stock_code, stock_name 
        FROM stock_info 
        WHERE stock_code NOT IN (
            SELECT DISTINCT stock_code FROM daily_data WHERE data_source = 'eastmoney_annual'
        )
        AND stock_code LIKE '0%'
        LIMIT 5
    """)
    
    sz_stocks = cursor.fetchall()
    if sz_stocks:
        print('可能的深市问题股票:')
        for row in sz_stocks:
            print(f'  {row[0]} ({row[1]})')

    conn.close()

if __name__ == "__main__":
    analyze_collection_status()
