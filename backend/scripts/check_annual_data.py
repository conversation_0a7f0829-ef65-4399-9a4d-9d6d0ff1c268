from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查年度数据收集结果
"""

import sqlite3

def check_annual_data():
    """检查年度数据收集结果"""
    db_path = get_database_path('stock_database')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    print('=== 检查年度数据收集结果 ===')

    # 统计年度数据
    cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source = 'eastmoney_annual'")
    annual_records = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source = 'eastmoney_annual'")
    annual_stocks = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM daily_data')
    total_records = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(DISTINCT stock_code) FROM stock_info')
    total_stocks = cursor.fetchone()[0]

    # 统计技术指标
    cursor.execute('SELECT COUNT(*) FROM technical_indicators')
    tech_records = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(DISTINCT stock_code) FROM technical_indicators')
    tech_stocks = cursor.fetchone()[0]

    print(f'年度数据记录: {annual_records}')
    print(f'年度数据股票: {annual_stocks}')
    print(f'总数据记录: {total_records}')
    print(f'总股票数: {total_stocks}')
    print(f'技术指标记录: {tech_records}')
    print(f'技术指标股票: {tech_stocks}')

    if annual_records > 0:
        cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM daily_data WHERE data_source = 'eastmoney_annual'")
        date_range = cursor.fetchone()
        print(f'年度数据时间范围: {date_range[0]} 至 {date_range[1]}')

    # 检查各数据源
    cursor.execute("SELECT data_source, COUNT(*) FROM daily_data GROUP BY data_source")
    data_sources = cursor.fetchall()
    print('\n数据源分布:')
    for source, count in data_sources:
        print(f'  {source}: {count} 条记录')

    conn.close()

if __name__ == "__main__":
    check_annual_data()
