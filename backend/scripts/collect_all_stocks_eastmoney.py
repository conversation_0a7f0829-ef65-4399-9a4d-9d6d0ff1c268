from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用东方财富API收集A股全部股票信息并存储到股票库
收集近半年的股票数据
"""

import asyncio
import sqlite3
import os
import sys
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backend/logs/stock_collection.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class StockDataCollector:
    """股票数据收集器"""
    
    def __init__(self):
        self.service_name = "StockDataCollector"
        self.version = "1.0.0"
        self.db_path = get_database_path("stock_database")
        self.eastmoney_service = None
        self.collected_count = 0
        self.failed_count = 0
        
        # 确保数据目录存在
        os.makedirs("backend/data", exist_ok=True)
        os.makedirs("backend/logs", exist_ok=True)
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    async def initialize_services(self):
        """初始化服务"""
        try:
            # 导入东方财富服务
            from backend.services.data.eastmoney_realtime_service import EastmoneyRealtimeService
            from backend.services.data.eastmoney_api import EastmoneyAPI
            
            self.eastmoney_service = EastmoneyRealtimeService()
            self.eastmoney_api = EastmoneyAPI()
            
            await self.eastmoney_service.initialize()
            
            logger.info("✅ 东方财富API服务初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 服务初始化失败: {e}")
            return False
    
    def create_database_tables(self):
        """创建数据库表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建股票信息表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS stock_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT UNIQUE NOT NULL,
                    stock_name TEXT NOT NULL,
                    exchange TEXT,
                    industry TEXT,
                    sector TEXT,
                    market_cap REAL,
                    pe_ratio REAL,
                    pb_ratio REAL,
                    total_shares REAL,
                    float_shares REAL,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 创建日线数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open_price REAL,
                    close_price REAL,
                    high_price REAL,
                    low_price REAL,
                    volume INTEGER,
                    amount REAL,
                    turnover_rate REAL,
                    change_percent REAL,
                    data_source TEXT DEFAULT 'eastmoney',
                    created_at TEXT,
                    updated_at TEXT,
                    UNIQUE(stock_code, trade_date)
                )
            """)
            
            # 创建实时数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS realtime_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT UNIQUE NOT NULL,
                    stock_name TEXT,
                    current_price REAL,
                    change_amount REAL,
                    change_percent REAL,
                    volume INTEGER,
                    amount REAL,
                    high_price REAL,
                    low_price REAL,
                    open_price REAL,
                    prev_close REAL,
                    market_cap REAL,
                    pe_ratio REAL,
                    pb_ratio REAL,
                    data_source TEXT DEFAULT 'eastmoney',
                    update_time TEXT,
                    created_at TEXT
                )
            """)
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 数据库表创建成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库表创建失败: {e}")
            return False
    
    async def get_all_stock_list(self) -> List[Dict[str, Any]]:
        """获取所有A股股票列表"""
        try:
            logger.info("🔍 开始获取A股股票列表...")
            
            # 使用东方财富API获取股票列表
            if self.eastmoney_service:
                stock_list = await self.eastmoney_service.get_all_stocks()
                if stock_list:
                    logger.info(f"✅ 从东方财富API获取到 {len(stock_list)} 只股票")
                    return stock_list
            
            # 备用方案：使用AkShare
            try:
                import akshare as ak
                
                # 获取沪深A股列表
                stock_info_a = ak.stock_info_a_code_name()
                
                stock_list = []
                for _, row in stock_info_a.iterrows():
                    stock_list.append({
                        "code": row["code"],
                        "name": row["name"],
                        "exchange": "SSE" if row["code"].startswith("6") else "SZSE"
                    })
                
                logger.info(f"✅ 从AkShare获取到 {len(stock_list)} 只股票")
                return stock_list
                
            except Exception as e:
                logger.warning(f"⚠️ AkShare获取失败: {e}")
            
            # 最后备用方案：手动构建常见股票列表
            logger.warning("⚠️ 使用备用股票列表")

        except Exception as e:
            logger.error(f"❌ 获取股票列表失败: {e}")
            return []
    
    def _get_fallback_stock_list(self) -> List[Dict[str, Any]]:
        """备用股票列表"""
        return [
            {"code": "000001", "name": "平安银行", "exchange": "SZSE"},
            {"code": "000002", "name": "万科A", "exchange": "SZSE"},
            {"code": "000858", "name": "五粮液", "exchange": "SZSE"},
            {"code": "002415", "name": "海康威视", "exchange": "SZSE"},
            {"code": "002594", "name": "比亚迪", "exchange": "SZSE"},
            {"code": "300059", "name": "东方财富", "exchange": "SZSE"},
            {"code": "300750", "name": "宁德时代", "exchange": "SZSE"},
            {"code": "600000", "name": "浦发银行", "exchange": "SSE"},
            {"code": "600036", "name": "招商银行", "exchange": "SSE"},
            {"code": "600519", "name": "贵州茅台", "exchange": "SSE"},
            {"code": "600887", "name": "伊利股份", "exchange": "SSE"},
            {"code": "688001", "name": "华兴源创", "exchange": "SSE"},
            {"code": "688111", "name": "金山办公", "exchange": "SSE"},
        ]
    
    async def collect_stock_basic_info(self, stock_list: List[Dict[str, Any]]):
        """收集股票基本信息"""
        try:
            logger.info(f"📊 开始收集 {len(stock_list)} 只股票的基本信息...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().isoformat()
            
            for i, stock in enumerate(stock_list):
                try:
                    stock_code = stock["code"]
                    stock_name = stock["name"]
                    exchange = stock["exchange"]
                    
                    # 获取股票详细信息
                    if self.eastmoney_service:
                        detail_info = await self.eastmoney_service.get_stock_detail(stock_code)
                        
                        if detail_info:
                            # 插入或更新股票信息
                            cursor.execute("""
                                INSERT OR REPLACE INTO stock_info 
                                (stock_code, stock_name, exchange, industry, sector, 
                                 market_cap, pe_ratio, pb_ratio, total_shares, float_shares,
                                 created_at, updated_at)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                stock_code, stock_name, exchange,
                                detail_info.get("industry", ""),
                                detail_info.get("sector", ""),
                                detail_info.get("market_cap", 0),
                                detail_info.get("pe_ratio", 0),
                                detail_info.get("pb_ratio", 0),
                                detail_info.get("total_shares", 0),
                                detail_info.get("float_shares", 0),
                                current_time, current_time
                            ))
                            
                            self.collected_count += 1
                        else:
                            # 插入基本信息
                            cursor.execute("""
                                INSERT OR REPLACE INTO stock_info 
                                (stock_code, stock_name, exchange, created_at, updated_at)
                                VALUES (?, ?, ?, ?, ?)
                            """, (stock_code, stock_name, exchange, current_time, current_time))
                    
                    # 进度显示
                    if (i + 1) % 100 == 0:
                        logger.info(f"📈 已处理 {i + 1}/{len(stock_list)} 只股票")
                        conn.commit()  # 定期提交
                    
                    # 限制请求频率
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.warning(f"⚠️ 处理股票 {stock.get('code', 'unknown')} 失败: {e}")
                    self.failed_count += 1
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ 股票基本信息收集完成，成功: {self.collected_count}, 失败: {self.failed_count}")
            
        except Exception as e:
            logger.error(f"❌ 收集股票基本信息失败: {e}")
    
    async def collect_historical_data(self, stock_list: List[Dict[str, Any]], days: int = 180):
        """收集历史数据（近半年）"""
        try:
            logger.info(f"📈 开始收集近 {days} 天的历史数据...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            current_time = datetime.now().isoformat()
            
            for i, stock in enumerate(stock_list[:50]):  # 限制数量避免超时
                try:
                    stock_code = stock["code"]
                    
                    # 获取历史数据
                    if self.eastmoney_service:
                        historical_data = await self.eastmoney_service.get_historical_data(
                            stock_code, start_date.strftime("%Y%m%d"), end_date.strftime("%Y%m%d")
                        )
                        
                        if historical_data:
                            for data_point in historical_data:
                                cursor.execute("""
                                    INSERT OR REPLACE INTO daily_data 
                                    (stock_code, trade_date, open_price, close_price, high_price, low_price,
                                     volume, amount, turnover_rate, change_percent, data_source, created_at, updated_at)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                """, (
                                    stock_code,
                                    data_point.get("date", ""),
                                    data_point.get("open", 0),
                                    data_point.get("close", 0),
                                    data_point.get("high", 0),
                                    data_point.get("low", 0),
                                    data_point.get("volume", 0),
                                    data_point.get("amount", 0),
                                    data_point.get("turnover_rate", 0),
                                    data_point.get("change_percent", 0),
                                    "eastmoney",
                                    current_time, current_time
                                ))
                    
                    # 进度显示
                    if (i + 1) % 10 == 0:
                        logger.info(f"📊 已处理历史数据 {i + 1}/{min(50, len(stock_list))} 只股票")
                        conn.commit()
                    
                    # 限制请求频率
                    await asyncio.sleep(0.2)
                    
                except Exception as e:
                    logger.warning(f"⚠️ 收集股票 {stock_code} 历史数据失败: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 历史数据收集完成")
            
        except Exception as e:
            logger.error(f"❌ 收集历史数据失败: {e}")
    
    async def collect_realtime_data(self, stock_list: List[Dict[str, Any]]):
        """收集实时数据"""
        try:
            logger.info("⚡ 开始收集实时数据...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().isoformat()
            
            # 批量获取实时数据
            if self.eastmoney_service:
                stock_codes = [stock["code"] for stock in stock_list[:100]]  # 限制数量
                realtime_data = await self.eastmoney_service.get_batch_realtime_data(stock_codes)
                
                if realtime_data:
                    for stock_code, data in realtime_data.items():
                        cursor.execute("""
                            INSERT OR REPLACE INTO realtime_data 
                            (stock_code, stock_name, current_price, change_amount, change_percent,
                             volume, amount, high_price, low_price, open_price, prev_close,
                             market_cap, pe_ratio, pb_ratio, data_source, update_time, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            stock_code,
                            data.get("name", ""),
                            data.get("current_price", 0),
                            data.get("change_amount", 0),
                            data.get("change_percent", 0),
                            data.get("volume", 0),
                            data.get("amount", 0),
                            data.get("high", 0),
                            data.get("low", 0),
                            data.get("open", 0),
                            data.get("prev_close", 0),
                            data.get("market_cap", 0),
                            data.get("pe_ratio", 0),
                            data.get("pb_ratio", 0),
                            "eastmoney",
                            current_time, current_time
                        ))
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 实时数据收集完成")
            
        except Exception as e:
            logger.error(f"❌ 收集实时数据失败: {e}")
    
    async def run_collection(self):
        """运行完整的数据收集流程"""
        try:
            logger.info("🚀 开始A股数据收集流程...")
            
            # 1. 初始化服务
            if not await self.initialize_services():
                logger.error("❌ 服务初始化失败，退出")
                return False
            
            # 2. 创建数据库表
            if not self.create_database_tables():
                logger.error("❌ 数据库初始化失败，退出")
                return False
            
            # 3. 获取股票列表
            stock_list = await self.get_all_stock_list()
            if not stock_list:
                logger.error("❌ 获取股票列表失败，退出")
                return False
            
            # 4. 收集基本信息
            await self.collect_stock_basic_info(stock_list)
            
            # 5. 收集历史数据
            await self.collect_historical_data(stock_list)
            
            # 6. 收集实时数据
            await self.collect_realtime_data(stock_list)
            
            # 7. 生成统计报告
            self.generate_collection_report()
            
            logger.info("🎉 A股数据收集流程完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据收集流程失败: {e}")
            return False
    
    def generate_collection_report(self):
        """生成收集报告"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计数据
            cursor.execute("SELECT COUNT(*) FROM stock_info")
            stock_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM daily_data")
            daily_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM realtime_data")
            realtime_count = cursor.fetchone()[0]
            
            conn.close()
            
            report = f"""
📊 A股数据收集报告
==========================================
🕐 收集时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📈 股票信息: {stock_count} 只
📊 历史数据: {daily_count} 条
⚡ 实时数据: {realtime_count} 条
✅ 成功收集: {self.collected_count} 只
❌ 失败数量: {self.failed_count} 只
==========================================
"""
            
            # 保存报告
            report_path = f"backend/data/stock_collection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(report)
            logger.info(f"📄 报告已保存: {report_path}")
            
        except Exception as e:
            logger.error(f"❌ 生成报告失败: {e}")

async def main():
    """主函数"""
    collector = StockDataCollector()
    success = await collector.run_collection()
    
    if success:
        print("🎉 A股数据收集成功完成！")
    else:
        print("❌ A股数据收集失败")

if __name__ == "__main__":
    asyncio.run(main())
