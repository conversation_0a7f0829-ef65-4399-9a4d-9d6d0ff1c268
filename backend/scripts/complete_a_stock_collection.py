from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整A股数据收集器
使用东方财富API收集全部5418只A股的完整年度历史数据
"""

import sqlite3
import asyncio
import logging
import time
from datetime import datetime
from backend.scripts.annual_a_stock_collector import AnnualAStockCollector

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CompleteAStockCollector:
    """完整A股数据收集器"""
    
    def __init__(self):
        self.db_path = get_database_path('stock_database')
        self.collector = AnnualAStockCollector()
        
    def get_collection_status(self):
        """获取收集状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 检查股票总数和已收集数据
        cursor.execute('SELECT COUNT(*) FROM stock_info')
        total_stocks = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
        collected_stocks = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
        collected_records = cursor.fetchone()[0]
        
        # 获取未收集的股票
        cursor.execute("""
            SELECT stock_code, stock_name, exchange FROM stock_info 
            WHERE stock_code NOT IN (
                SELECT DISTINCT stock_code FROM daily_data WHERE data_source LIKE 'eastmoney%'
            )
            ORDER BY stock_code
        """)
        
        uncollected_stocks = cursor.fetchall()
        
        conn.close()
        
        return {
            'total_stocks': total_stocks,
            'collected_stocks': collected_stocks,
            'collected_records': collected_records,
            'uncollected_stocks': uncollected_stocks,
            'remaining_count': len(uncollected_stocks),
            'completion_rate': collected_stocks / total_stocks * 100 if total_stocks > 0 else 0
        }
    
    def collect_all_stocks(self):
        """收集所有A股数据"""
        print("🚀 开始完整A股数据收集...")
        start_time = datetime.now()
        
        # 获取当前状态
        status = self.get_collection_status()
        
        print(f"📊 收集状态:")
        print(f"  总股票数: {status['total_stocks']}")
        print(f"  已收集股票: {status['collected_stocks']}")
        print(f"  已收集记录: {status['collected_records']}")
        print(f"  剩余股票: {status['remaining_count']}")
        print(f"  完成率: {status['completion_rate']:.1f}%")
        
        if status['remaining_count'] == 0:
            print("✅ 所有股票数据已收集完成！")
            return True
        
        print(f"\n📦 开始收集剩余 {status['remaining_count']} 只股票...")
        
        # 分批收集
        uncollected_stocks = status['uncollected_stocks']
        batch_size = 50  # 每批50只股票
        total_batches = (len(uncollected_stocks) + batch_size - 1) // batch_size
        
        success_count = 0
        failed_count = 0
        total_new_records = 0
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(uncollected_stocks))
            batch_stocks = uncollected_stocks[start_idx:end_idx]
            
            print(f"\n📦 处理第 {batch_num + 1}/{total_batches} 批 ({len(batch_stocks)} 只股票)")
            
            batch_success = 0
            batch_failed = 0
            
            for i, (stock_code, stock_name, exchange) in enumerate(batch_stocks):
                try:
                    print(f"  📊 [{i+1}/{len(batch_stocks)}] 收集 {stock_code} ({stock_name})...")
                    
                    # 构造股票信息
                    stock_info = {
                        'code': stock_code,
                        'name': stock_name,
                        'exchange': exchange,
                        'secid': f'1.{stock_code}' if stock_code.startswith('6') else f'0.{stock_code}'
                    }
                    
                    # 记录收集前的记录数
                    before_count = self._get_stock_record_count(stock_code)
                    
                    # 收集单只股票数据
                    success = self.collector.collect_stock_annual_data(stock_info)
                    
                    if success:
                        # 记录收集后的记录数
                        after_count = self._get_stock_record_count(stock_code)
                        new_records = after_count - before_count
                        
                        batch_success += 1
                        total_new_records += new_records
                        print(f"    ✅ 成功收集 {new_records} 条记录")
                    else:
                        batch_failed += 1
                        print(f"    ❌ 收集失败")
                    
                    # 控制请求频率
                    time.sleep(0.3)
                    
                except Exception as e:
                    batch_failed += 1
                    logger.error(f"收集 {stock_code} 失败: {e}")
                    continue
            
            success_count += batch_success
            failed_count += batch_failed
            
            print(f"✅ 第 {batch_num + 1} 批完成: {batch_success}/{len(batch_stocks)} 只股票成功")
            
            # 批次间休息
            if batch_num + 1 < total_batches:
                print("⏳ 休息10秒...")
                time.sleep(10)
        
        # 最终统计
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        final_status = self.get_collection_status()
        
        print(f"\n🎉 完整A股数据收集完成！")
        print(f"📊 收集统计:")
        print(f"  处理股票: {len(uncollected_stocks)} 只")
        print(f"  成功收集: {success_count} 只")
        print(f"  失败数量: {failed_count} 只")
        print(f"  新增记录: {total_new_records} 条")
        print(f"  耗时: {duration/60:.1f} 分钟")
        
        print(f"\n📈 最终状态:")
        print(f"  总股票数: {final_status['total_stocks']}")
        print(f"  已收集股票: {final_status['collected_stocks']}")
        print(f"  已收集记录: {final_status['collected_records']}")
        print(f"  完成率: {final_status['completion_rate']:.1f}%")
        
        return True
    
    def _get_stock_record_count(self, stock_code: str) -> int:
        """获取股票记录数"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM daily_data WHERE stock_code = ? AND data_source LIKE 'eastmoney%'", (stock_code,))
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except:
            return 0

def main():
    """主函数"""
    collector = CompleteAStockCollector()
    
    # 显示当前状态
    status = collector.get_collection_status()
    print("=== 完整A股数据收集器 ===")
    print(f"当前完成率: {status['completion_rate']:.1f}%")
    print(f"剩余股票: {status['remaining_count']} 只")
    
    if status['remaining_count'] > 0:
        # 开始收集
        collector.collect_all_stocks()
    else:
        print("✅ 所有股票数据已收集完成！")

if __name__ == "__main__":
    main()
