from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整A股数据收集器
使用AkShare和东方财富API收集全部A股近半年数据
"""

import asyncio
import sqlite3
import os
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import time
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backend/logs/complete_stock_collection.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class CompleteAStockCollector:
    """完整A股数据收集器"""
    
    def __init__(self):
        self.service_name = "CompleteAStockCollector"
        self.version = "1.0.0"
        self.db_path = get_database_path("stock_database")
        self.collected_count = 0
        self.failed_count = 0
        self.total_records = 0
        
        # 确保数据目录存在
        os.makedirs("backend/data/complete_a_stock_library", exist_ok=True)
        os.makedirs("backend/logs", exist_ok=True)
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def create_database_tables(self):
        """创建完整的数据库表结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建股票信息表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS stock_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT UNIQUE NOT NULL,
                    stock_name TEXT NOT NULL,
                    exchange TEXT,
                    industry TEXT,
                    sector TEXT,
                    list_date TEXT,
                    market_cap REAL,
                    total_shares REAL,
                    float_shares REAL,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 创建日线数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open_price REAL,
                    close_price REAL,
                    high_price REAL,
                    low_price REAL,
                    volume INTEGER,
                    amount REAL,
                    turnover_rate REAL,
                    change_percent REAL,
                    change_amount REAL,
                    pe_ratio REAL,
                    pb_ratio REAL,
                    data_source TEXT DEFAULT 'akshare',
                    created_at TEXT,
                    updated_at TEXT,
                    UNIQUE(stock_code, trade_date)
                )
            """)
            
            # 创建指数
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_stock_info_code ON stock_info(stock_code)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_daily_data_stock_date ON daily_data(stock_code, trade_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_daily_data_date ON daily_data(trade_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_daily_data_stock ON daily_data(stock_code)")
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 数据库表结构创建成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库表创建失败: {e}")
            return False
    
    def get_all_a_stocks(self) -> List[Dict[str, Any]]:
        """获取全部A股股票列表"""
        try:
            import akshare as ak
            
            logger.info("🔍 开始获取全部A股股票列表...")
            
            # 获取沪深A股列表
            stock_info_a = ak.stock_info_a_code_name()
            
            stock_list = []
            for _, row in stock_info_a.iterrows():
                stock_code = row["code"]
                stock_name = row["name"]
                
                # 确定交易所
                if stock_code.startswith("6"):
                    exchange = "SSE"  # 上海证券交易所
                elif stock_code.startswith(("000", "001", "002", "003")):
                    exchange = "SZSE"  # 深圳证券交易所主板/中小板
                elif stock_code.startswith("300"):
                    exchange = "SZSE"  # 创业板
                elif stock_code.startswith("688"):
                    exchange = "SSE"  # 科创板
                else:
                    exchange = "OTHER"
                
                stock_list.append({
                    "code": stock_code,
                    "name": stock_name,
                    "exchange": exchange
                })
            
            logger.info(f"✅ 获取到 {len(stock_list)} 只A股股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"❌ 获取A股股票列表失败: {e}")
            return []
    
    def collect_stock_basic_info(self, stock_list: List[Dict[str, Any]]):
        """收集股票基本信息"""
        try:
            logger.info(f"📊 开始收集 {len(stock_list)} 只股票的基本信息...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().isoformat()
            
            for i, stock in enumerate(stock_list):
                try:
                    stock_code = stock["code"]
                    stock_name = stock["name"]
                    exchange = stock["exchange"]
                    
                    # 插入基本信息
                    cursor.execute("""
                        INSERT OR REPLACE INTO stock_info 
                        (stock_code, stock_name, exchange, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                    """, (stock_code, stock_name, exchange, current_time, current_time))
                    
                    self.collected_count += 1
                    
                    # 进度显示
                    if (i + 1) % 500 == 0:
                        logger.info(f"📈 已处理 {i + 1}/{len(stock_list)} 只股票")
                        conn.commit()  # 定期提交
                    
                except Exception as e:
                    logger.warning(f"⚠️ 处理股票 {stock.get('code', 'unknown')} 失败: {e}")
                    self.failed_count += 1
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ 股票基本信息收集完成，成功: {self.collected_count}, 失败: {self.failed_count}")
            
        except Exception as e:
            logger.error(f"❌ 收集股票基本信息失败: {e}")
    
    def collect_historical_data_batch(self, stock_list: List[Dict[str, Any]], days: int = 180):
        """批量收集历史数据（近半年）"""
        try:
            import akshare as ak
            
            logger.info(f"📈 开始收集近 {days} 天的历史数据...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            current_time = datetime.now().isoformat()
            
            # 分批处理，每批100只股票
            batch_size = 100
            total_batches = (len(stock_list) + batch_size - 1) // batch_size
            
            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, len(stock_list))
                batch_stocks = stock_list[start_idx:end_idx]
                
                logger.info(f"📊 处理第 {batch_idx + 1}/{total_batches} 批，股票 {start_idx + 1}-{end_idx}")
                
                for i, stock in enumerate(batch_stocks):
                    try:
                        stock_code = stock["code"]
                        stock_name = stock["name"]
                        
                        logger.info(f"  [{start_idx + i + 1}/{len(stock_list)}] 收集 {stock_code} {stock_name}")
                        
                        # 获取历史数据
                        hist_data = ak.stock_zh_a_hist(
                            symbol=stock_code,
                            period="daily",
                            start_date=start_date.strftime("%Y%m%d"),
                            end_date=end_date.strftime("%Y%m%d"),
                            adjust=""
                        )
                        
                        if not hist_data.empty:
                            records_inserted = 0
                            for _, row in hist_data.iterrows():
                                try:
                                    cursor.execute("""
                                        INSERT OR REPLACE INTO daily_data 
                                        (stock_code, trade_date, open_price, close_price, high_price, low_price,
                                         volume, amount, turnover_rate, change_percent, change_amount,
                                         data_source, created_at, updated_at)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    """, (
                                        stock_code,
                                        row["日期"].strftime("%Y-%m-%d"),
                                        float(row["开盘"]),
                                        float(row["收盘"]),
                                        float(row["最高"]),
                                        float(row["最低"]),
                                        int(row["成交量"]),
                                        float(row["成交额"]),
                                        float(row["换手率"]) if "换手率" in row and pd.notna(row["换手率"]) else 0.0,
                                        float(row["涨跌幅"]),
                                        float(row["涨跌额"]),
                                        "akshare",
                                        current_time, current_time
                                    ))
                                    records_inserted += 1
                                    self.total_records += 1
                                except Exception as e:
                                    logger.debug(f"    插入记录失败 {stock_code} {row['日期']}: {e}")
                            
                            logger.info(f"    ✅ {stock_code}: 插入 {records_inserted} 条记录")
                        else:
                            logger.warning(f"    ⚠️ {stock_code}: 无历史数据")
                        
                        # 限制请求频率，避免被封
                        time.sleep(0.5)
                        
                    except Exception as e:
                        logger.warning(f"    ❌ 收集股票 {stock_code} 历史数据失败: {e}")
                        self.failed_count += 1
                        continue
                
                # 每批提交一次
                conn.commit()
                logger.info(f"✅ 第 {batch_idx + 1} 批数据收集完成")
                
                # 批次间休息
                if batch_idx < total_batches - 1:
                    logger.info("😴 批次间休息 10 秒...")
                    time.sleep(10)
            
            conn.close()
            
            logger.info(f"✅ 历史数据收集完成，总记录数: {self.total_records}")
            
        except Exception as e:
            logger.error(f"❌ 收集历史数据失败: {e}")
    
    def generate_collection_report(self):
        """生成收集报告"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计数据
            cursor.execute("SELECT COUNT(*) FROM stock_info")
            stock_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM daily_data")
            daily_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data")
            stocks_with_data = cursor.fetchone()[0]
            
            cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM daily_data")
            date_range = cursor.fetchone()
            
            # 按交易所统计
            cursor.execute("""
                SELECT exchange, COUNT(*) 
                FROM stock_info 
                GROUP BY exchange
            """)
            exchange_stats = cursor.fetchall()
            
            conn.close()
            
            report = f"""
📊 完整A股数据收集报告
==========================================
🕐 收集时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📈 股票信息: {stock_count} 只
📊 历史数据: {daily_count} 条
📋 有数据股票: {stocks_with_data} 只
📅 数据时间范围: {date_range[0]} 至 {date_range[1]}
✅ 成功收集: {self.collected_count} 只
❌ 失败数量: {self.failed_count} 只

📊 交易所分布:
"""
            
            for exchange, count in exchange_stats:
                report += f"   {exchange}: {count} 只\n"
            
            report += f"""
📈 数据质量:
   平均每股记录数: {daily_count / stocks_with_data if stocks_with_data > 0 else 0:.1f} 条
   数据覆盖率: {stocks_with_data / stock_count * 100 if stock_count > 0 else 0:.1f}%
==========================================
"""
            
            # 保存报告
            report_path = f"backend/data/complete_a_stock_collection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(report)
            logger.info(f"📄 报告已保存: {report_path}")
            
        except Exception as e:
            logger.error(f"❌ 生成报告失败: {e}")
    
    def run_complete_collection(self):
        """运行完整的数据收集流程"""
        try:
            logger.info("🚀 开始完整A股数据收集流程...")
            
            # 1. 创建数据库表
            if not self.create_database_tables():
                logger.error("❌ 数据库初始化失败，退出")
                return False
            
            # 2. 获取全部A股股票列表
            stock_list = self.get_all_a_stocks()
            if not stock_list:
                logger.error("❌ 获取股票列表失败，退出")
                return False
            
            # 3. 收集基本信息
            self.collect_stock_basic_info(stock_list)
            
            # 4. 收集近半年历史数据
            self.collect_historical_data_batch(stock_list, days=180)
            
            # 5. 生成统计报告
            self.generate_collection_report()
            
            logger.info("🎉 完整A股数据收集流程完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据收集流程失败: {e}")
            return False

def main():
    """主函数"""
    collector = CompleteAStockCollector()
    success = collector.run_complete_collection()
    
    if success:
        print("🎉 完整A股数据收集成功完成！")
    else:
        print("❌ 完整A股数据收集失败")

if __name__ == "__main__":
    main()
