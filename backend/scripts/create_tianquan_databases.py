from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建天权星专用数据库
"""

import sqlite3
import os
from datetime import datetime

def create_tianquan_databases():
    """创建天权星所有数据库"""
    
    # 创建目录
    os.makedirs("backend/data/tianquan_strategies", exist_ok=True)
    os.makedirs("backend/data/decision_making", exist_ok=True)
    
    # 1. 创建策略数据库
    create_strategies_database()
    
    # 2. 创建决策数据库
    create_decision_database()
    
    print("✅ 天权星数据库创建完成")

def create_strategies_database():
    """创建策略数据库"""
    db_path = "backend/data/tianquan_strategies.db"
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 策略定义表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS strategies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            strategy_id TEXT UNIQUE,
            strategy_name TEXT,
            strategy_type TEXT,
            description TEXT,
            parameters TEXT,
            created_at TEXT,
            updated_at TEXT,
            status TEXT DEFAULT 'active'
        )
    ''')
    
    # 策略执行历史表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS strategy_executions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            execution_id TEXT UNIQUE,
            strategy_id TEXT,
            stock_code TEXT,
            execution_time TEXT,
            result TEXT,
            performance_score REAL,
            profit_loss REAL,
            FOREIGN KEY (strategy_id) REFERENCES strategies (strategy_id)
        )
    ''')
    
    # 策略性能表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS strategy_performance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            strategy_id TEXT,
            metric_name TEXT,
            metric_value REAL,
            measurement_date TEXT,
            FOREIGN KEY (strategy_id) REFERENCES strategies (strategy_id)
        )
    ''')
    
    # 插入默认策略
    default_strategies = [
        ('longtou_strategy', '龙头战法', 'longtou', '追踪行业龙头股票', '{"sector_focus": true, "market_cap_min": 10000000000}'),
        ('shouban_strategy', '首板战法', 'shouban', '捕捉首次涨停机会', '{"volume_threshold": 2.0, "price_change_min": 0.095}'),
        ('fanbao_strategy', '反包战法', 'fanbao', '反包涨停策略', '{"previous_limit": true, "volume_ratio": 1.5}'),
        ('boduan_strategy', '波段战法', 'boduan', '中期波段操作', '{"ma_period": 20, "rsi_threshold": [30, 70]}'),
        ('event_strategy', '事件驱动战法', 'event_driven', '基于事件的投资策略', '{"news_sentiment": 0.7, "event_impact": "high"}')
    ]
    
    for strategy_data in default_strategies:
        cursor.execute('''
            INSERT OR IGNORE INTO strategies 
            (strategy_id, strategy_name, strategy_type, description, parameters, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (*strategy_data, datetime.now().isoformat(), datetime.now().isoformat()))
    
    conn.commit()
    conn.close()
    print(f"✅ 策略数据库创建完成: {db_path}")

def create_decision_database():
    """创建决策数据库"""
    db_path = "backend/data/decision_making.db"
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 决策历史表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS decision_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            decision_id TEXT UNIQUE,
            stock_code TEXT,
            decision_type TEXT,
            action TEXT,
            confidence REAL,
            reasoning TEXT,
            market_context TEXT,
            created_at TEXT,
            executed_at TEXT,
            result TEXT
        )
    ''')
    
    # 多目标优化结果表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS optimization_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            decision_id TEXT,
            objective_values TEXT,
            pareto_frontier TEXT,
            optimal_solution TEXT,
            constraint_violations TEXT,
            optimization_confidence REAL,
            created_at TEXT,
            FOREIGN KEY (decision_id) REFERENCES decision_history (decision_id)
        )
    ''')
    
    # 帕累托前沿表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS pareto_frontier (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            decision_id TEXT,
            solution_point TEXT,
            objective_values TEXT,
            dominance_rank INTEGER,
            crowding_distance REAL,
            timestamp TEXT
        )
    ''')
    
    # 协调任务表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS coordination_tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id TEXT UNIQUE,
            task_type TEXT,
            assigned_roles TEXT,
            status TEXT,
            priority INTEGER,
            created_at TEXT,
            completed_at TEXT,
            results TEXT
        )
    ''')
    
    # 性能监控表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS performance_monitoring (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            execution_id TEXT,
            metric_name TEXT,
            metric_value REAL,
            threshold_value REAL,
            alert_triggered BOOLEAN DEFAULT FALSE,
            timestamp TEXT
        )
    ''')
    
    conn.commit()
    conn.close()
    print(f"✅ 决策数据库创建完成: {db_path}")

if __name__ == "__main__":
    create_tianquan_databases()
