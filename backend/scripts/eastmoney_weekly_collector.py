from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富A股一周数据收集器
专门用于测试和收集真实的股票数据
"""

import requests
import sqlite3
import os
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

# 禁用代理
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EastmoneyWeeklyCollector:
    """东方财富一周数据收集器"""
    
    def __init__(self):
        self.service_name = "EastmoneyWeeklyCollector"
        self.version = "1.0.0"
        self.db_path = get_database_path("stock_database")
        self.collected_stocks = 0
        self.collected_records = 0
        self.failed_stocks = 0

        # 配置请求会话，禁用代理
        self.session = requests.Session()
        self.session.proxies = {}
        self.session.verify = False

        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def get_stock_list_from_eastmoney(self, max_stocks: int = 100) -> List[Dict[str, Any]]:
        """从东方财富获取股票列表"""
        try:
            logger.info(f"🔍 从东方财富获取前 {max_stocks} 只股票列表...")
            
            url = 'http://push2.eastmoney.com/api/qt/clist/get'
            params = {
                'pn': 1,
                'pz': max_stocks,
                'po': 1,
                'np': 1,
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fid': 'f3',
                'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',  # A股
                'fields': 'f12,f14,f2,f3,f4,f5,f6,f7,f8,f9,f10,f15,f16,f17,f18'
            }
            
            response = self.session.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and 'diff' in data['data']:
                    stocks = data['data']['diff']
                    
                    stock_list = []
                    for stock in stocks:
                        stock_code = stock.get('f12', '')
                        stock_name = stock.get('f14', '')
                        
                        if stock_code and stock_name:
                            # 确定交易所
                            if stock_code.startswith('6'):
                                exchange = 'SSE'
                                secid = f'1.{stock_code}'
                            else:
                                exchange = 'SZSE'
                                secid = f'0.{stock_code}'
                            
                            stock_list.append({
                                'code': stock_code,
                                'name': stock_name,
                                'exchange': exchange,
                                'secid': secid,
                                'current_price': stock.get('f2', 0)
                            })
                    
                    logger.info(f"✅ 成功获取 {len(stock_list)} 只股票信息")
                    return stock_list
                else:
                    logger.error("❌ 东方财富API返回数据格式异常")
                    return []
            else:
                logger.error(f"❌ 东方财富API请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"❌ 获取股票列表失败: {e}")
            return []
    
    def get_stock_historical_data(self, stock_info: Dict[str, Any], days: int = 7) -> List[Dict[str, Any]]:
        """获取单只股票的历史数据"""
        try:
            stock_code = stock_info['code']
            secid = stock_info['secid']
            
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
            
            url = 'http://push2his.eastmoney.com/api/qt/stock/kline/get'
            params = {
                'secid': secid,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'fields1': 'f1,f2,f3,f4,f5,f6',
                'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                'klt': '101',  # 日K
                'fqt': '1',    # 前复权
                'beg': start_date,
                'end': end_date
            }
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and data['data'] and 'klines' in data['data']:
                    klines = data['data']['klines']
                    
                    historical_data = []
                    for kline in klines:
                        parts = kline.split(',')
                        if len(parts) >= 11:
                            try:
                                record = {
                                    'trade_date': parts[0],
                                    'open_price': float(parts[1]),
                                    'close_price': float(parts[2]),
                                    'high_price': float(parts[3]),
                                    'low_price': float(parts[4]),
                                    'volume': int(parts[5]),
                                    'amount': float(parts[6]),
                                    'change_percent': float(parts[8]),
                                    'turnover_rate': float(parts[10]) if parts[10] != '-' else 0.0
                                }
                                historical_data.append(record)
                            except (ValueError, IndexError) as e:
                                logger.debug(f"解析数据行失败 {stock_code}: {e}")
                                continue
                    
                    logger.debug(f"✅ {stock_code} 获取到 {len(historical_data)} 条历史数据")
                    return historical_data
                else:
                    logger.warning(f"⚠️ {stock_code} 无历史数据")
                    return []
            else:
                logger.warning(f"⚠️ {stock_code} 历史数据请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.warning(f"⚠️ 获取 {stock_code} 历史数据异常: {e}")
            return []
    
    def save_to_database(self, stock_info: Dict[str, Any], historical_data: List[Dict[str, Any]]):
        """保存数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().isoformat()
            stock_code = stock_info['code']
            stock_name = stock_info['name']
            exchange = stock_info['exchange']
            
            # 1. 更新股票基本信息
            cursor.execute("""
                INSERT OR REPLACE INTO stock_info 
                (stock_code, stock_name, exchange, updated_at)
                VALUES (?, ?, ?, ?)
            """, (stock_code, stock_name, exchange, current_time))
            
            # 2. 插入历史数据
            records_inserted = 0
            for record in historical_data:
                try:
                    cursor.execute("""
                        INSERT OR REPLACE INTO daily_data 
                        (stock_code, trade_date, open_price, close_price, high_price, low_price,
                         volume, amount, change_percent, turnover_rate, data_source, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        stock_code,
                        record['trade_date'],
                        record['open_price'],
                        record['close_price'],
                        record['high_price'],
                        record['low_price'],
                        record['volume'],
                        record['amount'],
                        record['change_percent'],
                        record['turnover_rate'],
                        'eastmoney_real',
                        current_time,
                        current_time
                    ))
                    records_inserted += 1
                except Exception as e:
                    logger.debug(f"插入记录失败 {stock_code} {record['trade_date']}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            self.collected_stocks += 1
            self.collected_records += records_inserted
            
            logger.info(f"✅ {stock_code} {stock_name}: 插入 {records_inserted} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存数据失败 {stock_code}: {e}")
            self.failed_stocks += 1
            return False
    
    def run_weekly_collection(self, max_stocks: int = 50):
        """运行一周数据收集"""
        try:
            start_time = datetime.now()
            logger.info(f"🚀 开始收集A股一周数据，目标股票数: {max_stocks}")
            
            # 1. 获取股票列表
            stock_list = self.get_stock_list_from_eastmoney(max_stocks)
            if not stock_list:
                logger.error("❌ 无法获取股票列表，退出")
                return False
            
            # 2. 逐个收集数据
            for i, stock_info in enumerate(stock_list):
                try:
                    stock_code = stock_info['code']
                    stock_name = stock_info['name']
                    
                    logger.info(f"📊 [{i+1}/{len(stock_list)}] 收集 {stock_code} {stock_name}")
                    
                    # 获取历史数据
                    historical_data = self.get_stock_historical_data(stock_info)
                    
                    if historical_data:
                        # 保存到数据库
                        self.save_to_database(stock_info, historical_data)
                    else:
                        logger.warning(f"⚠️ {stock_code} 无历史数据")
                        self.failed_stocks += 1
                    
                    # 控制请求频率，避免被限制
                    time.sleep(0.5)
                    
                    # 每10只股票显示进度
                    if (i + 1) % 10 == 0:
                        logger.info(f"📈 进度: {i+1}/{len(stock_list)}, 成功: {self.collected_stocks}, 失败: {self.failed_stocks}")
                    
                except Exception as e:
                    logger.error(f"❌ 处理股票 {stock_info} 失败: {e}")
                    self.failed_stocks += 1
                    continue
            
            # 3. 生成收集报告
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.generate_collection_report(duration)
            
            logger.info("🎉 A股一周数据收集完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据收集失败: {e}")
            return False
    
    def generate_collection_report(self, duration: float):
        """生成收集报告"""
        try:
            # 验证数据库中的数据
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计数据
            cursor.execute("SELECT COUNT(*) FROM stock_info")
            total_stocks = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source = 'eastmoney_real'")
            real_data_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source = 'eastmoney_real'")
            stocks_with_real_data = cursor.fetchone()[0]
            
            cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM daily_data WHERE data_source = 'eastmoney_real'")
            date_range = cursor.fetchone()
            
            conn.close()
            
            report = f"""
📊 东方财富A股一周数据收集报告
==========================================
🕐 收集时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
⏱️ 收集耗时: {duration:.1f} 秒
📈 目标股票: {self.collected_stocks + self.failed_stocks} 只
✅ 成功收集: {self.collected_stocks} 只
❌ 失败数量: {self.failed_stocks} 只
📊 真实数据: {real_data_count} 条
📋 有数据股票: {stocks_with_real_data} 只
📅 数据时间范围: {date_range[0]} 至 {date_range[1]}
💾 数据库总股票: {total_stocks} 只
🎯 成功率: {(self.collected_stocks / (self.collected_stocks + self.failed_stocks) * 100):.1f}%
==========================================
"""
            
            # 保存报告
            report_path = f"backend/data/eastmoney_weekly_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(report)
            logger.info(f"📄 报告已保存: {report_path}")
            
        except Exception as e:
            logger.error(f"❌ 生成报告失败: {e}")

def main():
    """主函数"""
    collector = EastmoneyWeeklyCollector()
    
    # 收集50只股票的一周数据
    success = collector.run_weekly_collection(max_stocks=50)
    
    if success:
        print("🎉 东方财富A股一周数据收集成功！")
    else:
        print("❌ 东方财富A股一周数据收集失败")

if __name__ == "__main__":
    main()
