from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高效A股数据收集器
使用多线程和缓存机制收集完整A股半年历史数据
"""

import asyncio
import sqlite3
import os
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backend/logs/efficient_stock_collection.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class EfficientAStockCollector:
    """高效A股数据收集器"""
    
    def __init__(self):
        self.service_name = "EfficientAStockCollector"
        self.version = "2.0.0"
        self.db_path = "backend/data/efficient_a_stock_data.db"
        self.collected_count = 0
        self.failed_count = 0
        self.total_records = 0
        self.cache = {}
        self.lock = threading.Lock()
        
        # 确保数据目录存在
        os.makedirs("backend/data", exist_ok=True)
        os.makedirs("backend/logs", exist_ok=True)
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def create_optimized_database(self):
        """创建优化的数据库结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建股票信息表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS stock_info (
                    stock_code TEXT PRIMARY KEY,
                    stock_name TEXT NOT NULL,
                    exchange TEXT,
                    industry TEXT,
                    sector TEXT,
                    list_date TEXT,
                    market_cap REAL,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 创建日线数据表（优化结构）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open_price REAL,
                    close_price REAL,
                    high_price REAL,
                    low_price REAL,
                    volume INTEGER,
                    amount REAL,
                    change_percent REAL,
                    turnover_rate REAL,
                    created_at TEXT,
                    UNIQUE(stock_code, trade_date)
                )
            """)
            
            # 创建技术指标表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS technical_indicators (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    ma5 REAL,
                    ma10 REAL,
                    ma20 REAL,
                    ma60 REAL,
                    rsi REAL,
                    macd REAL,
                    kdj_k REAL,
                    kdj_d REAL,
                    bollinger_upper REAL,
                    bollinger_lower REAL,
                    created_at TEXT,
                    UNIQUE(stock_code, trade_date)
                )
            """)
            
            # 创建索引优化查询
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_daily_stock_date ON daily_data(stock_code, trade_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_daily_date ON daily_data(trade_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tech_stock_date ON technical_indicators(stock_code, trade_date)")
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 优化数据库结构创建成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库创建失败: {e}")
            return False
    
    def get_stock_list_efficiently(self) -> List[Dict[str, Any]]:
        """高效获取股票列表"""
        try:
            import akshare as ak
            
            logger.info("🔍 高效获取A股股票列表...")
            
            # 使用缓存
            cache_key = "stock_list"
            if cache_key in self.cache:
                logger.info("📋 使用缓存的股票列表")
                return self.cache[cache_key]
            
            # 获取股票列表
            stock_info = ak.stock_info_a_code_name()
            
            stock_list = []
            for _, row in stock_info.iterrows():
                stock_code = row["code"]
                stock_name = row["name"]
                
                # 确定交易所
                if stock_code.startswith("6"):
                    exchange = "SSE"
                elif stock_code.startswith(("000", "001", "002", "003")):
                    exchange = "SZSE"
                elif stock_code.startswith("300"):
                    exchange = "SZSE"
                elif stock_code.startswith("688"):
                    exchange = "SSE"
                else:
                    exchange = "OTHER"
                
                stock_list.append({
                    "code": stock_code,
                    "name": stock_name,
                    "exchange": exchange
                })
            
            # 缓存结果
            self.cache[cache_key] = stock_list
            
            logger.info(f"✅ 获取到 {len(stock_list)} 只A股股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"❌ 获取股票列表失败: {e}")
            return []
    
    def collect_single_stock_data(self, stock_info: Dict[str, Any], days: int = 180) -> bool:
        """收集单只股票数据"""
        try:
            import akshare as ak
            
            stock_code = stock_info["code"]
            stock_name = stock_info["name"]
            exchange = stock_info["exchange"]
            
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 获取历史数据
            hist_data = ak.stock_zh_a_hist(
                symbol=stock_code,
                period="daily",
                start_date=start_date.strftime("%Y%m%d"),
                end_date=end_date.strftime("%Y%m%d"),
                adjust=""
            )
            
            if hist_data.empty:
                logger.warning(f"⚠️ {stock_code} 无历史数据")
                return False
            
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().isoformat()
            
            # 插入股票基本信息
            cursor.execute("""
                INSERT OR REPLACE INTO stock_info 
                (stock_code, stock_name, exchange, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            """, (stock_code, stock_name, exchange, current_time, current_time))
            
            # 插入历史数据
            records_inserted = 0
            for _, row in hist_data.iterrows():
                try:
                    cursor.execute("""
                        INSERT OR REPLACE INTO daily_data 
                        (stock_code, trade_date, open_price, close_price, high_price, low_price,
                         volume, amount, change_percent, turnover_rate, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        stock_code,
                        row["日期"].strftime("%Y-%m-%d"),
                        float(row["开盘"]),
                        float(row["收盘"]),
                        float(row["最高"]),
                        float(row["最低"]),
                        int(row["成交量"]),
                        float(row["成交额"]),
                        float(row["涨跌幅"]),
                        float(row["换手率"]) if "换手率" in row and pd.notna(row["换手率"]) else 0.0,
                        current_time
                    ))
                    records_inserted += 1
                except Exception as e:
                    logger.debug(f"插入记录失败 {stock_code} {row['日期']}: {e}")
            
            conn.commit()
            conn.close()
            
            with self.lock:
                self.collected_count += 1
                self.total_records += records_inserted
            
            logger.info(f"✅ {stock_code} {stock_name}: {records_inserted} 条记录")
            return True
            
        except Exception as e:
            logger.warning(f"❌ 收集 {stock_code} 失败: {e}")
            with self.lock:
                self.failed_count += 1
            return False
    
    def calculate_technical_indicators(self, stock_code: str):
        """计算技术指标"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 获取股票历史数据
            df = pd.read_sql_query("""
                SELECT trade_date, close_price, high_price, low_price, volume
                FROM daily_data 
                WHERE stock_code = ? 
                ORDER BY trade_date
            """, conn, params=(stock_code,))
            
            if len(df) < 60:  # 数据不足，跳过
                conn.close()
                return
            
            # 计算移动平均线
            df['ma5'] = df['close_price'].rolling(window=5).mean()
            df['ma10'] = df['close_price'].rolling(window=10).mean()
            df['ma20'] = df['close_price'].rolling(window=20).mean()
            df['ma60'] = df['close_price'].rolling(window=60).mean()
            
            # 计算RSI
            delta = df['close_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # 计算布林带
            df['bollinger_middle'] = df['close_price'].rolling(window=20).mean()
            std = df['close_price'].rolling(window=20).std()
            df['bollinger_upper'] = df['bollinger_middle'] + (std * 2)
            df['bollinger_lower'] = df['bollinger_middle'] - (std * 2)
            
            # 插入技术指标数据
            cursor = conn.cursor()
            current_time = datetime.now().isoformat()
            
            for _, row in df.iterrows():
                if pd.notna(row['ma20']):  # 确保有足够数据
                    cursor.execute("""
                        INSERT OR REPLACE INTO technical_indicators 
                        (stock_code, trade_date, ma5, ma10, ma20, ma60, rsi, 
                         bollinger_upper, bollinger_lower, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        stock_code, row['trade_date'],
                        row['ma5'], row['ma10'], row['ma20'], row['ma60'],
                        row['rsi'], row['bollinger_upper'], row['bollinger_lower'],
                        current_time
                    ))
            
            conn.commit()
            conn.close()
            
            logger.debug(f"✅ {stock_code} 技术指标计算完成")
            
        except Exception as e:
            logger.warning(f"❌ {stock_code} 技术指标计算失败: {e}")
    
    def run_efficient_collection(self, max_workers: int = 10, batch_size: int = 50):
        """运行高效数据收集"""
        try:
            logger.info("🚀 开始高效A股数据收集...")
            
            # 1. 创建数据库
            if not self.create_optimized_database():
                return False
            
            # 2. 获取股票列表
            stock_list = self.get_stock_list_efficiently()
            if not stock_list:
                return False
            
            # 3. 多线程收集数据
            logger.info(f"📊 开始多线程数据收集，工作线程: {max_workers}")
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 分批处理
                for i in range(0, len(stock_list), batch_size):
                    batch = stock_list[i:i + batch_size]
                    
                    # 提交任务
                    futures = [
                        executor.submit(self.collect_single_stock_data, stock)
                        for stock in batch
                    ]
                    
                    # 等待批次完成
                    for future in as_completed(futures):
                        try:
                            future.result()
                        except Exception as e:
                            logger.error(f"任务执行失败: {e}")
                    
                    # 批次间休息
                    logger.info(f"📈 已完成 {min(i + batch_size, len(stock_list))}/{len(stock_list)} 只股票")
                    time.sleep(2)
            
            # 4. 计算技术指标
            logger.info("📊 开始计算技术指标...")
            successful_stocks = [stock["code"] for stock in stock_list[:self.collected_count]]
            
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = [
                    executor.submit(self.calculate_technical_indicators, stock_code)
                    for stock_code in successful_stocks[:100]  # 先处理前100只
                ]
                
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        logger.error(f"技术指标计算失败: {e}")
            
            # 5. 生成报告
            self.generate_collection_report()
            
            logger.info("🎉 高效A股数据收集完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据收集失败: {e}")
            return False
    
    def generate_collection_report(self):
        """生成收集报告"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计数据
            cursor.execute("SELECT COUNT(*) FROM stock_info")
            stock_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM daily_data")
            daily_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data")
            stocks_with_data = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM technical_indicators")
            tech_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM daily_data")
            date_range = cursor.fetchone()
            
            conn.close()
            
            report = f"""
📊 高效A股数据收集报告
==========================================
🕐 收集时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📈 股票信息: {stock_count} 只
📊 历史数据: {daily_count} 条
📋 有数据股票: {stocks_with_data} 只
🔧 技术指标: {tech_count} 条
📅 数据时间范围: {date_range[0]} 至 {date_range[1]}
✅ 成功收集: {self.collected_count} 只
❌ 失败数量: {self.failed_count} 只
📈 平均每股记录数: {daily_count / stocks_with_data if stocks_with_data > 0 else 0:.1f} 条
==========================================
"""
            
            # 保存报告
            report_path = f"backend/data/efficient_collection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(report)
            logger.info(f"📄 报告已保存: {report_path}")
            
        except Exception as e:
            logger.error(f"❌ 生成报告失败: {e}")

def main():
    """主函数"""
    collector = EfficientAStockCollector()
    success = collector.run_efficient_collection(max_workers=8, batch_size=30)
    
    if success:
        print("🎉 高效A股数据收集成功完成！")
    else:
        print("❌ 高效A股数据收集失败")

if __name__ == "__main__":
    main()
