from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全部A股历史数据收集器
收集所有5000+只A股的完整年度历史数据
"""

import sqlite3
import asyncio
import logging
from datetime import datetime
from backend.scripts.annual_a_stock_collector import AnnualAStockCollector

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_collection_status():
    """检查当前收集状态"""
    db_path = get_database_path('stock_database')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 检查股票总数和已收集数据
    cursor.execute('SELECT COUNT(*) FROM stock_info')
    total_stocks = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
    collected_stocks = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
    collected_records = cursor.fetchone()[0]

    print(f'数据库中股票总数: {total_stocks}')
    print(f'已收集数据股票: {collected_stocks}')
    print(f'已收集数据记录: {collected_records}')
    print(f'收集进度: {collected_stocks}/{total_stocks} ({collected_stocks/total_stocks*100:.1f}%)')
    
    # 获取未收集的股票
    cursor.execute("""
        SELECT stock_code FROM stock_info 
        WHERE stock_code NOT IN (
            SELECT DISTINCT stock_code FROM daily_data WHERE data_source LIKE 'eastmoney%'
        )
        ORDER BY stock_code
    """)
    
    uncollected_stocks = [row[0] for row in cursor.fetchall()]
    
    conn.close()
    
    return {
        'total_stocks': total_stocks,
        'collected_stocks': collected_stocks,
        'collected_records': collected_records,
        'uncollected_stocks': uncollected_stocks,
        'remaining_count': len(uncollected_stocks)
    }

def run_full_collection():
    """运行全部A股数据收集"""
    print("🚀 开始全部A股历史数据收集...")
    
    # 检查当前状态
    status = check_collection_status()
    
    if status['remaining_count'] == 0:
        print("✅ 所有股票数据已收集完成！")
        return True
    
    print(f"📊 需要收集 {status['remaining_count']} 只股票的数据")
    
    # 创建收集器
    collector = AnnualAStockCollector()
    
    # 分批收集未收集的股票
    uncollected_stocks = status['uncollected_stocks']
    batch_size = 100  # 每批100只股票
    
    for i in range(0, len(uncollected_stocks), batch_size):
        batch = uncollected_stocks[i:i + batch_size]
        
        print(f"\n📦 处理第 {i//batch_size + 1} 批，股票 {i+1}-{min(i+batch_size, len(uncollected_stocks))}")
        
        # 构造股票信息
        stock_infos = []
        for stock_code in batch:
            stock_info = {
                'code': stock_code,
                'name': f'股票{stock_code}',
                'exchange': 'SSE' if stock_code.startswith('6') else 'SZSE',
                'secid': f'1.{stock_code}' if stock_code.startswith('6') else f'0.{stock_code}'
            }
            stock_infos.append(stock_info)
        
        # 收集这批股票的数据
        success_count = 0
        for j, stock_info in enumerate(stock_infos):
            try:
                print(f"  📊 [{j+1}/{len(stock_infos)}] 收集 {stock_info['code']}...")
                
                success = collector.collect_stock_annual_data(stock_info)
                if success:
                    success_count += 1
                
                # 控制请求频率
                import time
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"收集 {stock_info['code']} 失败: {e}")
                continue
        
        print(f"✅ 第 {i//batch_size + 1} 批完成: {success_count}/{len(stock_infos)} 只股票成功")
        
        # 批次间休息
        if i + batch_size < len(uncollected_stocks):
            print("⏳ 休息5秒...")
            import time
            time.sleep(5)
    
    # 最终状态检查
    final_status = check_collection_status()
    
    print(f"\n🎉 全部A股数据收集完成！")
    print(f"📊 最终统计:")
    print(f"  总股票数: {final_status['total_stocks']}")
    print(f"  已收集股票: {final_status['collected_stocks']}")
    print(f"  已收集记录: {final_status['collected_records']}")
    print(f"  完成率: {final_status['collected_stocks']/final_status['total_stocks']*100:.1f}%")
    
    return True

if __name__ == "__main__":
    run_full_collection()
