from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控A股数据收集进度
"""

import sqlite3
import time
from datetime import datetime

def monitor_collection_progress():
    """监控收集进度"""
    db_path = get_database_path('stock_database')
    
    print("🔍 A股数据收集进度监控")
    print("=" * 50)
    
    last_count = 0
    last_records = 0
    
    while True:
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 统计当前数据
            cursor.execute('SELECT COUNT(*) FROM stock_info')
            total_stocks = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
            collected_stocks = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
            collected_records = cursor.fetchone()[0]
            
            # 计算增量
            stock_increase = collected_stocks - last_count
            record_increase = collected_records - last_records
            
            # 计算进度
            progress = collected_stocks / 5454 * 100 if collected_stocks > 0 else 0
            
            # 显示进度
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f"\r[{current_time}] 📊 进度: {collected_stocks}/5454 ({progress:.1f}%) | "
                  f"记录: {collected_records:,} | "
                  f"增量: +{stock_increase}股票 +{record_increase}记录", end="", flush=True)
            
            # 更新计数
            last_count = collected_stocks
            last_records = collected_records
            
            conn.close()
            
            # 如果完成，退出监控
            if collected_stocks >= 5454:
                print(f"\n🎉 收集完成！总共收集了 {collected_stocks} 只股票，{collected_records:,} 条记录")
                break
            
            time.sleep(10)  # 每10秒检查一次
            
        except Exception as e:
            print(f"\n❌ 监控错误: {e}")
            time.sleep(5)
            continue

if __name__ == "__main__":
    monitor_collection_progress()
