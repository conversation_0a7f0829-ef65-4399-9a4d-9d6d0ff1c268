from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控A股数据收集失败率
"""

import sqlite3
import time
from datetime import datetime

def monitor_failure_rate():
    """监控失败率"""
    db_path = get_database_path('stock_database')
    
    print("📊 A股数据收集失败率监控")
    print("=" * 60)
    
    last_collected = 0
    last_records = 0
    start_time = datetime.now()
    
    while True:
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 统计当前数据
            cursor.execute('SELECT COUNT(*) FROM stock_info')
            total_stocks = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source = 'eastmoney_annual'")
            collected_stocks = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source = 'eastmoney_annual'")
            collected_records = cursor.fetchone()[0]
            
            # 计算增量和速度
            stock_increase = collected_stocks - last_collected
            record_increase = collected_records - last_records
            
            # 计算进度和失败率
            progress = collected_stocks / 5454 * 100 if collected_stocks > 0 else 0
            
            # 估算失败率（基于已处理的股票）
            # 假设收集器按顺序处理，已处理的股票数约等于已收集+失败的股票数
            estimated_processed = max(collected_stocks, last_collected + 50)  # 估算已处理数量
            estimated_failed = max(0, estimated_processed - collected_stocks)
            failure_rate = estimated_failed / estimated_processed * 100 if estimated_processed > 0 else 0
            
            # 计算收集速度
            elapsed_time = (datetime.now() - start_time).total_seconds()
            speed_stocks = collected_stocks / elapsed_time * 3600 if elapsed_time > 0 else 0  # 股票/小时
            speed_records = collected_records / elapsed_time * 3600 if elapsed_time > 0 else 0  # 记录/小时
            
            # 估算剩余时间
            remaining_stocks = 5454 - collected_stocks
            eta_hours = remaining_stocks / speed_stocks if speed_stocks > 0 else 0
            
            # 显示进度
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f"\r[{current_time}] 📊 进度: {collected_stocks}/5454 ({progress:.1f}%) | "
                  f"记录: {collected_records:,} | "
                  f"失败率: {failure_rate:.1f}% | "
                  f"速度: {speed_stocks:.0f}股票/时 | "
                  f"ETA: {eta_hours:.1f}小时", end="", flush=True)
            
            # 更新计数
            last_collected = collected_stocks
            last_records = collected_records
            
            conn.close()
            
            # 如果完成，退出监控
            if collected_stocks >= 5454:
                print(f"\n🎉 收集完成！总共收集了 {collected_stocks} 只股票，{collected_records:,} 条记录")
                print(f"📊 最终失败率: {failure_rate:.1f}%")
                break
            
            time.sleep(15)  # 每15秒检查一次
            
        except Exception as e:
            print(f"\n❌ 监控错误: {e}")
            time.sleep(5)
            continue

if __name__ == "__main__":
    monitor_failure_rate()
