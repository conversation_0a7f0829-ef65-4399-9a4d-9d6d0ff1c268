from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""

直接调用API，避免复杂依赖
"""

import requests
import sqlite3
import time
import logging
from datetime import datetime
from typing import List, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleEastmoneyCollector:
    pass
    def __init__(self):
        self.db_path = get_database_path('stock_database')
        self.session = requests.Session()
        self.session.proxies = {}
        self.session.verify = False
        
    def get_all_stocks(self) -> List[str]:
        """获取全部A股股票代码"""
        try:
            all_stocks = []
            page = 1
            page_size = 100
            
            logger.info("🔍 获取全部A股股票列表...")
            
            while True:
                url = 'http://push2.eastmoney.com/api/qt/clist/get'
                params = {
                    'pn': page,
                    'pz': page_size,
                    'po': 1,
                    'np': 1,
                    'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                    'fltt': 2,
                    'invt': 2,
                    'fid': 'f3',
                    'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',  # A股
                    'fields': 'f12,f14'
                }
                
                response = self.session.get(url, params=params, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    if 'data' in data and 'diff' in data['data'] and data['data']['diff']:
                        stocks = data['data']['diff']
                        total_stocks_api = data['data'].get('total', 0)
                        
                        for stock in stocks:
                            stock_code = stock.get('f12', '')
                            if stock_code:
                                all_stocks.append(stock_code)
                        
                        logger.info(f"📄 第{page}页: 获取{len(stocks)}只股票，累计{len(all_stocks)}只")
                        
                        # 检查是否已获取所有股票
                        if len(stocks) < page_size:
                            logger.info(f"✅ 已获取所有股票: {len(all_stocks)}/{total_stocks_api}")
                            break
                        
                        page += 1
                        time.sleep(0.5)  # 控制请求频率
                    else:
                        logger.warning(f"⚠️ 第{page}页无数据")
                        break
                else:
                    logger.error(f"❌ 第{page}页请求失败: {response.status_code}")
                    break
            
            return all_stocks
            
        except Exception as e:
            logger.error(f"获取A股列表失败: {e}")
            return []
    
    def collect_stock_data(self, stock_code: str, max_retries: int = 3) -> Tuple[bool, int]:
        """收集单只股票数据"""
        try:
            secid = f'1.{stock_code}' if stock_code.startswith('6') else f'0.{stock_code}'
            
            for attempt in range(max_retries):
                try:
                    url = 'http://push2his.eastmoney.com/api/qt/stock/kline/get'
                    params = {
                        'secid': secid,
                        'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                        'fields1': 'f1,f2,f3,f4,f5,f6',
                        'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                        'klt': '101',  # 日K
                        'fqt': '1',    # 前复权
                        'beg': '20240619',
                        'end': '20250619'
                    }
                    
                    response = self.session.get(url, params=params, timeout=20)
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        if 'data' in data and data['data'] and 'klines' in data['data']:
                            klines = data['data']['klines']
                            
                            if klines and len(klines) > 0:
                                # 保存数据到数据库
                                saved_count = self.save_stock_data(stock_code, klines)
                                
                                if saved_count > 0:
                                    logger.debug(f"✅ {stock_code} 成功保存 {saved_count} 条数据")
                                    return True, saved_count
                                else:
                                    logger.warning(f"⚠️ {stock_code} 数据保存失败")
                                    return False, 0
                            else:
                                logger.debug(f"⚠️ {stock_code} 无K线数据（可能是新股或停牌）")
                                return False, 0
                        else:
                            logger.debug(f"⚠️ {stock_code} API返回数据格式异常")
                            return False, 0
                    
                    elif response.status_code in [502, 503, 429, 500]:
                        # API限制，需要重试
                        if attempt < max_retries - 1:
                            wait_time = (attempt + 1) * 5
                            logger.debug(f"🔄 {stock_code} API限制({response.status_code})，等待{wait_time}秒重试...")
                            time.sleep(wait_time)
                            continue
                        else:
                            logger.debug(f"⚠️ {stock_code} API请求失败: {response.status_code}")
                            return False, 0
                    
                    else:
                        logger.debug(f"⚠️ {stock_code} API请求失败: {response.status_code}")
                        return False, 0
                
                except requests.exceptions.Timeout:
                    if attempt < max_retries - 1:
                        logger.debug(f"🔄 {stock_code} 超时，重试...")
                        time.sleep(2)
                        continue
                    else:
                        logger.debug(f"⚠️ {stock_code} 请求超时")
                        return False, 0
                
                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.debug(f"🔄 {stock_code} 异常，重试: {e}")
                        time.sleep(1)
                        continue
                    else:
                        logger.debug(f"⚠️ {stock_code} 请求异常: {e}")
                        return False, 0
            
            return False, 0
            
        except Exception as e:
            logger.error(f"收集 {stock_code} 数据失败: {e}")
            return False, 0
    
    def save_stock_data(self, stock_code: str, klines: List[str]) -> int:
        """保存股票数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            saved_count = 0
            
            for kline in klines:
                try:
                    # 解析K线数据
                    parts = kline.split(',')
                    if len(parts) >= 11:
                        trade_date = parts[0]
                        open_price = float(parts[1]) if parts[1] else 0
                        close_price = float(parts[2]) if parts[2] else 0
                        high_price = float(parts[3]) if parts[3] else 0
                        low_price = float(parts[4]) if parts[4] else 0
                        volume = int(parts[5]) if parts[5] else 0
                        amount = float(parts[6]) if parts[6] else 0
                        change_percent = float(parts[8]) if parts[8] else 0
                        turnover_rate = float(parts[10]) if parts[10] else 0
                        
                        # 插入数据
                        cursor.execute("""
                            INSERT OR REPLACE INTO daily_data 
                            (stock_code, trade_date, open_price, close_price, high_price, low_price,
                             volume, amount, change_percent, turnover_rate, data_source, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            stock_code, trade_date, open_price, close_price, high_price, low_price,
                            volume, amount, change_percent, turnover_rate, 'eastmoney_simple',
                            datetime.now().isoformat()
                        ))
                        
                        saved_count += 1
                
                except Exception as e:
                    logger.debug(f"解析K线数据失败 {stock_code}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            return saved_count
            
        except Exception as e:
            logger.error(f"保存股票数据失败 {stock_code}: {e}")
            return 0
    
    def run_collection(self, limit: int = None):
        """运行收集任务"""

        # 获取股票列表
        stock_codes = self.get_all_stocks()
        
        if not stock_codes:
            logger.error("❌ 无法获取股票列表")
            return
        
        if limit:
            stock_codes = stock_codes[:limit]
        
        logger.info(f"📊 开始收集 {len(stock_codes)} 只股票的历史数据")
        
        # 分批处理
        batch_size = 50
        total_batches = (len(stock_codes) + batch_size - 1) // batch_size
        
        collected_count = 0
        failed_count = 0
        total_records = 0
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(stock_codes))
            batch_stocks = stock_codes[start_idx:end_idx]
            
            logger.info(f"📦 处理第 {batch_num + 1}/{total_batches} 批，股票 {start_idx + 1}-{end_idx}")
            
            # 处理当前批次
            for i, stock_code in enumerate(batch_stocks):
                try:
                    success, records = self.collect_stock_data(stock_code)
                    
                    if success:
                        collected_count += 1
                        total_records += records
                    else:
                        failed_count += 1
                    
                    # 每10只股票显示一次进度
                    if (i + 1) % 10 == 0 or i == len(batch_stocks) - 1:
                        current_total = start_idx + i + 1
                        progress = current_total / len(stock_codes) * 100
                        success_rate = collected_count / current_total * 100 if current_total > 0 else 0
                        logger.info(f"📈 进度: {current_total}/{len(stock_codes)} ({progress:.1f}%)")
                        logger.info(f"📊 统计: 成功{collected_count}, 失败{failed_count}, 记录{total_records}, 成功率{success_rate:.1f}%")
                    
                    # 控制请求频率
                    time.sleep(0.5)
                
                except Exception as e:
                    logger.error(f"❌ 处理 {stock_code} 失败: {e}")
                    failed_count += 1
                    continue
            
            # 批次间休息
            if batch_num + 1 < total_batches:
                logger.info("⏳ 批次间休息10秒...")
                time.sleep(10)
        
        # 最终统计
        success_rate = collected_count / len(stock_codes) * 100 if len(stock_codes) > 0 else 0
        logger.info(f"🎉 收集完成！")
        logger.info(f"📊 最终统计:")
        logger.info(f"  总股票数: {len(stock_codes)}")
        logger.info(f"  成功收集: {collected_count}")
        logger.info(f"  失败数量: {failed_count}")
        logger.info(f"  总记录数: {total_records}")
        logger.info(f"  成功率: {success_rate:.1f}%")

def main():
    """主函数"""
    collector = SimpleEastmoneyCollector()
    
    # 先测试收集100只股票
    collector.run_collection(limit=100)

if __name__ == "__main__":
    main()
