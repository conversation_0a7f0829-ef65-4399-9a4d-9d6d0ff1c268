from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的东方财富数据测试
测试能否收集和存储真实数据
"""

import requests
import sqlite3
import os
import time
from datetime import datetime, timed<PERSON><PERSON>

def test_eastmoney_data_collection():
    """测试东方财富数据收集和存储"""
    print("🚀 开始测试东方财富数据收集...")
    
    # 禁用代理
    session = requests.Session()
    session.proxies = {}
    
    # 测试股票列表（手动指定几只知名股票）
    test_stocks = [
        {'code': '000001', 'name': '平安银行', 'secid': '0.000001'},
        {'code': '000002', 'name': '万科A', 'secid': '0.000002'},
        {'code': '600036', 'name': '招商银行', 'secid': '1.600036'},
        {'code': '600519', 'name': '贵州茅台', 'secid': '1.600519'},
        {'code': '000858', 'name': '五粮液', 'secid': '0.000858'}
    ]
    
    db_path = get_database_path("stock_database")
    
    # 确保数据库存在
    if not os.path.exists(db_path):
        print(f"❌ 数据库不存在: {db_path}")
        return False
    
    success_count = 0
    total_records = 0
    
    for i, stock in enumerate(test_stocks):
        try:
            stock_code = stock['code']
            stock_name = stock['name']
            secid = stock['secid']
            
            print(f"\\n📊 [{i+1}/{len(test_stocks)}] 测试 {stock_code} {stock_name}")
            
            # 获取近7天历史数据
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
            
            url = 'http://push2his.eastmoney.com/api/qt/stock/kline/get'
            params = {
                'secid': secid,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'fields1': 'f1,f2,f3,f4,f5,f6',
                'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                'klt': '101',  # 日K
                'fqt': '1',    # 前复权
                'beg': start_date,
                'end': end_date
            }
            
            print(f"  🔍 请求历史数据...")
            response = session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and data['data'] and 'klines' in data['data']:
                    klines = data['data']['klines']
                    print(f"  ✅ 获取到 {len(klines)} 条历史数据")
                    
                    # 解析数据
                    records = []
                    for kline in klines:
                        parts = kline.split(',')
                        if len(parts) >= 11:
                            try:
                                record = {
                                    'trade_date': parts[0],
                                    'open_price': float(parts[1]),
                                    'close_price': float(parts[2]),
                                    'high_price': float(parts[3]),
                                    'low_price': float(parts[4]),
                                    'volume': int(parts[5]),
                                    'amount': float(parts[6]),
                                    'change_percent': float(parts[8]),
                                    'turnover_rate': float(parts[10]) if parts[10] != '-' else 0.0
                                }
                                records.append(record)
                            except (ValueError, IndexError):
                                continue
                    
                    if records:
                        # 存储到数据库
                        print(f"  💾 存储 {len(records)} 条记录到数据库...")
                        
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        
                        current_time = datetime.now().isoformat()
                        
                        # 更新股票信息
                        cursor.execute("""
                            INSERT OR REPLACE INTO stock_info 
                            (stock_code, stock_name, updated_at)
                            VALUES (?, ?, ?)
                        """, (stock_code, stock_name, current_time))
                        
                        # 插入历史数据
                        inserted_count = 0
                        for record in records:
                            try:
                                cursor.execute("""
                                    INSERT OR REPLACE INTO daily_data 
                                    (stock_code, trade_date, open_price, close_price, high_price, low_price,
                                     volume, amount, change_percent, turnover_rate, data_source, created_at, updated_at)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                """, (
                                    stock_code,
                                    record['trade_date'],
                                    record['open_price'],
                                    record['close_price'],
                                    record['high_price'],
                                    record['low_price'],
                                    record['volume'],
                                    record['amount'],
                                    record['change_percent'],
                                    record['turnover_rate'],
                                    'eastmoney_test',
                                    current_time,
                                    current_time
                                ))
                                inserted_count += 1
                            except Exception as e:
                                print(f"    ⚠️ 插入记录失败: {e}")
                                continue
                        
                        conn.commit()
                        conn.close()
                        
                        print(f"  ✅ 成功插入 {inserted_count} 条记录")
                        success_count += 1
                        total_records += inserted_count
                        
                        # 显示最新一条数据
                        if records:
                            latest = records[-1]
                            print(f"  📈 最新数据: {latest['trade_date']} 收盘价 {latest['close_price']} 涨跌幅 {latest['change_percent']:.2f}%")
                    else:
                        print(f"  ⚠️ 无有效数据记录")
                else:
                    print(f"  ⚠️ API返回数据格式异常")
            else:
                print(f"  ❌ API请求失败: {response.status_code}")
            
            # 控制请求频率
            time.sleep(1)
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            continue
    
    # 验证数据库中的数据
    print(f"\\n📊 验证数据库中的数据...")
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 统计东方财富测试数据
        cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source = 'eastmoney_test'")
        test_data_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source = 'eastmoney_test'")
        test_stocks_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM daily_data WHERE data_source = 'eastmoney_test'")
        date_range = cursor.fetchone()
        
        # 显示最新的几条记录
        cursor.execute("""
            SELECT stock_code, trade_date, close_price, change_percent 
            FROM daily_data 
            WHERE data_source = 'eastmoney_test' 
            ORDER BY trade_date DESC, stock_code 
            LIMIT 5
        """)
        latest_records = cursor.fetchall()
        
        conn.close()
        
        print(f"✅ 数据库验证结果:")
        print(f"  测试数据总数: {test_data_count} 条")
        print(f"  涉及股票数: {test_stocks_count} 只")
        print(f"  数据时间范围: {date_range[0]} 至 {date_range[1]}")
        
        if latest_records:
            print(f"  最新记录:")
            for record in latest_records:
                code, date, price, change = record
                print(f"    {code} {date}: ¥{price} ({change:+.2f}%)")
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
    
    # 生成测试报告
    print(f"\\n🎉 测试完成!")
    print(f"成功股票: {success_count}/{len(test_stocks)}")
    print(f"总记录数: {total_records}")
    print(f"成功率: {success_count/len(test_stocks)*100:.1f}%")
    
    return success_count > 0

if __name__ == "__main__":
    success = test_eastmoney_data_collection()
    if success:
        print("\\n✅ 东方财富数据收集测试成功！")
    else:
        print("\\n❌ 东方财富数据收集测试失败！")
