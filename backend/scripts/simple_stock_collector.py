from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""

使用AkShare收集A股数据并存储到本地数据库
"""

import sqlite3
import os
import logging
from datetime import datetime, timedelta
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleStockCollector:
    pass
    def __init__(self):
        self.db_path = get_database_path("stock_database")
        self.collected_count = 0
        self.failed_count = 0
        
        # 确保数据目录存在
        os.makedirs("backend/data", exist_ok=True)

    def create_database_tables(self):
        """创建数据库表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建股票信息表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS stock_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT UNIQUE NOT NULL,
                    stock_name TEXT NOT NULL,
                    exchange TEXT,
                    industry TEXT,
                    sector TEXT,
                    market_cap REAL,
                    pe_ratio REAL,
                    pb_ratio REAL,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 创建日线数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open_price REAL,
                    close_price REAL,
                    high_price REAL,
                    low_price REAL,
                    volume INTEGER,
                    amount REAL,
                    change_percent REAL,
                    data_source TEXT DEFAULT 'akshare',
                    created_at TEXT,
                    updated_at TEXT,
                    UNIQUE(stock_code, trade_date)
                )
            """)
            
            # 创建实时数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS realtime_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT UNIQUE NOT NULL,
                    stock_name TEXT,
                    current_price REAL,
                    change_percent REAL,
                    volume INTEGER,
                    amount REAL,
                    market_cap REAL,
                    pe_ratio REAL,
                    pb_ratio REAL,
                    data_source TEXT DEFAULT 'akshare',
                    update_time TEXT,
                    created_at TEXT
                )
            """)
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 数据库表创建成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库表创建失败: {e}")
            return False
    
    def get_stock_list_akshare(self):
        """使用AkShare获取股票列表"""
        try:
            import akshare as ak
            
            logger.info("🔍 使用AkShare获取A股股票列表...")
            
            # 获取沪深A股列表
            stock_info_a = ak.stock_info_a_code_name()
            
            stock_list = []
            for _, row in stock_info_a.iterrows():
                stock_list.append({
                    "code": row["code"],
                    "name": row["name"],
                    "exchange": "SSE" if row["code"].startswith("6") else "SZSE"
                })
            
            logger.info(f"✅ 从AkShare获取到 {len(stock_list)} 只股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"❌ AkShare获取股票列表失败: {e}")
            # 返回备用股票列表

    def _get_fallback_stock_list(self):
        """备用股票列表（主要股票）"""
        logger.info("⚠️ 使用备用股票列表")
        return [
            {"code": "000001", "name": "平安银行", "exchange": "SZSE"},
            {"code": "000002", "name": "万科A", "exchange": "SZSE"},
            {"code": "000858", "name": "五粮液", "exchange": "SZSE"},
            {"code": "002415", "name": "海康威视", "exchange": "SZSE"},
            {"code": "002594", "name": "比亚迪", "exchange": "SZSE"},
            {"code": "300059", "name": "东方财富", "exchange": "SZSE"},
            {"code": "300750", "name": "宁德时代", "exchange": "SZSE"},
            {"code": "600000", "name": "浦发银行", "exchange": "SSE"},
            {"code": "600036", "name": "招商银行", "exchange": "SSE"},
            {"code": "600519", "name": "贵州茅台", "exchange": "SSE"},
            {"code": "600887", "name": "伊利股份", "exchange": "SSE"},
            {"code": "601318", "name": "中国平安", "exchange": "SSE"},
            {"code": "000725", "name": "京东方A", "exchange": "SZSE"},
            {"code": "002230", "name": "科大讯飞", "exchange": "SZSE"},
            {"code": "300015", "name": "爱尔眼科", "exchange": "SZSE"},
            {"code": "600276", "name": "恒瑞医药", "exchange": "SSE"},
            {"code": "600309", "name": "万华化学", "exchange": "SSE"},
            {"code": "600585", "name": "海螺水泥", "exchange": "SSE"},
            {"code": "601012", "name": "隆基绿能", "exchange": "SSE"},
            {"code": "601888", "name": "中国中免", "exchange": "SSE"},
        ]
    
    def collect_stock_basic_info(self, stock_list):
        """收集股票基本信息"""
        try:
            logger.info(f"📊 开始收集 {len(stock_list)} 只股票的基本信息...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().isoformat()
            
            for i, stock in enumerate(stock_list):
                try:
                    stock_code = stock["code"]
                    stock_name = stock["name"]
                    exchange = stock["exchange"]
                    
                    # 插入基本信息
                    cursor.execute("""
                        INSERT OR REPLACE INTO stock_info 
                        (stock_code, stock_name, exchange, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                    """, (stock_code, stock_name, exchange, current_time, current_time))
                    
                    self.collected_count += 1
                    
                    # 进度显示
                    if (i + 1) % 50 == 0:
                        logger.info(f"📈 已处理 {i + 1}/{len(stock_list)} 只股票")
                        conn.commit()  # 定期提交
                    
                except Exception as e:
                    logger.warning(f"⚠️ 处理股票 {stock.get('code', 'unknown')} 失败: {e}")
                    self.failed_count += 1
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ 股票基本信息收集完成，成功: {self.collected_count}, 失败: {self.failed_count}")
            
        except Exception as e:
            logger.error(f"❌ 收集股票基本信息失败: {e}")
    
    def collect_sample_historical_data(self, stock_list, days=30):
        """收集样本历史数据"""
        try:
            import akshare as ak
            
            logger.info(f"📈 开始收集近 {days} 天的样本历史数据...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().isoformat()
            
            # 只收集前10只股票的历史数据作为样本
            sample_stocks = stock_list[:10]
            
            for i, stock in enumerate(sample_stocks):
                try:
                    stock_code = stock["code"]
                    
                    logger.info(f"📊 正在收集 {stock_code} {stock['name']} 的历史数据...")
                    
                    # 获取历史数据
                    hist_data = ak.stock_zh_a_hist(
                        symbol=stock_code, 
                        period="daily", 
                        start_date=(datetime.now() - timedelta(days=days)).strftime("%Y%m%d"),
                        end_date=datetime.now().strftime("%Y%m%d"),
                        adjust=""
                    )
                    
                    if not hist_data.empty:
                        for _, row in hist_data.iterrows():
                            cursor.execute("""
                                INSERT OR REPLACE INTO daily_data 
                                (stock_code, trade_date, open_price, close_price, high_price, low_price,
                                 volume, amount, change_percent, data_source, created_at, updated_at)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                stock_code,
                                row["日期"].strftime("%Y-%m-%d"),
                                float(row["开盘"]),
                                float(row["收盘"]),
                                float(row["最高"]),
                                float(row["最低"]),
                                int(row["成交量"]),
                                float(row["成交额"]),
                                float(row["涨跌幅"]),
                                "akshare",
                                current_time, current_time
                            ))
                    
                    # 限制请求频率
                    time.sleep(1)
                    
                    logger.info(f"✅ {stock_code} 历史数据收集完成")
                    
                except Exception as e:
                    logger.warning(f"⚠️ 收集股票 {stock_code} 历史数据失败: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 样本历史数据收集完成")
            
        except Exception as e:
            logger.error(f"❌ 收集历史数据失败: {e}")
    
    def generate_collection_report(self):
        """生成收集报告"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计数据
            cursor.execute("SELECT COUNT(*) FROM stock_info")
            stock_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM daily_data")
            daily_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data")
            stocks_with_data = cursor.fetchone()[0]
            
            conn.close()
            
            report = f"""
📊 A股数据收集报告
==========================================
🕐 收集时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📈 股票信息: {stock_count} 只
📊 历史数据: {daily_count} 条
📋 有数据股票: {stocks_with_data} 只
✅ 成功收集: {self.collected_count} 只
❌ 失败数量: {self.failed_count} 只
==========================================
"""
            
            # 保存报告
            report_path = f"backend/data/stock_collection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(report)
            logger.info(f"📄 报告已保存: {report_path}")
            
        except Exception as e:
            logger.error(f"❌ 生成报告失败: {e}")
    
    def run_collection(self):
        """运行完整的数据收集流程"""
        try:
            logger.info("🚀 开始A股数据收集流程...")
            
            # 1. 创建数据库表
            if not self.create_database_tables():
                logger.error("❌ 数据库初始化失败，退出")
                return False
            
            # 2. 获取股票列表
            stock_list = self.get_stock_list_akshare()
            if not stock_list:
                logger.error("❌ 获取股票列表失败，退出")
                return False
            
            # 3. 收集基本信息
            self.collect_stock_basic_info(stock_list)
            
            # 4. 收集样本历史数据
            self.collect_sample_historical_data(stock_list)
            
            # 5. 生成统计报告
            self.generate_collection_report()
            
            logger.info("🎉 A股数据收集流程完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据收集流程失败: {e}")
            return False

def main():
    """主函数"""
    collector = SimpleStockCollector()
    success = collector.run_collection()
    
    if success:
        print("🎉 A股数据收集成功完成！")
    else:
        print("❌ A股数据收集失败")

if __name__ == "__main__":
    main()
