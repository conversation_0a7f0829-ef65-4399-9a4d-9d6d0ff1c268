from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统真实状态评估
"""

import sqlite3
import os
import requests

def evaluate_system_status():
    """评估系统真实状态"""
    print('=== 系统真实状态评估 ===')

    # 1. 数据库真实状态
    db_path = get_database_path('stock_database')
    if os.path.exists(db_path):
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 统计真实数据
        cursor.execute('SELECT COUNT(*) FROM stock_info')
        total_stocks = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
        real_data_stocks = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
        real_data_records = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM technical_indicators WHERE data_source = 'real_calculation'")
        real_tech_indicators = cursor.fetchone()[0]
        
        print(f'📊 数据库状态:')
        print(f'  总股票数: {total_stocks}')
        print(f'  有真实数据股票: {real_data_stocks}')
        print(f'  真实数据记录: {real_data_records}')
        print(f'  真实技术指标: {real_tech_indicators}')
        print(f'  数据完整度: {real_data_stocks/total_stocks*100:.1f}%')
        
        conn.close()
    else:
        print('❌ 数据库不存在')

    # 2. API服务状态
    print(f'\n🔌 API服务状态:')
    
    # 开阳星
    try:
        response = requests.get('http://localhost:8002/api/kaiyang-star/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            capabilities = data.get('capabilities', [])
            print(f'  ✅ 开阳星: {data.get("version", "unknown")} - {len(capabilities)} 项能力')
        else:
            print(f'  ❌ 开阳星: 不可用')
    except:
        print(f'  ❌ 开阳星: 连接失败')

    # 天枢星
    try:
        response = requests.get('http://localhost:8002/api/tianshu-star/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f'  ✅ 天枢星: {data.get("version", "unknown")}')
        else:
            print(f'  ❌ 天枢星: 不可用')
    except:
        print(f'  ❌ 天枢星: 连接失败')

    # 天权星
    try:
        response = requests.get('http://localhost:8002/api/tianquan-star/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f'  ✅ 天权星: {data.get("version", "unknown")}')
        else:
            print(f'  ❌ 天权星: 不可用')
    except:
        print(f'  ❌ 天权星: 连接失败')

    # 3. 核心功能测试
    print(f'\n🧪 核心功能测试:')

    # 股票筛选
    try:
        response = requests.post(
            'http://localhost:8002/api/kaiyang-star/stock-screening/enhanced',
            json={'screening_criteria': {'market_cap_min': **********}},
            timeout=30
        )
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                summary = data.get('data', {}).get('screening_summary', {})
                candidates = summary.get('total_candidates', 0)
                qualified = summary.get('qualified_stocks', 0)
                print(f'  ✅ 股票筛选: {candidates}→{qualified} 只股票')
            else:
                print(f'  ⚠️ 股票筛选: 功能有限')
        else:
            print(f'  ❌ 股票筛选: API失败')
    except:
        print(f'  ❌ 股票筛选: 连接失败')

    # 投资组合构建
    try:
        response = requests.post(
            'http://localhost:8002/api/kaiyang-star/portfolio/build',
            json={'total_amount': 1000000, 'max_stocks': 5},
            timeout=30
        )
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                portfolio = data.get('data', {}).get('portfolio', {})
                holdings = len(portfolio.get('holdings', []))
                print(f'  ✅ 投资组合: {holdings} 只股票')
            else:
                print(f'  ⚠️ 投资组合: 构建失败')
        else:
            print(f'  ❌ 投资组合: API失败')
    except:
        print(f'  ❌ 投资组合: 连接失败')

    # 缓存系统
    try:
        response = requests.get('http://localhost:8002/api/kaiyang-star/cache/stats', timeout=5)
        if response.status_code == 200:
            data = response.json()
            cache_data = data.get('data', {})
            total_size = sum(stats.get('cache_size', 0) for stats in cache_data.values())
            total_hits = sum(stats.get('hit_count', 0) for stats in cache_data.values())
            print(f'  ✅ 缓存系统: 大小{total_size}, 命中{total_hits}')
        else:
            print(f'  ❌ 缓存系统: API失败')
    except:
        print(f'  ❌ 缓存系统: 连接失败')

    print(f'\n=== 评估完成 ===')

if __name__ == "__main__":
    evaluate_system_status()
