from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有修复功能
"""

import requests
import time

def test_all_fixes():
    """测试所有修复功能"""
    print("🔧 测试所有修复功能")
    print("=" * 60)
    
    # 1. 测试缓存系统修复
    print("\n1. 测试缓存系统修复...")
    try:
        # 清空缓存
        response = requests.post('http://localhost:8002/api/kaiyang-star/cache/clear?cache_type=all', timeout=10)
        print(f"  清空缓存: {response.status_code}")
        
        # 第一次调用（缓存未命中）
        start_time = time.time()
        response = requests.post(
            'http://localhost:8002/api/kaiyang-star/stock-screening/enhanced',
            json={'screening_criteria': {'market_cap_min': 1000000000}},
            timeout=60
        )
        first_time = time.time() - start_time
        print(f"  第一次筛选耗时: {first_time:.2f}秒")
        
        # 检查缓存统计
        response = requests.get('http://localhost:8002/api/kaiyang-star/cache/stats', timeout=10)
        if response.status_code == 200:
            data = response.json()
            cache_data = data.get('data', {})
            total_cache_size = sum(stats.get('cache_size', 0) for stats in cache_data.values())
            print(f"  缓存大小: {total_cache_size}")
            
            if total_cache_size > 0:
                print("  ✅ 缓存系统修复成功")
            else:
                print("  ⚠️ 缓存系统仍未使用")
        
    except Exception as e:
        print(f"  ❌ 缓存测试失败: {e}")
    
    # 2. 测试天玑星风险评估修复
    print("\n2. 测试天玑星风险评估修复...")
    try:
        response = requests.get('http://localhost:8002/api/kaiyang-star/risk/stock/000001', timeout=30)
        if response.status_code == 200:
            data = response.json()
            risk_data = data.get('data', {})
            data_source = risk_data.get('data_source', 'unknown')
            risk_summary = risk_data.get('risk_summary', 'unknown')
            
            print(f"  数据源: {data_source}")
            print(f"  风险摘要: {risk_summary}")
            
            if data_source != "默认风险评估" or risk_summary != "风险数据不可用":
                print("  ✅ 天玑星风险评估修复成功")
            else:
                print("  ⚠️ 天玑星风险评估仍返回默认数据")
        else:
            print(f"  ❌ 风险评估API失败: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ 风险评估测试失败: {e}")
    
    # 3. 测试投资组合构建修复
    print("\n3. 测试投资组合构建修复...")
    try:
        response = requests.post(
            'http://localhost:8002/api/kaiyang-star/portfolio/build',
            json={
                'total_amount': 1000000,
                'max_stocks': 8,
                'min_stocks': 3,
                'investment_style': 'balanced',
                'risk_tolerance': 'medium'
            },
            timeout=120
        )
        
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            
            if success:
                portfolio_data = data.get('data', {})
                portfolio = portfolio_data.get('portfolio', {})
                holdings_count = len(portfolio.get('holdings', []))
                print(f"  构建成功: {holdings_count} 只股票")
                print("  ✅ 投资组合构建修复成功")
            else:
                error = data.get('data', {}).get('error', 'unknown')
                print(f"  构建失败: {error}")
                if "候选股票不足" not in error:
                    print("  ✅ 投资组合构建部分修复（错误信息改善）")
                else:
                    print("  ⚠️ 投资组合构建仍有候选股票问题")
        else:
            print(f"  ❌ 投资组合API失败: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ 投资组合测试失败: {e}")
    
    # 4. 测试瑶光星API端点修复
    print("\n4. 测试瑶光星API端点修复...")
    try:
        # 测试健康检查
        response = requests.get('http://localhost:8002/api/yaoguang-star/health', timeout=10)
        print(f"  健康检查: {response.status_code}")
        
        # 测试数据收集状态
        response = requests.get('http://localhost:8002/api/yaoguang-star/data/collection-status', timeout=30)
        print(f"  数据收集状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            collection_data = data.get('data', {})
            total_stocks = collection_data.get('total_stocks', 0)
            eastmoney_stocks = collection_data.get('eastmoney_stocks', 0)
            print(f"  总股票数: {total_stocks}")
            print(f"  东方财富股票: {eastmoney_stocks}")
            print("  ✅ 瑶光星API端点修复成功")
        else:
            print("  ⚠️ 瑶光星新API端点仍不可用")
            
    except Exception as e:
        print(f"  ❌ 瑶光星测试失败: {e}")
    
    # 5. 检查全部A股数据收集进度
    print("\n5. 检查全部A股数据收集进度...")
    try:
        import sqlite3
        
        db_path = get_database_path('stock_database')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查总体进度
        cursor.execute('SELECT COUNT(*) FROM stock_info')
        total_stocks = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
        collected_stocks = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
        collected_records = cursor.fetchone()[0]
        
        conn.close()
        
        progress = collected_stocks / total_stocks * 100 if total_stocks > 0 else 0
        
        print(f"  总股票数: {total_stocks}")
        print(f"  已收集股票: {collected_stocks}")
        print(f"  已收集记录: {collected_records}")
        print(f"  收集进度: {progress:.1f}%")
        
        if collected_stocks > 100:
            print("  ✅ A股数据收集进展良好")
        else:
            print("  ⚠️ A股数据收集仍在进行中")
            
    except Exception as e:
        print(f"  ❌ 数据收集检查失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 修复功能测试完成")

if __name__ == "__main__":
    test_all_fixes()
