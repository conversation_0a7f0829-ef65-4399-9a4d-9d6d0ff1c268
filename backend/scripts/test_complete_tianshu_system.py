from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星完整系统测试脚本
测试所有真实功能：新闻收集、事件识别、影响评估、信息推送等
"""

import asyncio
import logging
import sys
import os
import json
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
project_root = os.path.dirname(backend_dir)
sys.path.append(project_root)
sys.path.append(backend_dir)

logger = logging.getLogger(__name__)

async def test_complete_tianshu_system():
    """测试天枢星完整系统"""
    print(" 天枢星完整系统测试")
    print("="*60)
    
    test_results = {
        "news_collection": {"status": "pending", "details": {}},
        "event_identification": {"status": "pending", "details": {}},
        "impact_assessment": {"status": "pending", "details": {}},
        "information_push": {"status": "pending", "details": {}},
        "api_routes": {"status": "pending", "details": {}},
        "overall": {"status": "pending", "success_rate": 0}
    }
    
    # 1. 测试新闻收集服务
    print("\n📰 1. 测试新闻收集服务")
    print("-" * 40)
    
    try:
        from backend.roles.tianshu_star.services.news_collection_service import news_collection_service
        
        # 测试股票新闻收集
        test_symbol = "000001"
        news_result = await news_collection_service.collect_stock_news(test_symbol, limit=5)
        
        if news_result.get("success"):
            news_count = news_result.get("news_count", 0)
            print(f" 股票新闻收集成功: {news_count}条新闻")
            test_results["news_collection"]["status"] = "success"
            test_results["news_collection"]["details"] = {
                "news_count": news_count,
                "sources": news_result.get("sources_used", []),
                "success_rate": news_result.get("success_rate", 0)
            }
        else:
            print(f" 股票新闻收集失败: {news_result.get('error', '未知错误')}")
            test_results["news_collection"]["status"] = "failed"
            test_results["news_collection"]["details"] = {"error": news_result.get("error")}
        
        # 测试市场新闻收集
        market_result = await news_collection_service.collect_market_news(limit=3)
        if market_result.get("success"):
            print(f" 市场新闻收集成功: {market_result.get('news_count', 0)}条新闻")
        else:
            print(f"⚠️ 市场新闻收集失败: {market_result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f" 新闻收集服务测试异常: {e}")
        test_results["news_collection"]["status"] = "error"
        test_results["news_collection"]["details"] = {"error": str(e)}
    
    # 2. 测试事件识别服务
    print("\n🔍 2. 测试事件识别服务")
    print("-" * 40)
    
    try:
        from backend.roles.tianshu_star.services.event_identification_service import event_identification_service
        
        # 准备测试新闻数据
        test_news = [
            {
                "title": "某公司发布重大重组公告",
                "content": "该公司宣布与行业龙头企业进行重大资产重组，预计将显著提升公司竞争力",
                "source": "财经网",
                "timestamp": datetime.now().isoformat()
            },
            {
                "title": "央行宣布降准0.5个百分点",
                "content": "中国人民银行决定下调存款准备金率0.5个百分点，释放流动性约1万亿元",
                "source": "新华社",
                "timestamp": datetime.now().isoformat()
            }
        ]
        
        events_result = await event_identification_service.identify_events_from_news(test_news, threshold=0.6)
        
        if events_result.get("success"):
            events_count = len(events_result.get("events", []))
            print(f" 事件识别成功: 发现{events_count}个事件")
            
            # 显示识别的事件
            for i, event in enumerate(events_result.get("events", [])[:2], 1):
                print(f"   事件{i}: {event.get('title', 'N/A')}")
                print(f"   类型: {event.get('event_type', 'N/A')}")
                print(f"   重要性: {event.get('importance_level', 'N/A')}")
                print(f"   置信度: {event.get('confidence_score', 0):.2f}")
            
            test_results["event_identification"]["status"] = "success"
            test_results["event_identification"]["details"] = {
                "events_count": events_count,
                "statistics": events_result.get("statistics", {})
            }
        else:
            print(f" 事件识别失败: {events_result.get('error', '未知错误')}")
            test_results["event_identification"]["status"] = "failed"
            test_results["event_identification"]["details"] = {"error": events_result.get("error")}
            
    except Exception as e:
        print(f" 事件识别服务测试异常: {e}")
        test_results["event_identification"]["status"] = "error"
        test_results["event_identification"]["details"] = {"error": str(e)}
    
    # 3. 测试影响评估服务
    print("\n 3. 测试影响评估服务")
    print("-" * 40)
    
    try:
        from backend.roles.tianshu_star.services.impact_assessment_service import impact_assessment_service
        
        # 准备测试新闻数据
        test_news_data = {
            "title": "某科技公司发布突破性AI芯片",
            "content": "该公司发布的新一代AI芯片性能提升300%，预计将重塑行业格局",
            "event_type": "corporate",
            "importance_level": "high"
        }
        
        test_symbols = ["000001", "300750"]
        
        impact_result = await impact_assessment_service.assess_news_impact(test_news_data, test_symbols)
        
        if impact_result.get("success"):
            overall_score = impact_result.get("overall_assessment", {}).get("overall_impact_score", 0)
            print(f" 影响评估成功: 综合影响分数 {overall_score:.2f}")
            
            # 显示股票特定影响
            stock_impacts = impact_result.get("stock_specific_impacts", {})
            for symbol, impact in stock_impacts.items():
                if impact.get("predicted_impact_score"):
                    print(f"   {symbol}: 预测影响 {impact['predicted_impact_score']:.2f}")
            
            test_results["impact_assessment"]["status"] = "success"
            test_results["impact_assessment"]["details"] = {
                "overall_score": overall_score,
                "stocks_analyzed": len(stock_impacts)
            }
        else:
            print(f" 影响评估失败: {impact_result.get('error', '未知错误')}")
            test_results["impact_assessment"]["status"] = "failed"
            test_results["impact_assessment"]["details"] = {"error": impact_result.get("error")}
            
    except Exception as e:
        print(f" 影响评估服务测试异常: {e}")
        test_results["impact_assessment"]["status"] = "error"
        test_results["impact_assessment"]["details"] = {"error": str(e)}
    
    # 4. 测试信息推送服务
    print("\n📤 4. 测试信息推送服务")
    print("-" * 40)
    
    try:
        from backend.roles.tianshu_star.services.information_push_service import information_push_service
        
        # 启动推送处理器
        await information_push_service.start_push_processor()
        
        # 测试新闻预警推送
        test_news_alert = {
            "title": "重要市场消息",
            "content": "测试新闻预警推送功能",
            "importance_level": "high"
        }
        
        push_result = await information_push_service.push_news_alert(
            test_news_alert, 
            ["tianquan", "tianji"], 
            "high"
        )
        
        if push_result.get("success"):
            print(f" 新闻预警推送成功: 推送ID {push_result.get('push_id', 'N/A')}")
            print(f"   目标: {push_result.get('targets', [])}")
            print(f"   优先级: {push_result.get('priority', 'N/A')}")
            
            # 等待推送处理
            await asyncio.sleep(2)
            
            # 获取推送统计
            stats = await information_push_service.get_push_stats()
            print(f"   推送统计: 总计{stats['stats']['total_pushed']}次")
            
            test_results["information_push"]["status"] = "success"
            test_results["information_push"]["details"] = {
                "push_id": push_result.get("push_id"),
                "targets_count": len(push_result.get("targets", [])),
                "stats": stats["stats"]
            }
        else:
            print(f" 信息推送失败: {push_result.get('error', '未知错误')}")
            test_results["information_push"]["status"] = "failed"
            test_results["information_push"]["details"] = {"error": push_result.get("error")}
        
        # 停止推送处理器
        await information_push_service.stop_push_processor()
        
    except Exception as e:
        print(f" 信息推送服务测试异常: {e}")
        test_results["information_push"]["status"] = "error"
        test_results["information_push"]["details"] = {"error": str(e)}
    
    # 5. 测试API路由
    print("\n🌐 5. 测试API路由")
    print("-" * 40)
    
    try:
        from backend.api.roles.tianshu_star_api import tianshu_star_router
        
        # 检查路由注册
        routes_count = len(tianshu_star_router.routes)
        print(f" API路由加载成功: {routes_count}个端点")
        
        # 显示主要端点
        main_endpoints = [
            "/api/tianshu/news/collect/start",
            "/api/tianshu/events/identify", 
            "/api/tianshu/impact/assess",
            "/api/tianshu/push/news",
            "/api/tianshu/health"
        ]
        
        print("   主要端点:")
        for endpoint in main_endpoints:
            print(f"   - {endpoint}")
        
        test_results["api_routes"]["status"] = "success"
        test_results["api_routes"]["details"] = {
            "routes_count": routes_count,
            "main_endpoints": main_endpoints
        }
        
    except Exception as e:
        print(f" API路由测试异常: {e}")
        test_results["api_routes"]["status"] = "error"
        test_results["api_routes"]["details"] = {"error": str(e)}
    
    # 6. 生成测试报告
    print("\n 6. 测试报告")
    print("="*60)
    
    success_count = sum(1 for result in test_results.values() if result.get("status") == "success")
    total_tests = len(test_results) - 1  # 排除overall
    success_rate = (success_count / total_tests) * 100
    
    test_results["overall"]["success_rate"] = success_rate
    test_results["overall"]["status"] = "success" if success_rate >= 80 else "partial" if success_rate >= 60 else "failed"
    
    print(f"测试完成时间: {datetime.now().isoformat()}")
    print(f"总体成功率: {success_rate:.1f}% ({success_count}/{total_tests})")
    print(f"系统状态: {test_results['overall']['status']}")
    
    print("\n详细结果:")
    for test_name, result in test_results.items():
        if test_name != "overall":
            status_icon = "✅" if result["status"] == "success" else "⚠️" if result["status"] == "partial" else "❌"
            print(f"{status_icon} {test_name}: {result['status']}")
    
    # 保存测试报告
    try:
        report_path = "backend/data/tianshu_system_test_report.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 测试报告已保存: {report_path}")
        
    except Exception as e:
        print(f"⚠️ 保存测试报告失败: {e}")
    
    # 总结和建议
    print("\n 总结和建议:")
    if success_rate >= 90:
        print("  天枢星系统运行优秀！所有核心功能正常工作。")
    elif success_rate >= 80:
        print("👍 天枢星系统运行良好，大部分功能正常。")
    elif success_rate >= 60:
        print("⚠️ 天枢星系统部分功能存在问题，需要优化。")
    else:
        print(" 天枢星系统存在严重问题，需要立即修复。")
    
    print("\n🔧 系统能力:")
    print(" 真实新闻收集 - 使用Crawl4AI爬取专业财经网站")
    print(" 智能事件识别 - 从新闻中识别重要市场事件")
    print(" 专业影响评估 - 评估事件对股价和市场的影响")
    print(" 实时信息推送 - 向其他星推送重要信息")
    print(" 完整API接口 - 提供标准化的API服务")
    print(" 统一数据源 - 集成东方财富等真实数据源")
    
    return test_results

async def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        test_results = await test_complete_tianshu_system()
        
        # 返回退出码
        success_rate = test_results["overall"]["success_rate"]
        if success_rate >= 80:
            sys.exit(0)  # 成功
        else:
            sys.exit(1)  # 失败
            
    except Exception as e:
        print(f" 测试执行异常: {e}")
        logger.error(f"天枢星系统测试失败: {e}")
        sys.exit(2)  # 异常

if __name__ == "__main__":
    asyncio.run(main())
