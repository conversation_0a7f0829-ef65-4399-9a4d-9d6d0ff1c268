from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试开阳星到天权星的数据传输
"""

import requests
import os

def test_kaiyang_tianquan_transfer():
    """测试开阳星到天权星数据传输"""
    print('=== 测试开阳星到天权星数据传输 ===')

    try:
        # 测试机会推送功能
        response = requests.post(
            'http://localhost:8002/api/kaiyang-star/opportunity-push/tianquan',
            json={
                'opportunities': [
                    {
                        'stock_code': '000001',
                        'stock_name': '平安银行',
                        'opportunity_type': '技术突破',
                        'confidence': 0.85,
                        'expected_return': 0.15,
                        'risk_level': '中等',
                        'time_horizon': '短期',
                        'analysis_reason': 'AI智能分析推荐'
                    }
                ]
            },
            timeout=30
        )
        
        print(f'推送状态码: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'推送成功: {data.get("success")}')
            
            if data.get('success'):
                push_data = data.get('data', {})
                print(f'总机会数: {push_data.get("total_opportunities", 0)}')
                print(f'推送成功数: {push_data.get("pushed_count", 0)}')
                print(f'成功率: {push_data.get("success_rate", 0):.1%}')
                
                results = push_data.get('push_results', [])
                if results:
                    print('推送结果:')
                    for result in results:
                        status = result.get('status', 'unknown')
                        stock_code = result.get('stock_code', 'unknown')
                        print(f'  {stock_code}: {status}')
                        if status == 'success':
                            strategy_id = result.get('strategy_id', 'unknown')
                            print(f'    策略ID: {strategy_id}')
                
                print('✅ 开阳星到天权星数据传输正常工作')
            else:
                print('⚠️ 推送失败')
        else:
            print(f'❌ API调用失败: {response.text[:200]}')

    except Exception as e:
        print(f'❌ 测试失败: {e}')

    # 检查是否有策略文件生成
    strategy_dir = 'backend/data/tianquan_strategies'
    if os.path.exists(strategy_dir):
        files = os.listdir(strategy_dir)
        print(f'\n天权星策略文件: {len(files)} 个')
        if files:
            print('最新策略文件:')
            for file in sorted(files)[-3:]:  # 显示最新3个
                print(f'  {file}')
    else:
        print('\n天权星策略目录不存在')

if __name__ == "__main__":
    test_kaiyang_tianquan_transfer()
