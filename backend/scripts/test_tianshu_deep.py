from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星深度测试脚本
测试清理后的天枢星系统，确保所有核心功能正常工作
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_tianshu_core_service():
    """测试天枢星核心服务"""
    print("\n🔍 测试天枢星核心服务")
    print("-" * 50)
    
    try:
        from backend.roles.tianshu_star.services.tianshu_core_service import tianshu_core_service
        
        # 1. 测试服务状态
        status = tianshu_core_service.get_service_status()
        print(f"✅ 服务状态: {status['service_name']} v{status['version']}")
        print(f"   组件状态: {status['components']}")
        
        # 2. 测试新闻收集
        print("\n📰 测试新闻收集...")
        news_result = await tianshu_core_service._collect_news(symbols=["000001"], limit=3)
        print(f"   收集结果: {len(news_result)}条新闻")
        
        # 3. 测试事件识别
        print("\n🎯 测试事件识别...")
        events = await tianshu_core_service._identify_events(news_result[:2])
        print(f"   识别结果: {len(events)}个事件")
        
        # 4. 测试影响评估
        print("\n📊 测试影响评估...")
        impact = await tianshu_core_service._assess_impact(events, ["000001"])
        print(f"   评估结果: {impact.get('impact_level', 'unknown')}级影响")
        
        # 5. 测试完整流程
        print("\n🔄 测试完整情报分析流程...")
        full_result = await tianshu_core_service.collect_and_analyze_news(["000001"], 5)
        print(f"   完整流程: {'成功' if full_result.get('success') else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 天枢星核心服务测试失败: {e}")
        return False

async def test_news_collection_service():
    """测试新闻收集服务"""
    print("\n📰 测试新闻收集服务")
    print("-" * 50)
    
    try:
        from backend.roles.tianshu_star.services.news_collection_service import news_collection_service
        
        # 1. 测试服务状态
        status = news_collection_service.get_service_status()
        print(f"✅ 服务状态: {status['service_name']} v{status['version']}")
        print(f"   爬虫可用: {status['news_crawler_available']}")
        
        # 2. 测试股票新闻收集
        print("\n🏢 测试股票新闻收集...")
        stock_result = await news_collection_service.collect_stock_news("000001", 3)
        print(f"   股票新闻: {'成功' if stock_result.get('success') else '失败'}")
        if stock_result.get('success'):
            print(f"   收集数量: {stock_result.get('news_count', 0)}条")
        
        # 3. 测试市场新闻收集
        print("\n🌐 测试市场新闻收集...")
        market_result = await news_collection_service.collect_market_news(3)
        print(f"   市场新闻: {'成功' if market_result.get('success') else '失败'}")
        if market_result.get('success'):
            print(f"   收集数量: {market_result.get('news_count', 0)}条")
        
        # 4. 测试最新新闻获取
        print("\n🆕 测试最新新闻获取...")
        latest_result = await news_collection_service.get_latest_news(["000001"], 5)
        print(f"   最新新闻: {'成功' if latest_result.get('success') else '失败'}")
        if latest_result.get('success'):
            print(f"   获取数量: {latest_result.get('news_count', 0)}条")
        
        return True
        
    except Exception as e:
        print(f"❌ 新闻收集服务测试失败: {e}")
        return False

async def test_api_endpoints():
    """测试API端点"""
    print("\n🔌 测试API端点")
    print("-" * 50)
    
    try:
        import requests
        import time
        
        # 启动API服务器（模拟）
        base_url = "http://localhost:8000"
        
        # 测试端点列表
        endpoints = [
            ("GET", "/api/tianshu/health", "健康检查"),
            ("GET", "/api/tianshu/intelligence/latest", "最新情报"),
            ("POST", "/api/tianshu/intelligence/analyze", "情报分析"),
            ("POST", "/api/tianshu/news/collect", "新闻收集"),
            ("GET", "/api/tianshu/events/identify", "事件识别"),
            ("POST", "/api/tianshu/impact/assess", "影响评估"),
            ("POST", "/api/tianshu/push/news", "新闻推送")
        ]
        
        print("📋 API端点清单:")
        for method, endpoint, description in endpoints:
            print(f"   {method:4} {endpoint:35} - {description}")
        
        print(f"\n✅ 天枢星API共有 {len(endpoints)} 个端点")
        return True
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        return False

def test_code_cleanup():
    """测试代码清理效果"""
    print("\n🧹 测试代码清理效果")
    print("-" * 50)
    
    try:
        import os
        
        # 检查已删除的文件
        deleted_files = [
            "backend/roles/tianshu_star/services/ai_search_service.py",
            "backend/roles/tianshu_star/services/dual_mode_intelligence_service.py",
            "backend/roles/tianshu_star/services/intelligence_analysis_service.py",
            "backend/roles/tianshu_star/services/market_intelligence_service.py",
            "backend/roles/tianshu_star/services/unified_data_collector.py"
        ]
        
        deleted_count = 0
        for file_path in deleted_files:
            if not os.path.exists(file_path):
                deleted_count += 1
        
        print(f"✅ 已删除重复文件: {deleted_count}/{len(deleted_files)}")
        
        # 检查核心文件存在
        core_files = [
            "backend/roles/tianshu_star/services/tianshu_core_service.py",
            "backend/roles/tianshu_star/services/news_collection_service.py",
            "backend/api/roles/tianshu_star_api.py"
        ]
        
        core_count = 0
        for file_path in core_files:
            if os.path.exists(file_path):
                core_count += 1
        
        print(f"✅ 核心文件完整: {core_count}/{len(core_files)}")
        
        if os.path.exists("backend/roles/tianshu_star/services/news_collection_service.py"):
            size = os.path.getsize("backend/roles/tianshu_star/services/news_collection_service.py")
            print(f"✅ 新闻收集服务文件大小: {size//1024}KB (精简版)")
        
        return True
        
    except Exception as e:
        print(f"❌ 代码清理检查失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 天枢星深度测试开始")
    print("=" * 80)
    
    test_results = {}
    
    # 1. 测试代码清理
    test_results["code_cleanup"] = test_code_cleanup()
    
    # 2. 测试核心服务
    test_results["core_service"] = await test_tianshu_core_service()
    
    # 3. 测试新闻收集服务
    test_results["news_service"] = await test_news_collection_service()
    
    # 4. 测试API端点
    test_results["api_endpoints"] = await test_api_endpoints()
    
    # 计算总体成功率
    success_count = sum(1 for result in test_results.values() if result)
    total_count = len(test_results)
    success_rate = (success_count / total_count) * 100
    
    print("\n" + "=" * 80)
    print("📊 天枢星深度测试报告")
    print("=" * 80)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 总体成功率: {success_rate:.1f}% ({success_count}/{total_count})")
    
    if success_rate >= 80:
        print("🏆 天枢星系统状态: 优秀")
    elif success_rate >= 60:
        print("👍 天枢星系统状态: 良好")
    else:
        print("⚠️ 天枢星系统状态: 需要改进")
    
    # 保存测试报告
    report = {
        "test_time": datetime.now().isoformat(),
        "test_results": test_results,
        "success_rate": success_rate,
        "summary": {
            "total_tests": total_count,
            "passed_tests": success_count,
            "failed_tests": total_count - success_count
        }
    }
    
    with open("backend/data/tianshu_deep_test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 测试报告已保存: backend/data/tianshu_deep_test_report.json")

if __name__ == "__main__":
    asyncio.run(main())
