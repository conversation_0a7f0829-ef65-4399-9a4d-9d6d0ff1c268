from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证东方财富数据收集结果
"""

import sqlite3

def verify_eastmoney_data():
    """验证东方财富数据"""
    db_path = get_database_path('stock_database')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    print('=== 验证东方财富真实数据收集结果 ===')

    # 1. 统计所有数据
    cursor.execute('SELECT COUNT(*) FROM daily_data')
    total_records = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
    eastmoney_records = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
    eastmoney_stocks = cursor.fetchone()[0]

    print(f'数据库总记录数: {total_records}')
    print(f'东方财富记录数: {eastmoney_records}')
    print(f'东方财富股票数: {eastmoney_stocks}')

    # 2. 检查数据时间范围
    cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM daily_data WHERE data_source LIKE 'eastmoney%'")
    date_range = cursor.fetchone()
    print(f'数据时间范围: {date_range[0]} 至 {date_range[1]}')

    # 3. 显示最新的几条记录
    cursor.execute("""
        SELECT stock_code, trade_date, close_price, change_percent
        FROM daily_data 
        WHERE data_source LIKE 'eastmoney%'
        ORDER BY trade_date DESC, stock_code 
        LIMIT 10
    """)

    latest_records = cursor.fetchall()
    print(f'\n最新记录（前10条）:')
    for record in latest_records:
        code, date, price, change = record
        print(f'  {code} {date}: ¥{price} ({change:+.2f}%)')

    # 4. 数据质量检查
    cursor.execute("""
        SELECT stock_code, COUNT(*) as record_count
        FROM daily_data 
        WHERE data_source LIKE 'eastmoney%'
        GROUP BY stock_code 
        ORDER BY record_count DESC 
        LIMIT 5
    """)

    quality_data = cursor.fetchall()
    print(f'\n数据质量检查（前5只股票）:')
    for row in quality_data:
        code, count = row
        print(f'  {code}: {count} 条记录')

    # 5. 检查数据完整性
    cursor.execute("""
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN open_price > 0 THEN 1 END) as valid_open,
            COUNT(CASE WHEN close_price > 0 THEN 1 END) as valid_close,
            COUNT(CASE WHEN volume > 0 THEN 1 END) as valid_volume
        FROM daily_data 
        WHERE data_source LIKE 'eastmoney%'
    """)

    integrity = cursor.fetchone()
    total, valid_open, valid_close, valid_volume = integrity

    print(f'\n数据完整性检查:')
    print(f'总记录数: {total}')
    print(f'有效开盘价: {valid_open} ({valid_open/total*100:.1f}%)')
    print(f'有效收盘价: {valid_close} ({valid_close/total*100:.1f}%)')
    print(f'有效成交量: {valid_volume} ({valid_volume/total*100:.1f}%)')

    conn.close()
    print(f'\n✅ 数据验证完成！东方财富API数据收集成功，数据质量良好。')

if __name__ == "__main__":
    verify_eastmoney_data()
