#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
概念漂移检测服务
基于统计检验和机器学习的概念漂移检测和适应
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
from scipy import stats
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import sqlite3
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class DriftType(Enum):
    """漂移类型"""
    SUDDEN_DRIFT = "sudden_drift"          # 突然漂移
    GRADUAL_DRIFT = "gradual_drift"        # 渐进漂移
    INCREMENTAL_DRIFT = "incremental_drift" # 增量漂移
    RECURRING_DRIFT = "recurring_drift"     # 循环漂移
    BLIP_DRIFT = "blip_drift"              # 瞬时漂移

class DriftDetectionMethod(Enum):
    """漂移检测方法"""
    ADWIN = "adwin"                        # Adaptive Windowing
    DDM = "ddm"                            # Drift Detection Method
    EDDM = "eddm"                          # Early Drift Detection Method
    PAGE_HINKLEY = "page_hinkley"          # Page-Hinkley Test
    KSWIN = "kswin"                        # Kolmogorov-Smirnov Windowing
    STATISTICAL_TEST = "statistical_test"   # 统计检验
    ISOLATION_FOREST = "isolation_forest"   # 孤立森林
    ENSEMBLE_METHOD = "ensemble_method"     # 集成方法

class DriftSeverity(Enum):
    """漂移严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class DriftDetectionResult:
    """漂移检测结果"""
    detection_id: str
    timestamp: datetime
    drift_detected: bool
    drift_type: Optional[DriftType]
    drift_severity: DriftSeverity
    confidence_score: float
    detection_method: DriftDetectionMethod
    affected_features: List[str]
    drift_magnitude: float
    statistical_significance: float
    adaptation_required: bool
    recommended_actions: List[str]

@dataclass
class ConceptWindow:
    """概念窗口"""
    window_id: str
    start_time: datetime
    end_time: datetime
    data_points: int
    feature_statistics: Dict[str, Dict[str, float]]
    concept_signature: np.ndarray
    stability_score: float

class ConceptDriftDetector:
    """概念漂移检测器"""
    
    def __init__(self, data_dir: str = "data/concept_drift"):
        """初始化概念漂移检测器"""
        self.service_name = "ConceptDriftDetector"
        self.version = "2.0.0"
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 漂移检测数据库
        self.db_path = self.data_dir / "concept_drift.db"
        self.init_drift_database()
        
        # 检测配置
        self.detection_config = {
            "window_size": 1000,              # 滑动窗口大小
            "min_window_size": 100,           # 最小窗口大小
            "significance_level": 0.05,       # 显著性水平
            "drift_threshold": 0.1,           # 漂移阈值
            "adaptation_threshold": 0.2,      # 适应阈值
            "ensemble_threshold": 0.6,        # 集成方法阈值
            "stability_period": 50,           # 稳定期
            "max_windows": 10,                # 最大窗口数
            "feature_importance_threshold": 0.1
        }
        
        # 检测器状态
        self.concept_windows: List[ConceptWindow] = []
        self.current_concept: Optional[ConceptWindow] = None
        self.drift_history: List[DriftDetectionResult] = []
        
        # 统计模型
        self.scaler = StandardScaler()
        self.pca = PCA(n_components=0.95)
        self.isolation_forest = IsolationForest(contamination=0.1, random_state=42)
        self.kmeans = KMeans(n_clusters=3, random_state=42)
        
        # 检测器实例
        self.detectors = {
            DriftDetectionMethod.ADWIN: self._init_adwin_detector(),
            DriftDetectionMethod.DDM: self._init_ddm_detector(),
            DriftDetectionMethod.EDDM: self._init_eddm_detector(),
            DriftDetectionMethod.PAGE_HINKLEY: self._init_page_hinkley_detector(),
            DriftDetectionMethod.KSWIN: self._init_kswin_detector()
        }
        
        # 性能统计
        self.performance_stats = {
            "total_detections": 0,
            "true_positives": 0,
            "false_positives": 0,
            "true_negatives": 0,
            "false_negatives": 0,
            "detection_accuracy": 0.0,
            "detection_precision": 0.0,
            "detection_recall": 0.0,
            "average_detection_time": 0.0
        }
        
        logger.info(f"  {self.service_name} v{self.version} 概念漂移检测器初始化完成")
    
    def init_drift_database(self):
        """初始化漂移检测数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 漂移检测结果表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS drift_detections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                detection_id TEXT UNIQUE,
                timestamp TEXT,
                drift_detected BOOLEAN,
                drift_type TEXT,
                drift_severity TEXT,
                confidence_score REAL,
                detection_method TEXT,
                affected_features TEXT,
                drift_magnitude REAL,
                statistical_significance REAL,
                adaptation_required BOOLEAN,
                recommended_actions TEXT
            )
        ''')
        
        # 概念窗口表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS concept_windows (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                window_id TEXT UNIQUE,
                start_time TEXT,
                end_time TEXT,
                data_points INTEGER,
                feature_statistics TEXT,
                concept_signature TEXT,
                stability_score REAL
            )
        ''')
        
        # 特征统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS feature_statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                window_id TEXT,
                feature_name TEXT,
                mean_value REAL,
                std_value REAL,
                min_value REAL,
                max_value REAL,
                skewness REAL,
                kurtosis REAL,
                timestamp TEXT
            )
        ''')
        
        # 模型性能表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS detection_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                detection_method TEXT,
                accuracy REAL,
                precision REAL,
                recall REAL,
                f1_score REAL,
                detection_delay REAL,
                timestamp TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("概念漂移检测数据库初始化完成")
    
    async def detect_concept_drift(
        self,
        new_data: pd.DataFrame,
        feature_columns: List[str],
        target_column: Optional[str] = None,
        detection_methods: List[DriftDetectionMethod] = None
    ) -> DriftDetectionResult:
        """检测概念漂移"""
        
        detection_id = f"drift_detect_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            logger.info(f"🔍 开始概念漂移检测: {detection_id}")
            logger.info(f"  数据点数: {len(new_data)}")
            logger.info(f"  特征数量: {len(feature_columns)}")
            
            # 默认使用所有检测方法
            if detection_methods is None:
                detection_methods = [
                    DriftDetectionMethod.STATISTICAL_TEST,
                    DriftDetectionMethod.ISOLATION_FOREST,
                    DriftDetectionMethod.ADWIN,
                    DriftDetectionMethod.PAGE_HINKLEY
                ]
            
            # 第一步：数据预处理
            processed_data = await self._preprocess_drift_data(new_data, feature_columns)
            
            # 第二步：更新概念窗口
            current_window = await self._update_concept_window(processed_data, feature_columns)
            
            # 第三步：执行多种漂移检测
            detection_results = {}
            for method in detection_methods:
                try:
                    result = await self._execute_drift_detection(
                        processed_data, feature_columns, method, current_window
                    )
                    detection_results[method] = result
                    logger.info(f"    {method.value} 检测完成: {'漂移' if result['drift_detected'] else '无漂移'}")
                except Exception as e:
                    logger.error(f"    {method.value} 检测失败: {e}")
                    detection_results[method] = {
                        "drift_detected": False,
                        "confidence": 0.0,
                        "details": {"error": str(e)}
                    }
            
            # 第四步：集成检测结果
            integrated_result = await self._integrate_detection_results(
                detection_results, detection_methods
            )
            
            # 第五步：分析漂移特征
            drift_analysis = await self._analyze_drift_characteristics(
                processed_data, feature_columns, integrated_result
            )
            
            # 第六步：生成适应建议
            adaptation_recommendations = await self._generate_adaptation_recommendations(
                integrated_result, drift_analysis
            )
            
            # 构建检测结果
            result = DriftDetectionResult(
                detection_id=detection_id,
                timestamp=datetime.now(),
                drift_detected=integrated_result["drift_detected"],
                drift_type=drift_analysis.get("drift_type"),
                drift_severity=drift_analysis.get("drift_severity", DriftSeverity.LOW),
                confidence_score=integrated_result["confidence_score"],
                detection_method=DriftDetectionMethod.ENSEMBLE_METHOD,
                affected_features=drift_analysis.get("affected_features", []),
                drift_magnitude=drift_analysis.get("drift_magnitude", 0.0),
                statistical_significance=integrated_result.get("statistical_significance", 0.0),
                adaptation_required=integrated_result["confidence_score"] > self.detection_config["adaptation_threshold"],
                recommended_actions=adaptation_recommendations
            )
            
            # 保存检测结果
            await self._save_drift_detection_result(result)
            
            # 更新性能统计
            self._update_performance_stats(result)
            
            # 添加到历史记录
            self.drift_history.append(result)
            
            logger.info(f"  概念漂移检测完成: {detection_id}")
            logger.info(f"  漂移检测: {'是' if result.drift_detected else '否'}")
            logger.info(f"  置信度: {result.confidence_score:.2%}")
            logger.info(f"  漂移类型: {result.drift_type.value if result.drift_type else '无'}")
            logger.info(f"  严重程度: {result.drift_severity.value}")
            logger.info(f"  受影响特征: {len(result.affected_features)}个")
            
            return result
            
        except Exception as e:
            logger.error(f"  概念漂移检测失败: {detection_id} - {e}")
            raise
    
    async def _preprocess_drift_data(
        self,
        data: pd.DataFrame,
        feature_columns: List[str]
    ) -> pd.DataFrame:
        """预处理漂移检测数据"""
        
        # 选择特征列
        processed_data = data[feature_columns].copy()
        
        # 处理缺失值
        processed_data = processed_data.fillna(processed_data.mean())
        
        # 移除异常值（3倍标准差）
        for col in feature_columns:
            if processed_data[col].dtype in ['float64', 'int64']:
                mean = processed_data[col].mean()
                std = processed_data[col].std()
                processed_data = processed_data[
                    abs(processed_data[col] - mean) <= 3 * std
                ]
        
        # 标准化
        if len(self.concept_windows) > 0:
            # 使用历史数据的标准化参数
            processed_data[feature_columns] = self.scaler.transform(processed_data[feature_columns])
        else:
            # 首次标准化
            processed_data[feature_columns] = self.scaler.fit_transform(processed_data[feature_columns])
        
        return processed_data
    
    async def _update_concept_window(
        self,
        data: pd.DataFrame,
        feature_columns: List[str]
    ) -> ConceptWindow:
        """更新概念窗口"""
        
        window_id = f"window_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 计算特征统计
        feature_stats = {}
        for col in feature_columns:
            if col in data.columns:
                stats_dict = {
                    "mean": float(data[col].mean()),
                    "std": float(data[col].std()),
                    "min": float(data[col].min()),
                    "max": float(data[col].max()),
                    "skewness": float(data[col].skew()),
                    "kurtosis": float(data[col].kurtosis())
                }
                feature_stats[col] = stats_dict
        
        # 计算概念签名（PCA降维后的特征向量）
        if len(data) >= 10:  # 确保有足够的数据点
            try:
                if hasattr(self.pca, 'components_'):
                    concept_signature = self.pca.transform(data[feature_columns]).mean(axis=0)
                else:
                    concept_signature = self.pca.fit_transform(data[feature_columns]).mean(axis=0)
            except:
                concept_signature = data[feature_columns].mean().values
        else:
            concept_signature = data[feature_columns].mean().values
        
        # 计算稳定性分数
        stability_score = self._calculate_stability_score(data, feature_columns)
        
        # 创建概念窗口
        window = ConceptWindow(
            window_id=window_id,
            start_time=datetime.now() - timedelta(minutes=len(data)),
            end_time=datetime.now(),
            data_points=len(data),
            feature_statistics=feature_stats,
            concept_signature=concept_signature,
            stability_score=stability_score
        )
        
        # 更新窗口列表
        self.concept_windows.append(window)
        if len(self.concept_windows) > self.detection_config["max_windows"]:
            self.concept_windows.pop(0)
        
        self.current_concept = window
        
        # 保存窗口
        await self._save_concept_window(window)
        
        return window
    
    def _calculate_stability_score(self, data: pd.DataFrame, feature_columns: List[str]) -> float:
        """计算稳定性分数"""

        if len(data) < 10:
            return self._calculate_real_score()  # 移除await，改为同步调用

        # 计算特征的变异系数
        stability_scores = []
        for col in feature_columns:
            if col in data.columns and data[col].std() > 0:
                cv = data[col].std() / abs(data[col].mean()) if data[col].mean() != 0 else 1.0
                stability_score = 1.0 / (1.0 + cv)  # 变异系数越小，稳定性越高
                stability_scores.append(stability_score)

        return np.mean(stability_scores) if stability_scores else 0.5

    def _calculate_real_score(self) -> float:
        """计算真实稳定性分数"""
        # 基于历史窗口计算稳定性
        if len(self.concept_windows) < 2:
            return 0.5

        # 计算最近几个窗口的稳定性变化
        recent_scores = [window.stability_score for window in self.concept_windows[-3:]]
        if recent_scores:
            return np.mean(recent_scores)

        return 0.5
    
    async def _execute_drift_detection(
        self,
        data: pd.DataFrame,
        feature_columns: List[str],
        method: DriftDetectionMethod,
        current_window: ConceptWindow
    ) -> Dict[str, Any]:
        """执行特定的漂移检测方法"""
        
        if method == DriftDetectionMethod.STATISTICAL_TEST:
            return await self._statistical_test_detection(data, feature_columns, current_window)
        elif method == DriftDetectionMethod.ISOLATION_FOREST:
            return await self._isolation_forest_detection(data, feature_columns, current_window)
        elif method == DriftDetectionMethod.ADWIN:
            return await self._adwin_detection(data, feature_columns, current_window)
        elif method == DriftDetectionMethod.PAGE_HINKLEY:
            return await self._page_hinkley_detection(data, feature_columns, current_window)
        elif method == DriftDetectionMethod.KSWIN:
            return await self._kswin_detection(data, feature_columns, current_window)
        else:
            return {"drift_detected": False, "confidence": 0.0, "details": {}}
    
    async def _statistical_test_detection(
        self,
        data: pd.DataFrame,
        feature_columns: List[str],
        current_window: ConceptWindow
    ) -> Dict[str, Any]:
        """基于统计检验的漂移检测"""
        
        if len(self.concept_windows) < 2:
            return {"drift_detected": False, "confidence": 0.0, "details": {"reason": "insufficient_history"}}
        
        # 获取参考窗口（前一个窗口）
        reference_window = self.concept_windows[-2]
        
        # 执行多种统计检验
        test_results = []
        
        for col in feature_columns:
            if col in data.columns:
                current_values = data[col].values
                reference_stats = reference_window.feature_statistics.get(col, {})
                
                # Kolmogorov-Smirnov检验
                if len(current_values) >= 10:
                    # 生成参考分布样本
                    ref_mean = reference_stats.get("mean", 0)
                    ref_std = reference_stats.get("std", 1)
                    reference_sample = (ref_mean + ref_std, len(current_values * 0.5))
                    
                    ks_stat, ks_p_value = stats.ks_2samp(current_values, reference_sample)
                    
                    # Mann-Whitney U检验
                    mw_stat, mw_p_value = stats.mannwhitneyu(
                        current_values, reference_sample, alternative='two-sided'
                    )
                    
                    test_results.append({
                        "feature": col,
                        "ks_statistic": ks_stat,
                        "ks_p_value": ks_p_value,
                        "mw_statistic": mw_stat,
                        "mw_p_value": mw_p_value,
                        "drift_detected": ks_p_value < self.detection_config["significance_level"]
                    })
        
        # 综合判断
        if test_results:
            drift_count = sum(1 for result in test_results if result["drift_detected"])
            drift_ratio = drift_count / len(test_results)
            
            # 计算平均p值
            avg_p_value = np.mean([result["ks_p_value"] for result in test_results])
            confidence = 1.0 - avg_p_value
            
            drift_detected = drift_ratio > 0.5  # 超过一半特征检测到漂移
            
            return {
                "drift_detected": drift_detected,
                "confidence": confidence,
                "details": {
                    "test_results": test_results,
                    "drift_ratio": drift_ratio,
                    "avg_p_value": avg_p_value
                }
            }
        
        return {"drift_detected": False, "confidence": 0.0, "details": {"reason": "no_valid_tests"}}

    def _init_adwin_detector(self):
        """初始化ADWIN检测器"""
        return {"window_size": 100, "delta": 0.002}

    def _init_ddm_detector(self):
        """初始化DDM检测器"""
        return {"warning_level": 2.0, "drift_level": 3.0}

    def _init_eddm_detector(self):
        """初始化EDDM检测器"""
        return {"alpha": 0.95, "beta": 0.9}

    def _init_page_hinkley_detector(self):
        """初始化Page-Hinkley检测器"""
        return {"threshold": 50, "alpha": 0.9999}

    def _init_kswin_detector(self):
        """初始化KSWIN检测器"""
        return {"window_size": 100, "stat_size": 30}

    async def _isolation_forest_detection(self, data: pd.DataFrame, feature_columns: List[str], current_window: ConceptWindow) -> Dict[str, Any]:
        """基于孤立森林的漂移检测"""

        if len(self.concept_windows) < 2:
            return {"drift_detected": False, "confidence": 0.0, "details": {"reason": "insufficient_history"}}

        try:
            # 训练孤立森林
            reference_data = []
            for window in self.concept_windows[:-1]:  # 使用历史窗口
                ref_data = (0 + 1, (100, len(feature_columns * 0.5)))
                reference_data.append(ref_data)

            if reference_data:
                reference_data = np.vstack(reference_data)
                self.isolation_forest.fit(reference_data)

                # 检测当前数据
                current_data = data[feature_columns].values
                anomaly_scores = self.isolation_forest.decision_function(current_data)
                outlier_fraction = np.sum(anomaly_scores < 0) / len(anomaly_scores)

                drift_detected = outlier_fraction > 0.1  # 10%异常阈值
                confidence = outlier_fraction

                return {
                    "drift_detected": drift_detected,
                    "confidence": confidence,
                    "details": {
                        "outlier_fraction": outlier_fraction,
                        "anomaly_scores_mean": float(np.mean(anomaly_scores))
                    }
                }
        except Exception as e:
            logger.error(f"孤立森林检测失败: {e}")

        return {"drift_detected": False, "confidence": 0.0, "details": {"reason": "detection_failed"}}

    async def _adwin_detection(self, data: pd.DataFrame, feature_columns: List[str], current_window: ConceptWindow) -> Dict[str, Any]:
        """ADWIN漂移检测"""

        if len(self.concept_windows) < 2:
            return {"drift_detected": False, "confidence": 0.0, "details": {"reason": "insufficient_history"}}

        # 计算特征均值变化
        reference_window = self.concept_windows[-2]
        drift_scores = []

        for col in feature_columns:
            if col in data.columns:
                current_mean = data[col].mean()
                ref_mean = reference_window.feature_statistics.get(col, {}).get("mean", current_mean)

                # 计算变化幅度
                change_ratio = abs(current_mean - ref_mean) / (abs(ref_mean) + 1e-8)
                drift_scores.append(change_ratio)

        if drift_scores:
            avg_drift_score = np.mean(drift_scores)
            drift_detected = avg_drift_score > 0.1

            return {
                "drift_detected": drift_detected,
                "confidence": min(1.0, avg_drift_score),
                "details": {"drift_scores": drift_scores, "avg_drift_score": avg_drift_score}
            }

        return {"drift_detected": False, "confidence": 0.0, "details": {"reason": "no_valid_features"}}

    async def _page_hinkley_detection(self, data: pd.DataFrame, feature_columns: List[str], current_window: ConceptWindow) -> Dict[str, Any]:
        """Page-Hinkley检测"""

        if len(self.concept_windows) < 2:
            return {"drift_detected": False, "confidence": 0.0, "details": {"reason": "insufficient_history"}}

        reference_window = self.concept_windows[-2]
        ph_statistics = []

        for col in feature_columns:
            if col in data.columns:
                current_values = data[col].values
                ref_mean = reference_window.feature_statistics.get(col, {}).get("mean", 0)

                # 计算累积和
                cumsum = np.cumsum(current_values - ref_mean)
                ph_stat = np.max(cumsum) - np.min(cumsum)
                ph_statistics.append(ph_stat)

        if ph_statistics:
            max_ph_stat = max(ph_statistics)
            threshold = self.detectors[DriftDetectionMethod.PAGE_HINKLEY]["threshold"]
            drift_detected = max_ph_stat > threshold

            return {
                "drift_detected": drift_detected,
                "confidence": min(1.0, max_ph_stat / threshold),
                "details": {"ph_statistics": ph_statistics, "max_ph_stat": max_ph_stat}
            }

        return {"drift_detected": False, "confidence": 0.0, "details": {"reason": "no_valid_features"}}

    async def _kswin_detection(self, data: pd.DataFrame, feature_columns: List[str], current_window: ConceptWindow) -> Dict[str, Any]:
        """KSWIN检测"""

        if len(self.concept_windows) < 2:
            return {"drift_detected": False, "confidence": 0.0, "details": {"reason": "insufficient_history"}}

        reference_window = self.concept_windows[-2]
        ks_statistics = []

        for col in feature_columns:
            if col in data.columns and len(data[col]) >= 10:
                current_values = data[col].values

                # 生成参考分布样本
                ref_stats = reference_window.feature_statistics.get(col, {})
                ref_mean = ref_stats.get("mean", 0)
                ref_std = ref_stats.get("std", 1)
                reference_sample = (ref_mean + ref_std, len(current_values * 0.5))

                # KS检验
                ks_stat, ks_p_value = stats.ks_2samp(current_values, reference_sample)
                ks_statistics.append({"stat": ks_stat, "p_value": ks_p_value})

        if ks_statistics:
            avg_ks_stat = np.mean([ks["stat"] for ks in ks_statistics])
            min_p_value = min([ks["p_value"] for ks in ks_statistics])

            drift_detected = min_p_value < self.detection_config["significance_level"]
            confidence = 1.0 - min_p_value

            return {
                "drift_detected": drift_detected,
                "confidence": confidence,
                "details": {"ks_statistics": ks_statistics, "avg_ks_stat": avg_ks_stat}
            }

        return {"drift_detected": False, "confidence": 0.0, "details": {"reason": "no_valid_features"}}

    async def _save_concept_window(self, window: ConceptWindow):
        """保存概念窗口"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO concept_windows
            (window_id, start_time, end_time, data_points, feature_statistics, concept_signature, stability_score)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            window.window_id,
            window.start_time.isoformat(),
            window.end_time.isoformat(),
            window.data_points,
            json.dumps(window.feature_statistics),
            json.dumps(window.concept_signature.tolist()),
            window.stability_score
        ))

        conn.commit()
        conn.close()

    async def _save_drift_detection_result(self, result: DriftDetectionResult):
        """保存漂移检测结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO drift_detections
            (detection_id, timestamp, drift_detected, drift_type, drift_severity, confidence_score,
             detection_method, affected_features, drift_magnitude, statistical_significance,
             adaptation_required, recommended_actions)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            result.detection_id,
            result.timestamp.isoformat(),
            result.drift_detected,
            result.drift_type.value if result.drift_type else None,
            result.drift_severity.value,
            result.confidence_score,
            result.detection_method.value,
            json.dumps(result.affected_features),
            result.drift_magnitude,
            result.statistical_significance,
            result.adaptation_required,
            json.dumps(result.recommended_actions)
        ))

        conn.commit()
        conn.close()

    def _update_performance_stats(self, result: DriftDetectionResult):
        """更新性能统计"""
        self.performance_stats["total_detections"] += 1

        if result.drift_detected:
            self.performance_stats["true_positives"] += 1
        else:
            self.performance_stats["true_negatives"] += 1

    async def _integrate_detection_results(self, detection_results: Dict, detection_methods: List[DriftDetectionMethod]) -> Dict[str, Any]:
        """集成检测结果"""

        # 计算加权平均置信度
        total_weight = 0
        weighted_confidence = 0
        drift_votes = 0

        method_weights = {
            DriftDetectionMethod.STATISTICAL_TEST: 0.3,
            DriftDetectionMethod.ISOLATION_FOREST: 0.25,
            DriftDetectionMethod.ADWIN: 0.2,
            DriftDetectionMethod.PAGE_HINKLEY: 0.15,
            DriftDetectionMethod.KSWIN: 0.1
        }

        for method, result in detection_results.items():
            weight = method_weights.get(method, 0.1)
            total_weight += weight
            weighted_confidence += weight * result.get("confidence", 0.0)

            if result.get("drift_detected", False):
                drift_votes += weight

        # 集成决策
        final_confidence = weighted_confidence / total_weight if total_weight > 0 else 0.0
        drift_detected = drift_votes > (total_weight * self.detection_config["ensemble_threshold"])

        return {
            "drift_detected": drift_detected,
            "confidence_score": final_confidence,
            "statistical_significance": final_confidence,
            "method_results": detection_results
        }

    async def _analyze_drift_characteristics(self, data: pd.DataFrame, feature_columns: List[str], integrated_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析漂移特征"""

        analysis = {

            "drift_severity": DriftSeverity.MEDIUM,

            "drift_magnitude": integrated_result.get("confidence_score", 0.0)
        }

        # 根据置信度确定严重程度
        confidence = integrated_result.get("confidence_score", 0.0)
        if confidence > 0.8:
            analysis["drift_severity"] = DriftSeverity.CRITICAL
        elif confidence > 0.6:
            analysis["drift_severity"] = DriftSeverity.HIGH
        elif confidence > 0.4:
            analysis["drift_severity"] = DriftSeverity.MEDIUM
        else:
            analysis["drift_severity"] = DriftSeverity.LOW

        return analysis

    async def _generate_adaptation_recommendations(self, integrated_result: Dict[str, Any], drift_analysis: Dict[str, Any]) -> List[str]:
        """生成适应建议"""

        recommendations = []

        if integrated_result.get("drift_detected", False):
            severity = drift_analysis.get("drift_severity", DriftSeverity.LOW)

            if severity == DriftSeverity.CRITICAL:
                recommendations.extend([
                    "立即停止当前模型使用",
                    "启动紧急模型重训练",
                    "增加数据监控频率",
                    "通知风险管理团队"
                ])
            elif severity == DriftSeverity.HIGH:
                recommendations.extend([
                    "计划模型重训练",
                    "增加模型验证",
                    "调整模型参数",
                    "加强数据质量检查"
                ])
            elif severity == DriftSeverity.MEDIUM:
                recommendations.extend([
                    "监控模型性能",
                    "准备数据更新",
                    "考虑模型调优"
                ])
            else:
                recommendations.extend([
                    "继续监控",
                    "记录变化趋势"
                ])

        return recommendations
