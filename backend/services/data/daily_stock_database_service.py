from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
当日股票数据库服务
瑶光星负责数据收集和更新，其他星直接查询
实现统一的股票数据管理，避免重复调用外部API
"""

import asyncio
import sqlite3
import logging
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import aiofiles
import aiosqlite

logger = logging.getLogger(__name__)

class DailyStockDatabaseService:
    """当日股票数据库服务"""
    
    def __init__(self):
        self.service_name = "DailyStockDatabaseService"
        self.version = "1.0.0"
        
        # 数据库路径
        self.db_path = "data/daily_stock_data.db"
        self.historical_db_path = get_database_path("stock_database")
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def _init_database(self):
        """初始化当日股票数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建当日股票数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS daily_stock_data (
                        stock_code TEXT PRIMARY KEY,
                        stock_name TEXT,
                        current_price REAL,
                        open_price REAL,
                        high_price REAL,
                        low_price REAL,
                        volume INTEGER,
                        turnover_rate REAL,
                        pe_ratio REAL,
                        pb_ratio REAL,
                        market_cap REAL,
                        change_percent REAL,
                        change_amount REAL,
                        industry TEXT,
                        update_time TEXT,
                        data_source TEXT
                    )
                ''')
                
                # 创建分钟级数据表（实盘模式用）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS minute_stock_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_code TEXT,
                        price REAL,
                        volume INTEGER,
                        timestamp TEXT,
                        data_source TEXT,
                        FOREIGN KEY (stock_code) REFERENCES daily_stock_data (stock_code)
                    )
                ''')
                
                # 创建开阳星选股记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS kaiyang_selections (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_code TEXT,
                        selection_date TEXT,
                        selection_reason TEXT,
                        score REAL,
                        is_active INTEGER DEFAULT 1
                    )
                ''')
                
                conn.commit()
                logger.info("✅ 当日股票数据库初始化完成")
                
        except Exception as e:
            logger.error(f"❌ 当日股票数据库初始化失败: {e}")
    
    async def update_daily_stock_data(self, stock_code: str, stock_data: Dict[str, Any]) -> bool:
        """更新当日股票数据（瑶光星调用）"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute('''
                    INSERT OR REPLACE INTO daily_stock_data 
                    (stock_code, stock_name, current_price, open_price, high_price, low_price,
                     volume, turnover_rate, pe_ratio, pb_ratio, market_cap, change_percent,
                     change_amount, industry, update_time, data_source)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    stock_code,
                    stock_data.get('stock_name', ''),
                    stock_data.get('current_price', 0.0),
                    stock_data.get('open_price', 0.0),
                    stock_data.get('high_price', 0.0),
                    stock_data.get('low_price', 0.0),
                    stock_data.get('volume', 0),
                    stock_data.get('turnover_rate', 0.0),
                    stock_data.get('pe_ratio', 0.0),
                    stock_data.get('pb_ratio', 0.0),
                    stock_data.get('market_cap', 0.0),
                    stock_data.get('change_percent', 0.0),
                    stock_data.get('change_amount', 0.0),
                    stock_data.get('industry', ''),
                    datetime.now().isoformat(),
                    stock_data.get('data_source', 'yaoguang_collection')
                ))
                await conn.commit()
                
            logger.info(f"✅ 更新当日股票数据: {stock_code}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新当日股票数据失败 {stock_code}: {e}")
            return False
    
    async def add_minute_data(self, stock_code: str, price: float, volume: int, data_source: str = "yaoguang_realtime") -> bool:
        """添加分钟级数据（实盘模式用）"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute('''
                    INSERT INTO minute_stock_data 
                    (stock_code, price, volume, timestamp, data_source)
                    VALUES (?, ?, ?, ?, ?)
                ''', (stock_code, price, volume, datetime.now().isoformat(), data_source))
                await conn.commit()
                
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加分钟级数据失败 {stock_code}: {e}")
            return False
    
    async def get_stock_data(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取股票当日数据（其他星调用）"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                conn.row_factory = aiosqlite.Row
                cursor = await conn.execute(
                    'SELECT * FROM daily_stock_data WHERE stock_code = ?',
                    (stock_code,)
                )
                row = await cursor.fetchone()
                
                if row:
                    return dict(row)
                else:
                    # 如果当日数据不存在，从历史库获取基础信息
                    return await self._get_from_historical_db(stock_code)
                    
        except Exception as e:
            logger.error(f"❌ 获取股票数据失败 {stock_code}: {e}")
            return None
    
    async def get_multiple_stocks_data(self, stock_codes: List[str]) -> Dict[str, Any]:
        """批量获取股票数据"""
        results = {}
        
        for stock_code in stock_codes:
            data = await self.get_stock_data(stock_code)
            if data:
                results[stock_code] = data
        
        return results
    
    async def get_minute_data(self, stock_code: str, minutes: int = 60) -> List[Dict[str, Any]]:
        """获取最近N分钟的数据"""
        try:
            cutoff_time = (datetime.now() - timedelta(minutes=minutes)).isoformat()
            
            async with aiosqlite.connect(self.db_path) as conn:
                conn.row_factory = aiosqlite.Row
                cursor = await conn.execute('''
                    SELECT * FROM minute_stock_data 
                    WHERE stock_code = ? AND timestamp > ?
                    ORDER BY timestamp DESC
                ''', (stock_code, cutoff_time))
                
                rows = await cursor.fetchall()
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"❌ 获取分钟级数据失败 {stock_code}: {e}")
            return []
    
    async def record_kaiyang_selection(self, stock_code: str, reason: str, score: float) -> bool:
        """记录开阳星选股结果"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute('''
                    INSERT INTO kaiyang_selections 
                    (stock_code, selection_date, selection_reason, score)
                    VALUES (?, ?, ?, ?)
                ''', (stock_code, datetime.now().date().isoformat(), reason, score))
                await conn.commit()
                
            logger.info(f"✅ 记录开阳星选股: {stock_code}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 记录开阳星选股失败 {stock_code}: {e}")
            return False
    
    async def get_kaiyang_selected_stocks(self, date: str = None) -> List[str]:
        """获取开阳星选择的股票"""
        try:
            if not date:
                date = datetime.now().date().isoformat()
            
            async with aiosqlite.connect(self.db_path) as conn:
                cursor = await conn.execute('''
                    SELECT DISTINCT stock_code FROM kaiyang_selections 
                    WHERE selection_date = ? AND is_active = 1
                ''', (date,))
                
                rows = await cursor.fetchall()
                return [row[0] for row in rows]
                
        except Exception as e:
            logger.error(f"❌ 获取开阳星选股失败: {e}")
            return []
    
    async def _get_from_historical_db(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """从历史数据库获取基础信息"""
        try:
            if not os.path.exists(self.historical_db_path):
                logger.warning(f"历史数据库不存在: {self.historical_db_path}")
                return None
            
            async with aiosqlite.connect(self.historical_db_path) as conn:
                conn.row_factory = aiosqlite.Row
                
                # 尝试从不同的表获取数据
                tables = ['stock_info', 'stocks', 'stock_basic']
                
                for table in tables:
                    try:
                        cursor = await conn.execute(f'SELECT * FROM {table} WHERE stock_code = ? LIMIT 1', (stock_code,))
                        row = await cursor.fetchone()
                        
                        if row:
                            data = dict(row)
                            # 标准化字段名
                            return {
                                'stock_code': stock_code,
                                'stock_name': data.get('stock_name', data.get('name', '')),
                                'current_price': 0.0,  # 当日价格需要实时获取
                                'industry': data.get('industry', ''),
                                'market_cap': data.get('market_cap', 0.0),
                                'pe_ratio': data.get('pe_ratio', 0.0),
                                'pb_ratio': data.get('pb_ratio', 0.0),
                                'data_source': 'historical_db',
                                'update_time': datetime.now().isoformat()
                            }
                    except Exception:
                        continue
                
                # 清理股票代码再次尝试
                clean_code = stock_code.replace('.XSHE', '').replace('.XSHG', '')

                # 尝试用清理后的代码查询
                cursor.execute("""
                    SELECT * FROM daily_data
                    WHERE stock_code = ?
                    ORDER BY trade_date DESC
                    LIMIT 1
                """, (clean_code,))

                row = cursor.fetchone()
                if row:
                    logger.info(f"✅ 找到股票信息: {clean_code}")
                    return dict(row)

                logger.debug(f"数据库中未找到股票信息: {stock_code} (已尝试: {clean_code})")
                return None
                
        except Exception as e:
            logger.error(f"❌ 从历史数据库获取数据失败 {stock_code}: {e}")
            return None
    
    async def cleanup_old_data(self, days: int = 7):
        """清理旧数据"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            async with aiosqlite.connect(self.db_path) as conn:
                # 清理旧的分钟级数据
                await conn.execute('DELETE FROM minute_stock_data WHERE timestamp < ?', (cutoff_date,))
                
                # 清理旧的选股记录
                cutoff_date_only = (datetime.now() - timedelta(days=days)).date().isoformat()
                await conn.execute('DELETE FROM kaiyang_selections WHERE selection_date < ?', (cutoff_date_only,))
                
                await conn.commit()
                
            logger.info(f"✅ 清理{days}天前的旧数据")
            
        except Exception as e:
            logger.error(f"❌ 清理旧数据失败: {e}")
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                # 统计当日数据
                cursor = await conn.execute('SELECT COUNT(*) FROM daily_stock_data')
                daily_count = (await cursor.fetchone())[0]
                
                # 统计分钟级数据
                cursor = await conn.execute('SELECT COUNT(*) FROM minute_stock_data')
                minute_count = (await cursor.fetchone())[0]
                
                # 统计选股记录
                cursor = await conn.execute('SELECT COUNT(*) FROM kaiyang_selections WHERE is_active = 1')
                selection_count = (await cursor.fetchone())[0]
                
                return {
                    'daily_stocks_count': daily_count,
                    'minute_data_count': minute_count,
                    'active_selections_count': selection_count,
                    'database_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                    'last_update': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ 获取数据库统计失败: {e}")
            return {}

# 全局实例
daily_stock_db_service = DailyStockDatabaseService()

__all__ = ["daily_stock_db_service", "DailyStockDatabaseService"]
