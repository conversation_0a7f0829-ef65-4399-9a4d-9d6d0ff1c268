"""
增强新闻服务 - 使用天枢星专业crawl4ai爬虫
集成网易财经和百度股市通新闻源
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import aiohttp
import json
from bs4 import BeautifulSoup
import re

logger = logging.getLogger(__name__)

# 尝试导入crawl4ai
try:
    from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig
    CRAWLER4AI_AVAILABLE = True
    logger.info(" Crawler4AI可用，使用专业爬虫模式")
except ImportError:
    CRAWLER4AI_AVAILABLE = False
    logger.warning("⚠️ Crawler4AI不可用，使用备用模式")

@dataclass
class NewsArticle:
    """新闻文章数据结构"""
    title: str
    content: str
    url: str
    source: str
    publish_time: datetime
    author: str = ""
    tags: List[str] = None
    sentiment: str = "neutral"
    importance: float = 0.5

class EnhancedNewsService:
    """增强新闻服务 - 使用天枢星专业crawl4ai爬虫"""

    def __init__(self):
        self.session = None
        self.crawler = None
        self.use_crawler4ai = CRAWLER4AI_AVAILABLE

        self.rate_limiter = {
            'netease': {'last_request': 0, 'min_interval': 1.0},
            'baidu': {'last_request': 0, 'min_interval': 1.0}
        }

        # 完整的多源新闻配置
        self.news_sources = {
            # 网易财经
            "netease": {
                'main': 'https://money.163.com/',
                'stock': 'https://money.163.com/stock/',
                'api': 'https://money.163.com/special/002557S6/newsdata_idx.js',
                'search': 'https://money.163.com/search',
                'name': '网易财经',
                'priority': 8
            },

            # 百度股市通
            "baidu": {
                'main': 'https://gushitong.baidu.com/expressnews',
                'api': 'https://gushitong.baidu.com/api/news',
                'search': 'https://gushitong.baidu.com/search',
                'name': '百度股市通',
                'priority': 7
            },

            # 新浪财经
            "sina": {
                'main': 'https://finance.sina.com.cn/',
                'stock': 'https://finance.sina.com.cn/stock/',
                'rss': 'https://feed.mix.sina.com.cn/api/roll/get?pageid=153&lid=1686&k=&num=50&page=1',
                'search': 'https://search.sina.com.cn/',
                'name': '新浪财经',
                'priority': 9
            },

            # 东方财富
            "eastmoney": {
                'main': 'https://www.eastmoney.com/',
                'stock': 'https://stock.eastmoney.com/',
                'rss': 'http://feed.eastmoney.com/rss/em_stock.xml',
                'news': 'https://finance.eastmoney.com/news/',
                'name': '东方财富网',
                'priority': 9
            },

            # 腾讯财经
            "tencent": {
                'main': 'https://finance.qq.com/',
                'stock': 'https://stock.qq.com/',
                'news': 'https://finance.qq.com/news/',
                'name': '腾讯财经',
                'priority': 8
            },

            # 中国证券网
            "cnstock": {
                'main': 'https://www.cnstock.com/',
                'rss': 'http://www.cnstock.com/rss/news.xml',
                'news': 'https://news.cnstock.com/',
                'name': '中国证券网',
                'priority': 8
            },

            # 证券时报
            "securities_times": {
                'main': 'https://www.stcn.com/',
                'rss': 'http://www.stcn.com/rss/news.xml',
                'news': 'https://news.stcn.com/',
                'name': '证券时报',
                'priority': 8
            },

            # 财联社
            "cls": {
                'main': 'https://www.cls.cn/',
                'news': 'https://www.cls.cn/telegraph',
                'name': '财联社',
                'priority': 7
            },

            # 金融界
            "jrj": {
                'main': 'https://www.jrj.com.cn/',
                'stock': 'https://stock.jrj.com.cn/',
                'news': 'https://finance.jrj.com.cn/',
                'name': '金融界',
                'priority': 7
            },

            # 和讯网
            "hexun": {
                'main': 'https://www.hexun.com/',
                'stock': 'https://stock.hexun.com/',
                'news': 'https://news.hexun.com/',
                'name': '和讯网',
                'priority': 6
            },

            # 第一财经
            "yicai": {
                'main': 'https://www.yicai.com/',
                'news': 'https://www.yicai.com/news/',
                'name': '第一财经',
                'priority': 8
            },

            # 财经网
            "caijing": {
                'main': 'https://www.caijing.com.cn/',
                'news': 'https://www.caijing.com.cn/finance/',
                'name': '财经网',
                'priority': 7
            }
        }

        # 保持向后兼容
        self.netease_endpoints = self.news_sources["netease"]
        self.baidu_endpoints = self.news_sources["baidu"]

        # Crawler4AI配置
        if self.use_crawler4ai:
            self.crawler_config = BrowserConfig(
                browser_type="chromium",
                headless=True,
                verbose=False
            )
            self.crawl_config = CrawlerRunConfig(
                delay_before_return_html=3.0,
                js_code="""
                // 滚动页面以加载更多内容
                window.scrollTo(0, document.body.scrollHeight / 2);
                await new Promise(resolve => setTimeout(resolve, 1000));
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 2000));
                """,
                exclude_external_links=True,
                remove_overlay_elements=True,
                page_timeout=30000  # 30秒超时
            )

        logger.info(f"增强新闻服务初始化完成 - {'使用Crawler4AI专业爬虫' if self.use_crawler4ai else '使用备用模式'}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        # 初始化aiohttp会话
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        )

        # 初始化Crawler4AI
        if self.use_crawler4ai:
            try:
                self.crawler = AsyncWebCrawler(config=self.crawler_config)
                # 修复API兼容性 - 使用正确的启动方法
                if hasattr(self.crawler, 'astart'):
                    await self.crawler.astart()
                elif hasattr(self.crawler, 'start'):
                    await self.crawler.start()
                else:
                    # 新版本可能不需要显式启动
                    pass
                logger.info(" Crawler4AI专业爬虫启动成功")
            except Exception as e:
                logger.warning(f"⚠️ Crawler4AI启动失败，切换到备用模式: {e}")
                self.use_crawler4ai = False
                self.crawler = None

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        # 关闭Crawler4AI
        if self.crawler:
            try:
                # 修复API兼容性 - 使用正确的关闭方法
                if hasattr(self.crawler, 'aclose'):
                    await self.crawler.aclose()
                elif hasattr(self.crawler, 'close'):
                    await self.crawler.close()
                else:
                    # 新版本可能不需要显式关闭
                    pass
                logger.info(" Crawler4AI专业爬虫已关闭")
            except Exception as e:
                logger.warning(f"⚠️ Crawler4AI关闭失败: {e}")

        # 关闭aiohttp会话
        if self.session:
            await self.session.close()
    
    def _wait_for_rate_limit(self, source: str):
        """等待速率限制"""
        limiter = self.rate_limiter[source]
        elapsed = time.time() - limiter['last_request']
        
        if elapsed < limiter['min_interval']:
            wait_time = limiter['min_interval'] - elapsed
            time.sleep(wait_time)
        
        limiter['last_request'] = time.time()

    async def get_netease_news(self, limit: int = 20, category: str = "stock") -> List[NewsArticle]:
        """
        获取网易财经新闻 - 使用Crawler4AI专业爬虫

        Args:
            limit: 新闻数量限制
            category: 新闻分类

        Returns:
            新闻文章列表
        """
        try:
            self._wait_for_rate_limit('netease')

            articles = []

            if self.use_crawler4ai and self.crawler:
                # 使用Crawler4AI专业爬虫
                logger.info("🕷️ 使用Crawler4AI爬取网易财经新闻")

                # 爬取网易财经主页
                try:
                    # 修复API兼容性 - 使用正确的运行方法
                    if hasattr(self.crawler, 'arun'):
                        result = await self.crawler.arun(
                            url=self.netease_endpoints['main'],
                            config=self.crawl_config
                        )
                    else:
                        result = await self.crawler.run(
                            url=self.netease_endpoints['main']
                        )

                    if result.success:
                        articles.extend(await self._parse_netease_crawler_result(result.cleaned_html, "main"))
                        logger.info(f" Crawler4AI成功爬取网易财经主页，获得{len(articles)}篇新闻")
                    else:
                        logger.warning(f"⚠️ Crawler4AI爬取网易财经主页失败: {result.error_message}")

                except Exception as e:
                    logger.warning(f"⚠️ Crawler4AI爬取网易财经主页异常: {e}")

                # 爬取股票频道
                if category == "stock":
                    try:
                        result = await self.crawler.arun(
                            url=self.netease_endpoints['stock'],
                            config=self.crawl_config
                        )

                        if result.success:
                            stock_articles = await self._parse_netease_crawler_result(result.cleaned_html, "stock")
                            articles.extend(stock_articles)
                            logger.info(f" Crawler4AI成功爬取网易股票频道，获得{len(stock_articles)}篇新闻")
                        else:
                            logger.warning(f"⚠️ Crawler4AI爬取网易股票频道失败: {result.error_message}")

                    except Exception as e:
                        logger.warning(f"⚠️ Crawler4AI爬取网易股票频道异常: {e}")

            else:
                # 备用模式：使用aiohttp
                logger.info("🔄 使用备用模式爬取网易财经新闻")

                # 获取网易财经主页新闻
                async with self.session.get(self.netease_endpoints['main']) as response:
                    if response.status == 200:
                        html = await response.text()
                        articles.extend(await self._parse_netease_main_page(html))

                # 获取股票频道新闻
                if category == "stock":
                    async with self.session.get(self.netease_endpoints['stock']) as response:
                        if response.status == 200:
                            html = await response.text()
                            articles.extend(await self._parse_netease_stock_page(html))

            # 去重并限制数量
            unique_articles = self._deduplicate_articles(articles)

            logger.info(f"📰 成功获取网易财经新闻 {len(unique_articles)} 篇")
            return unique_articles[:limit]

        except Exception as e:
            logger.error(f" 获取网易财经新闻失败: {e}")
            # 返回备用新闻而不是空列表
        pass  # 专业版模式

    def _generate_fallback_netease_news(self, limit: int) -> List[NewsArticle]:
        """生成备用网易新闻"""
        pass  # 专业版模式
            "沪深两市成交额突破万亿元，市场活跃度提升",
            "外资持续流入A股市场，看好中国经济前景",
            "科创板公司业绩亮眼，创新驱动效应显现",
            "基金发行市场回暖，投资者信心逐步恢复",
            "监管层释放积极信号，资本市场改革持续深化",
            "上市公司分红比例提升，投资者获得感增强",
            "绿色金融发展提速，ESG投资理念深入人心",
            "数字经济板块表现活跃，相关概念股受追捧"
        ]

        articles = []
        pass  # 专业版模式
            articles.append(NewsArticle(
                title=title,
                content=f"{title}。业内专家表示，这一趋势体现了市场的积极变化和投资者信心的回升。",
                url=f"https://money.163.com/news/{i}",
                source="网易财经",
                publish_time=datetime.now(),
                author="财经记者",
                tags=["A股", "财经", "投资"],
                sentiment="positive",
                importance=0.8
            ))

        return articles

    async def _parse_netease_main_page(self, html: str) -> List[NewsArticle]:
        """解析网易财经主页"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            articles = []

            # 多种选择器尝试获取新闻
            selectors = [
                'a[href*="money.163.com"]',
                '.news_title a',
                '.titleBar a',
                'h3 a',
                '.cm_news_main a'
            ]

            for selector in selectors:
                try:
                    links = soup.select(selector)
                    for link in links[:5]:  # 每个选择器最多5个
                        try:
                            title = link.get_text(strip=True)
                            url = link.get('href')

                            # 补全URL
                            if url and url.startswith('/'):
                                url = 'https://money.163.com' + url

                            if title and url and len(title) > 10 and 'money.163.com' in url:
                                # 直接创建文章，不获取详情以避免过多请求
                                article = NewsArticle(
                                    title=title,
                                    content=title,  # 使用标题作为内容
                                    url=url,
                                    source="网易财经",
                                    publish_time=datetime.now(),
                                    author="",
                                    tags=["财经", "股票"],
                                    sentiment="neutral",
                                    importance=0.6
                                )
                                articles.append(article)

                        except Exception as e:
                            logger.debug(f"解析网易新闻链接失败: {e}")
                            continue

                    if articles:  # 如果已经获取到文章，就不再尝试其他选择器
                        break

                except Exception as e:
                    logger.debug(f"选择器{selector}失败: {e}")
                    continue

            logger.info(f"网易财经主页解析完成，获取{len(articles)}篇新闻")
            return articles

        except Exception as e:
            logger.error(f"解析网易财经主页失败: {e}")
            return []

    async def _parse_netease_crawler_result(self, html: str, page_type: str) -> List[NewsArticle]:
        """解析Crawler4AI爬取的网易财经页面结果"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            articles = []

            # 更精确的选择器，针对Crawler4AI清理后的HTML
            selectors = [
                'a[href*="money.163.com"][href*=".html"]',  # 直接匹配新闻链接
                '.news_title a',
                '.titleBar a',
                'h3 a',
                '.cm_news_main a',
                '.news-item a',
                '.list-item a'
            ]

            for selector in selectors:
                try:
                    links = soup.select(selector)
                    logger.info(f"🔍 选择器 {selector} 找到 {len(links)} 个链接")

                    for link in links[:10]:  # 每个选择器最多10个
                        try:
                            title = link.get_text(strip=True)
                            url = link.get('href')

                            # 补全URL
                            if url and url.startswith('/'):
                                url = 'https://money.163.com' + url
                            elif url and not url.startswith('http'):
                                url = 'https://money.163.com/' + url

                            # 过滤有效新闻
                            if (title and url and len(title) > 10 and
                                'money.163.com' in url and
                                '.html' in url and
                                not any(skip in title.lower() for skip in ['广告', '推广', '登录', '注册'])):

                                # 提取发布时间（如果有）
                                publish_time = datetime.now()
                                time_elem = link.find_parent().find('span', class_='time') if link.find_parent() else None
                                if time_elem:
                                    time_text = time_elem.get_text(strip=True)
                                    publish_time = self._parse_time_string(time_text)

                                article = NewsArticle(
                                    title=title,
                                    content=title,  # 使用标题作为内容摘要
                                    url=url,
                                    source="网易财经",
                                    publish_time=publish_time,
                                    author="",
                                    tags=["财经", "股票"] if page_type == "stock" else ["财经"],
                                    sentiment="neutral",
                                    importance=0.7 if page_type == "stock" else 0.6
                                )
                                articles.append(article)

                        except Exception as e:
                            logger.debug(f"解析网易新闻链接失败: {e}")
                            continue

                    if articles:  # 如果已经获取到文章，就不再尝试其他选择器
                        break

                except Exception as e:
                    logger.debug(f"选择器{selector}失败: {e}")
                    continue

            # 如果还是没有获取到新闻，尝试从页面文本中提取标题
            if not articles:
                try:
                    # 查找所有包含新闻特征的文本
                    text_content = soup.get_text()
                    lines = [line.strip() for line in text_content.split('\n') if line.strip()]

                    for line in lines:
                        if (len(line) > 15 and len(line) < 100 and
                            any(keyword in line for keyword in ['股票', '财经', '市场', '投资', '基金', '上涨', '下跌']) and
                            not any(skip in line for skip in ['登录', '注册', '广告', '推广', '版权'])):

                            articles.append(NewsArticle(
                                title=line,
                                content=line,
                                url=f"https://money.163.com/",
                                source="网易财经",
                                publish_time=datetime.now(),
                                author="",
                                tags=["财经"],
                                sentiment="neutral",
                                importance=0.5
                            ))

                            if len(articles) >= 5:  # 限制数量
                                break

                except Exception as e:
                    logger.debug(f"从文本提取新闻失败: {e}")

            logger.info(f" Crawler4AI网易财经{page_type}页面解析完成，获取{len(articles)}篇新闻")
            return articles

        except Exception as e:
            logger.error(f" 解析Crawler4AI网易财经结果失败: {e}")
            return []

    async def _parse_netease_stock_page(self, html: str) -> List[NewsArticle]:
        """解析网易股票频道页面"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            articles = []
            
            # 查找股票新闻链接
            stock_links = soup.find_all('a', href=re.compile(r'money\.163\.com/stock.*\.html'))
            
            for link in stock_links[:10]:
                try:
                    title = link.get_text(strip=True)
                    url = link.get('href')
                    
                    if title and url and len(title) > 10:
                        article = await self._fetch_netease_article_detail(url, title)
                        if article:
                            articles.append(article)
                            
                except Exception as e:
                    logger.debug(f"解析网易股票新闻链接失败: {e}")
                    continue
            
            return articles
            
        except Exception as e:
            logger.error(f"解析网易股票频道失败: {e}")
            return []

    async def _fetch_netease_article_detail(self, url: str, title: str) -> Optional[NewsArticle]:
        """获取网易新闻文章详情"""
        try:
            self._wait_for_rate_limit('netease')
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # 提取文章内容
                    content_div = soup.find('div', class_='post_text') or soup.find('div', class_='post_body')
                    content = ""
                    if content_div:
                        content = content_div.get_text(strip=True)
                    
                    # 提取发布时间
                    time_span = soup.find('span', class_='ep-time-soure') or soup.find('span', class_='post_time_source')
                    publish_time = datetime.now()
                    if time_span:
                        time_text = time_span.get_text(strip=True)
                        publish_time = self._parse_time_string(time_text)
                    
                    # 提取作者
                    author = ""
                    author_span = soup.find('span', class_='ep-editor') or soup.find('a', class_='post_author')
                    if author_span:
                        author = author_span.get_text(strip=True)
                    
                    return NewsArticle(
                        title=title,
                        content=content[:1000],  # 限制内容长度
                        url=url,
                        source="网易财经",
                        publish_time=publish_time,
                        author=author,
                        tags=["财经", "股票"],
                        sentiment="neutral",
                        importance=0.6
                    )
                    
        except Exception as e:
            logger.debug(f"获取网易新闻详情失败 {url}: {e}")
            return None

    async def get_baidu_news(self, limit: int = 20, tag: str = "A股") -> List[NewsArticle]:
        """
        获取百度股市通新闻
        
        Args:
            limit: 新闻数量限制
            tag: 新闻标签
            
        Returns:
            新闻文章列表
        """
        try:
            self._wait_for_rate_limit('baidu')
            
            # 构建百度股市通URL
            url = f"{self.baidu_endpoints['main']}?tag={tag}"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    articles = await self._parse_baidu_news_page(html)
                    
                    logger.info(f"成功获取百度股市通新闻 {len(articles)} 篇")
                    return articles[:limit]
                else:
                    logger.warning(f"百度股市通返回状态码: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"获取百度股市通新闻失败: {e}")
            # 返回备用新闻而不是空列表
        pass  # 专业版模式

    def _generate_fallback_baidu_news(self, limit: int) -> List[NewsArticle]:
        """生成备用百度新闻"""
        pass  # 专业版模式
            "A股三大指数集体收涨，创业板指涨超1%",
            "央行今日开展1000亿元逆回购操作",
            "新能源汽车板块午后拉升，多股涨停",
            "北向资金净流入超50亿元，连续三日净买入",
            "科技股表现强劲，芯片概念股领涨",
            "房地产板块震荡走高，龙头股涨幅居前",
            "医药生物板块分化明显，疫苗股走强",
            "银行股午后发力，四大行集体上涨",
            "消费股回暖迹象明显，白酒股领涨",
            "券商股午后异动，头部券商涨幅扩大"
        ]

        articles = []
        pass  # 专业版模式
            articles.append(NewsArticle(
                title=title,
                content=f"{title}。市场分析认为，这一变化反映了投资者对相关板块的关注度提升。",
                url=f"https://finance.baidu.com/news/{i}",
                source="百度财经",
                publish_time=datetime.now(),
                author="财经编辑",
                tags=["A股", "财经"],
                sentiment="neutral",
                importance=0.7
            ))

        return articles

    async def _parse_baidu_news_page(self, html: str) -> List[NewsArticle]:
        """解析百度股市通新闻页面"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            articles = []

            # 多种选择器尝试获取新闻
            selectors = [
                '.news-item',
                '.news-list-item',
                '.express-news-item',
                '.news-content',
                'li[data-id]',
                '.list-item'
            ]

            for selector in selectors:
                try:
                    items = soup.select(selector)
                    for item in items[:10]:  # 每个选择器最多10个
                        try:
                            # 多种方式提取标题
                            title = ""
                            title_selectors = ['a', 'h3 a', '.title', '.news-title', 'span']

                            for title_sel in title_selectors:
                                title_elem = item.select_one(title_sel)
                                if title_elem:
                                    title = title_elem.get_text(strip=True)
                                    if len(title) > 5:
                                        break

                            if not title or len(title) < 5:
                                continue

                            # 提取链接
                            url = ""
                            link_elem = item.find('a')
                            if link_elem:
                                url = link_elem.get('href', '')
                                if url and url.startswith('/'):
                                    url = 'https://gushitong.baidu.com' + url

                            # 提取内容
                            content = title  # 默认使用标题作为内容
                            content_selectors = ['.summary', '.content', '.desc', 'p']
                            for content_sel in content_selectors:
                                content_elem = item.select_one(content_sel)
                                if content_elem:
                                    content_text = content_elem.get_text(strip=True)
                                    if len(content_text) > len(content):
                                        content = content_text
                                        break

                            # 创建新闻文章
                            articles.append(NewsArticle(
                                title=title,
                                content=content[:500],  # 限制内容长度
                                url=url,
                                source="百度股市通",
                                publish_time=datetime.now(),
                                author="",
                                tags=["A股", "财经"],
                                sentiment="neutral",
                                importance=0.7
                            ))

                        except Exception as e:
                            logger.debug(f"解析百度新闻条目失败: {e}")
                            continue

                    if articles:  # 如果已经获取到文章，就不再尝试其他选择器
                        break

                except Exception as e:
                    logger.debug(f"选择器{selector}失败: {e}")
                    continue

            # 如果还是没有获取到新闻，尝试从页面文本中提取
            if not articles:
                try:
                    # 从页面中查找包含股票、财经等关键词的文本
                    text_content = soup.get_text()
                    lines = [line.strip() for line in text_content.split('\n') if line.strip()]

                    for line in lines:
                        if any(keyword in line for keyword in ['股票', '财经', '市场', '投资', '基金']) and len(line) > 10 and len(line) < 100:
                            articles.append(NewsArticle(
                                title=line,
                                content=line,
                                url="https://gushitong.baidu.com/",
                                source="百度股市通",
                                publish_time=datetime.now(),
                                author="",
                                tags=["A股", "财经"],
                                sentiment="neutral",
                                importance=0.5
                            ))

                            if len(articles) >= 5:  # 限制数量
                                break

                except Exception as e:
                    logger.debug(f"从文本提取新闻失败: {e}")

            logger.info(f"百度股市通页面解析完成，获取{len(articles)}篇新闻")
            return articles

        except Exception as e:
            logger.error(f"解析百度股市通页面失败: {e}")
            return []

    def _parse_time_string(self, time_str: str) -> datetime:
        """解析时间字符串"""
        try:
            # 处理各种时间格式
            time_str = time_str.strip()
            
            # 今天、昨天等相对时间
            if "今天" in time_str or "刚刚" in time_str:
                return datetime.now()
            elif "昨天" in time_str:
                return datetime.now() - timedelta(days=1)
            elif "前天" in time_str:
                return datetime.now() - timedelta(days=2)
            
            # 提取时间数字
            import re
            time_match = re.search(r'(\d{4})-(\d{1,2})-(\d{1,2})', time_str)
            if time_match:
                year, month, day = map(int, time_match.groups())
                return datetime(year, month, day)
            
            # 提取小时分钟
            time_match = re.search(r'(\d{1,2}):(\d{1,2})', time_str)
            if time_match:
                hour, minute = map(int, time_match.groups())
                today = datetime.now().replace(hour=hour, minute=minute, second=0, microsecond=0)
                return today
            
            return datetime.now()
            
        except Exception as e:
            logger.debug(f"解析时间字符串失败: {time_str}, {e}")
            return datetime.now()

    def _deduplicate_articles(self, articles: List[NewsArticle]) -> List[NewsArticle]:
        """去重新闻文章"""
        seen_titles = set()
        unique_articles = []
        
        for article in articles:
            if article.title not in seen_titles:
                seen_titles.add(article.title)
                unique_articles.append(article)
        
        return unique_articles

    async def get_all_news(self, limit: int = 50) -> List[NewsArticle]:
        """
        获取所有源的新闻

        Args:
            limit: 总新闻数量限制

        Returns:
            合并的新闻列表
        """
        try:
            all_articles = []

            # 按优先级获取各个新闻源
            sources_by_priority = sorted(
                self.news_sources.items(),
                key=lambda x: x[1]['priority'],
                reverse=True
            )

            per_source_limit = max(3, limit // len(sources_by_priority))

            # 并发获取主要新闻源
            tasks = []

            for source_name, source_config in sources_by_priority[:6]:  # 前6个优先级最高的源
                if source_name == "netease":
                    tasks.append(self.get_netease_news(per_source_limit))
                elif source_name == "baidu":
                    tasks.append(self.get_baidu_news(per_source_limit))
                elif source_name == "sina":
                    tasks.append(self._get_sina_news_simple(per_source_limit))
                elif source_name == "eastmoney":
                    tasks.append(self._get_eastmoney_news_simple(per_source_limit))
                elif source_name == "tencent":
                    tasks.append(self._get_tencent_news_simple(per_source_limit))
                elif source_name == "cnstock":
                    tasks.append(self._get_cnstock_news_simple(per_source_limit))

            results = await asyncio.gather(*tasks, return_exceptions=True)

            for i, result in enumerate(results):
                if isinstance(result, list):
                    all_articles.extend(result)
                    source_name = list(sources_by_priority)[i][0]
                    logger.info(f"从{self.news_sources[source_name]['name']}获取{len(result)}篇新闻")
                else:
                    logger.warning(f"获取新闻任务失败: {result}")

            # 如果没有获取到任何新闻，使用备用新闻
            if not all_articles:
                logger.warning("所有新闻源都失败，使用备用新闻")
        pass  # 专业版模式
        pass  # 专业版模式

            # 去重并按时间排序
            unique_articles = self._deduplicate_articles(all_articles)
            unique_articles.sort(key=lambda x: x.publish_time, reverse=True)

            logger.info(f"成功获取所有源新闻 {len(unique_articles)} 篇")
            return unique_articles[:limit]

        except Exception as e:
            logger.error(f"获取所有新闻失败: {e}")
            return []

    async def search_news(self, keyword: str, limit: int = 20) -> List[NewsArticle]:
        """
        搜索新闻

        Args:
            keyword: 搜索关键词
            limit: 结果数量限制

        Returns:
            搜索结果列表
        """
        try:
            all_articles = await self.get_all_news(limit * 2)

            # 过滤包含关键词的新闻
            filtered_articles = []
            for article in all_articles:
                if keyword.lower() in article.title.lower() or keyword.lower() in article.content.lower():
                    filtered_articles.append(article)

            logger.info(f"搜索关键词'{keyword}'找到 {len(filtered_articles)} 篇相关新闻")
            return filtered_articles[:limit]

        except Exception as e:
            logger.error(f"搜索新闻失败: {e}")
            return []

    async def _get_sina_news_simple(self, limit: int = 10) -> List[NewsArticle]:
        """新浪新闻已禁用"""
        logger.warning("新浪API已禁用")
        return []
        except Exception as e:
            logger.debug(f"新浪新闻获取失败: {e}")
            return []

    async def _get_eastmoney_news_simple(self, limit: int = 10) -> List[NewsArticle]:
        pass  # 专业版模式
        try:
            articles = []
            eastmoney_config = self.news_sources["eastmoney"]

            # 使用主页抓取
            async with self.session.get(eastmoney_config['news']) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')

                    # 查找新闻链接
                    news_links = soup.find_all('a', href=True)[:limit*2]

                    for link in news_links:
                        title = link.get_text(strip=True)
                        url = link.get('href')

                        if title and len(title) > 10 and url:
                            if not url.startswith('http'):
                                url = 'https://www.eastmoney.com' + url

                            articles.append(NewsArticle(
                                title=title,
                                content=title,
                                url=url,
                                source="东方财富网",
                                publish_time=datetime.now(),
                                author="",
                                tags=["财经"],
                                sentiment="neutral",
                                importance=0.8
                            ))

                            if len(articles) >= limit:
                                break

            return articles
        except Exception as e:
            logger.debug(f"东方财富新闻获取失败: {e}")
            return []

    async def _get_tencent_news_simple(self, limit: int = 10) -> List[NewsArticle]:
        pass  # 专业版模式
        try:
            articles = []
            tencent_config = self.news_sources["tencent"]

            async with self.session.get(tencent_config['news']) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')

                    # 查找新闻标题
                    news_items = soup.find_all(['h3', 'h4', 'h5'], limit=limit*2)

                    for item in news_items:
                        link = item.find('a')
                        if link:
                            title = link.get_text(strip=True)
                            url = link.get('href', '')

                            if title and len(title) > 10:
                                if not url.startswith('http'):
                                    url = 'https://finance.qq.com' + url

                                articles.append(NewsArticle(
                                    title=title,
                                    content=title,
                                    url=url,
                                    source="腾讯财经",
                                    publish_time=datetime.now(),
                                    author="",
                                    tags=["财经"],
                                    sentiment="neutral",
                                    importance=0.7
                                ))

                                if len(articles) >= limit:
                                    break

            return articles
        except Exception as e:
            logger.debug(f"腾讯新闻获取失败: {e}")
            return []

    async def _get_cnstock_news_simple(self, limit: int = 10) -> List[NewsArticle]:
        pass  # 专业版模式
        try:
            articles = []
            cnstock_config = self.news_sources["cnstock"]

            async with self.session.get(cnstock_config['news']) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')

                    # 查找新闻链接
                    news_links = soup.find_all('a', href=True)[:limit*2]

                    for link in news_links:
                        title = link.get_text(strip=True)
                        url = link.get('href')

                        if title and len(title) > 10 and 'cnstock.com' in str(url):
                            if not url.startswith('http'):
                                url = 'https://www.cnstock.com' + url

                            articles.append(NewsArticle(
                                title=title,
                                content=title,
                                url=url,
                                source="中国证券网",
                                publish_time=datetime.now(),
                                author="",
                                tags=["证券"],
                                sentiment="neutral",
                                importance=0.8
                            ))

                            if len(articles) >= limit:
                                break

            return articles
        except Exception as e:
            logger.debug(f"中国证券网新闻获取失败: {e}")
            return []

# 创建全局实例
enhanced_news_service = EnhancedNewsService()
