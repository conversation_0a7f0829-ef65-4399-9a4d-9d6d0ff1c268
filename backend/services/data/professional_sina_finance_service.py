#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业版新浪财经数据服务
修复新浪API问题，提供稳定的数据接口
"""

import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import re
import time
from urllib.parse import quote

# 导入股票代码转换器
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from shared.utils.stock_code_converter import stock_converter

logger = logging.getLogger(__name__)

class ProfessionalSinaFinanceService:
    """专业版新浪财经数据服务"""
    
    def __init__(self):
        """初始化服务"""
        self.service_name = "ProfessionalSinaFinanceService"
        self.version = "professional_v2.0"
        self.session = None
        self.rate_limit_delay = 0.5  # 0.5秒间隔
        self.last_request_time = 0
        
        # 新浪财经API端点配置
        self.api_endpoints = {
            # 实时行情数据
#             'realtime': 'https://hq.sinajs.cn/list=',  # 已禁用新浪API - 超时问题
#             'realtime_backup': 'http://hq.sinajs.cn/list=',  # 已禁用新浪API - 超时问题
            
            # K线数据
            'kline': 'https://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData',
            
            # 财务数据
            'finance': 'https://money.finance.sina.com.cn/corp/go.php/vFD_FinanceSummary/stockid/{}.phtml',
            
            # 新闻数据
            'news': 'https://feed.mix.sina.com.cn/api/roll/get',
            
            # 资金流向
            'money_flow': 'https://money.finance.sina.com.cn/quotes_service/api/json_v2.php/MoneyFlow.ssl_qsfx_zjlrqs',
            
            # 板块数据
            'sector': 'https://money.finance.sina.com.cn/q/view/newSinaHy.php',
            
            # 搜索接口
            'search': 'https://suggest3.sinajs.cn/suggest/type=11,12,13,14,15&key={}',
            
            # 分时数据
            'minute': 'https://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData'
        }
        
        # 请求头配置
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://finance.sina.com.cn/',
            'Cache-Control': 'no-cache'
        }
        
        logger.info(f" {self.service_name} v{self.version} 初始化完成")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=100, limit_per_host=10)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def _rate_limit(self):
        """速率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_delay:
            await asyncio.sleep(self.rate_limit_delay - time_since_last)
        
        self.last_request_time = time.time()
    
    async def _make_request(self, url: str, params: Dict = None, encoding: str = 'utf-8') -> Optional[Dict]:
        """发起HTTP请求"""
        await self._rate_limit()
        
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(
                    headers=self.headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                )
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    if encoding == 'gbk':
                        text = await response.text(encoding='gbk', errors='ignore')
                    else:
                        text = await response.text(encoding='utf-8', errors='ignore')
                    
                    return {
                        'status': 'success',
                        'data': text,
                        'url': str(response.url)
                    }
                else:
                    logger.warning(f"请求失败: {url}, 状态码: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"请求异常: {url}, 错误: {e}")
            return None
    
    # ==================== 实时行情接口 ====================
    
    async def get_realtime_quote(self, stock_code: str) -> Optional[Dict]:
        """获取实时行情数据"""
        sina_code = stock_converter.to_sina_format(stock_code)
        
        # 尝试主要接口
        url = f"{self.api_endpoints['realtime']}{sina_code}"
        result = await self._make_request(url, encoding='gbk')
        
        if not result:
            # 尝试备用接口
            url = f"{self.api_endpoints['realtime_backup']}{sina_code}"
            result = await self._make_request(url, encoding='gbk')
        
        if result and result['data']:
            parsed_data = self._parse_realtime_data(result['data'], sina_code)
            if parsed_data:
                logger.info(f" 获取实时行情成功: {stock_code}")
                return parsed_data
        
        logger.warning(f"⚠️ 获取实时行情失败: {stock_code}")
        return None
    
    def _parse_realtime_data(self, data: str, sina_code: str) -> Optional[Dict]:
        """解析实时行情数据"""
        try:
            # 新浪实时数据格式: var hq_str_sh600000="浦发银行,7.83,7.85,7.79,7.84,7.78,7.79,7.80,..."
            if f'hq_str_{sina_code}=' in data:
                start = data.find(f'hq_str_{sina_code}="') + len(f'hq_str_{sina_code}="')
                end = data.find('";', start)
                
                if start > 0 and end > start:
                    quote_data = data[start:end].split(',')
                    
                    if len(quote_data) >= 32:
                        return {
                            'stock_code': sina_code,
                            'name': quote_data[0],
                            'open': float(quote_data[1]) if quote_data[1] else 0.0,
                            'prev_close': float(quote_data[2]) if quote_data[2] else 0.0,
                            'current': float(quote_data[3]) if quote_data[3] else 0.0,
                            'high': float(quote_data[4]) if quote_data[4] else 0.0,
                            'low': float(quote_data[5]) if quote_data[5] else 0.0,
                            'bid': float(quote_data[6]) if quote_data[6] else 0.0,
                            'ask': float(quote_data[7]) if quote_data[7] else 0.0,
                            'volume': int(quote_data[8]) if quote_data[8] else 0,
                            'amount': float(quote_data[9]) if quote_data[9] else 0.0,
                            'date': quote_data[30],
                            'time': quote_data[31],
                            'change': float(quote_data[3]) - float(quote_data[2]) if quote_data[3] and quote_data[2] else 0.0,
                            'change_percent': ((float(quote_data[3]) - float(quote_data[2])) / float(quote_data[2]) * 100) if quote_data[3] and quote_data[2] and float(quote_data[2]) > 0 else 0.0,
                            'timestamp': datetime.now().isoformat()
                        }
            
            return None
            
        except Exception as e:
            logger.error(f"解析实时数据失败: {e}")
            return None
    
    async def get_multiple_quotes(self, stock_codes: List[str]) -> Dict[str, Dict]:
        """批量获取实时行情"""
        results = {}
        
        # 新浪支持批量查询，最多50只股票
        batch_size = 50
        
        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i + batch_size]
            sina_codes = [stock_converter.to_sina_format(code) for code in batch_codes]
            
            # 构建批量查询URL
            codes_str = ','.join(sina_codes)
            url = f"{self.api_endpoints['realtime']}{codes_str}"
            
            result = await self._make_request(url, encoding='gbk')
            
            if result and result['data']:
                # 解析批量数据
                for j, original_code in enumerate(batch_codes):
                    sina_code = sina_codes[j]
                    parsed_data = self._parse_realtime_data(result['data'], sina_code)
                    if parsed_data:
                        results[original_code] = parsed_data
            
            # 避免请求过快
            await asyncio.sleep(0.1)
        
        logger.info(f" 批量获取行情完成: {len(results)}/{len(stock_codes)}")
        return results
    
    # ==================== K线数据接口 ====================
    
    async def get_kline_data(self, stock_code: str, period: str = 'daily', count: int = 100) -> Optional[List[Dict]]:
        """获取K线数据"""
        sina_code = stock_converter.to_sina_format(stock_code)
        
        # 构建K线数据请求
        params = {
            'symbol': sina_code,
            'scale': self._get_period_scale(period),
            'ma': 'no',
            'datalen': count
        }
        
        result = await self._make_request(self.api_endpoints['kline'], params)
        
        if result and result['data']:
            try:
                # 解析JSON数据
                kline_data = json.loads(result['data'])
                if isinstance(kline_data, list):
                    parsed_klines = []
                    for item in kline_data:
                        if isinstance(item, dict):
                            parsed_klines.append({
                                'date': item.get('day', ''),
                                'open': float(item.get('open', 0)),
                                'high': float(item.get('high', 0)),
                                'low': float(item.get('low', 0)),
                                'close': float(item.get('close', 0)),
                                'volume': int(item.get('volume', 0)),
                                'amount': float(item.get('amount', 0))
                            })
                    
                    logger.info(f" 获取K线数据成功: {stock_code}, {len(parsed_klines)}条")
                    return parsed_klines
                    
            except json.JSONDecodeError as e:
                logger.error(f"K线数据JSON解析失败: {e}")
        
        logger.warning(f"⚠️ 获取K线数据失败: {stock_code}")
        return None
    
    def _get_period_scale(self, period: str) -> str:
        """获取周期对应的scale参数"""
        period_mapping = {
            '1min': '1',
            '5min': '5',
            '15min': '15',
            '30min': '30',
            '60min': '60',
            'daily': '240',
            'weekly': '1680',
            'monthly': '7200'
        }
        return period_mapping.get(period, '240')
    
    # ==================== 新闻数据接口 ====================
    
    async def get_stock_news(self, stock_code: str, limit: int = 10) -> List[Dict]:
        """获取股票相关新闻"""
        try:
            # 获取股票信息
            stock_info = stock_converter.get_stock_info(stock_code)
            stock_name = stock_info['name']
            
            # 构建新闻搜索参数
            params = {
                'pageid': '153',
                'lid': '1686',
                'k': stock_name,
                'num': limit,
                'page': '1'
            }
            
            result = await self._make_request(self.api_endpoints['news'], params)
            
            if result and result['data']:
                try:
                    news_data = json.loads(result['data'])
                    if 'result' in news_data and 'data' in news_data['result']:
                        news_items = []
                        for item in news_data['result']['data'][:limit]:
                            news_items.append({
                                'title': item.get('title', ''),
                                'content': item.get('intro', ''),
                                'url': item.get('url', ''),
                                'source': item.get('media_name', '新浪财经'),
                                'publish_time': item.get('ctime', ''),
                                'relevance_score': 0.8,
                                'timestamp': datetime.now().isoformat()
                            })
                        
                        logger.info(f" 获取股票新闻成功: {stock_code}, {len(news_items)}条")
                        return news_items
                        
                except json.JSONDecodeError as e:
                    logger.error(f"新闻数据JSON解析失败: {e}")
            
        except Exception as e:
            logger.error(f"获取股票新闻失败: {e}")
        
        # 返回空列表而不是None
        return []
    
    # ==================== 搜索接口 ====================
    
    async def search_stocks(self, keyword: str) -> List[Dict]:
        """搜索股票"""
        try:
            encoded_keyword = quote(keyword)
            url = self.api_endpoints['search'].format(encoded_keyword)
            
            result = await self._make_request(url, encoding='gbk')
            
            if result and result['data']:
                # 解析搜索结果
                search_results = self._parse_search_results(result['data'])
                logger.info(f" 股票搜索成功: {keyword}, {len(search_results)}个结果")
                return search_results
            
        except Exception as e:
            logger.error(f"股票搜索失败: {e}")
        
        return []
    
    def _parse_search_results(self, data: str) -> List[Dict]:
        """解析搜索结果"""
        results = []
        
        try:
            # 新浪搜索返回格式: var suggestvalue="sh600000,浦发银行,..."
            if 'suggestvalue=' in data:
                start = data.find('suggestvalue="') + len('suggestvalue="')
                end = data.find('";', start)
                
                if start > 0 and end > start:
                    suggest_data = data[start:end]
                    items = suggest_data.split(';')
                    
                    for item in items:
                        if ',' in item:
                            parts = item.split(',')
                            if len(parts) >= 2:
                                results.append({
                                    'code': parts[0],
                                    'name': parts[1],
                                    'type': 'stock'
                                })
            
        except Exception as e:
            logger.error(f"解析搜索结果失败: {e}")
        
        return results
    
    # ==================== 综合数据接口 ====================
    
    async def get_comprehensive_stock_data(self, stock_code: str) -> Dict[str, Any]:
        """获取股票综合数据"""
        logger.info(f"🔍 开始获取新浪股票综合数据: {stock_code}")
        
        tasks = [
            ("realtime_quote", self.get_realtime_quote(stock_code)),
            ("kline_data", self.get_kline_data(stock_code, 'daily', 30)),
            ("stock_news", self.get_stock_news(stock_code, 5))
        ]
        
        results = {}
        for name, task in tasks:
            try:
                result = await task
                results[name] = result
                await asyncio.sleep(0.2)  # 避免请求过快
            except Exception as e:
                logger.error(f"获取{name}数据失败: {e}")
                results[name] = None
        
        logger.info(f" 新浪股票综合数据获取完成: {stock_code}")
        return results

# 全局服务实例
professional_sina_service = ProfessionalSinaFinanceService()

async def get_sina_stock_data(stock_code: str) -> Dict[str, Any]:
    """快速获取新浪股票数据"""
    async with professional_sina_service as service:
        return await service.get_comprehensive_stock_data(stock_code)

async def get_sina_realtime_quotes(stock_codes: List[str]) -> Dict[str, Dict]:
    """快速获取新浪实时行情"""
    async with professional_sina_service as service:
        return await service.get_multiple_quotes(stock_codes)

if __name__ == "__main__":
    # 测试代码
    async def test_service():
        logger.warning("新浪API已禁用")
        return {"success": False, "error": "新浪API已禁用"}
        async with ProfessionalSinaFinanceService() as service:
            # 测试实时行情
            quote = await service.get_realtime_quote("600000")
            print(f"实时行情: {json.dumps(quote, ensure_ascii=False, indent=2)}")
            
            # 测试K线数据
            klines = await service.get_kline_data("600000", 'daily', 5)
            print(f"K线数据: {len(klines) if klines else 0}条")
            
            # 测试新闻数据
            news = await service.get_stock_news("600000", 3)
            print(f"新闻数据: {len(news)}条")
    
    asyncio.run(test_service())
