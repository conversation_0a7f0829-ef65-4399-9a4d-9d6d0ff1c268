"""
统一财经数据获取接口
整合百度、新浪、腾讯等多个数据源，提供统一的调用方法
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import aiohttp
import json
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class DataSource(Enum):
    """数据源枚举"""
    BAIDU = "baidu"
    SINA = "sina" 
    TENCENT = "tencent"
    EASTMONEY = "eastmoney"

@dataclass
class StockData:
    """股票数据标准格式"""
    code: str
    name: str
    price: float
    change: float
    change_percent: float
    volume: int
    turnover: float
    high: float
    low: float
    open: float
    close: float
    timestamp: datetime
    source: str

@dataclass
class MarketData:
    """市场数据标准格式"""
    market_type: str
    data: Dict[str, Any]
    timestamp: datetime
    source: str

class UnifiedFinancialAPI:
    """统一财经数据获取接口"""
    
    def __init__(self):
        self.session = None
        self.rate_limiter = {}  # 每个源的速率限制
        
        # 百度财经API端点 - 基于您提供的验证过的API
        self.baidu_endpoints = {
            # 市场行情
            'market_quote': 'https://finance.pae.baidu.com/sapi/v1/marketquote',
            'blocks': 'https://finance.pae.baidu.com/vapi/v2/blocks',
            'fund_flow': 'https://finance.pae.baidu.com/sapi/v1/marketquote',
            'blocks_overview': 'https://finance.pae.baidu.com/vapi/v1/blocks/overview',
            'anomaly_signal': 'https://finance.pae.baidu.com/sapi/v1/marketquote',
            'stock_rank': 'https://finance.pae.baidu.com/sapi/v1/ranks',
            
            # 个股数据
            'stock_overview': 'https://finance.pae.baidu.com/vapi/v1/overviewwidget',
            'stock_basic': 'https://finance.pae.baidu.com/vapi/v1/overviewwidget',
            'related_block': 'https://finance.pae.baidu.com/api/getrelatedblock',
            'related_objects': 'https://finance.pae.baidu.com/vapi/v1/stockrelatedobjects',
            'stock_fund_flow': 'https://finance.pae.baidu.com/vapi/v1/fundflow',
            'stock_news': 'https://finance.pae.baidu.com/vapi/sentimentlist',
            'stock_finance': 'https://finance.pae.baidu.com/api/stockwidget',
            'stock_analysis': 'https://finance.pae.baidu.com/vapi/v1/analysis',
            'stock_company': 'https://finance.pae.baidu.com/api/stockwidget'
        }
        
        # 新浪财经API端点
        self.sina_endpoints = {
#             'realtime': 'https://hq.sinajs.cn/list=',  # 已禁用新浪API - 超时问题
            'kline': 'https://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData'
        }
        
        # 腾讯财经API端点  
        self.tencent_endpoints = {
            'realtime': 'https://qt.gtimg.cn/q=',
            'kline': 'https://web.ifzq.gtimg.cn/appstock/app/fqkline/get'
        }
        
        # 初始化速率限制器
        for source in DataSource:
            self.rate_limiter[source.value] = {
                'last_request': 0,
                'min_interval': 1.0  # 1秒间隔，符合您的要求
            }
        
        logger.info("统一财经数据API初始化完成")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def _wait_for_rate_limit(self, source: str):
        """等待速率限制"""
        limiter = self.rate_limiter[source]
        elapsed = time.time() - limiter['last_request']
        
        if elapsed < limiter['min_interval']:
            wait_time = limiter['min_interval'] - elapsed
            time.sleep(wait_time)
        
        limiter['last_request'] = time.time()

    async def get_stock_data(self, stock_code: str, source: DataSource = None) -> Optional[StockData]:
        """
        获取股票数据 - 统一接口
        
        Args:
            stock_code: 股票代码 (如: 600633)
            source: 指定数据源，None则自动选择最佳源
            
        Returns:
            标准化的股票数据
        """
        if source is None:
            # 按优先级尝试数据源
            sources = [DataSource.BAIDU, DataSource.SINA, DataSource.TENCENT]
        else:
            sources = [source]
        
        for src in sources:
            try:
                self._wait_for_rate_limit(src.value)
                
                if src == DataSource.BAIDU:
                    data = await self._get_baidu_stock_data(stock_code)
                elif src == DataSource.SINA:
                    data = await self._get_sina_stock_data(stock_code)
                elif src == DataSource.TENCENT:
                    data = await self._get_tencent_stock_data(stock_code)
                else:
                    continue
                
                if data:
                    logger.info(f"从{src.value}成功获取股票{stock_code}数据")
                    return data
                    
            except Exception as e:
                logger.warning(f"从{src.value}获取股票{stock_code}数据失败: {e}")
                continue
        
        logger.error(f"所有数据源获取股票{stock_code}数据失败")
        return None

    async def get_market_data(self, market_type: str = "ab", source: DataSource = DataSource.BAIDU) -> Optional[MarketData]:
        """
        获取市场数据
        
        Args:
            market_type: 市场类型 (ab=A股+B股)
            source: 数据源
            
        Returns:
            市场数据
        """
        try:
            self._wait_for_rate_limit(source.value)
            
            if source == DataSource.BAIDU:
                return await self._get_baidu_market_data(market_type)
            else:
                logger.warning(f"暂不支持{source.value}的市场数据")
                return None
                
        except Exception as e:
            logger.error(f"获取市场数据失败: {e}")
            return None

    async def _get_baidu_stock_data(self, stock_code: str) -> Optional[StockData]:
        """从百度财经获取股票数据"""
        try:
            # 获取股票基本信息
            params = {
                'market': 'ab',
                'code': stock_code,
                'financeType': 'stock',
                'modules': 'basicinfo',
                'finClientType': 'pc'
            }
            
            async with self.session.get(self.baidu_endpoints['stock_basic'], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_baidu_stock_data(data, stock_code)
                else:
                    logger.warning(f"百度API返回状态码: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"百度股票数据获取失败: {e}")
            return None

    def _parse_baidu_stock_data(self, data: Dict, stock_code: str) -> Optional[StockData]:
        """解析百度股票数据"""
        try:
            logger.debug(f"百度API返回数据结构: {data}")

            # 尝试多种可能的数据结构
            result = None
            basic_info = {}

            if isinstance(data, dict):
                if 'Result' in data:
                    result = data['Result']
                    if isinstance(result, dict):
                        basic_info = result.get('basicInfo', {})
                elif 'data' in data:
                    result = data['data']
                    if isinstance(result, dict):
                        basic_info = result
                elif 'result' in data:
                    result = data['result']
                    if isinstance(result, dict):
                        basic_info = result
                else:
                    # 直接使用data作为basic_info
                    basic_info = data

            # 如果还是没有数据，记录警告但继续处理
            if not basic_info or not any(key in basic_info for key in ['currentPrice', 'price', 'current']):
                logger.warning(f"百度API数据格式不匹配，使用备用价格生成: {stock_code}")
                basic_info = {"currentPrice": 0}  # 触发下面的价格生成逻辑

            # 提取价格信息，尝试多个可能的字段名
            price_fields = ['currentPrice', 'price', 'current', 'now', 'last']
            price = 0.0
            for field in price_fields:
                if field in basic_info and basic_info[field]:
                    try:
                        price = float(basic_info[field])
                        if price > 0:
                            break
                    except (ValueError, TypeError):
                        continue

            # 如果价格还是0，返回None而不是生成测试价格
            if price <= 0:
                logger.error(f"无法获取真实价格，API数据无效: {stock_code}")
                return None

            # 提取其他信息
            name = basic_info.get('stockName', basic_info.get('name', f'股票{stock_code}'))
            change = float(basic_info.get('change', basic_info.get('chg', 0)))
            change_percent = float(basic_info.get('changePercent', basic_info.get('chgPercent', 0)))
            volume = int(basic_info.get('volume', basic_info.get('vol', 0)))
            turnover = float(basic_info.get('turnover', basic_info.get('amount', 0)))
            high = float(basic_info.get('high', basic_info.get('dayHigh', price * 1.02)))
            low = float(basic_info.get('low', basic_info.get('dayLow', price * 0.98)))
            open_price = float(basic_info.get('open', basic_info.get('dayOpen', price * 0.99)))
            close_price = float(basic_info.get('close', basic_info.get('preClose', price)))

            return StockData(
                code=stock_code,
                name=name,
                price=price,
                change=change,
                change_percent=change_percent,
                volume=volume,
                turnover=turnover,
                high=high,
                low=low,
                open=open_price,
                close=close_price,
                timestamp=datetime.now(),
                source='baidu'
            )

        except Exception as e:
            logger.error(f"解析百度股票数据失败: {e}")
            # 返回None而不是生成测试数据
            return None

    async def _get_sina_stock_data(self, stock_code: str) -> Optional[StockData]:
        """从新浪财经获取股票数据"""
        try:
            # 新浪API需要特定的股票代码格式
            sina_code = self._convert_to_sina_code(stock_code)
            
            async with self.session.get(f"{self.sina_endpoints['realtime']}{sina_code}") as response:
                if response.status == 200:
                    content = await response.text()
                    return self._parse_sina_stock_data(content, stock_code)
                else:
                    return None
                    
        except Exception as e:
            logger.error(f"新浪股票数据获取失败: {e}")
            return None

    def _convert_to_sina_code(self, stock_code: str) -> str:
        """转换为新浪股票代码格式"""
        if stock_code.startswith('6'):
            return f"sh{stock_code}"
        elif stock_code.startswith(('0', '3')):
            return f"sz{stock_code}"
        else:
            return stock_code

    def _parse_sina_stock_data(self, content: str, stock_code: str) -> Optional[StockData]:
        """解析新浪股票数据"""
        try:
            # 新浪返回格式: var hq_str_sh600633="白云山,27.55,27.65,..."
            if 'hq_str_' not in content:
                return None
            
            data_part = content.split('"')[1]
            fields = data_part.split(',')
            
            if len(fields) < 32:
                return None
            
            name = fields[0]
            price = float(fields[3])
            open_price = float(fields[1])
            close_price = float(fields[2])
            high = float(fields[4])
            low = float(fields[5])
            volume = int(fields[8])
            turnover = float(fields[9])
            
            change = price - close_price
            change_percent = (change / close_price * 100) if close_price > 0 else 0
            
            return StockData(
                code=stock_code,
                name=name,
                price=price,
                change=change,
                change_percent=change_percent,
                volume=volume,
                turnover=turnover,
                high=high,
                low=low,
                open=open_price,
                close=close_price,
                timestamp=datetime.now(),
                source='sina'
            )
            
        except Exception as e:
            logger.error(f"解析新浪股票数据失败: {e}")
            return None

    async def _get_tencent_stock_data(self, stock_code: str) -> Optional[StockData]:
        """从腾讯财经获取股票数据"""
        try:
            # 腾讯API需要特定的股票代码格式
            tencent_code = self._convert_to_tencent_code(stock_code)

            async with self.session.get(f"{self.tencent_endpoints['realtime']}{tencent_code}") as response:
                if response.status == 200:
                    content = await response.text()
                    return self._parse_tencent_stock_data(content, stock_code)
                else:
                    return None

        except Exception as e:
            logger.error(f"腾讯股票数据获取失败: {e}")
            return None

    def _convert_to_tencent_code(self, stock_code: str) -> str:
        """转换为腾讯股票代码格式"""
        if stock_code.startswith('6'):
            return f"sh{stock_code}"
        elif stock_code.startswith(('0', '3')):
        logger.warning("新浪API已禁用")
        return {"success": False, "error": "新浪API已禁用"}
            return f"sz{stock_code}"
        else:
            return stock_code

    def _parse_tencent_stock_data(self, content: str, stock_code: str) -> Optional[StockData]:
        """解析腾讯股票数据"""
        try:
            # 腾讯返回格式类似: v_sh600633="1~白云山~600633~27.55~..."
            if f'v_{self._convert_to_tencent_code(stock_code)}=' not in content:
                return None

            data_part = content.split('"')[1]
            fields = data_part.split('~')

            if len(fields) < 50:
                return None

            name = fields[1]
            price = float(fields[3])
            open_price = float(fields[5])
            close_price = float(fields[4])
            high = float(fields[33])
            low = float(fields[34])
            volume = int(fields[6])
            turnover = float(fields[37])

            change = price - close_price
            change_percent = (change / close_price * 100) if close_price > 0 else 0

            return StockData(
                code=stock_code,
                name=name,
                price=price,
                change=change,
                change_percent=change_percent,
                volume=volume,
                turnover=turnover,
                high=high,
                low=low,
                open=open_price,
                close=close_price,
                timestamp=datetime.now(),
                source='tencent'
            )

        except Exception as e:
            logger.error(f"解析腾讯股票数据失败: {e}")
            return None

    async def _get_baidu_market_data(self, market_type: str) -> Optional[MarketData]:
        """从百度财经获取市场数据"""
        try:
            # 获取市场行情数据
            params = {
                'bizType': 'chgdiagram',
                'market': market_type,
                'finClientType': 'pc'
            }

            async with self.session.get(self.baidu_endpoints['market_quote'], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return MarketData(
                        market_type=market_type,
                        data=data,
                        timestamp=datetime.now(),
                        source='baidu'
                    )
                else:
                    return None

        except Exception as e:
            logger.error(f"百度市场数据获取失败: {e}")
            return None

    async def get_stock_analysis(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        获取股票分析数据 - 使用百度财经API

        Args:
            stock_code: 股票代码

        Returns:
            股票分析数据
        """
        try:
            self._wait_for_rate_limit('baidu')

            params = {
                'code': stock_code,
                'market': 'ab',
                'isNew': '1',
                'finClientType': 'pc'
            }

            async with self.session.get(self.baidu_endpoints['stock_analysis'], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"成功获取股票{stock_code}分析数据")
                    return data
                else:
                    logger.warning(f"获取股票分析数据失败，状态码: {response.status}")
                    return None

        except Exception as e:
            logger.error(f"获取股票分析数据失败: {e}")
            return None

    async def get_stock_fund_flow(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        获取股票资金流向数据

        Args:
            stock_code: 股票代码

        Returns:
            资金流向数据
        """
        try:
            self._wait_for_rate_limit('baidu')

            params = {
                'finance_type': 'stock',
                'fund_flow_type': '',
                'market': 'ab',
                'code': stock_code,
                'type': 'stock',
                'finClientType': 'pc'
            }

            async with self.session.get(self.baidu_endpoints['stock_fund_flow'], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"成功获取股票{stock_code}资金流向数据")
                    return data
                else:
                    return None

        except Exception as e:
            logger.error(f"获取股票资金流向数据失败: {e}")
            return None

    async def get_market_blocks(self, style: str = "heatmap") -> Optional[Dict[str, Any]]:
        """
        获取市场板块数据

        Args:
            style: 显示样式 (heatmap等)

        Returns:
            板块数据
        """
        try:
            self._wait_for_rate_limit('baidu')

            params = {
                'style': style,
                'market': 'ab',
                'typeCode': 'HY',
                'sortKey': 'amount',
                'sortType': 'desc',
                'pn': '0',
                'rn': '200',
                'finClientType': 'pc'
            }

            async with self.session.get(self.baidu_endpoints['blocks'], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("成功获取市场板块数据")
                    return data
                else:
                    return None

        except Exception as e:
            logger.error(f"获取市场板块数据失败: {e}")
            return None

    async def get_stock_ranking(self, category: str = "", page: int = 0, limit: int = 50) -> Optional[Dict[str, Any]]:
        """
        获取股票排名数据

        Args:
            category: 分类
            page: 页码
            limit: 限制数量

        Returns:
            股票排名数据
        """
        try:
            self._wait_for_rate_limit('baidu')

            params = {
                'bizType': 'stock_rank',
                'category': category,
                'market': 'ab',
                'pn': str(page),
                'rn': str(limit),
                'fieldsType': 'base',
                'finClientType': 'pc'
            }

            async with self.session.get(self.baidu_endpoints['stock_rank'], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"成功获取股票排名数据，页码: {page}")
                    return data
                else:
                    return None

        except Exception as e:
            logger.error(f"获取股票排名数据失败: {e}")
            return None

# 创建全局实例
unified_financial_api = UnifiedFinancialAPI()
