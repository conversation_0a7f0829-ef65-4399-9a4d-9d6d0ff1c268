#!/usr/bin/env python3
"""
AkShare历史数据学习服务
持续在后台下载历史交易数据，为架构师提供学习素材
"""

import asyncio
import sqlite3
import pandas as pd
import akshare as ak
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
import os
import time
from pathlib import Path

logger = logging.getLogger(__name__)

class AkShareLearningService:
    """AkShare历史数据学习服务"""
    
    def __init__(self, data_dir: str = "data/learning"):
        """初始化学习服务
        
        Args:
            data_dir: 数据存储目录
        """
        self.service_name = "AkShareLearningService"
        self.version = "1.0.0"
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 数据库文件
        self.db_path = self.data_dir / "historical_data.db"
        self.init_database()
        
        # 学习配置
        self.learning_config = {
            "download_interval": 3600,  # 1小时下载一次
            "batch_size": 10,  # 每批处理10只股票
            "max_history_days": 1000,  # 最多下载1000天历史数据
            "retry_attempts": 3,  # 重试次数
            "sleep_between_requests": 2,  # 请求间隔2秒
        }
        
        # A股主要股票池
        self.stock_pool = [
            "000001.SZ", "000002.SZ", "000858.SZ", "002415.SZ", "300059.SZ",
            "600036.SH", "600519.SH", "000725.SZ", "002594.SZ", "300750.SZ",
            "000166.SZ", "002230.SZ", "300015.SZ", "600276.SH", "600887.SH",
            "000063.SZ", "002304.SZ", "300142.SZ", "600031.SH", "601318.SH",
            "000568.SZ", "002352.SZ", "300408.SZ", "600104.SH", "601888.SH",
            "000876.SZ", "002475.SZ", "300498.SZ", "600196.SH", "603259.SH"
        ]
        
        # 运行状态
        self.is_running = False
        self.download_stats = {
            "total_downloads": 0,
            "successful_downloads": 0,
            "failed_downloads": 0,
            "last_update": None
        }
        
        logger.info(f"{self.service_name} v{self.version} 初始化完成")
        logger.info(f"数据存储目录: {self.data_dir}")
        logger.info(f"股票池大小: {len(self.stock_pool)}只股票")
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建历史数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS historical_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                date TEXT NOT NULL,
                open_price REAL,
                high_price REAL,
                low_price REAL,
                close_price REAL,
                volume INTEGER,
                turnover REAL,
                change_pct REAL,
                download_time TEXT,
                UNIQUE(symbol, date)
            )
        ''')
        
        # 创建下载日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS download_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                download_time TEXT,
                status TEXT,
                records_count INTEGER,
                error_message TEXT
            )
        ''')
        
        # 创建学习进度表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_progress (
                symbol TEXT PRIMARY KEY,
                last_download_date TEXT,
                total_records INTEGER,
                data_quality_score REAL,
                last_update TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("数据库初始化完成")
    
    async def start_learning_service(self):
        """启动学习服务"""
        if self.is_running:
            logger.warning("学习服务已在运行中")
            return
        
        self.is_running = True
        logger.info("  AkShare历史数据学习服务启动")
        
        try:
            while self.is_running:
                await self.run_learning_cycle()
                
                # 等待下一个周期
                logger.info(f"等待 {self.learning_config['download_interval']} 秒后开始下一轮学习")
                await asyncio.sleep(self.learning_config['download_interval'])
                
        except Exception as e:
            logger.error(f"学习服务运行错误: {e}")
        finally:
            self.is_running = False
            logger.info("学习服务已停止")
    
    async def run_learning_cycle(self):
        """运行一个学习周期"""
        logger.info("📚 开始新的学习周期")
        
        # 分批处理股票
        for i in range(0, len(self.stock_pool), self.learning_config['batch_size']):
            batch = self.stock_pool[i:i + self.learning_config['batch_size']]
            logger.info(f"处理批次 {i//self.learning_config['batch_size'] + 1}: {batch}")
            
            for symbol in batch:
                try:
                    await self.download_historical_data(symbol)
                    await asyncio.sleep(self.learning_config['sleep_between_requests'])
                except Exception as e:
                    logger.error(f"下载 {symbol} 历史数据失败: {e}")
                    self.download_stats['failed_downloads'] += 1
            
            # 批次间休息
            await asyncio.sleep(5)
        
        self.download_stats['last_update'] = datetime.now().isoformat()
        logger.info(" 学习周期完成")
        self.log_download_stats()
    
    async def download_historical_data(self, symbol: str):
        """下载单只股票的历史数据"""
        logger.info(f"📥 下载 {symbol} 历史数据")
        
        # 检查上次下载进度
        last_date = self.get_last_download_date(symbol)
        
        # 确定下载日期范围
        end_date = datetime.now()
        if last_date:
            start_date = datetime.fromisoformat(last_date) + timedelta(days=1)
        else:
            start_date = end_date - timedelta(days=self.learning_config['max_history_days'])
        
        if start_date >= end_date:
            logger.info(f"  {symbol} 数据已是最新，跳过")
            return
        
        # 下载数据
        for attempt in range(self.learning_config['retry_attempts']):
            try:
                # 转换股票代码格式
                ak_symbol = symbol[:6]  # AkShare使用6位代码
                
                # 调用AkShare获取历史数据
                df = ak.stock_zh_a_hist(
                    symbol=ak_symbol,
                    period="daily",
                    start_date=start_date.strftime('%Y%m%d'),
                    end_date=end_date.strftime('%Y%m%d'),
                    adjust=""
                )
                
                if df.empty:
                    logger.warning(f"  {symbol} 无历史数据")
                    return
                
                # 保存到数据库
                records_saved = self.save_historical_data(symbol, df)
                
                # 更新学习进度
                self.update_learning_progress(symbol, end_date.date().isoformat(), records_saved)
                
                # 记录下载日志
                self.log_download(symbol, "success", records_saved)
                
                self.download_stats['successful_downloads'] += 1
                self.download_stats['total_downloads'] += 1
                
                logger.info(f"    {symbol} 下载完成: {records_saved} 条记录")
                return
                
            except Exception as e:
                logger.warning(f"    {symbol} 下载失败 (尝试 {attempt + 1}/{self.learning_config['retry_attempts']}): {e}")
                if attempt < self.learning_config['retry_attempts'] - 1:
                    await asyncio.sleep(5)  # 重试前等待
        
        # 所有尝试都失败
        self.log_download(symbol, "failed", 0, str(e))
        self.download_stats['failed_downloads'] += 1
        self.download_stats['total_downloads'] += 1
    
    def save_historical_data(self, symbol: str, df: pd.DataFrame) -> int:
        """保存历史数据到数据库"""
        conn = sqlite3.connect(self.db_path)
        
        records_saved = 0
        for _, row in df.iterrows():
            try:
                conn.execute('''
                    INSERT OR REPLACE INTO historical_data 
                    (symbol, date, open_price, high_price, low_price, close_price, 
                     volume, turnover, change_pct, download_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    symbol,
                    row['日期'],
                    row['开盘'],
                    row['最高'],
                    row['最低'],
                    row['收盘'],
                    row['成交量'],
                    row['成交额'],
                    row.get('涨跌幅', 0),
                    datetime.now().isoformat()
                ))
                records_saved += 1
            except Exception as e:
                logger.warning(f"保存数据记录失败: {e}")
        
        conn.commit()
        conn.close()
        return records_saved
    
    def get_last_download_date(self, symbol: str) -> Optional[str]:
        """获取最后下载日期"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'SELECT last_download_date FROM learning_progress WHERE symbol = ?',
            (symbol,)
        )
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result else None
    
    def update_learning_progress(self, symbol: str, last_date: str, records_count: int):
        """更新学习进度"""
        conn = sqlite3.connect(self.db_path)
        
        quality_score = min(1.0, records_count / 250)  # 250个交易日为满分
        
        conn.execute('''
            INSERT OR REPLACE INTO learning_progress 
            (symbol, last_download_date, total_records, data_quality_score, last_update)
            VALUES (?, ?, ?, ?, ?)
        ''', (symbol, last_date, records_count, quality_score, datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
    
    def log_download(self, symbol: str, status: str, records_count: int, error_message: str = None):
        """记录下载日志"""
        conn = sqlite3.connect(self.db_path)
        
        conn.execute('''
            INSERT INTO download_log 
            (symbol, download_time, status, records_count, error_message)
            VALUES (?, ?, ?, ?, ?)
        ''', (symbol, datetime.now().isoformat(), status, records_count, error_message))
        
        conn.commit()
        conn.close()
    
    def log_download_stats(self):
        """输出下载统计"""
        logger.info(" 下载统计:")
        logger.info(f"  总下载次数: {self.download_stats['total_downloads']}")
        logger.info(f"  成功次数: {self.download_stats['successful_downloads']}")
        logger.info(f"  失败次数: {self.download_stats['failed_downloads']}")
        if self.download_stats['total_downloads'] > 0:
            success_rate = self.download_stats['successful_downloads'] / self.download_stats['total_downloads'] * 100
            logger.info(f"  成功率: {success_rate:.1f}%")
    
    def get_learning_data_for_architect(self, symbol: str, days: int = 250) -> pd.DataFrame:
        """为架构师提供学习数据"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT date, open_price, high_price, low_price, close_price, volume, turnover, change_pct
            FROM historical_data 
            WHERE symbol = ? 
            ORDER BY date DESC 
            LIMIT ?
        '''
        
        df = pd.read_sql_query(query, conn, params=(symbol, days))
        conn.close()
        
        return df.sort_values('date')  # 按日期正序排列
    
    def stop_learning_service(self):
        """停止学习服务"""
        self.is_running = False
        logger.info("🛑 学习服务停止信号已发送")
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "service_name": self.service_name,
            "version": self.version,
            "is_running": self.is_running,
            "stock_pool_size": len(self.stock_pool),
            "download_stats": self.download_stats,
            "data_directory": str(self.data_dir),
            "database_path": str(self.db_path)
        }
