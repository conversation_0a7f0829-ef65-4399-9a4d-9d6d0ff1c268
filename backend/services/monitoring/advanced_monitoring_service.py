from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级监控告警服务
实时监控系统状态、性能指标和异常情况
"""

import asyncio
import logging
import json
import os
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import sqlite3
import aiosqlite

logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class MetricType(Enum):
    """指标类型"""
    SYSTEM = "system"
    APPLICATION = "application"
    BUSINESS = "business"
    CUSTOM = "custom"

@dataclass
class MetricData:
    """指标数据"""
    metric_name: str
    metric_type: str
    value: float
    unit: str
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    tags: Dict[str, str] = field(default_factory=dict)

@dataclass
class AlertRule:
    """告警规则"""
    rule_id: str
    metric_name: str
    condition: str  # >, <, >=, <=, ==, !=
    threshold: float
    alert_level: str
    message_template: str
    enabled: bool = True
    cooldown_minutes: int = 5

@dataclass
class Alert:
    """告警信息"""
    alert_id: str
    rule_id: str
    metric_name: str
    current_value: float
    threshold: float
    alert_level: str
    message: str
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    resolved: bool = False
    resolved_time: Optional[str] = None

class AdvancedMonitoringService:
    """高级监控告警服务"""
    
    def __init__(self, db_path: str = None):
        self.service_name = "AdvancedMonitoringService"
        self.version = "1.0.0"
        
        # 数据库路径
        if db_path is None:
            db_path = "backend/data/monitoring.db"
        
        self.db_path = db_path
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 监控配置
        self.monitoring_config = {
            "collection_interval": 30,  # 秒
            "retention_days": 30,
            "max_alerts_per_hour": 100
        }
        
        # 内存存储
        self.metrics_buffer = []
        self.alert_rules = {}
        self.active_alerts = {}
        self.alert_cooldowns = {}
        
        # 监控任务
        self.monitoring_task = None
        self.is_running = False
        
        # 初始化数据库
        self._init_database()
        
        # 加载默认告警规则
        self._load_default_alert_rules()
        
        logger.info(f" {self.service_name} v{self.version} 初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建指标表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT NOT NULL,
                    metric_type TEXT NOT NULL,
                    value REAL NOT NULL,
                    unit TEXT,
                    timestamp TEXT NOT NULL,
                    tags TEXT  -- JSON格式存储
                )
            ''')
            
            # 创建告警规则表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alert_rules (
                    rule_id TEXT PRIMARY KEY,
                    metric_name TEXT NOT NULL,
                    condition TEXT NOT NULL,
                    threshold REAL NOT NULL,
                    alert_level TEXT NOT NULL,
                    message_template TEXT,
                    enabled BOOLEAN DEFAULT 1,
                    cooldown_minutes INTEGER DEFAULT 5
                )
            ''')
            
            # 创建告警记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    alert_id TEXT PRIMARY KEY,
                    rule_id TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    current_value REAL NOT NULL,
                    threshold REAL NOT NULL,
                    alert_level TEXT NOT NULL,
                    message TEXT,
                    timestamp TEXT NOT NULL,
                    resolved BOOLEAN DEFAULT 0,
                    resolved_time TEXT
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_metrics_name_time ON metrics (metric_name, timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_alerts_level_time ON alerts (alert_level, timestamp)')
            
            conn.commit()
            conn.close()
            
            logger.info(" 监控数据库初始化完成")
            
        except Exception as e:
            logger.error(f"监控数据库初始化失败: {e}")
            raise
    
    def _load_default_alert_rules(self):
        """加载默认告警规则"""
        default_rules = [
            AlertRule(
                rule_id="cpu_high",
                metric_name="system.cpu.usage",
                condition=">",
                threshold=80.0,
                alert_level="warning",
                message_template="CPU使用率过高: {current_value}%"
            ),
            AlertRule(
                rule_id="memory_high",
                metric_name="system.memory.usage",
                condition=">",
                threshold=85.0,
                alert_level="warning",
                message_template="内存使用率过高: {current_value}%"
            ),
            AlertRule(
                rule_id="disk_high",
                metric_name="system.disk.usage",
                condition=">",
                threshold=90.0,
                alert_level="error",
                message_template="磁盘使用率过高: {current_value}%"
            ),
            AlertRule(
                rule_id="api_error_rate_high",
                metric_name="app.api.error_rate",
                condition=">",
                threshold=5.0,
                alert_level="error",
                message_template="API错误率过高: {current_value}%"
            ),
            AlertRule(
                rule_id="news_collection_failure",
                metric_name="app.news.collection_failure_rate",
                condition=">",
                threshold=50.0,
                alert_level="warning",
                message_template="新闻收集失败率过高: {current_value}%"
            )
        ]
        
        for rule in default_rules:
            self.alert_rules[rule.rule_id] = rule
    
    async def start_monitoring(self):
        """启动监控"""
        if not self.is_running:
            self.is_running = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            logger.info(" 高级监控服务已启动")
    
    async def stop_monitoring(self):
        """停止监控"""
        if self.is_running:
            self.is_running = False
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            logger.info("⏹️ 高级监控服务已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        logger.info("🔄 监控循环开始运行")
        
        while self.is_running:
            try:
                # 收集系统指标
                await self._collect_system_metrics()
                
                # 收集应用指标
                await self._collect_application_metrics()
                
                # 检查告警规则
                await self._check_alert_rules()
                
                # 持久化指标数据
                await self._persist_metrics()
                
                # 清理过期数据
                await self._cleanup_old_data()
                
                # 等待下次收集
                await asyncio.sleep(self.monitoring_config["collection_interval"])
                
            except asyncio.CancelledError:
                logger.info("监控循环被取消")
                break
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(5)
    
    async def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            await self.record_metric("system.cpu.usage", cpu_percent, "percent", MetricType.SYSTEM)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            await self.record_metric("system.memory.usage", memory.percent, "percent", MetricType.SYSTEM)
            await self.record_metric("system.memory.available", memory.available / 1024 / 1024 / 1024, "GB", MetricType.SYSTEM)
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            await self.record_metric("system.disk.usage", disk_percent, "percent", MetricType.SYSTEM)
            
            # 网络IO
            net_io = psutil.net_io_counters()
            await self.record_metric("system.network.bytes_sent", net_io.bytes_sent, "bytes", MetricType.SYSTEM)
            await self.record_metric("system.network.bytes_recv", net_io.bytes_recv, "bytes", MetricType.SYSTEM)
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
    
    async def _collect_application_metrics(self):
        """收集应用指标"""
        try:
            # 这里可以添加应用特定的指标收集
            # 例如：API响应时间、错误率、业务指标等
            
            # 模拟一些应用指标
            await self.record_metric("app.api.response_time", 150.0, "ms", MetricType.APPLICATION)
            await self.record_metric("app.api.error_rate", 2.5, "percent", MetricType.APPLICATION)
            await self.record_metric("app.news.collection_success_rate", 85.0, "percent", MetricType.BUSINESS)
            
        except Exception as e:
            logger.error(f"收集应用指标失败: {e}")
    
    async def record_metric(self, metric_name: str, value: float, unit: str, metric_type: MetricType, tags: Dict[str, str] = None):
        """记录指标"""
        try:
            metric = MetricData(
                metric_name=metric_name,
                metric_type=metric_type.value,
                value=value,
                unit=unit,
                tags=tags or {}
            )
            
            self.metrics_buffer.append(metric)
            
            # 如果缓冲区太大，立即持久化
            if len(self.metrics_buffer) > 100:
                await self._persist_metrics()
                
        except Exception as e:
            logger.error(f"记录指标失败: {e}")
    
    async def _check_alert_rules(self):
        """检查告警规则"""
        try:
            for rule in self.alert_rules.values():
                if not rule.enabled:
                    continue
                
                # 检查冷却时间
                if rule.rule_id in self.alert_cooldowns:
                    cooldown_end = self.alert_cooldowns[rule.rule_id]
                    if datetime.now() < cooldown_end:
                        continue
                
                # 获取最新指标值
                latest_value = await self._get_latest_metric_value(rule.metric_name)
                if latest_value is None:
                    continue
                
                # 检查条件
                if self._evaluate_condition(latest_value, rule.condition, rule.threshold):
                    await self._trigger_alert(rule, latest_value)
                    
        except Exception as e:
            logger.error(f"检查告警规则失败: {e}")
    
    def _evaluate_condition(self, value: float, condition: str, threshold: float) -> bool:
        """评估告警条件"""
        if condition == ">":
            return value > threshold
        elif condition == "<":
            return value < threshold
        elif condition == ">=":
            return value >= threshold
        elif condition == "<=":
            return value <= threshold
        elif condition == "==":
            return value == threshold
        elif condition == "!=":
            return value != threshold
        else:
            return False
    
    async def _trigger_alert(self, rule: AlertRule, current_value: float):
        """触发告警"""
        try:
            alert_id = f"alert_{rule.rule_id}_{int(time.time())}"
            
            message = rule.message_template.format(
                current_value=current_value,
                threshold=rule.threshold
            )
            
            alert = Alert(
                alert_id=alert_id,
                rule_id=rule.rule_id,
                metric_name=rule.metric_name,
                current_value=current_value,
                threshold=rule.threshold,
                alert_level=rule.alert_level,
                message=message
            )
            
            # 存储告警
            await self._store_alert(alert)
            
            # 发送通知
            await self._send_alert_notification(alert)
            
            # 设置冷却时间
            cooldown_end = datetime.now() + timedelta(minutes=rule.cooldown_minutes)
            self.alert_cooldowns[rule.rule_id] = cooldown_end
            
            logger.warning(f"🚨 告警触发: {alert.message}")
            
        except Exception as e:
            logger.error(f"触发告警失败: {e}")
    
    async def _send_alert_notification(self, alert: Alert):
        """发送告警通知"""
        try:
            # 这里可以集成各种通知方式：邮件、短信、钉钉、企业微信等
            # 目前只记录日志
            
            level_emoji = {
                "info": "ℹ️",
                "warning": "⚠️",
                "error": "❌",
                "critical": "🔥"
            }
            
            emoji = level_emoji.get(alert.alert_level, "📢")
            
            notification_message = f"{emoji} [{alert.alert_level.upper()}] {alert.message}"
            
            # 记录到日志
            if alert.alert_level == "critical":
                logger.critical(notification_message)
            elif alert.alert_level == "error":
                logger.error(notification_message)
            elif alert.alert_level == "warning":
                logger.warning(notification_message)
            else:
                logger.info(notification_message)
            
            # TODO: 集成实际的通知渠道
            
        except Exception as e:
            logger.error(f"发送告警通知失败: {e}")
    
    async def _get_latest_metric_value(self, metric_name: str) -> Optional[float]:
        """获取最新指标值"""
        try:
            # 先从缓冲区查找
            for metric in reversed(self.metrics_buffer):
                if metric.metric_name == metric_name:
                    return metric.value
            
            # 从数据库查找
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT value FROM metrics 
                    WHERE metric_name = ? 
                    ORDER BY timestamp DESC 
                    LIMIT 1
                ''', (metric_name,)) as cursor:
                    row = await cursor.fetchone()
                    return row[0] if row else None
                    
        except Exception as e:
            logger.error(f"获取最新指标值失败: {e}")
            return None
    
    async def _persist_metrics(self):
        """持久化指标数据"""
        if not self.metrics_buffer:
            return
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                for metric in self.metrics_buffer:
                    await db.execute('''
                        INSERT INTO metrics (metric_name, metric_type, value, unit, timestamp, tags)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        metric.metric_name,
                        metric.metric_type,
                        metric.value,
                        metric.unit,
                        metric.timestamp,
                        json.dumps(metric.tags, ensure_ascii=False)
                    ))
                await db.commit()
            
            logger.debug(f"持久化了{len(self.metrics_buffer)}个指标")
            self.metrics_buffer.clear()
            
        except Exception as e:
            logger.error(f"持久化指标失败: {e}")
    
    async def _store_alert(self, alert: Alert):
        """存储告警"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT INTO alerts (
                        alert_id, rule_id, metric_name, current_value, threshold,
                        alert_level, message, timestamp, resolved, resolved_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    alert.alert_id,
                    alert.rule_id,
                    alert.metric_name,
                    alert.current_value,
                    alert.threshold,
                    alert.alert_level,
                    alert.message,
                    alert.timestamp,
                    alert.resolved,
                    alert.resolved_time
                ))
                await db.commit()
                
        except Exception as e:
            logger.error(f"存储告警失败: {e}")
    
    async def _cleanup_old_data(self):
        """清理过期数据"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=self.monitoring_config["retention_days"])).isoformat()
            
            async with aiosqlite.connect(self.db_path) as db:
                # 清理过期指标
                await db.execute('DELETE FROM metrics WHERE timestamp < ?', (cutoff_date,))
                
                # 清理过期告警
                await db.execute('DELETE FROM alerts WHERE timestamp < ?', (cutoff_date,))
                
                await db.commit()
                
        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")
    
    async def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # 最近24小时的指标统计
                yesterday = (datetime.now() - timedelta(days=1)).isoformat()
                
                async with db.execute('''
                    SELECT metric_name, COUNT(*) as count, AVG(value) as avg_value, MAX(value) as max_value
                    FROM metrics 
                    WHERE timestamp >= ?
                    GROUP BY metric_name
                ''', (yesterday,)) as cursor:
                    metrics_stats = await cursor.fetchall()
                
                # 活跃告警统计
                async with db.execute('''
                    SELECT alert_level, COUNT(*) as count
                    FROM alerts 
                    WHERE timestamp >= ? AND resolved = 0
                    GROUP BY alert_level
                ''', (yesterday,)) as cursor:
                    alert_stats = await cursor.fetchall()
                
                return {
                    "metrics_stats": [
                        {
                            "metric_name": row[0],
                            "count": row[1],
                            "avg_value": row[2],
                            "max_value": row[3]
                        } for row in metrics_stats
                    ],
                    "alert_stats": dict(alert_stats),
                    "monitoring_status": "running" if self.is_running else "stopped",
                    "last_updated": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取指标摘要失败: {e}")
            return {}

# 全局实例
advanced_monitoring_service = AdvancedMonitoringService()

__all__ = ["AdvancedMonitoringService", "MetricData", "AlertRule", "Alert", "advanced_monitoring_service"]
