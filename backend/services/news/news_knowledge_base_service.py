from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻知识库服务
负责新闻数据的存储、检索和管理
"""

import asyncio
import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import sqlite3
import aiosqlite
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class NewsItem:
    """新闻项数据结构"""
    news_id: str
    title: str
    content: str
    source: str
    url: str
    timestamp: str
    sentiment_score: float
    sentiment_label: str
    importance_level: str
    related_symbols: List[str]
    tags: List[str]
    authority_score: float
    relevance_score: float
    created_time: str
    updated_time: str

@dataclass
class NewsAnalysis:
    """新闻分析结果"""
    news_id: str
    analysis_type: str
    analysis_result: Dict[str, Any]
    confidence_score: float
    analyzer_version: str
    created_time: str

class NewsKnowledgeBaseService:
    """新闻知识库服务"""
    
    def __init__(self, db_path: str = None):
        self.service_name = "NewsKnowledgeBaseService"
        self.version = "1.0.0"
        
        # 数据库路径
        if db_path is None:
            db_path = "backend/data/news_knowledge_base.db"
        
        self.db_path = db_path
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        logger.info(f" {self.service_name} v{self.version} 初始化完成")
        logger.info(f" 数据库路径: {self.db_path}")
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建新闻表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS news_items (
                    news_id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    content TEXT,
                    source TEXT,
                    url TEXT,
                    timestamp TEXT,
                    sentiment_score REAL,
                    sentiment_label TEXT,
                    importance_level TEXT,
                    related_symbols TEXT,  -- JSON格式存储
                    tags TEXT,            -- JSON格式存储
                    authority_score REAL,
                    relevance_score REAL,
                    created_time TEXT,
                    updated_time TEXT
                )
            ''')
            
            # 创建新闻分析表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS news_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    news_id TEXT,
                    analysis_type TEXT,
                    analysis_result TEXT,  -- JSON格式存储
                    confidence_score REAL,
                    analyzer_version TEXT,
                    created_time TEXT,
                    FOREIGN KEY (news_id) REFERENCES news_items (news_id)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_timestamp ON news_items (timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_source ON news_items (source)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_sentiment ON news_items (sentiment_label)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_news_id ON news_analysis (news_id)')
            
            conn.commit()
            conn.close()
            
            logger.info(" 新闻知识库数据表初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    async def store_news_item(self, news_item: NewsItem) -> bool:
        """存储新闻项"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT OR REPLACE INTO news_items (
                        news_id, title, content, source, url, timestamp,
                        sentiment_score, sentiment_label, importance_level,
                        related_symbols, tags, authority_score, relevance_score,
                        created_time, updated_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    news_item.news_id,
                    news_item.title,
                    news_item.content,
                    news_item.source,
                    news_item.url,
                    news_item.timestamp,
                    news_item.sentiment_score,
                    news_item.sentiment_label,
                    news_item.importance_level,
                    json.dumps(news_item.related_symbols, ensure_ascii=False),
                    json.dumps(news_item.tags, ensure_ascii=False),
                    news_item.authority_score,
                    news_item.relevance_score,
                    news_item.created_time,
                    news_item.updated_time
                ))
                await db.commit()
            
            logger.debug(f"新闻存储成功: {news_item.news_id}")
            return True
            
        except Exception as e:
            logger.error(f"新闻存储失败: {e}")
            return False
    
    async def store_news_analysis(self, analysis: NewsAnalysis) -> bool:
        """存储新闻分析结果"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT INTO news_analysis (
                        news_id, analysis_type, analysis_result,
                        confidence_score, analyzer_version, created_time
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    analysis.news_id,
                    analysis.analysis_type,
                    json.dumps(analysis.analysis_result, ensure_ascii=False),
                    analysis.confidence_score,
                    analysis.analyzer_version,
                    analysis.created_time
                ))
                await db.commit()
            
            logger.debug(f"新闻分析存储成功: {analysis.news_id}")
            return True
            
        except Exception as e:
            logger.error(f"新闻分析存储失败: {e}")
            return False
    
    async def get_news_by_id(self, news_id: str) -> Optional[NewsItem]:
        """根据ID获取新闻"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute(
                    'SELECT * FROM news_items WHERE news_id = ?', (news_id,)
                ) as cursor:
                    row = await cursor.fetchone()
                    
                    if row:
                        return self._row_to_news_item(row)
                    return None
                    
        except Exception as e:
            logger.error(f"获取新闻失败: {e}")
            return None
    
    async def search_news(self, 
                         keywords: List[str] = None,
                         sources: List[str] = None,
                         sentiment_labels: List[str] = None,
                         start_time: str = None,
                         end_time: str = None,
                         limit: int = 100) -> List[NewsItem]:
        """搜索新闻"""
        try:
            query = "SELECT * FROM news_items WHERE 1=1"
            params = []
            
            # 关键词搜索
            if keywords:
                keyword_conditions = []
                for keyword in keywords:
                    keyword_conditions.append("(title LIKE ? OR content LIKE ?)")
                    params.extend([f"%{keyword}%", f"%{keyword}%"])
                query += " AND (" + " OR ".join(keyword_conditions) + ")"
            
            # 来源过滤
            if sources:
                source_placeholders = ",".join(["?" for _ in sources])
                query += f" AND source IN ({source_placeholders})"
                params.extend(sources)
            
            # 情感标签过滤
            if sentiment_labels:
                sentiment_placeholders = ",".join(["?" for _ in sentiment_labels])
                query += f" AND sentiment_label IN ({sentiment_placeholders})"
                params.extend(sentiment_labels)
            
            # 时间范围过滤
            if start_time:
                query += " AND timestamp >= ?"
                params.append(start_time)
            
            if end_time:
                query += " AND timestamp <= ?"
                params.append(end_time)
            
            # 排序和限制
            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)
            
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute(query, params) as cursor:
                    rows = await cursor.fetchall()
                    return [self._row_to_news_item(row) for row in rows]
                    
        except Exception as e:
            logger.error(f"搜索新闻失败: {e}")
            return []
    
    async def get_latest_news(self, limit: int = 20) -> List[NewsItem]:
        """获取最新新闻"""
        return await self.search_news(limit=limit)
    
    async def get_news_by_symbol(self, symbol: str, limit: int = 50) -> List[NewsItem]:
        """获取特定股票相关新闻"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT * FROM news_items 
                    WHERE related_symbols LIKE ?
                    ORDER BY timestamp DESC 
                    LIMIT ?
                ''', (f"%{symbol}%", limit)) as cursor:
                    rows = await cursor.fetchall()
                    return [self._row_to_news_item(row) for row in rows]
                    
        except Exception as e:
            logger.error(f"获取股票新闻失败: {e}")
            return []
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # 总新闻数
                async with db.execute('SELECT COUNT(*) FROM news_items') as cursor:
                    total_news = (await cursor.fetchone())[0]
                
                # 今日新闻数
                today = datetime.now().strftime('%Y-%m-%d')
                async with db.execute(
                    'SELECT COUNT(*) FROM news_items WHERE timestamp >= ?', (today,)
                ) as cursor:
                    today_news = (await cursor.fetchone())[0]
                
                # 按来源统计
                async with db.execute('''
                    SELECT source, COUNT(*) as count 
                    FROM news_items 
                    GROUP BY source 
                    ORDER BY count DESC
                ''') as cursor:
                    source_stats = await cursor.fetchall()
                
                # 按情感标签统计
                async with db.execute('''
                    SELECT sentiment_label, COUNT(*) as count 
                    FROM news_items 
                    GROUP BY sentiment_label
                ''') as cursor:
                    sentiment_stats = await cursor.fetchall()
                
                return {
                    "total_news": total_news,
                    "today_news": today_news,
                    "source_distribution": dict(source_stats),
                    "sentiment_distribution": dict(sentiment_stats),
                    "last_updated": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    async def cleanup_old_news(self, days: int = 30) -> int:
        """清理旧新闻"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            async with aiosqlite.connect(self.db_path) as db:
                # 删除旧新闻
                await db.execute(
                    'DELETE FROM news_items WHERE timestamp < ?', (cutoff_date,)
                )
                
                # 删除相关分析
                await db.execute('''
                    DELETE FROM news_analysis 
                    WHERE news_id NOT IN (SELECT news_id FROM news_items)
                ''')
                
                deleted_count = db.total_changes
                await db.commit()
                
                logger.info(f"清理了{deleted_count}条{days}天前的旧新闻")
                return deleted_count
                
        except Exception as e:
            logger.error(f"清理旧新闻失败: {e}")
            return 0
    
    def _row_to_news_item(self, row) -> NewsItem:
        """将数据库行转换为NewsItem对象"""
        return NewsItem(
            news_id=row[0],
            title=row[1],
            content=row[2] or "",
            source=row[3] or "",
            url=row[4] or "",
            timestamp=row[5] or "",
            sentiment_score=row[6] or 0.0,
            sentiment_label=row[7] or "neutral",
            importance_level=row[8] or "medium",
            related_symbols=json.loads(row[9]) if row[9] else [],
            tags=json.loads(row[10]) if row[10] else [],
            authority_score=row[11] or 0.0,
            relevance_score=row[12] or 0.0,
            created_time=row[13] or "",
            updated_time=row[14] or ""
        )

# 全局实例
news_knowledge_base_service = NewsKnowledgeBaseService()

__all__ = ["NewsKnowledgeBaseService", "NewsItem", "NewsAnalysis", "news_knowledge_base_service"]
