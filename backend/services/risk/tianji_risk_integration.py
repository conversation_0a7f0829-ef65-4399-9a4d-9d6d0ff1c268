from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天玑星风险库集成服务
为开阳星提供风险参考数据
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import asdict

logger = logging.getLogger(__name__)

class TianjiRiskIntegrationService:
    """天玑星风险库集成服务"""
    
    def __init__(self):
        self.service_name = "TianjiRiskIntegrationService"
        self.version = "1.0.0"
        self.tianji_risk_service = None
        self.cache = {}
        self.cache_ttl = 300  # 5分钟缓存
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    async def _get_tianji_risk_service(self):
        """获取天玑星风险知识库服务"""
        if self.tianji_risk_service is None:
            try:
                # 尝试连接真实天玑星服务
                from backend.roles.tianji_star.services.risk_knowledge_base_service import risk_knowledge_base_service
                self.tianji_risk_service = risk_knowledge_base_service
                logger.info("✅ 连接天玑星风险库成功")
            except Exception as e:
                logger.warning(f"⚠️ 连接天玑星风险库失败: {e}")
                # 使用内置风险评估服务
                self.tianji_risk_service = self._create_builtin_risk_service()
                logger.info("✅ 使用内置风险评估服务")

        return self.tianji_risk_service

    def _create_builtin_risk_service(self):
        """创建内置风险评估服务"""
        return BuiltinRiskService()
    
    async def get_stock_risk_analysis(self, stock_code: str, risk_levels: List[str] = None) -> Dict[str, Any]:
        """获取股票风险分析"""
        try:
            cache_key = f"stock_risk_{stock_code}_{risk_levels}"
            
            # 检查缓存
            if cache_key in self.cache:
                cache_data = self.cache[cache_key]
                if datetime.now() - cache_data["timestamp"] < timedelta(seconds=self.cache_ttl):
                    return cache_data["data"]
            
            tianji_service = await self._get_tianji_risk_service()
            if not tianji_service:
                return self._get_default_risk_analysis(stock_code)
            
            # 转换风险等级
            tianji_risk_levels = None
            if risk_levels:
                from backend.roles.tianji_star.models.risk_models import RiskLevel
                tianji_risk_levels = []
                for level in risk_levels:
                    if hasattr(RiskLevel, level.upper()):
                        tianji_risk_levels.append(getattr(RiskLevel, level.upper()))
            
            # 获取增强风险分析
            enhanced_risks = await tianji_service.get_enhanced_stock_risks(stock_code, tianji_risk_levels)
            
            # 转换为开阳星格式
            risk_analysis = {
                "stock_code": stock_code,
                "total_risks": len(enhanced_risks),
                "risk_summary": self._generate_risk_summary(enhanced_risks),
                "risk_details": [self._convert_risk_item(risk) for risk in enhanced_risks],
                "risk_score": self._calculate_overall_risk_score(enhanced_risks),
                "risk_level": self._determine_overall_risk_level(enhanced_risks),
                "last_updated": datetime.now().isoformat(),
                "data_source": "天玑星风险库"
            }
            
            # 缓存结果
            self.cache[cache_key] = {
                "data": risk_analysis,
                "timestamp": datetime.now()
            }
            
            return risk_analysis
            
        except Exception as e:
            logger.error(f"获取股票风险分析失败 {stock_code}: {e}")
            return self._get_default_risk_analysis(stock_code)
    
    async def get_market_risk_overview(self, limit: int = 50) -> Dict[str, Any]:
        """获取市场风险概览"""
        try:
            tianji_service = await self._get_tianji_risk_service()
            if not tianji_service:
                return self._get_default_market_risk()
            
            # 获取最新风险分析
            all_risks = await tianji_service.get_all_risks(limit=limit)
            
            # 按风险等级分类
            risk_by_level = {"S": [], "A": [], "B": [], "C": [], "D": []}
            risk_by_type = {}
            affected_stocks = set()
            
            for risk in all_risks:
                level = risk.risk_level.name
                risk_by_level[level].append(risk)
                
                risk_type = risk.risk_type.name
                if risk_type not in risk_by_type:
                    risk_by_type[risk_type] = []
                risk_by_type[risk_type].append(risk)
                
                affected_stocks.add(risk.target)
                affected_stocks.update(risk.related_stocks)
            
            # 生成市场风险概览
            market_overview = {
                "total_risks": len(all_risks),
                "risk_distribution": {
                    level: len(risks) for level, risks in risk_by_level.items()
                },
                "risk_types": {
                    risk_type: len(risks) for risk_type, risks in risk_by_type.items()
                },
                "affected_stocks_count": len(affected_stocks),
                "high_risk_stocks": list(affected_stocks)[:20],  # 前20只高风险股票
                "market_risk_score": self._calculate_market_risk_score(all_risks),
                "risk_trends": self._analyze_risk_trends(all_risks),
                "last_updated": datetime.now().isoformat(),
                "data_source": "天玑星风险库"
            }
            
            return market_overview
            
        except Exception as e:
            logger.error(f"获取市场风险概览失败: {e}")
            return self._get_default_market_risk()
    
    async def get_risk_alerts(self, severity: str = "all") -> List[Dict[str, Any]]:
        """获取风险预警"""
        try:
            tianji_service = await self._get_tianji_risk_service()
            if not tianji_service:
                return []
            
            # 获取最新风险
            recent_risks = await tianji_service.get_all_risks(limit=100)
            
            # 筛选高风险项目
            alerts = []
            for risk in recent_risks:
                if severity == "all" or risk.risk_level.name in ["S", "A"]:
                    alert = {
                        "id": risk.risk_id,
                        "level": risk.risk_level.name.lower(),
                        "title": risk.risk_title,
                        "description": risk.risk_description[:200] + "..." if len(risk.risk_description) > 200 else risk.risk_description,
                        "target": risk.target,
                        "risk_score": risk.risk_score,
                        "timestamp": risk.created_time.isoformat(),
                        "status": "active" if not risk.is_expired() else "expired",
                        "factors": risk.risk_factors[:3],  # 前3个风险因子
                        "confidence": risk.confidence
                    }
                    alerts.append(alert)
            
            # 按风险分数排序
            alerts.sort(key=lambda x: x["risk_score"], reverse=True)
            
            return alerts[:50]  # 返回前50个预警
            
        except Exception as e:
            logger.error(f"获取风险预警失败: {e}")
            return []
    
    def _convert_risk_item(self, risk_item) -> Dict[str, Any]:
        """转换风险项目为开阳星格式"""
        return {
            "risk_id": risk_item.risk_id,
            "risk_level": risk_item.risk_level.name,
            "risk_type": risk_item.risk_type.name,
            "risk_score": risk_item.risk_score,
            "title": risk_item.risk_title,
            "description": risk_item.risk_description,
            "factors": risk_item.risk_factors,
            "impact_assessment": risk_item.impact_assessment,
            "mitigation_suggestions": risk_item.mitigation_suggestions,
            "confidence": risk_item.confidence,
            "created_time": risk_item.created_time.isoformat(),
            "expires_at": risk_item.expires_at.isoformat() if risk_item.expires_at else None,
            "related_stocks": risk_item.related_stocks,
            "is_expired": risk_item.is_expired()
        }
    
    def _generate_risk_summary(self, risks: List) -> str:
        """生成风险摘要"""
        if not risks:
            return "暂无风险"
        
        high_risks = [r for r in risks if r.risk_level.name in ["S", "A"]]
        medium_risks = [r for r in risks if r.risk_level.name == "B"]
        low_risks = [r for r in risks if r.risk_level.name in ["C", "D"]]
        
        summary_parts = []
        if high_risks:
            summary_parts.append(f"{len(high_risks)}个高风险")
        if medium_risks:
            summary_parts.append(f"{len(medium_risks)}个中等风险")
        if low_risks:
            summary_parts.append(f"{len(low_risks)}个低风险")
        
        return "，".join(summary_parts) if summary_parts else "风险较低"
    
    def _calculate_overall_risk_score(self, risks: List) -> float:
        """计算整体风险评分"""
        if not risks:
            return 0.0
        
        # 加权平均，高等级风险权重更大
        total_weighted_score = 0
        total_weight = 0
        
        for risk in risks:
            weight = {
                "S": 5.0,
                "A": 3.0,
                "B": 2.0,
                "C": 1.5,
                "D": 1.0
            }.get(risk.risk_level.name, 1.0)
            
            total_weighted_score += risk.risk_score * weight
            total_weight += weight
        
        return total_weighted_score / total_weight if total_weight > 0 else 0.0
    
    def _determine_overall_risk_level(self, risks: List) -> str:
        """确定整体风险等级"""
        if not risks:
            return "低风险"
        
        # 如果有S级风险，整体为高风险
        if any(r.risk_level.name == "S" for r in risks):
            return "极高风险"
        
        # 如果有A级风险，整体为高风险
        if any(r.risk_level.name == "A" for r in risks):
            return "高风险"
        
        # 如果有多个B级风险，整体为中高风险
        b_risks = [r for r in risks if r.risk_level.name == "B"]
        if len(b_risks) >= 3:
            return "中高风险"
        elif len(b_risks) >= 1:
            return "中等风险"
        
        return "低风险"
    
    def _calculate_market_risk_score(self, risks: List) -> float:
        """计算市场风险评分"""
        if not risks:
            return 0.0
        
        # 基于风险数量和等级计算市场风险
        risk_weights = {"S": 10, "A": 5, "B": 3, "C": 2, "D": 1}
        total_score = sum(risk_weights.get(r.risk_level.name, 1) for r in risks)
        
        # 归一化到0-100
        max_possible = len(risks) * 10
        return min(100, (total_score / max_possible * 100)) if max_possible > 0 else 0
    
    def _analyze_risk_trends(self, risks: List) -> Dict[str, Any]:
        """分析风险趋势"""
        if not risks:
            return {"trend": "stable", "change": 0}
        
        # 按时间分组
        now = datetime.now()
        recent_risks = [r for r in risks if (now - r.created_time).days <= 1]
        older_risks = [r for r in risks if 1 < (now - r.created_time).days <= 7]
        
        recent_count = len(recent_risks)
        older_count = len(older_risks)
        
        if recent_count > older_count * 1.5:
            return {"trend": "increasing", "change": recent_count - older_count}
        elif recent_count < older_count * 0.5:
            return {"trend": "decreasing", "change": recent_count - older_count}
        else:
            return {"trend": "stable", "change": 0}
    
    def _get_default_risk_analysis(self, stock_code: str) -> Dict[str, Any]:
        """获取默认风险分析（天玑星不可用时）"""
        return {
            "stock_code": stock_code,
            "total_risks": 0,
            "risk_summary": "风险数据不可用",
            "risk_details": [],
            "risk_score": 50.0,  # 中性风险
            "risk_level": "中等风险",
            "last_updated": datetime.now().isoformat(),
            "data_source": "默认风险评估"
        }
    
    def _get_default_market_risk(self) -> Dict[str, Any]:
        """获取默认市场风险（天玑星不可用时）"""
        return {
            "total_risks": 0,
            "risk_distribution": {"S": 0, "A": 0, "B": 0, "C": 0, "D": 0},
            "risk_types": {},
            "affected_stocks_count": 0,
            "high_risk_stocks": [],
            "market_risk_score": 50.0,
            "risk_trends": {"trend": "unknown", "change": 0},
            "last_updated": datetime.now().isoformat(),
            "data_source": "默认风险评估"
        }

class BuiltinRiskService:
    """内置风险评估服务（当天玑星不可用时使用）"""

    def __init__(self):
        self.db_path = get_database_path("stock_database")

    async def get_enhanced_stock_risks(self, stock_code: str, risk_levels: List = None):
        """获取股票风险（基于真实数据分析）"""
        try:
            # 从数据库获取股票数据进行风险分析
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取最近的价格数据
            cursor.execute("""
                SELECT close_price, change_percent, volume, turnover_rate
                FROM daily_data
                WHERE stock_code = ?
                ORDER BY trade_date DESC
                LIMIT 30
            """, (stock_code,))

            price_data = cursor.fetchall()
            conn.close()

            if not price_data:
                return []

            # 基于真实数据生成风险评估
            risks = []

            # 价格波动风险
            price_changes = [row[1] for row in price_data if row[1] is not None]
            if price_changes:
                volatility = sum(abs(change) for change in price_changes) / len(price_changes)
                if volatility > 5:
                    risks.append(self._create_risk_item(
                        "PRICE_VOLATILITY",
                        "A" if volatility > 8 else "B",
                        f"价格波动风险",
                        f"近期价格波动较大，平均波动幅度{volatility:.2f}%",
                        min(90, volatility * 10)
                    ))

            # 成交量风险
            volumes = [row[2] for row in price_data if row[2] is not None and row[2] > 0]
            if volumes and len(volumes) > 5:
                recent_vol = sum(volumes[:5]) / 5
                historical_vol = sum(volumes[5:]) / len(volumes[5:]) if len(volumes) > 5 else recent_vol

                if recent_vol < historical_vol * 0.5:
                    risks.append(self._create_risk_item(
                        "LIQUIDITY_RISK",
                        "B",
                        "流动性风险",
                        "近期成交量显著下降，可能存在流动性风险",
                        60
                    ))

            # 技术指标风险
            conn2 = sqlite3.connect(self.db_path)
            cursor2 = conn2.cursor()
            cursor2.execute("""
                SELECT rsi, macd, kdj_k
                FROM technical_indicators
                WHERE stock_code = ?
                ORDER BY trade_date DESC
                LIMIT 1
            """, (stock_code,))

            tech_data = cursor2.fetchone()
            conn2.close()

            if tech_data:
                rsi, macd, kdj_k = tech_data

                # RSI超买超卖风险
                if rsi and (rsi > 80 or rsi < 20):
                    risks.append(self._create_risk_item(
                        "TECHNICAL_RISK",
                        "B" if rsi > 80 else "C",
                        "技术指标风险",
                        f"RSI指标显示{'超买' if rsi > 80 else '超卖'}状态 (RSI: {rsi:.1f})",
                        70 if rsi > 80 else 40
                    ))

            return risks

        except Exception as e:
            logger.error(f"内置风险评估失败 {stock_code}: {e}")
            return []

    async def get_all_risks(self, limit: int = 50):
        """获取所有风险（基于数据库分析）"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取有数据的股票
            cursor.execute("""
                SELECT DISTINCT stock_code
                FROM daily_data
                WHERE data_source LIKE 'eastmoney%'
                ORDER BY stock_code
                LIMIT ?
            """, (limit,))

            stock_codes = [row[0] for row in cursor.fetchall()]
            conn.close()

            all_risks = []
            for stock_code in stock_codes[:20]:  # 限制分析数量
                stock_risks = await self.get_enhanced_stock_risks(stock_code)
                all_risks.extend(stock_risks)

            return all_risks

        except Exception as e:
            logger.error(f"获取所有风险失败: {e}")
            return []

    def _create_risk_item(self, risk_type: str, level: str, title: str, description: str, score: float):
        """创建风险项目"""
        from dataclasses import dataclass
        from datetime import datetime, timedelta

        @dataclass
        class RiskLevel:
            name: str

        @dataclass
        class RiskType:
            name: str

        @dataclass
        class RiskItem:
            risk_id: str
            risk_level: RiskLevel
            risk_type: RiskType
            risk_title: str
            risk_description: str
            risk_score: float
            risk_factors: list
            impact_assessment: str
            mitigation_suggestions: list
            confidence: float
            created_time: datetime
            expires_at: datetime
            target: str
            related_stocks: list

            def is_expired(self):
                return datetime.now() > self.expires_at

        return RiskItem(
            risk_id=f"risk_{int(datetime.now().timestamp())}",
            risk_level=RiskLevel(level),
            risk_type=RiskType(risk_type),
            risk_title=title,
            risk_description=description,
            risk_score=score,
            risk_factors=[risk_type.lower()],
            impact_assessment=f"风险评分: {score}/100",
            mitigation_suggestions=["密切关注", "适当减仓"],
            confidence=0.8,
            created_time=datetime.now(),
            expires_at=datetime.now() + timedelta(days=7),
            target="market",
            related_stocks=[]
        )

# 全局服务实例
tianji_risk_integration_service = TianjiRiskIntegrationService()
