from backend.config.database_config import get_database_path
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星智能数据收集管理器
自动收集、补全、管理历史股票数据
"""

import sqlite3
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import time
import sys
import os

# 添加backend路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入JQData服务
from jqdata_real_data_service import jqdata_service

logger = logging.getLogger(__name__)

class IntelligentDataCollector:
    """瑶光星智能数据收集管理器"""
    
    def __init__(self):
        self.db_path = get_database_path("stock_database")
        self.jqdata_service = jqdata_service
        
        # 检查JQData服务
        if not self.jqdata_service.is_available():
            logger.warning("JQData服务不可用，部分功能将受限")
        
        logger.info("瑶光星智能数据收集管理器初始化完成")
    
    def analyze_data_status(self) -> Dict[str, Any]:
        """分析当前数据收集状态"""
        try:
            if not os.path.exists(self.db_path):
                return {
                    "status": "no_database",
                    "message": "数据库文件不存在",
                    "total_records": 0,
                    "stocks_count": 0,
                    "date_range": None,
                    "missing_stocks": [],
                    "coverage": 0.0
                }
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计总记录数
            cursor.execute("SELECT COUNT(*) FROM daily_data")
            total_records = cursor.fetchone()[0]
            
            # 统计股票数量
            cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data")
            stocks_count = cursor.fetchone()[0]
            
            # 获取日期范围
            cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM daily_data")
            date_range = cursor.fetchone()
            
            # 获取已收集的股票列表
            cursor.execute("SELECT DISTINCT stock_code FROM daily_data")
            collected_stocks = set([row[0] for row in cursor.fetchall()])
            
            conn.close()
            
            # 获取应该收集的股票列表
            all_stocks = self._get_target_stock_list()
            all_stock_codes = set([stock['code'] for stock in all_stocks])
            
            # 计算缺失的股票
            missing_stocks = list(all_stock_codes - collected_stocks)
            coverage = len(collected_stocks) / len(all_stock_codes) if all_stock_codes else 0
            
            return {
                "status": "analyzed",
                "total_records": total_records,
                "stocks_count": stocks_count,
                "target_stocks_count": len(all_stock_codes),
                "date_range": {
                    "start": date_range[0] if date_range[0] else None,
                    "end": date_range[1] if date_range[1] else None
                },
                "missing_stocks": missing_stocks[:20],  # 只显示前20个缺失股票
                "missing_count": len(missing_stocks),
                "coverage": round(coverage * 100, 2),
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"分析数据状态失败: {e}")
            return {
                "status": "error",
                "message": f"分析失败: {e}",
                "total_records": 0,
                "stocks_count": 0
            }
    
    def analyze_date_gaps(self, target_start: str, target_end: str) -> Dict[str, Any]:
        """分析指定日期范围内的数据缺口"""
        try:
            if not os.path.exists(self.db_path):
                return {
                    "status": "no_database",
                    "gaps": [],
                    "missing_dates": [],
                    "coverage": 0.0
                }
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取目标日期范围内的数据
            cursor.execute("""
                SELECT DISTINCT trade_date 
                FROM daily_data 
                WHERE trade_date BETWEEN ? AND ? 
                ORDER BY trade_date
            """, (target_start, target_end))
            
            existing_dates = set([row[0] for row in cursor.fetchall()])
            
            # 生成目标日期范围（工作日）
            start_date = datetime.strptime(target_start, '%Y-%m-%d')
            end_date = datetime.strptime(target_end, '%Y-%m-%d')
            
            target_dates = set()
            current_date = start_date
            while current_date <= end_date:
                # 只包含工作日（周一到周五）
                if current_date.weekday() < 5:
                    target_dates.add(current_date.strftime('%Y-%m-%d'))
                current_date += timedelta(days=1)
            
            # 计算缺失的日期
            missing_dates = list(target_dates - existing_dates)
            coverage = len(existing_dates) / len(target_dates) if target_dates else 0
            
            conn.close()
            
            return {
                "status": "analyzed",
                "target_period": f"{target_start} 至 {target_end}",
                "target_dates_count": len(target_dates),
                "existing_dates_count": len(existing_dates),
                "missing_dates": sorted(missing_dates)[:10],  # 只显示前10个缺失日期
                "missing_dates_count": len(missing_dates),
                "coverage": round(coverage * 100, 2),
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"分析日期缺口失败: {e}")
            return {
                "status": "error",
                "message": f"分析失败: {e}"
            }
    
    def auto_collect_missing_data(self, target_start: str, target_end: str, max_stocks: int = 100) -> Dict[str, Any]:
        """自动收集缺失的数据"""
        try:
            if not self.jqdata_service.is_available():
                return {
                    "status": "service_unavailable",
                    "message": "JQData服务不可用，无法收集数据"
                }
            
            # 分析当前状态
            status = self.analyze_data_status()
            missing_stocks = status.get("missing_stocks", [])
            
            if not missing_stocks:
                return {
                    "status": "complete",
                    "message": "所有股票数据已收集完成",
                    "collected_count": 0
                }
            
            # 限制收集数量
            stocks_to_collect = missing_stocks[:max_stocks]
            
            logger.info(f"开始自动收集 {len(stocks_to_collect)} 只股票的数据")
            
            # 创建表结构
            self._ensure_database_structure()
            
            collected_count = 0
            failed_count = 0
            
            for i, stock_code in enumerate(stocks_to_collect):
                try:
                    logger.info(f"[{i+1}/{len(stocks_to_collect)}] 收集 {stock_code}")
                    
                    # 收集数据
                    if self._collect_single_stock(stock_code, target_start, target_end):
                        collected_count += 1
                    else:
                        failed_count += 1
                    
                    # 控制请求频率
                    time.sleep(0.2)
                    
                    # 每10只股票检查一次连接状态
                    if (i + 1) % 10 == 0:
                        if not self.jqdata_service.is_available():
                            logger.warning("JQData连接中断，停止收集")
                            break
                
                except Exception as e:
                    logger.error(f"收集 {stock_code} 时出错: {e}")
                    failed_count += 1
                    continue
            
            return {
                "status": "completed",
                "target_period": f"{target_start} 至 {target_end}",
                "total_attempted": len(stocks_to_collect),
                "collected_count": collected_count,
                "failed_count": failed_count,
                "success_rate": round(collected_count / len(stocks_to_collect) * 100, 2) if stocks_to_collect else 0,
                "completion_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"自动收集数据失败: {e}")
            return {
                "status": "error",
                "message": f"收集失败: {e}"
            }
    
    def _get_target_stock_list(self) -> List[Dict[str, Any]]:
        """获取目标股票列表"""
        try:
            if self.jqdata_service.is_available():
                stocks = self.jqdata_service.get_all_securities('stock')
                # 过滤A股股票
                a_stocks = []
                for stock in stocks:
                    code = stock['code']
                    if (code.endswith('.XSHE') and (code.startswith('000') or code.startswith('002') or code.startswith('300'))) or \
                       (code.endswith('.XSHG') and (code.startswith('600') or code.startswith('601') or code.startswith('603') or code.startswith('688'))):
                        a_stocks.append(stock)
                return a_stocks
            else:
                # 如果JQData不可用，返回空列表
                return []
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    def _ensure_database_structure(self):
        """确保数据库结构存在"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open_price REAL,
                    close_price REAL,
                    high_price REAL,
                    low_price REAL,
                    volume INTEGER,
                    amount REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, trade_date)
                )
            """)
            
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_daily_data_stock_date ON daily_data(stock_code, trade_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_daily_data_date ON daily_data(trade_date)")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"创建数据库结构失败: {e}")
            raise
    
    def _collect_single_stock(self, stock_code: str, start_date: str, end_date: str) -> bool:
        """收集单只股票的历史数据"""
        try:
            # 获取历史数据
            historical_data = self.jqdata_service.get_historical_data(stock_code, start_date, end_date)
            
            if not historical_data.get('success') or not historical_data.get('data'):
                return False
            
            data_list = historical_data['data']
            
            # 插入数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            inserted_count = 0
            for data in data_list:
                try:
                    cursor.execute("""
                        INSERT OR REPLACE INTO daily_data 
                        (stock_code, trade_date, open_price, close_price, high_price, low_price, volume, amount, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        stock_code,
                        data['date'],
                        data['open'],
                        data['close'],
                        data['high'],
                        data['low'],
                        data['volume'],
                        data['amount'],
                        datetime.now().isoformat()
                    ))
                    inserted_count += 1
                except Exception as e:
                    logger.warning(f"{stock_code} {data['date']} 插入失败: {e}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"{stock_code}: 成功插入 {inserted_count} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"收集 {stock_code} 数据失败: {e}")
            return False

# 创建全局实例
intelligent_collector = IntelligentDataCollector()

def test_intelligent_collector():
    """测试智能数据收集器"""
    print("🔍 测试瑶光星智能数据收集管理器...")
    
    # 分析当前状态
    status = intelligent_collector.analyze_data_status()
    print(f" 数据状态: {status}")
    
    # 分析日期缺口
    gaps = intelligent_collector.analyze_date_gaps("2024-03-01", "2024-12-31")
    print(f"📅 日期缺口: {gaps}")

if __name__ == "__main__":
    test_intelligent_collector()
