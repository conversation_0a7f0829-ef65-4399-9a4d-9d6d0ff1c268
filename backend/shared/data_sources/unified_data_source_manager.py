#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据源管理器
按优先级管理所有股票数据API：东方财富 > 新浪财经 > 腾讯财经 > 其他
"""

import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json

logger = logging.getLogger(__name__)

class UnifiedDataSourceManager:
    """统一数据源管理器"""
    
    def __init__(self):
        self.service_name = "UnifiedDataSourceManager"
        self.version = "1.0.0"
        
        # 数据源优先级配置（按优先级排序）
        self.data_source_priority = [
            "eastmoney",      # 东方财富 - 第一优先级
            "tencent",        # 腾讯财经 - 第二优先级
            "baidu",          # 百度财经 - 第三优先级
            "akshare"         # AkShare - 最后优先级
        ]
        
        # 数据源配置
        self.data_sources = {
            "eastmoney": {
                "name": "东方财富",
                "base_url": "http://push2.eastmoney.com",
                "enabled": True,
                "timeout": 10,
                "retry_count": 3
            },
            "sina": {
                "name": "新浪财经",
#                 "base_url": "https://hq.sinajs.cn",  # 已禁用新浪API - 超时问题
                "enabled": False,  # 完全禁用新浪API
                "timeout": 8,
                "retry_count": 2
            },
            "tencent": {
                "name": "腾讯财经",
                "base_url": "https://qt.gtimg.cn",
                "enabled": True,
                "timeout": 8,
                "retry_count": 2
            },
            "baidu": {
                "name": "百度财经",
                "base_url": "https://gupiao.baidu.com",
                "enabled": True,
                "timeout": 8,
                "retry_count": 2
            },
            "akshare": {
                "name": "AkShare",
                "base_url": "local",
                "enabled": True,
                "timeout": 15,
                "retry_count": 1
            }
        }
        
        # 会话管理
        self.session = None
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "source_usage": {source: 0 for source in self.data_source_priority}
        }
        
        logger.info(f" {self.service_name} v{self.version} 初始化完成")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def get_stock_data(self, symbol: str, data_type: str = "realtime") -> Dict[str, Any]:
        """
        获取股票数据 - 按优先级尝试各数据源
        
        Args:
            symbol: 股票代码
            data_type: 数据类型 (realtime/historical/news)
        """
        logger.info(f" 获取股票数据: {symbol} - {data_type}")
        
        self.stats["total_requests"] += 1
        
        # 按优先级尝试各数据源
        for source_name in self.data_source_priority:
            source_config = self.data_sources[source_name]
            
            if not source_config["enabled"]:
                continue
            
            try:
                logger.debug(f"🔄 尝试数据源: {source_config['name']}")
                
                # 根据数据源调用相应方法
                if source_name == "eastmoney":
                    result = await self._get_eastmoney_data(symbol, data_type)
                elif source_name == "sina":
                    result = await self._get_sina_data(symbol, data_type)
                elif source_name == "tencent":
                    result = await self._get_tencent_data(symbol, data_type)
                elif source_name == "baidu":
                    result = await self._get_baidu_data(symbol, data_type)
                elif source_name == "akshare":
                    result = await self._get_akshare_data(symbol, data_type)
                else:
                    continue
                
                if result.get("success"):
                    self.stats["successful_requests"] += 1
                    self.stats["source_usage"][source_name] += 1
                    
                    result["data_source"] = source_config["name"]
                    result["source_priority"] = self.data_source_priority.index(source_name) + 1
                    
                    logger.info(f" 数据获取成功: {source_config['name']} - {symbol}")
                    return result
                
            except Exception as e:
                logger.warning(f"⚠️ 数据源 {source_config['name']} 失败: {e}")
                continue
        
        # 所有数据源都失败
        self.stats["failed_requests"] += 1
        logger.error(f" 所有数据源获取失败: {symbol}")
        
        return {
            "success": False,
            "symbol": symbol,
            "data_type": data_type,
            "error": "所有数据源均不可用",
            "attempted_sources": [self.data_sources[s]["name"] for s in self.data_source_priority if self.data_sources[s]["enabled"]]
        }
    
    async def _get_eastmoney_data(self, symbol: str, data_type: str) -> Dict[str, Any]:
        """获取东方财富数据 - 第一优先级"""
        try:
            # 导入东方财富API
            from backend.services.data.eastmoney_api import EastmoneyAPI
            
            eastmoney_api = EastmoneyAPI()
            
            if data_type == "realtime":
                # 获取实时数据
                result = await eastmoney_api.get_realtime_data(symbol)
            elif data_type == "historical":
                # 获取历史数据
                result = await eastmoney_api.get_historical_data(symbol, days=30)
            elif data_type == "news":
                # 获取新闻数据
                result = await eastmoney_api.get_stock_news(symbol, limit=10)
            else:
                result = {"success": False, "error": f"不支持的数据类型: {data_type}"}
            
            return result
            
        except Exception as e:
            logger.error(f"东方财富API调用失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _get_sina_data(self, symbol: str, data_type: str) -> Dict[str, Any]:
        """获取新浪财经数据 - 已禁用"""
        logger.warning("新浪API已禁用 - 超时问题")
        return {
            "success": False,
            "error": "新浪API已禁用",
            "data": None
        }
    
    async def _get_tencent_data(self, symbol: str, data_type: str) -> Dict[str, Any]:
        """获取腾讯财经数据 - 第三优先级"""
        try:
            if data_type == "realtime":
                # 腾讯实时数据API
                tencent_symbol = self._convert_symbol_for_tencent(symbol)
                url = f"https://qt.gtimg.cn/q={tencent_symbol}"
                
                async with self.session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        return self._parse_tencent_realtime_data(content, symbol)
            
            return {"success": False, "error": f"腾讯财经不支持数据类型: {data_type}"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _get_baidu_data(self, symbol: str, data_type: str) -> Dict[str, Any]:
        """获取百度财经数据 - 第四优先级"""
        try:
            # 百度财经API实现
            return {"success": False, "error": "百度财经API暂未实现"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _get_akshare_data(self, symbol: str, data_type: str) -> Dict[str, Any]:
        """获取AkShare数据 - 最后优先级"""
        try:
            import akshare as ak
            
            if data_type == "realtime":
                # AkShare实时数据
                df = ak.stock_zh_a_spot_em()
                stock_data = df[df['代码'] == symbol]
                
                if not stock_data.empty:
                    return {
                        "success": True,
                        "data": stock_data.to_dict('records')[0],
                        "symbol": symbol
                    }
            
            return {"success": False, "error": f"AkShare未找到数据: {symbol}"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _convert_symbol_for_sina(self, symbol: str) -> str:
        """转换股票代码为新浪格式"""
        if symbol.startswith('6'):
            return f"sh{symbol}"
        elif symbol.startswith(('0', '3')):
            return f"sz{symbol}"
        return symbol
    
    def _convert_symbol_for_tencent(self, symbol: str) -> str:
        """转换股票代码为腾讯格式"""
        if symbol.startswith('6'):
            return f"sh{symbol}"
        elif symbol.startswith(('0', '3')):
            return f"sz{symbol}"
        return symbol
    
    def _parse_sina_realtime_data(self, content: str, symbol: str) -> Dict[str, Any]:
        """解析新浪实时数据"""
        try:
            # 解析新浪返回的数据格式
            if 'var hq_str_' in content:
                data_part = content.split('="')[1].split('";')[0]
                fields = data_part.split(',')
                
                if len(fields) >= 32:
                    return {
                        "success": True,
                        "symbol": symbol,
                        "data": {
                            "name": fields[0],
                            "open": float(fields[1]) if fields[1] else 0,
                            "close": float(fields[2]) if fields[2] else 0,
                            "current": float(fields[3]) if fields[3] else 0,
                            "high": float(fields[4]) if fields[4] else 0,
                            "low": float(fields[5]) if fields[5] else 0,
                            "volume": int(fields[8]) if fields[8] else 0,
                            "amount": float(fields[9]) if fields[9] else 0,
                            "date": fields[30],
                            "time": fields[31]
                        }
                    }
            
            return {"success": False, "error": "新浪数据解析失败"}
            
        except Exception as e:
            return {"success": False, "error": f"新浪数据解析错误: {e}"}
    
    def _parse_tencent_realtime_data(self, content: str, symbol: str) -> Dict[str, Any]:
        """解析腾讯实时数据"""
        try:
            # 解析腾讯返回的数据格式
            if 'v_' in content:
                data_part = content.split('="')[1].split('";')[0]
                fields = data_part.split('~')
                
                if len(fields) >= 50:
                    return {
                        "success": True,
                        "symbol": symbol,
                        "data": {
                            "name": fields[1],
                            "current": float(fields[3]) if fields[3] else 0,
                            "close": float(fields[4]) if fields[4] else 0,
                            "open": float(fields[5]) if fields[5] else 0,
                            "high": float(fields[33]) if fields[33] else 0,
                            "low": float(fields[34]) if fields[34] else 0,
                            "volume": int(fields[6]) if fields[6] else 0,
                            "amount": float(fields[37]) if fields[37] else 0
                        }
                    }
            
            return {"success": False, "error": "腾讯数据解析失败"}
            
        except Exception as e:
            return {"success": False, "error": f"腾讯数据解析错误: {e}"}
    
    async def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "service_name": self.service_name,
            "version": self.version,
            "data_source_priority": self.data_source_priority,
            "enabled_sources": [name for name, config in self.data_sources.items() if config["enabled"]],
            "stats": self.stats,
            "success_rate": self.stats["successful_requests"] / max(self.stats["total_requests"], 1) * 100
        }

# 全局实例
unified_data_source_manager = UnifiedDataSourceManager()

__all__ = ["UnifiedDataSourceManager", "unified_data_source_manager"]
