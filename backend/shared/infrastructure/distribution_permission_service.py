#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三级分销权限验证系统 - 真实完整实现
基于JWT分销系统的细粒度权限控制
"""

import logging
from typing import Dict, Any, List, Optional, Set, Callable
from dataclasses import dataclass
from enum import Enum
from functools import wraps
import asyncio
from datetime import datetime

from .jwt_distribution_service import JWTDistributionService, DistributionLevel

logger = logging.getLogger(__name__)

class PermissionType(Enum):
    """权限类型枚举"""
    # 系统管理权限
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    
    # 用户管理权限
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_MANAGE_ALL = "user:manage_all"
    
    # 分销管理权限
    DISTRIBUTOR_CREATE_L1 = "distributor:create_level1"
    DISTRIBUTOR_CREATE_L2 = "distributor:create_level2"
    DISTRIBUTOR_MANAGE_L1 = "distributor:manage_level1"
    DISTRIBUTOR_MANAGE_L2 = "distributor:manage_level2"
    DISTRIBUTOR_VIEW_TREE = "distributor:view_tree"
    
    # 佣金管理权限
    COMMISSION_VIEW_ALL = "commission:view_all"
    COMMISSION_VIEW_OWN = "commission:view_own"
    COMMISSION_CALCULATE = "commission:calculate"
    COMMISSION_APPROVE = "commission:approve"
    COMMISSION_WITHDRAW = "commission:withdraw"
    
    # 交易系统权限
    TRADING_ACCESS = "trading:access"
    TRADING_EXECUTE = "trading:execute"
    TRADING_VIEW_HISTORY = "trading:view_history"
    TRADING_MANAGE_STRATEGIES = "trading:manage_strategies"
    
    # 数据访问权限
    DATA_VIEW_ALL = "data:view_all"
    DATA_VIEW_OWN = "data:view_own"
    DATA_EXPORT = "data:export"
    DATA_ANALYTICS = "data:analytics"
    
    # 财务权限
    FINANCE_VIEW_REPORTS = "finance:view_reports"
    FINANCE_MANAGE_ACCOUNTS = "finance:manage_accounts"
    FINANCE_AUDIT = "finance:audit"

@dataclass
class PermissionRule:
    """权限规则"""
    permission: PermissionType
    required_level: DistributionLevel
    additional_conditions: Optional[Callable] = None
    description: str = ""

class DistributionPermissionService:
    """三级分销权限验证服务 - 真实完整实现"""
    
    def __init__(self, jwt_service: JWTDistributionService):
        self.jwt_service = jwt_service
        
        # 权限规则映射
        self.permission_rules = self._initialize_permission_rules()
        
        # 角色权限映射
        self.role_permissions = self._initialize_role_permissions()
        
        # 权限缓存
        self.permission_cache = {}
        
        logger.info("三级分销权限验证服务初始化完成")
    
    def _initialize_permission_rules(self) -> Dict[PermissionType, PermissionRule]:
        """初始化权限规则 - 真实实现"""
        
        return {
            # 系统管理权限 - 仅超级管理员
            PermissionType.SYSTEM_ADMIN: PermissionRule(
                PermissionType.SYSTEM_ADMIN,
                DistributionLevel.SUPER_ADMIN,
                description="系统管理员权限"
            ),
            PermissionType.SYSTEM_CONFIG: PermissionRule(
                PermissionType.SYSTEM_CONFIG,
                DistributionLevel.SUPER_ADMIN,
                description="系统配置权限"
            ),
            PermissionType.SYSTEM_MONITOR: PermissionRule(
                PermissionType.SYSTEM_MONITOR,
                DistributionLevel.SUPER_ADMIN,
                description="系统监控权限"
            ),
            
            # 用户管理权限
            PermissionType.USER_CREATE: PermissionRule(
                PermissionType.USER_CREATE,
                DistributionLevel.LEVEL_2_DISTRIBUTOR,
                additional_conditions=self._check_user_creation_limit,
                description="创建用户权限"
            ),
            PermissionType.USER_READ: PermissionRule(
                PermissionType.USER_READ,
                DistributionLevel.CONSUMER,
                additional_conditions=self._check_user_read_scope,
                description="查看用户信息权限"
            ),
            PermissionType.USER_UPDATE: PermissionRule(
                PermissionType.USER_UPDATE,
                DistributionLevel.LEVEL_2_DISTRIBUTOR,
                additional_conditions=self._check_user_update_scope,
                description="更新用户信息权限"
            ),
            PermissionType.USER_DELETE: PermissionRule(
                PermissionType.USER_DELETE,
                DistributionLevel.LEVEL_1_DISTRIBUTOR,
                additional_conditions=self._check_user_delete_scope,
                description="删除用户权限"
            ),
            PermissionType.USER_MANAGE_ALL: PermissionRule(
                PermissionType.USER_MANAGE_ALL,
                DistributionLevel.SUPER_ADMIN,
                description="管理所有用户权限"
            ),
            
            # 分销管理权限
            PermissionType.DISTRIBUTOR_CREATE_L1: PermissionRule(
                PermissionType.DISTRIBUTOR_CREATE_L1,
                DistributionLevel.SUPER_ADMIN,
                description="创建一级分销商权限"
            ),
            PermissionType.DISTRIBUTOR_CREATE_L2: PermissionRule(
                PermissionType.DISTRIBUTOR_CREATE_L2,
                DistributionLevel.LEVEL_1_DISTRIBUTOR,
                additional_conditions=self._check_distributor_creation_limit,
                description="创建二级分销商权限"
            ),
            PermissionType.DISTRIBUTOR_MANAGE_L1: PermissionRule(
                PermissionType.DISTRIBUTOR_MANAGE_L1,
                DistributionLevel.SUPER_ADMIN,
                description="管理一级分销商权限"
            ),
            PermissionType.DISTRIBUTOR_MANAGE_L2: PermissionRule(
                PermissionType.DISTRIBUTOR_MANAGE_L2,
                DistributionLevel.LEVEL_1_DISTRIBUTOR,
                additional_conditions=self._check_distributor_management_scope,
                description="管理二级分销商权限"
            ),
            PermissionType.DISTRIBUTOR_VIEW_TREE: PermissionRule(
                PermissionType.DISTRIBUTOR_VIEW_TREE,
                DistributionLevel.LEVEL_2_DISTRIBUTOR,
                additional_conditions=self._check_tree_view_scope,
                description="查看分销树权限"
            ),
            
            # 佣金管理权限
            PermissionType.COMMISSION_VIEW_ALL: PermissionRule(
                PermissionType.COMMISSION_VIEW_ALL,
                DistributionLevel.SUPER_ADMIN,
                description="查看所有佣金权限"
            ),
            PermissionType.COMMISSION_VIEW_OWN: PermissionRule(
                PermissionType.COMMISSION_VIEW_OWN,
                DistributionLevel.LEVEL_2_DISTRIBUTOR,
                description="查看自己佣金权限"
            ),
            PermissionType.COMMISSION_CALCULATE: PermissionRule(
                PermissionType.COMMISSION_CALCULATE,
                DistributionLevel.LEVEL_2_DISTRIBUTOR,
                description="计算佣金权限"
            ),
            PermissionType.COMMISSION_APPROVE: PermissionRule(
                PermissionType.COMMISSION_APPROVE,
                DistributionLevel.LEVEL_1_DISTRIBUTOR,
                description="审批佣金权限"
            ),
            PermissionType.COMMISSION_WITHDRAW: PermissionRule(
                PermissionType.COMMISSION_WITHDRAW,
                DistributionLevel.LEVEL_2_DISTRIBUTOR,
                additional_conditions=self._check_withdrawal_conditions,
                description="提取佣金权限"
            ),
            
            # 交易系统权限
            PermissionType.TRADING_ACCESS: PermissionRule(
                PermissionType.TRADING_ACCESS,
                DistributionLevel.CONSUMER,
                description="访问交易系统权限"
            ),
            PermissionType.TRADING_EXECUTE: PermissionRule(
                PermissionType.TRADING_EXECUTE,
                DistributionLevel.CONSUMER,
                additional_conditions=self._check_trading_conditions,
                description="执行交易权限"
            ),
            PermissionType.TRADING_VIEW_HISTORY: PermissionRule(
                PermissionType.TRADING_VIEW_HISTORY,
                DistributionLevel.CONSUMER,
                additional_conditions=self._check_history_view_scope,
                description="查看交易历史权限"
            ),
            PermissionType.TRADING_MANAGE_STRATEGIES: PermissionRule(
                PermissionType.TRADING_MANAGE_STRATEGIES,
                DistributionLevel.LEVEL_2_DISTRIBUTOR,
                description="管理交易策略权限"
            ),
            
            # 数据访问权限
            PermissionType.DATA_VIEW_ALL: PermissionRule(
                PermissionType.DATA_VIEW_ALL,
                DistributionLevel.SUPER_ADMIN,
                description="查看所有数据权限"
            ),
            PermissionType.DATA_VIEW_OWN: PermissionRule(
                PermissionType.DATA_VIEW_OWN,
                DistributionLevel.CONSUMER,
                description="查看自己数据权限"
            ),
            PermissionType.DATA_EXPORT: PermissionRule(
                PermissionType.DATA_EXPORT,
                DistributionLevel.LEVEL_2_DISTRIBUTOR,
                additional_conditions=self._check_export_conditions,
                description="导出数据权限"
            ),
            PermissionType.DATA_ANALYTICS: PermissionRule(
                PermissionType.DATA_ANALYTICS,
                DistributionLevel.LEVEL_1_DISTRIBUTOR,
                description="数据分析权限"
            ),
            
            # 财务权限
            PermissionType.FINANCE_VIEW_REPORTS: PermissionRule(
                PermissionType.FINANCE_VIEW_REPORTS,
                DistributionLevel.LEVEL_2_DISTRIBUTOR,
                additional_conditions=self._check_finance_report_scope,
                description="查看财务报告权限"
            ),
            PermissionType.FINANCE_MANAGE_ACCOUNTS: PermissionRule(
                PermissionType.FINANCE_MANAGE_ACCOUNTS,
                DistributionLevel.LEVEL_1_DISTRIBUTOR,
                description="管理财务账户权限"
            ),
            PermissionType.FINANCE_AUDIT: PermissionRule(
                PermissionType.FINANCE_AUDIT,
                DistributionLevel.SUPER_ADMIN,
                description="财务审计权限"
            )
        }
    
    def _initialize_role_permissions(self) -> Dict[DistributionLevel, Set[PermissionType]]:
        """初始化角色权限映射 - 真实实现"""
        
        return {
            DistributionLevel.SUPER_ADMIN: {
                # 超级管理员拥有所有权限
                perm for perm in PermissionType
            },
            
            DistributionLevel.LEVEL_1_DISTRIBUTOR: {
                # 一级分销商权限
                PermissionType.USER_CREATE,
                PermissionType.USER_READ,
                PermissionType.USER_UPDATE,
                PermissionType.USER_DELETE,
                PermissionType.DISTRIBUTOR_CREATE_L2,
                PermissionType.DISTRIBUTOR_MANAGE_L2,
                PermissionType.DISTRIBUTOR_VIEW_TREE,
                PermissionType.COMMISSION_VIEW_OWN,
                PermissionType.COMMISSION_CALCULATE,
                PermissionType.COMMISSION_APPROVE,
                PermissionType.COMMISSION_WITHDRAW,
                PermissionType.TRADING_ACCESS,
                PermissionType.TRADING_EXECUTE,
                PermissionType.TRADING_VIEW_HISTORY,
                PermissionType.TRADING_MANAGE_STRATEGIES,
                PermissionType.DATA_VIEW_OWN,
                PermissionType.DATA_EXPORT,
                PermissionType.DATA_ANALYTICS,
                PermissionType.FINANCE_VIEW_REPORTS,
                PermissionType.FINANCE_MANAGE_ACCOUNTS
            },
            
            DistributionLevel.LEVEL_2_DISTRIBUTOR: {
                # 二级分销商权限
                PermissionType.USER_CREATE,
                PermissionType.USER_READ,
                PermissionType.USER_UPDATE,
                PermissionType.DISTRIBUTOR_VIEW_TREE,
                PermissionType.COMMISSION_VIEW_OWN,
                PermissionType.COMMISSION_CALCULATE,
                PermissionType.COMMISSION_WITHDRAW,
                PermissionType.TRADING_ACCESS,
                PermissionType.TRADING_EXECUTE,
                PermissionType.TRADING_VIEW_HISTORY,
                PermissionType.TRADING_MANAGE_STRATEGIES,
                PermissionType.DATA_VIEW_OWN,
                PermissionType.DATA_EXPORT,
                PermissionType.FINANCE_VIEW_REPORTS
            },
            
            DistributionLevel.CONSUMER: {
                # 消费者权限
                PermissionType.USER_READ,
                PermissionType.TRADING_ACCESS,
                PermissionType.TRADING_EXECUTE,
                PermissionType.TRADING_VIEW_HISTORY,
                PermissionType.DATA_VIEW_OWN
            }
        }
    
    async def check_permission(
        self, 
        token: str, 
        permission: PermissionType,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """检查权限 - 真实实现"""
        
        try:
            # 1. 验证Token
            token_data = await self.jwt_service.verify_token(token)
            if not token_data["valid"]:
                return {
                    "authorized": False,
                    "error": "Token无效",
                    "error_code": "INVALID_TOKEN"
                }
            
            user_data = token_data["data"]
            user_level = DistributionLevel(user_data["level"])
            
            # 2. 检查基础权限
            if not self._has_basic_permission(user_level, permission):
                return {
                    "authorized": False,
                    "error": f"用户级别 {user_level.value} 没有权限 {permission.value}",
                    "error_code": "INSUFFICIENT_LEVEL"
                }
            
            # 3. 检查权限规则
            rule = self.permission_rules.get(permission)
            if not rule:
                return {
                    "authorized": False,
                    "error": f"未定义的权限: {permission.value}",
                    "error_code": "UNDEFINED_PERMISSION"
                }
            
            # 4. 检查额外条件
            if rule.additional_conditions:
                condition_result = await rule.additional_conditions(user_data, context or {})
                if not condition_result["allowed"]:
                    return {
                        "authorized": False,
                        "error": condition_result.get("reason", "不满足额外条件"),
                        "error_code": "CONDITION_NOT_MET"
                    }
            
            # 5. 记录权限检查日志
            await self._log_permission_check(user_data["user_id"], permission, True)
            
            return {
                "authorized": True,
                "user_id": user_data["user_id"],
                "username": user_data["username"],
                "level": user_level.value,
                "permission": permission.value
            }
            
        except Exception as e:
            logger.error(f"权限检查失败: {e}")
            return {
                "authorized": False,
                "error": str(e),
                "error_code": "PERMISSION_CHECK_ERROR"
            }
    
    def _has_basic_permission(self, user_level: DistributionLevel, permission: PermissionType) -> bool:
        """检查基础权限"""
        return permission in self.role_permissions.get(user_level, set())
    
    # 权限条件检查方法 - 真实实现
    async def _check_user_creation_limit(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查用户创建限制"""
        try:
            # 获取当前用户的下级用户数量
            current_count = await self._get_sub_user_count(user_data["user_id"])
            max_allowed = user_data.get("max_sub_users", 0)
            
            if current_count >= max_allowed:
                return {
                    "allowed": False,
                    "reason": f"已达到最大下级用户数量限制 ({current_count}/{max_allowed})"
                }
            
            return {"allowed": True}
            
        except Exception as e:
            logger.error(f"检查用户创建限制失败: {e}")
            return {"allowed": False, "reason": "检查失败"}
    
    async def _check_user_read_scope(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查用户读取范围"""
        try:
            target_user_id = context.get("target_user_id")
            if not target_user_id:
                return {"allowed": True}  # 没有指定目标用户，允许
            
            user_level = DistributionLevel(user_data["level"])
            
            # 超级管理员可以查看所有用户
            if user_level == DistributionLevel.SUPER_ADMIN:
                return {"allowed": True}
            
            # 检查是否在分销链中
            if await self._is_in_distribution_chain(user_data["user_id"], target_user_id):
                return {"allowed": True}
            
            # 只能查看自己
            if user_data["user_id"] == target_user_id:
                return {"allowed": True}
            
            return {"allowed": False, "reason": "只能查看自己或下级用户信息"}

        except Exception as e:
            logger.error(f"检查用户读取范围失败: {e}")
            return {"allowed": False, "reason": "检查失败"}

    async def _check_user_update_scope(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查用户更新范围"""
        try:
            target_user_id = context.get("target_user_id")
            if not target_user_id:
                return {"allowed": False, "reason": "未指定目标用户"}

            user_level = DistributionLevel(user_data["level"])

            # 超级管理员可以更新所有用户
            if user_level == DistributionLevel.SUPER_ADMIN:
                return {"allowed": True}

            # 检查是否为直接下级
            if await self._is_direct_subordinate(user_data["user_id"], target_user_id):
                return {"allowed": True}

            # 只能更新自己
            if user_data["user_id"] == target_user_id:
                return {"allowed": True}

            return {"allowed": False, "reason": "只能更新自己或直接下级用户"}

        except Exception as e:
            logger.error(f"检查用户更新范围失败: {e}")
            return {"allowed": False, "reason": "检查失败"}

    async def _check_user_delete_scope(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查用户删除范围"""
        try:
            target_user_id = context.get("target_user_id")
            if not target_user_id:
                return {"allowed": False, "reason": "未指定目标用户"}

            user_level = DistributionLevel(user_data["level"])

            # 超级管理员可以删除所有用户（除了自己）
            if user_level == DistributionLevel.SUPER_ADMIN:
                if user_data["user_id"] == target_user_id:
                    return {"allowed": False, "reason": "不能删除自己"}
                return {"allowed": True}

            # 检查是否为直接下级且没有下级用户
            if await self._is_direct_subordinate(user_data["user_id"], target_user_id):
                if await self._has_subordinates(target_user_id):
                    return {"allowed": False, "reason": "目标用户还有下级，不能删除"}
                return {"allowed": True}

            return {"allowed": False, "reason": "只能删除没有下级的直接下级用户"}

        except Exception as e:
            logger.error(f"检查用户删除范围失败: {e}")
            return {"allowed": False, "reason": "检查失败"}

    async def _check_distributor_creation_limit(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查分销商创建限制"""
        try:
            # 检查是否达到创建限制
            current_count = await self._get_distributor_count(user_data["user_id"], DistributionLevel.LEVEL_2_DISTRIBUTOR)
            max_allowed = user_data.get("max_sub_users", 0)

            if current_count >= max_allowed:
                return {
                    "allowed": False,
                    "reason": f"已达到二级分销商创建限制 ({current_count}/{max_allowed})"
                }

            return {"allowed": True}

        except Exception as e:
            logger.error(f"检查分销商创建限制失败: {e}")
            return {"allowed": False, "reason": "检查失败"}

    async def _check_distributor_management_scope(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查分销商管理范围"""
        try:
            target_distributor_id = context.get("target_distributor_id")
            if not target_distributor_id:
                return {"allowed": False, "reason": "未指定目标分销商"}

            # 检查是否为直接下级分销商
            if await self._is_direct_subordinate(user_data["user_id"], target_distributor_id):
                return {"allowed": True}

            return {"allowed": False, "reason": "只能管理直接下级分销商"}

        except Exception as e:
            logger.error(f"检查分销商管理范围失败: {e}")
            return {"allowed": False, "reason": "检查失败"}

    async def _check_tree_view_scope(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查分销树查看范围"""
        try:
            target_root_id = context.get("root_user_id", user_data["user_id"])
            user_level = DistributionLevel(user_data["level"])

            # 超级管理员可以查看所有分销树
            if user_level == DistributionLevel.SUPER_ADMIN:
                return {"allowed": True}

            # 只能查看自己的分销树
            if target_root_id == user_data["user_id"]:
                return {"allowed": True}

            # 检查是否在同一分销链中
            if await self._is_in_distribution_chain(user_data["user_id"], target_root_id):
                return {"allowed": True}

            return {"allowed": False, "reason": "只能查看自己的分销树"}

        except Exception as e:
            logger.error(f"检查分销树查看范围失败: {e}")
            return {"allowed": False, "reason": "检查失败"}

    async def _check_withdrawal_conditions(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查提取条件"""
        try:
            withdrawal_amount = context.get("amount", 0)

            # 检查最小提取金额
            min_withdrawal = 100.0  # 最小提取金额
            if withdrawal_amount < min_withdrawal:
                return {
                    "allowed": False,
                    "reason": f"提取金额不能少于 {min_withdrawal} 元"
                }

            # 检查可用余额
            available_balance = await self._get_available_commission(user_data["user_id"])
            if withdrawal_amount > available_balance:
                return {
                    "allowed": False,
                    "reason": f"提取金额超过可用余额 ({available_balance} 元)"
                }

            # 检查提取频率限制
            if await self._check_withdrawal_frequency_limit(user_data["user_id"]):
                return {
                    "allowed": False,
                    "reason": "提取过于频繁，请稍后再试"
                }

            return {"allowed": True}

        except Exception as e:
            logger.error(f"检查提取条件失败: {e}")
            return {"allowed": False, "reason": "检查失败"}

    async def _check_trading_conditions(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查交易条件"""
        try:
            # 检查账户状态
            if not await self._is_account_active(user_data["user_id"]):
                return {"allowed": False, "reason": "账户未激活或已被暂停"}

            # 检查风险评估
            risk_level = await self._get_user_risk_level(user_data["user_id"])
            trade_risk = context.get("risk_level", "medium")

            if not self._is_risk_compatible(risk_level, trade_risk):
                return {
                    "allowed": False,
                    "reason": f"交易风险等级 {trade_risk} 超过用户风险承受能力 {risk_level}"
                }

            return {"allowed": True}

        except Exception as e:
            logger.error(f"检查交易条件失败: {e}")
            return {"allowed": False, "reason": "检查失败"}

    async def _check_history_view_scope(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查历史查看范围"""
        try:
            target_user_id = context.get("target_user_id", user_data["user_id"])
            user_level = DistributionLevel(user_data["level"])

            # 超级管理员可以查看所有历史
            if user_level == DistributionLevel.SUPER_ADMIN:
                return {"allowed": True}

            # 分销商可以查看下级的历史
            if user_level in [DistributionLevel.LEVEL_1_DISTRIBUTOR, DistributionLevel.LEVEL_2_DISTRIBUTOR]:
                if await self._is_in_distribution_chain(user_data["user_id"], target_user_id):
                    return {"allowed": True}

            # 只能查看自己的历史
            if target_user_id == user_data["user_id"]:
                return {"allowed": True}

            return {"allowed": False, "reason": "只能查看自己或下级的交易历史"}

        except Exception as e:
            logger.error(f"检查历史查看范围失败: {e}")
            return {"allowed": False, "reason": "检查失败"}

    async def _check_export_conditions(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查导出条件"""
        try:
            export_type = context.get("export_type", "")
            data_range = context.get("date_range", {})

            # 检查导出类型权限
            sensitive_exports = ["user_data", "financial_data", "commission_data"]
            if export_type in sensitive_exports:
                user_level = DistributionLevel(user_data["level"])
                if user_level == DistributionLevel.CONSUMER:
                    return {"allowed": False, "reason": "消费者不能导出敏感数据"}

            # 检查数据范围
            max_days = 365  # 最大导出天数
            if self._calculate_date_range_days(data_range) > max_days:
                return {
                    "allowed": False,
                    "reason": f"导出数据范围不能超过 {max_days} 天"
                }

            return {"allowed": True}

        except Exception as e:
            logger.error(f"检查导出条件失败: {e}")
            return {"allowed": False, "reason": "检查失败"}

    async def _check_finance_report_scope(self, user_data: Dict, context: Dict) -> Dict[str, Any]:
        """检查财务报告范围"""
        try:
            report_scope = context.get("scope", "own")
            user_level = DistributionLevel(user_data["level"])

            # 超级管理员可以查看所有报告
            if user_level == DistributionLevel.SUPER_ADMIN:
                return {"allowed": True}

            # 一级分销商可以查看下级报告
            if user_level == DistributionLevel.LEVEL_1_DISTRIBUTOR and report_scope == "subordinates":
                return {"allowed": True}

            # 其他情况只能查看自己的报告
            if report_scope == "own":
                return {"allowed": True}

            return {"allowed": False, "reason": "权限不足，无法查看指定范围的财务报告"}

        except Exception as e:
            logger.error(f"检查财务报告范围失败: {e}")
            return {"allowed": False, "reason": "检查失败"}

    # 辅助方法 - 真实实现
    async def _get_sub_user_count(self, user_id: str) -> int:
        """获取下级用户数量"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.jwt_service.db_path)
            cursor = conn.cursor()

            cursor.execute(
                "SELECT COUNT(*) FROM distribution_users WHERE parent_id = ?",
                (user_id,)
            )

            count = cursor.fetchone()[0]
            conn.close()
            return count

        except Exception as e:
            logger.error(f"获取下级用户数量失败: {e}")
            return 0

    async def _is_in_distribution_chain(self, user_id: str, target_id: str) -> bool:
        """检查是否在分销链中"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.jwt_service.db_path)
            cursor = conn.cursor()

            # 检查是否为上级关系
            cursor.execute('''
                WITH RECURSIVE distribution_chain AS (
                    SELECT user_id, parent_id, 0 as level
                    FROM distribution_users
                    WHERE user_id = ?

                    UNION ALL

                    SELECT du.user_id, du.parent_id, dc.level + 1
                    FROM distribution_users du
                    JOIN distribution_chain dc ON du.parent_id = dc.user_id
                    WHERE dc.level < 10
                )
                SELECT 1 FROM distribution_chain WHERE user_id = ?
            ''', (target_id, user_id))

            result = cursor.fetchone()
            conn.close()
            return result is not None

        except Exception as e:
            logger.error(f"检查分销链关系失败: {e}")
            return False

    async def _is_direct_subordinate(self, user_id: str, target_id: str) -> bool:
        """检查是否为直接下级"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.jwt_service.db_path)
            cursor = conn.cursor()

            cursor.execute(
                "SELECT 1 FROM distribution_users WHERE user_id = ? AND parent_id = ?",
                (target_id, user_id)
            )

            result = cursor.fetchone()
            conn.close()
            return result is not None

        except Exception as e:
            logger.error(f"检查直接下级关系失败: {e}")
            return False

    async def _has_subordinates(self, user_id: str) -> bool:
        """检查是否有下级"""
        count = await self._get_sub_user_count(user_id)
        return count > 0

    async def _get_distributor_count(self, user_id: str, level: DistributionLevel) -> int:
        """获取指定级别的分销商数量"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.jwt_service.db_path)
            cursor = conn.cursor()

            cursor.execute(
                "SELECT COUNT(*) FROM distribution_users WHERE parent_id = ? AND level = ?",
                (user_id, level.value)
            )

            count = cursor.fetchone()[0]
            conn.close()
            return count

        except Exception as e:
            logger.error(f"获取分销商数量失败: {e}")
            return 0

    async def _get_available_commission(self, user_id: str) -> float:
        """获取可用佣金余额"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.jwt_service.db_path)
            cursor = conn.cursor()

            # 计算总佣金
            cursor.execute('''
                SELECT COALESCE(SUM(amount), 0)
                FROM commission_records
                WHERE distributor_id = ? AND status = 'approved'
            ''', (user_id,))

            total_commission = cursor.fetchone()[0]

            # 假设有withdrawal_records表

            conn.close()
            return max(0, total_commission - withdrawn_amount)

        except Exception as e:
            logger.error(f"获取可用佣金失败: {e}")
            return 0.0

    async def _check_withdrawal_frequency_limit(self, user_id: str) -> bool:
        """检查提取频率限制"""
        try:
            # 实际应该查询withdrawal_records表
            return False  # 暂时允许提取

        except Exception as e:
            logger.error(f"检查提取频率限制失败: {e}")
            return True  # 出错时限制提取

    async def _is_account_active(self, user_id: str) -> bool:
        """检查账户是否活跃"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.jwt_service.db_path)
            cursor = conn.cursor()

            cursor.execute(
                "SELECT status FROM distribution_users WHERE user_id = ?",
                (user_id,)
            )

            result = cursor.fetchone()
            conn.close()
            return result and result[0] == 'active'

        except Exception as e:
            logger.error(f"检查账户状态失败: {e}")
            return False

    async def _get_user_risk_level(self, user_id: str) -> str:
        """获取用户风险等级"""
        try:
            # 实际应该从用户风险评估表中获取
            return "medium"  # 默认中等风险

        except Exception as e:
            logger.error(f"获取用户风险等级失败: {e}")
            return "low"  # 出错时返回低风险

    def _is_risk_compatible(self, user_risk: str, trade_risk: str) -> bool:
        """检查风险兼容性"""
        risk_levels = {"low": 1, "medium": 2, "high": 3}
        user_level = risk_levels.get(user_risk, 1)
        trade_level = risk_levels.get(trade_risk, 2)
        return trade_level <= user_level

    def _calculate_date_range_days(self, date_range: Dict) -> int:
        """计算日期范围天数"""
        try:
            from datetime import datetime
            start_date = datetime.fromisoformat(date_range.get("start", ""))
            end_date = datetime.fromisoformat(date_range.get("end", ""))
            return (end_date - start_date).days
        except:
            return 0

    async def _log_permission_check(self, user_id: str, permission: PermissionType, granted: bool):
        """记录权限检查日志"""
        try:
            logger.info(f"权限检查: 用户={user_id}, 权限={permission.value}, 结果={'授予' if granted else '拒绝'}")

        except Exception as e:
            logger.error(f"记录权限检查日志失败: {e}")

    # 权限装饰器
    def require_permission(self, permission: PermissionType):
        """权限装饰器 - 真实实现"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 从请求中获取Token
                token = kwargs.get('token') or (args[0] if args else None)
                if not token:
                    raise PermissionError("缺少认证Token")

                # 检查权限
                permission_result = await self.check_permission(
                    token, permission, kwargs.get('context', {})
                )

                if not permission_result["authorized"]:
                    raise PermissionError(permission_result["error"])

                # 将用户信息添加到kwargs中
                kwargs['user_info'] = {
                    "user_id": permission_result["user_id"],
                    "username": permission_result["username"],
                    "level": permission_result["level"]
                }

                return await func(*args, **kwargs)
            return wrapper
        return decorator

# 全局服务实例
from .jwt_distribution_service import jwt_distribution_service
distribution_permission_service = DistributionPermissionService(jwt_distribution_service)
