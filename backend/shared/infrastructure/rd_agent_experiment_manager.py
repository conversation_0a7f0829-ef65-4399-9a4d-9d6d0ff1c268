# -*- coding: utf-8 -*-
"""
RD-Agent实验管理框架
基于RD-Agent的A/B测试自动化、策略效果追踪和参数敏感性分析
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import numpy as np
import pandas as pd

from shared.infrastructure.mcp_client import MCPClient, MCPTaskRequest, MCPTaskType

logger = logging.getLogger(__name__)

class ExperimentType(Enum):
                """实验类型"""
                AB_TEST = "ab_test"
                MULTIVARIATE_TEST = "multivariate_test"
                PARAMETER_SENSITIVITY = "parameter_sensitivity"
                STRATEGY_COMPARISON = "strategy_comparison"
                PERFORMANCE_OPTIMIZATION = "performance_optimization"
                RISK_ASSESSMENT = "risk_assessment"

class ExperimentStatus(Enum):
                """实验状态"""
                DESIGNED = "designed"
                RUNNING = "running"
                COMPLETED = "completed"
                FAILED = "failed"
                PAUSED = "paused"
                CANCELLED = "cancelled"

@dataclass
class ExperimentConfig:
                """实验配置"""
                experiment_id: str
                experiment_type: ExperimentType
                name: str
                description: str
                hypothesis: str
                success_metrics: List[str]
                test_groups: Dict[str, Any]
                control_group: Dict[str, Any]
                sample_size: int
                duration_days: int
                significance_level: float = 0.05
                power: float = 0.8

@dataclass
class ExperimentResult:
                """实验结果"""
                experiment_id: str
                status: ExperimentStatus
                start_time: datetime
                end_time: Optional[datetime]
                results: Dict[str, Any]
                statistical_significance: Dict[str, float]
                effect_sizes: Dict[str, float]
                confidence_intervals: Dict[str, Tuple[float, float]]
                rd_agent_insights: Dict[str, Any]
                recommendations: List[str]

class RDAgentExperimentManager:
                """RD-Agent实验管理框架"""

                def __init__(self):
                                self.service_name = "RDAgentExperimentManager"
                                self.version = "2.0.0"

                                # 初始化MCP客户端
                                self.mcp_client = MCPClient()

                                # 实验存储
                                self.experiments_dir = Path("experiments")
                                self.experiments_dir.mkdir(exist_ok=True)

                                # 活跃实验
                                self.active_experiments: Dict[str, ExperimentConfig] = {}
                                self.experiment_results: Dict[str, ExperimentResult] = {}

                                # 实验监控任务
                                self.monitoring_tasks: Dict[str, asyncio.Task] = {}

                                # 性能统计
                                self.performance_stats = {
                                                "total_experiments": 0,
                                                "completed_experiments": 0,
                                                "successful_experiments": 0,
                                                "average_experiment_duration": 0.0,
                                                "significant_results_count": 0,
                                                "rd_agent_optimizations": 0,
                                                "automated_decisions": 0
                                }

                                logger.info(f"  {self.service_name} v{self.version} 实验管理框架初始化完成")

                async def design_experiment_with_rd_agent(
                                self,
                                experiment_request: Dict[str, Any]
                ) -> ExperimentConfig:
                                """使用RD-Agent设计实验"""

                                try:
                                                # 确保MCP连接
                                                await self.mcp_client.connect()

                                                # 构建实验设计请求
                                                design_request = {
                                                                "task_type": "experiment_design",
                                                                "experiment_objective": experiment_request.get("objective"),
                                                                "hypothesis": experiment_request.get("hypothesis"),
                                                                "target_metrics": experiment_request.get("target_metrics", []),
                                                                "constraints": experiment_request.get("constraints", {}),
                                                                "available_resources": experiment_request.get("resources", {}),
                                                                "design_config": {
                                                                                "experiment_type": experiment_request.get("type", "ab_test"),
                                                                                "statistical_power": 0.8,
                                                                                "significance_level": 0.05,
                                                                                "minimum_detectable_effect": experiment_request.get("min_effect", 0.05)
                                                                },
                                                                "rd_agent_config": {
                                                                                "use_experimental_design": True,
                                                                                "use_power_analysis": True,
                                                                                "use_sample_size_calculation": True,
                                                                                "use_bias_detection": True
                                                                }
                                                }

                                                # 调用RD-Agent实验设计
                                                result = await self.mcp_client.call_tool(
                                                                "design_experiment",
                                                                design_request
                                                )

                                                if result.get("status") == "success":
                                                                design_result = result.get("result", {})

                                                                # 创建实验配置
                                                                experiment_config = ExperimentConfig(
                                                                                experiment_id=str(uuid.uuid4()),
                                                                                experiment_type=ExperimentType(experiment_request.get("type", "ab_test")),
                                                                                name=experiment_request.get("name", "RD-Agent实验"),
                                                                                description=experiment_request.get("description", ""),
                                                                                hypothesis=experiment_request.get("hypothesis", ""),
                                                                                success_metrics=design_result.get("success_metrics", []),
                                                                                test_groups=design_result.get("test_groups", {}),
                                                                                control_group=design_result.get("control_group", {}),
                                                                                sample_size=design_result.get("sample_size", 1000),
                                                                                duration_days=design_result.get("duration_days", 30),
                                                                                significance_level=design_result.get("significance_level", 0.05),
                                                                                power=design_result.get("power", 0.8)
                                                                )

                                                                # 保存实验配置
                                                                await self._save_experiment_config(experiment_config)

                                                                self.performance_stats["total_experiments"] += 1
                                                                self.performance_stats["rd_agent_optimizations"] += 1

                                                                logger.info(f"  实验设计完成: {experiment_config.experiment_id}")
                                                                return experiment_config
                                                else:
        pass  # 专业版模式

                                except Exception as e:
                                                logger.error(f"  RD-Agent实验设计失败: {e}")
        pass  # 专业版模式

                async def start_experiment_with_rd_agent_monitoring(
                                self,
                                experiment_config: ExperimentConfig
                ) -> str:
                                """启动RD-Agent监控的实验"""

                                try:
                                                # 确保MCP连接
                                                await self.mcp_client.connect()

                                                # 构建实验启动请求
                                                start_request = {
                                                                "task_type": "experiment_execution",
                                                                "experiment_config": asdict(experiment_config),
                                                                "monitoring_config": {
                                                                                "real_time_monitoring": True,
                                                                                "early_stopping": True,
                                                                                "adaptive_allocation": True,
                                                                                "bias_detection": True
                                                                },
                                                                "rd_agent_config": {
                                                                                "use_real_time_analysis": True,
                                                                                "use_sequential_testing": True,
                                                                                "use_adaptive_design": True,
                                                                                "use_anomaly_detection": True
                                                                }
                                                }

                                                # 启动RD-Agent实验执行
                                                result = await self.mcp_client.call_tool(
                                                                "start_experiment_execution",
                                                                start_request
                                                )

                                                if result.get("status") == "success":
                                                                execution_result = result.get("result", {})

                                                                # 记录活跃实验
                                                                self.active_experiments[experiment_config.experiment_id] = experiment_config

                                                                # 创建实验结果记录
                                                                experiment_result = ExperimentResult(
                                                                                experiment_id=experiment_config.experiment_id,
                                                                                status=ExperimentStatus.RUNNING,
                                                                                start_time=datetime.now(),
                                                                                end_time=None,
                                                                                results={},
                                                                                statistical_significance={},
                                                                                effect_sizes={},
                                                                                confidence_intervals={},
                                                                                rd_agent_insights={},
                                                                                recommendations=[]
                                                                )

                                                                self.experiment_results[experiment_config.experiment_id] = experiment_result

                                                                # 启动实验监控
                                                                monitor_task = asyncio.create_task(
                                                                                self._monitor_experiment_with_rd_agent(
                                                                                                experiment_config.experiment_id,
                                                                                                execution_result.get("session_id")
                                                                                )
                                                                )
                                                                self.monitoring_tasks[experiment_config.experiment_id] = monitor_task

                                                                logger.info(f"  实验已启动: {experiment_config.experiment_id}")
                                                                return experiment_config.experiment_id
                                                else:
                                                                raise Exception(f"启动实验失败: {result.get('message', 'Unknown error')}")

                                except Exception as e:
                                                logger.error(f"  启动RD-Agent实验失败: {e}")
                                                raise

                async def run_parameter_sensitivity_analysis_with_rd_agent(
                                self,
                                strategy_config: Dict[str, Any],
                                parameter_ranges: Dict[str, Tuple[float, float]],
                                sensitivity_metrics: List[str]
                ) -> Dict[str, Any]:
                                """使用RD-Agent运行参数敏感性分析"""

                                try:
                                                # 确保MCP连接
                                                await self.mcp_client.connect()

                                                # 构建敏感性分析请求
                                                sensitivity_request = {
                                                                "task_type": "parameter_sensitivity_analysis",
                                                                "strategy_config": strategy_config,
                                                                "parameter_ranges": parameter_ranges,
                                                                "sensitivity_metrics": sensitivity_metrics,
                                                                "analysis_config": {
                                                                                "sampling_method": "latin_hypercube",
                                                                                "sample_size": 1000,
                                                                                "sensitivity_methods": ["sobol", "morris", "delta"],
                                                                                "interaction_analysis": True
                                                                },
                                                                "rd_agent_config": {
                                                                                "use_advanced_sampling": True,
                                                                                "use_metamodeling": True,
                                                                                "use_global_sensitivity": True,
                                                                                "use_interaction_detection": True
                                                                }
                                                }

                                                # 调用RD-Agent敏感性分析
                                                result = await self.mcp_client.call_tool(
                                                                "run_sensitivity_analysis",
                                                                sensitivity_request
                                                )

                                                if result.get("status") == "success":
                                                                sensitivity_result = result.get("result", {})

                                                                # 处理敏感性分析结果
                                                                processed_result = await self._process_sensitivity_results(
                                                                                sensitivity_result,
                                                                                parameter_ranges,
                                                                                sensitivity_metrics
                                                                )

                                                                # 生成参数优化建议
                                                                optimization_recommendations = await self._generate_parameter_optimization_recommendations(
                                                                                processed_result
                                                                )

                                                                return {
                                                                                "success": True,
                                                                                "sensitivity_analysis": processed_result,
                                                                                "parameter_rankings": sensitivity_result.get("parameter_rankings", {}),
                                                                                "interaction_effects": sensitivity_result.get("interactions", {}),
                                                                                "optimization_recommendations": optimization_recommendations,
                                                                                "rd_agent_insights": sensitivity_result.get("insights", {}),
                                                                                "robustness_assessment": sensitivity_result.get("robustness", {})
                                                                }
                                                else:
        pass  # 专业版模式
                                                                                strategy_config,
                                                                                parameter_ranges,
                                                                                sensitivity_metrics
                                                                )

                                except Exception as e:
                                                logger.error(f"  RD-Agent参数敏感性分析失败: {e}")
        pass  # 专业版模式
                                                                strategy_config,
                                                                parameter_ranges,
                                                                sensitivity_metrics
                                                )

                async def run_strategy_comparison_with_rd_agent(
                                self,
                                strategies: Dict[str, Dict[str, Any]],
                                comparison_metrics: List[str],
                                test_period: Tuple[str, str]
                ) -> Dict[str, Any]:
                                """使用RD-Agent运行策略比较"""

                                try:
                                                # 确保MCP连接
                                                await self.mcp_client.connect()

                                                # 构建策略比较请求
                                                comparison_request = {
                                                                "task_type": "strategy_comparison",
                                                                "strategies": strategies,
                                                                "comparison_metrics": comparison_metrics,
                                                                "test_period": test_period,
                                                                "comparison_config": {
                                                                                "statistical_tests": ["t_test", "wilcoxon", "bootstrap"],
                                                                                "risk_adjusted_metrics": True,
                                                                                "regime_analysis": True,
                                                                                "drawdown_analysis": True
                                                                },
                                                                "rd_agent_config": {
                                                                                "use_advanced_statistics": True,
                                                                                "use_regime_detection": True,
                                                                                "use_risk_decomposition": True,
                                                                                "use_performance_attribution": True
                                                                }
                                                }

                                                # 调用RD-Agent策略比较
                                                result = await self.mcp_client.call_tool(
                                                                "compare_strategies",
                                                                comparison_request
                                                )

                                                if result.get("status") == "success":
                                                                comparison_result = result.get("result", {})

                                                                # 处理比较结果
                                                                processed_comparison = await self._process_strategy_comparison_results(
                                                                                comparison_result,
                                                                                strategies,
                                                                                comparison_metrics
                                                                )

                                                                # 生成策略选择建议
                                                                selection_recommendations = await self._generate_strategy_selection_recommendations(
                                                                                processed_comparison
                                                                )

                                                                return {
                                                                                "success": True,
                                                                                "strategy_comparison": processed_comparison,
                                                                                "statistical_significance": comparison_result.get("statistical_tests", {}),
                                                                                "risk_adjusted_performance": comparison_result.get("risk_adjusted", {}),
                                                                                "regime_analysis": comparison_result.get("regime_analysis", {}),
                                                                                "selection_recommendations": selection_recommendations,
                                                                                "rd_agent_insights": comparison_result.get("insights", {}),
                                                                                "confidence_scores": comparison_result.get("confidence", {})
                                                                }
                                                else:
        pass  # 专业版模式
                                                                                strategies,
                                                                                comparison_metrics,
                                                                                test_period
                                                                )

                                except Exception as e:
                                                logger.error(f"  RD-Agent策略比较失败: {e}")
        pass  # 专业版模式
                                                                strategies,
                                                                comparison_metrics,
                                                                test_period
                                                )

                async def _monitor_experiment_with_rd_agent(
                                self,
                                experiment_id: str,
                                rd_agent_session_id: str
                ):
                                """RD-Agent实验监控"""

                                experiment_result = self.experiment_results[experiment_id]

                                try:
                                                while experiment_result.status == ExperimentStatus.RUNNING:
                                                                # 获取实验状态
                                                                status_result = await self.mcp_client.call_tool(
                                                                                "get_experiment_status",
                                                                                {"session_id": rd_agent_session_id}
                                                                )

                                                                if status_result.get("status") == "success":
                                                                                experiment_status = status_result.get("result", {})

                                                                                # 更新实验结果
                                                                                experiment_result.results = experiment_status.get("current_results", {})
                                                                                experiment_result.rd_agent_insights = experiment_status.get("insights", {})

                                                                                # 检查是否需要早停
                                                                                if experiment_status.get("early_stopping_triggered", False):
                                                                                                experiment_result.status = ExperimentStatus.COMPLETED
                                                                                                experiment_result.end_time = datetime.now()

                                                                                                # 获取最终结果
                                                                                                final_results = await self._get_final_experiment_results(rd_agent_session_id)
                                                                                                experiment_result.results = final_results.get("results", {})
                                                                                                experiment_result.statistical_significance = final_results.get("significance", {})
                                                                                                experiment_result.effect_sizes = final_results.get("effect_sizes", {})
                                                                                                experiment_result.confidence_intervals = final_results.get("confidence_intervals", {})
                                                                                                experiment_result.recommendations = final_results.get("recommendations", [])

                                                                                                # 更新统计
                                                                                                self.performance_stats["completed_experiments"] += 1
                                                                                                if any(sig < 0.05 for sig in experiment_result.statistical_significance.values()):
                                                                                                                self.performance_stats["significant_results_count"] += 1
                                                                                                                self.performance_stats["successful_experiments"] += 1

                                                                                                logger.info(f"  实验完成: {experiment_id}")
                                                                                                break

                                                                                elif experiment_status.get("overall_status") == "failed":
                                                                                                experiment_result.status = ExperimentStatus.FAILED
                                                                                                experiment_result.end_time = datetime.now()
                                                                                                break

                                                                # 等待监控间隔
                                                                await asyncio.sleep(300)  # 每5分钟检查一次

                                except Exception as e:
                                                logger.error(f"  实验监控失败: {e}")
                                                experiment_result.status = ExperimentStatus.FAILED
                                                experiment_result.end_time = datetime.now()

                                finally:
                                                # 清理监控任务
                                                if experiment_id in self.monitoring_tasks:
                                                                del self.monitoring_tasks[experiment_id]

                                                # 从活跃实验中移除
                                                if experiment_id in self.active_experiments:
                                                                del self.active_experiments[experiment_id]

                async def get_experiment_status(self, experiment_id: str) -> Dict[str, Any]:
                                """获取实验状态"""

                                if experiment_id not in self.experiment_results:
                                                return {"error": "实验ID不存在"}

                                experiment_result = self.experiment_results[experiment_id]
                                experiment_config = self.active_experiments.get(experiment_id)

                                return {
                                                "experiment_id": experiment_id,
                                                "status": experiment_result.status.value,
                                                "start_time": experiment_result.start_time.isoformat(),
                                                "end_time": experiment_result.end_time.isoformat() if experiment_result.end_time else None,
                                                "current_results": experiment_result.results,
                                                "statistical_significance": experiment_result.statistical_significance,
                                                "effect_sizes": experiment_result.effect_sizes,
                                                "rd_agent_insights": experiment_result.rd_agent_insights,
                                                "recommendations": experiment_result.recommendations,
                                                "experiment_config": asdict(experiment_config) if experiment_config else None,
                                                "rd_agent_enhanced": True
                                }

                async def hyperparameter_optimization_with_rd_agent(
                                self,
                                experiment_id: str,
                                objective_function: callable,
                                parameter_space: Dict[str, Any],
                                optimization_method: str = "grid_search",
                                max_trials: int = 100
                ) -> Dict[str, Any]:
                                """使用RD-Agent进行超参数优化"""

                                try:
                                                # 确保MCP连接
                                                await self.mcp_client.connect()

                                                # 构建超参数优化请求
                                                optimization_request = {
                                                                "task_type": "hyperparameter_optimization",
                                                                "experiment_id": experiment_id,
                                                                "parameter_space": parameter_space,
                                                                "optimization_config": {
                                                                                "method": optimization_method,
                                                                                "max_trials": max_trials,
                                                                                "optimization_objective": "maximize",
                                                                                "early_stopping": True,
                                                                                "parallel_trials": 3
                                                                },
                                                                "rd_agent_config": {
                                                                                "use_bayesian_optimization": True,
                                                                                "use_multi_objective": True,
                                                                                "use_constraint_handling": True,
                                                                                "use_adaptive_sampling": True
                                                                }
                                                }

                                                # 调用RD-Agent超参数优化
                                                result = await self.mcp_client.call_tool(
                                                                "optimize_hyperparameters",
                                                                optimization_request
                                                )

                                                if result.get("status") == "success":
                                                                optimization_result = result.get("result", {})

                                                                return {
                                                                                "success": True,
                                                                                "best_params": optimization_result.get("best_parameters", {}),
                                                                                "best_score": optimization_result.get("best_score", 0.0),
                                                                                "optimization_history": optimization_result.get("history", []),
                                                                                "total_trials": optimization_result.get("total_trials", 0),
                                                                                "rd_agent_insights": optimization_result.get("insights", {}),
                                                                                "convergence_analysis": optimization_result.get("convergence", {})
                                                                }
                                                else:
        pass  # 专业版模式
                                                                                objective_function, parameter_space, optimization_method, max_trials
                                                                )

                                except Exception as e:
                                                logger.error(f"  RD-Agent超参数优化失败: {e}")
        pass  # 专业版模式
                                                                objective_function, parameter_space, optimization_method, max_trials
                                                )

                async def log_experiment_artifact_with_rd_agent(
                                self,
                                experiment_id: str,
                                artifact_name: str,
                                artifact_data: Any,
                                artifact_type: str = "model"
                ) -> str:
                                """使用RD-Agent记录实验工件"""

                                try:
                                                # 确保MCP连接
                                                await self.mcp_client.connect()

                                                # 构建工件记录请求
                                                artifact_request = {
                                                                "task_type": "experiment_artifact_logging",
                                                                "experiment_id": experiment_id,
                                                                "artifact_name": artifact_name,
                                                                "artifact_type": artifact_type,
                                                                "artifact_metadata": {
                                                                                "created_at": datetime.now().isoformat(),
                                                                                "size": len(str(artifact_data)) if isinstance(artifact_data, str) else 0,
                                                                                "format": type(artifact_data).__name__
                                                                },
                                                                "rd_agent_config": {
                                                                                "use_versioning": True,
                                                                                "use_compression": True,
                                                                                "use_metadata_extraction": True,
                                                                                "use_artifact_analysis": True
                                                                }
                                                }

                                                # 调用RD-Agent工件记录
                                                result = await self.mcp_client.call_tool(
                                                                "log_experiment_artifact",
                                                                artifact_request
                                                )

                                                if result.get("status") == "success":
                                                                artifact_result = result.get("result", {})

                                                                # 本地存储工件
                                                                artifact_path = await self._store_artifact_locally(
                                                                                experiment_id, artifact_name, artifact_data, artifact_type
                                                                )

                                                                return artifact_result.get("artifact_id", artifact_path)
                                                else:
                                                                return await self._store_artifact_locally(
                                                                                experiment_id, artifact_name, artifact_data, artifact_type
                                                                )

                                except Exception as e:
                                                logger.error(f"  RD-Agent工件记录失败: {e}")
                                                return await self._store_artifact_locally(
                                                                experiment_id, artifact_name, artifact_data, artifact_type
                                                )

                async def get_performance_stats(self) -> Dict[str, Any]:
                                """获取性能统计"""

                                success_rate = 0.0
                                if self.performance_stats["completed_experiments"] > 0:
                                                success_rate = self.performance_stats["successful_experiments"] / self.performance_stats["completed_experiments"]

                                significance_rate = 0.0
                                if self.performance_stats["completed_experiments"] > 0:
                                                significance_rate = self.performance_stats["significant_results_count"] / self.performance_stats["completed_experiments"]

                                return {
                                                **self.performance_stats,
                                                "success_rate": success_rate,
                                                "significance_rate": significance_rate,
                                                "active_experiments": len(self.active_experiments),
                                                "monitoring_tasks": len(self.monitoring_tasks),
                                                "service_type": "rd_agent_experiment_manager",
                                                "rd_agent_integration": True
                                }
