# -*- coding: utf-8 -*-
"""
RD-Agent知识库管理系统
基于RD-Agent的交易经验积累、策略模式识别和失败案例学习
"""

import asyncio
import logging
import json
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import numpy as np

from shared.infrastructure.mcp_client import MCPClient, MCPTaskRequest, MCPTaskType

logger = logging.getLogger(__name__)

class KnowledgeType(Enum):
                """知识类型"""
                TRADING_EXPERIENCE = "trading_experience"
                STRATEGY_PATTERN = "strategy_pattern"
                FAILURE_CASE = "failure_case"
                MARKET_INSIGHT = "market_insight"
                RISK_LESSON = "risk_lesson"
                OPTIMIZATION_RESULT = "optimization_result"

@dataclass
class KnowledgeEntry:
                """知识条目"""
                entry_id: str
                knowledge_type: KnowledgeType
                title: str
                description: str
                context: Dict[str, Any]
                outcomes: Dict[str, Any]
                lessons_learned: List[str]
                confidence_score: float
                relevance_tags: List[str]
                created_at: datetime
                updated_at: datetime
                usage_count: int = 0
                effectiveness_score: float = 0.0

@dataclass
class KnowledgeQuery:
                """知识查询"""
                query_id: str
                query_text: str
                context: Dict[str, Any]
                knowledge_types: List[KnowledgeType]
                max_results: int = 10
                min_confidence: float = 0.5

class RDAgentKnowledgeManager:
                """RD-Agent知识库管理系统"""

                def __init__(self):
                                self.service_name = "RDAgentKnowledgeManager"
                                self.version = "2.0.0"

                                # 初始化MCP客户端
                                self.mcp_client = MCPClient()

                                # 知识库存储
                                self.knowledge_base_dir = Path("knowledge_base")
                                self.knowledge_base_dir.mkdir(exist_ok=True)

                                # 分类知识库
                                self.knowledge_stores = {
                                                KnowledgeType.TRADING_EXPERIENCE: {},
                                                KnowledgeType.STRATEGY_PATTERN: {},
                                                KnowledgeType.FAILURE_CASE: {},
                                                KnowledgeType.MARKET_INSIGHT: {},
                                                KnowledgeType.RISK_LESSON: {},
                                                KnowledgeType.OPTIMIZATION_RESULT: {}
                                }

                                # 知识索引
                                self.knowledge_index = {
                                                "by_tags": {},
                                                "by_context": {},
                                                "by_effectiveness": {},
                                                "by_recency": {}
                                }

                                # 性能统计
                                self.performance_stats = {
                                                "total_knowledge_entries": 0,
                                                "successful_queries": 0,
                                                "knowledge_applications": 0,
                                                "average_effectiveness": 0.0,
                                                "rd_agent_insights_generated": 0,
                                                "knowledge_synthesis_operations": 0
                                }

                                # 加载现有知识库
                                asyncio.create_task(self._load_knowledge_base())

                                # 向量检索增强
                                self.vector_search_enabled = False
                                try:
                                                from sklearn.feature_extraction.text import TfidfVectorizer
                                                from sklearn.metrics.pairwise import cosine_similarity
                                                self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
                                                self.vector_search_enabled = True
                                                logger.info("  向量检索功能已启用")
                                except ImportError:
                                                logger.warning("  向量检索功能不可用，缺少scikit-learn")

                                logger.info(f"  {self.service_name} v{self.version} 知识库管理系统初始化完成")

                async def add_trading_experience_with_rd_agent(
                                self,
                                experience_data: Dict[str, Any],
                                outcomes: Dict[str, Any],
                                market_context: Dict[str, Any]
                ) -> str:
                                """使用RD-Agent添加交易经验"""

                                try:
                                                # 确保MCP连接
                                                await self.mcp_client.connect()

                                                # 构建经验分析请求
                                                experience_request = {
                                                                "task_type": "trading_experience_analysis",
                                                                "experience_data": experience_data,
                                                                "outcomes": outcomes,
                                                                "market_context": market_context,
                                                                "analysis_config": {
                                                                                "extract_patterns": True,
                                                                                "identify_success_factors": True,
                                                                                "analyze_failure_points": True,
                                                                                "generate_lessons": True
                                                                },
                                                                "rd_agent_config": {
                                                                                "use_pattern_recognition": True,
                                                                                "use_causal_analysis": True,
                                                                                "use_market_context": True,
                                                                                "use_behavioral_analysis": True
                                                                }
                                                }

                                                # 调用RD-Agent经验分析
                                                result = await self.mcp_client.call_tool(
                                                                "analyze_trading_experience",
                                                                experience_request
                                                )

                                                if result.get("status") == "success":
                                                                analysis_result = result.get("result", {})

                                                                # 创建知识条目
                                                                entry_id = f"exp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                                                                knowledge_entry = KnowledgeEntry(
                                                                                entry_id=entry_id,
                                                                                knowledge_type=KnowledgeType.TRADING_EXPERIENCE,
                                                                                title=analysis_result.get("title", "交易经验"),
                                                                                description=analysis_result.get("description", ""),
                                                                                context=market_context,
                                                                                outcomes=outcomes,
                                                                                lessons_learned=analysis_result.get("lessons_learned", []),
                                                                                confidence_score=analysis_result.get("confidence_score", 0.8),
                                                                                relevance_tags=analysis_result.get("tags", []),
                                                                                created_at=datetime.now(),
                                                                                updated_at=datetime.now()
                                                                )

                                                                # 存储知识条目
                                                                await self._store_knowledge_entry(knowledge_entry)

                                                                # 更新索引
                                                                await self._update_knowledge_index(knowledge_entry)

                                                                self.performance_stats["total_knowledge_entries"] += 1
                                                                self.performance_stats["rd_agent_insights_generated"] += 1

                                                                logger.info(f"  交易经验已添加到知识库: {entry_id}")
                                                                return entry_id
                                                else:
        pass  # 专业版模式
                                                                                experience_data, outcomes, market_context
                                                                )

                                except Exception as e:
                                                logger.error(f"  RD-Agent交易经验分析失败: {e}")
        pass  # 专业版模式
                                                                experience_data, outcomes, market_context
                                                )

                async def query_knowledge_with_rd_agent(
                                self,
                                query: KnowledgeQuery
                ) -> Dict[str, Any]:
                                """使用RD-Agent查询知识库"""

                                try:
                                                # 确保MCP连接
                                                await self.mcp_client.connect()

                                                # 构建知识查询请求
                                                query_request = {
                                                                "task_type": "knowledge_base_query",
                                                                "query_text": query.query_text,
                                                                "context": query.context,
                                                                "knowledge_types": [kt.value for kt in query.knowledge_types],
                                                                "search_config": {
                                                                                "max_results": query.max_results,
                                                                                "min_confidence": query.min_confidence,
                                                                                "semantic_search": True,
                                                                                "context_matching": True,
                                                                                "relevance_ranking": True
                                                                },
                                                                "rd_agent_config": {
                                                                                "use_semantic_understanding": True,
                                                                                "use_context_analysis": True,
                                                                                "use_relevance_scoring": True,
                                                                                "use_knowledge_synthesis": True
                                                                },
                                                                "knowledge_base_snapshot": await self._get_knowledge_base_snapshot()
                                                }

                                                # 调用RD-Agent知识查询
                                                result = await self.mcp_client.call_tool(
                                                                "query_knowledge_base",
                                                                query_request
                                                )

                                                if result.get("status") == "success":
                                                                query_result = result.get("result", {})

                                                                # 处理查询结果
                                                                processed_results = await self._process_query_results(
                                                                                query_result,
                                                                                query
                                                                )

                                                                # 生成知识合成
                                                                synthesized_knowledge = await self._synthesize_knowledge_with_rd_agent(
                                                                                processed_results,
                                                                                query.context
                                                                )

                                                                self.performance_stats["successful_queries"] += 1
                                                                if synthesized_knowledge:
                                                                                self.performance_stats["knowledge_synthesis_operations"] += 1

                                                                return {
                                                                                "success": True,
                                                                                "query_id": query.query_id,
                                                                                "results": processed_results,
                                                                                "synthesized_knowledge": synthesized_knowledge,
                                                                                "total_matches": len(processed_results),
                                                                                "rd_agent_insights": query_result.get("insights", {}),
                                                                                "relevance_scores": query_result.get("relevance_scores", [])
                                                                }
                                                else:
        pass  # 专业版模式

                                except Exception as e:
                                                logger.error(f"  RD-Agent知识查询失败: {e}")
        pass  # 专业版模式

                async def learn_from_failure_with_rd_agent(
                                self,
                                failure_data: Dict[str, Any],
                                root_cause_analysis: Dict[str, Any],
                                prevention_measures: List[str]
                ) -> str:
                                """使用RD-Agent从失败中学习"""

                                try:
                                                # 确保MCP连接
                                                await self.mcp_client.connect()

                                                # 构建失败分析请求
                                                failure_request = {
                                                                "task_type": "failure_case_analysis",
                                                                "failure_data": failure_data,
                                                                "root_cause_analysis": root_cause_analysis,
                                                                "prevention_measures": prevention_measures,
                                                                "analysis_config": {
                                                                                "deep_cause_analysis": True,
                                                                                "pattern_identification": True,
                                                                                "prevention_strategy_generation": True,
                                                                                "similar_case_matching": True
                                                                },
                                                                "rd_agent_config": {
                                                                                "use_causal_reasoning": True,
                                                                                "use_pattern_matching": True,
                                                                                "use_predictive_analysis": True,
                                                                                "use_prevention_optimization": True
                                                                },
                                                                "historical_failures": await self._get_historical_failures()
                                                }

                                                # 调用RD-Agent失败分析
                                                result = await self.mcp_client.call_tool(
                                                                "analyze_failure_case",
                                                                failure_request
                                                )

                                                if result.get("status") == "success":
                                                                analysis_result = result.get("result", {})

                                                                # 创建失败案例知识条目
                                                                entry_id = f"fail_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                                                                failure_entry = KnowledgeEntry(
                                                                                entry_id=entry_id,
                                                                                knowledge_type=KnowledgeType.FAILURE_CASE,
                                                                                title=analysis_result.get("title", "失败案例分析"),
                                                                                description=analysis_result.get("description", ""),
                                                                                context=failure_data,
                                                                                outcomes={"root_causes": root_cause_analysis, "prevention_measures": prevention_measures},
                                                                                lessons_learned=analysis_result.get("lessons_learned", []),
                                                                                confidence_score=analysis_result.get("confidence_score", 0.9),
                                                                                relevance_tags=analysis_result.get("tags", []),
                                                                                created_at=datetime.now(),
                                                                                updated_at=datetime.now()
                                                                )

                                                                # 存储失败案例
                                                                await self._store_knowledge_entry(failure_entry)

                                                                # 生成预防策略
                                                                prevention_strategies = await self._generate_prevention_strategies(
                                                                                analysis_result,
                                                                                failure_data
                                                                )

                                                                # 更新风险预警规则
                                                                await self._update_risk_warning_rules(prevention_strategies)

                                                                logger.info(f"  失败案例已添加到知识库: {entry_id}")
                                                                return entry_id
                                                else:
        pass  # 专业版模式
                                                                                failure_data, root_cause_analysis, prevention_measures
                                                                )

                                except Exception as e:
                                                logger.error(f"  RD-Agent失败分析失败: {e}")
        pass  # 专业版模式
                                                                failure_data, root_cause_analysis, prevention_measures
                                                )

                async def discover_strategy_patterns_with_rd_agent(
                                self,
                                strategy_data: List[Dict[str, Any]],
                                performance_data: List[Dict[str, Any]]
                ) -> Dict[str, Any]:
                                """使用RD-Agent发现策略模式"""

                                try:
                                                # 确保MCP连接
                                                await self.mcp_client.connect()

                                                # 构建模式发现请求
                                                pattern_request = {
                                                                "task_type": "strategy_pattern_discovery",
                                                                "strategy_data": strategy_data,
                                                                "performance_data": performance_data,
                                                                "discovery_config": {
                                                                                "pattern_types": ["success_patterns", "failure_patterns", "market_regime_patterns"],
                                                                                "min_pattern_support": 0.3,
                                                                                "min_pattern_confidence": 0.7,
                                                                                "include_temporal_patterns": True
                                                                },
                                                                "rd_agent_config": {
                                                                                "use_machine_learning": True,
                                                                                "use_pattern_mining": True,
                                                                                "use_statistical_analysis": True,
                                                                                "use_temporal_analysis": True
                                                                }
                                                }

                                                # 调用RD-Agent模式发现
                                                result = await self.mcp_client.call_tool(
                                                                "discover_strategy_patterns",
                                                                pattern_request
                                                )

                                                if result.get("status") == "success":
                                                                pattern_result = result.get("result", {})

                                                                # 处理发现的模式
                                                                discovered_patterns = await self._process_discovered_patterns(
                                                                                pattern_result,
                                                                                strategy_data,
                                                                                performance_data
                                                                )

                                                                # 存储模式到知识库
                                                                for pattern in discovered_patterns:
                                                                                await self._store_strategy_pattern(pattern)

                                                                return {
                                                                                "success": True,
                                                                                "discovered_patterns": discovered_patterns,
                                                                                "pattern_insights": pattern_result.get("insights", {}),
                                                                                "actionable_recommendations": pattern_result.get("recommendations", []),
                                                                                "confidence_scores": pattern_result.get("confidence_scores", {})
                                                                }
                                                else:
        pass  # 专业版模式

                                except Exception as e:
                                                logger.error(f"  RD-Agent策略模式发现失败: {e}")
        pass  # 专业版模式

                async def _synthesize_knowledge_with_rd_agent(
                                self,
                                query_results: List[Dict[str, Any]],
                                context: Dict[str, Any]
                ) -> Dict[str, Any]:
                                """使用RD-Agent合成知识"""

                                try:
                                                # 构建知识合成请求
                                                synthesis_request = {
                                                                "task_type": "knowledge_synthesis",
                                                                "query_results": query_results,
                                                                "context": context,
                                                                "synthesis_config": {
                                                                                "synthesis_type": "comprehensive",
                                                                                "include_contradictions": True,
                                                                                "generate_insights": True,
                                                                                "create_actionable_advice": True
                                                                },
                                                                "rd_agent_config": {
                                                                                "use_reasoning": True,
                                                                                "use_knowledge_integration": True,
                                                                                "use_insight_generation": True,
                                                                                "use_advice_optimization": True
                                                                }
                                                }

                                                # 调用RD-Agent知识合成
                                                result = await self.mcp_client.call_tool(
                                                                "synthesize_knowledge",
                                                                synthesis_request
                                                )

                                                if result.get("status") == "success":
                                                                synthesis_result = result.get("result", {})

                                                                return {
                                                                                "synthesized_insights": synthesis_result.get("insights", []),
                                                                                "actionable_advice": synthesis_result.get("advice", []),
                                                                                "confidence_level": synthesis_result.get("confidence", 0.8),
                                                                                "knowledge_gaps": synthesis_result.get("gaps", []),
                                                                                "contradictions_found": synthesis_result.get("contradictions", []),
                                                                                "synthesis_quality": synthesis_result.get("quality_score", 0.8)
                                                                }
                                                else:
                                                                return {}

                                except Exception as e:
                                                logger.warning(f"  RD-Agent知识合成失败: {e}")
                                                return {}

                async def _store_knowledge_entry(self, entry: KnowledgeEntry):
                                """存储知识条目"""

                                try:
                                                # 存储到内存
                                                self.knowledge_stores[entry.knowledge_type][entry.entry_id] = entry

                                                # 持久化存储
                                                storage_file = self.knowledge_base_dir / f"{entry.knowledge_type.value}_{entry.entry_id}.pkl"
                                                with open(storage_file, 'wb') as f:
                                                                pickle.dump(asdict(entry), f)

                                                logger.debug(f"知识条目已存储: {entry.entry_id}")

                                except Exception as e:
                                                logger.error(f"  存储知识条目失败: {e}")

                async def _update_knowledge_index(self, entry: KnowledgeEntry):
                                """更新知识索引"""

                                try:
                                                # 按标签索引
                                                for tag in entry.relevance_tags:
                                                                if tag not in self.knowledge_index["by_tags"]:
                                                                                self.knowledge_index["by_tags"][tag] = []
                                                                self.knowledge_index["by_tags"][tag].append(entry.entry_id)

                                                # 按上下文索引
                                                context_key = json.dumps(entry.context, sort_keys=True)
                                                if context_key not in self.knowledge_index["by_context"]:
                                                                self.knowledge_index["by_context"][context_key] = []
                                                self.knowledge_index["by_context"][context_key].append(entry.entry_id)

                                                # 按效果索引
                                                effectiveness_bucket = int(entry.effectiveness_score * 10)
                                                if effectiveness_bucket not in self.knowledge_index["by_effectiveness"]:
                                                                self.knowledge_index["by_effectiveness"][effectiveness_bucket] = []
                                                self.knowledge_index["by_effectiveness"][effectiveness_bucket].append(entry.entry_id)

                                                # 按时间索引
                                                date_key = entry.created_at.strftime("%Y%m")
                                                if date_key not in self.knowledge_index["by_recency"]:
                                                                self.knowledge_index["by_recency"][date_key] = []
                                                self.knowledge_index["by_recency"][date_key].append(entry.entry_id)

                                except Exception as e:
                                                logger.error(f"  更新知识索引失败: {e}")

                async def get_knowledge_statistics(self) -> Dict[str, Any]:
                                """获取知识库统计"""

                                total_entries = sum(len(store) for store in self.knowledge_stores.values())

                                type_distribution = {
                                                kt.value: len(self.knowledge_stores[kt]) 
                                                for kt in KnowledgeType
                                }

                                # 计算平均效果分数
                                all_entries = []
                                for store in self.knowledge_stores.values():
                                                all_entries.extend(store.values())

                                avg_effectiveness = 0.0
                                if all_entries:
                                                avg_effectiveness = np.mean([entry.effectiveness_score for entry in all_entries])

                                return {
                                                "total_knowledge_entries": total_entries,
                                                "type_distribution": type_distribution,
                                                "average_effectiveness": avg_effectiveness,
                                                "index_sizes": {
                                                                "by_tags": len(self.knowledge_index["by_tags"]),
                                                                "by_context": len(self.knowledge_index["by_context"]),
                                                                "by_effectiveness": len(self.knowledge_index["by_effectiveness"]),
                                                                "by_recency": len(self.knowledge_index["by_recency"])
                                                },
                                                **self.performance_stats,
                                                "service_type": "rd_agent_knowledge_manager",
                                                "rd_agent_integration": True
                                }
