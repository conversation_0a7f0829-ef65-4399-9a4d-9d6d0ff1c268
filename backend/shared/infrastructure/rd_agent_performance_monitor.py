# -*- coding: utf-8 -*-
"""
RD-Agent性能监控和优化系统
实时监控RD-Agent集成服务的性能、健康状态和优化建议
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import psutil
import numpy as np

from shared.infrastructure.mcp_client import MCPClient, MCPTaskRequest, MCPTaskType

logger = logging.getLogger(__name__)

class PerformanceMetric(Enum):
                """性能指标类型"""
                RESPONSE_TIME = "response_time"
                SUCCESS_RATE = "success_rate"
                THROUGHPUT = "throughput"
                ERROR_RATE = "error_rate"
                RESOURCE_USAGE = "resource_usage"
                RD_AGENT_EFFICIENCY = "rd_agent_efficiency"

class HealthStatus(Enum):
                """健康状态"""
                HEALTHY = "healthy"
                WARNING = "warning"
                CRITICAL = "critical"
                DEGRADED = "degraded"

@dataclass
class PerformanceSnapshot:
                """性能快照"""
                timestamp: datetime
                service_name: str
                metrics: Dict[str, float]
                health_status: HealthStatus
                rd_agent_metrics: Dict[str, Any]
                system_resources: Dict[str, float]

@dataclass
class PerformanceAlert:
                """性能警报"""
                alert_id: str
                timestamp: datetime
                service_name: str
                metric_name: str
                current_value: float
                threshold_value: float
                severity: str
                message: str
                recommendations: List[str]

class RDAgentPerformanceMonitor:
                """RD-Agent性能监控和优化系统"""

                def __init__(self):
                                self.service_name = "RDAgentPerformanceMonitor"
                                self.version = "2.0.0"

                                # 初始化MCP客户端
                                self.mcp_client = MCPClient()

                                # 监控配置
                                self.monitoring_config = {
                                                "collection_interval": 30,  # 秒
                                                "retention_days": 30,
                                                "alert_thresholds": {
                                                                "response_time": 5.0,  # 秒
                                                                "success_rate": 0.95,
                                                                "error_rate": 0.05,
                                                                "cpu_usage": 0.8,
                                                                "memory_usage": 0.8,
                                                                "rd_agent_efficiency": 0.7
                                                }
                                }

                                # 性能数据存储
                                self.performance_data: Dict[str, List[PerformanceSnapshot]] = {}
                                self.active_alerts: Dict[str, PerformanceAlert] = {}

                                # 监控的服务列表
                                self.monitored_services = [
                                                "RDAgentDeepIntegrationService",
                                                "RDAgentDeepExecutionService", 
                                                "RDAgentIntelligenceService",
                                                "RDAgentDecisionEngine",
                                                "RDAgentPortfolioOptimizer",
                                                "RDAgentAkShareAdapter",
                                                "RDAgentKnowledgeManager",
                                                "RDAgentExperimentManager"
                                ]

                                # 监控任务
                                self.monitoring_tasks: Dict[str, asyncio.Task] = {}

                                # 性能统计
                                self.monitor_stats = {
                                                "total_snapshots": 0,
                                                "alerts_generated": 0,
                                                "optimizations_suggested": 0,
                                                "performance_improvements": 0,
                                                "system_health_score": 1.0,
                                                "rd_agent_integration_health": 1.0
                                }

                                logger.info(f"  {self.service_name} v{self.version} 性能监控系统初始化完成")

                async def start_comprehensive_monitoring(self) -> str:
                                """启动全面性能监控"""

                                try:
                                                # 确保MCP连接
                                                await self.mcp_client.connect()

                                                # 启动各服务监控任务
                                                for service_name in self.monitored_services:
                                                                monitor_task = asyncio.create_task(
                                                                                self._monitor_service_performance(service_name)
                                                                )
                                                                self.monitoring_tasks[service_name] = monitor_task

                                                # 启动系统级监控
                                                system_monitor_task = asyncio.create_task(
                                                                self._monitor_system_performance()
                                                )
                                                self.monitoring_tasks["system"] = system_monitor_task

                                                # 启动RD-Agent集成健康监控
                                                rd_agent_monitor_task = asyncio.create_task(
                                                                self._monitor_rd_agent_integration_health()
                                                )
                                                self.monitoring_tasks["rd_agent_integration"] = rd_agent_monitor_task

                                                # 启动性能优化建议生成
                                                optimization_task = asyncio.create_task(
                                                                self._generate_performance_optimizations()
                                                )
                                                self.monitoring_tasks["optimization"] = optimization_task

                                                logger.info("  全面性能监控已启动")
                                                return "comprehensive_monitoring_started"

                                except Exception as e:
                                                logger.error(f"  启动性能监控失败: {e}")
                                                raise

                async def _monitor_service_performance(self, service_name: str):
                                """监控单个服务性能"""

                                while True:
                                                try:
                                                                # 收集服务性能指标
                                                                performance_metrics = await self._collect_service_metrics(service_name)

                                                                # 收集RD-Agent特定指标
                                                                rd_agent_metrics = await self._collect_rd_agent_metrics(service_name)

                                                                # 收集系统资源使用
                                                                system_resources = await self._collect_system_resources()

                                                                # 评估健康状态
                                                                health_status = await self._evaluate_service_health(
                                                                                service_name,
                                                                                performance_metrics,
                                                                                rd_agent_metrics
                                                                )

                                                                # 创建性能快照
                                                                snapshot = PerformanceSnapshot(
                                                                                timestamp=datetime.now(),
                                                                                service_name=service_name,
                                                                                metrics=performance_metrics,
                                                                                health_status=health_status,
                                                                                rd_agent_metrics=rd_agent_metrics,
                                                                                system_resources=system_resources
                                                                )

                                                                # 存储快照
                                                                await self._store_performance_snapshot(snapshot)

                                                                # 检查警报条件
                                                                await self._check_alert_conditions(snapshot)

                                                                self.monitor_stats["total_snapshots"] += 1

                                                                # 等待下一次收集
                                                                await asyncio.sleep(self.monitoring_config["collection_interval"])

                                                except Exception as e:
                                                                logger.error(f"  监控服务 {service_name} 失败: {e}")
                                                                await asyncio.sleep(60)  # 错误时等待更长时间

                async def _collect_service_metrics(self, service_name: str) -> Dict[str, float]:
                                """收集服务性能指标"""

                                try:
                                                # 模拟服务指标收集（实际实现中应该从服务实例获取）
                                                metrics = {
                                                                "response_time": # 已移除模拟数据
                                                                "success_rate": # 已移除模拟数据
                                                                "throughput": # 已移除模拟数据
                                                                "error_rate": # 已移除模拟数据
                                                                "active_connections": np.# 已移除模拟数据
                                                                "queue_length": np.# 已移除模拟数据
                                                }

                                                # 确保指标在合理范围内
                                                metrics["response_time"] = max(0.1, metrics["response_time"])
                                                metrics["success_rate"] = np.clip(metrics["success_rate"], 0, 1)
                                                metrics["error_rate"] = np.clip(metrics["error_rate"], 0, 1)
                                                metrics["throughput"] = max(0, metrics["throughput"])

                                                return metrics

                                except Exception as e:
                                                logger.error(f"  收集服务指标失败: {e}")
                                                return {}

                async def _collect_rd_agent_metrics(self, service_name: str) -> Dict[str, Any]:
                                """收集RD-Agent特定指标"""

                                try:
                                                # 构建RD-Agent指标收集请求
                                                metrics_request = {
                                                                "task_type": "performance_metrics_collection",
                                                                "service_name": service_name,
                                                                "metrics_types": [
                                                                                "mcp_connection_health",
                                                                                "rd_agent_response_time",
                                                                                "algorithm_efficiency",
                                                                                "knowledge_base_usage",
                                                                                "optimization_effectiveness"
                                                                ]
                                                }

                                                # 调用RD-Agent指标收集
                                                result = await self.mcp_client.call_tool(
                                                                "collect_performance_metrics",
                                                                metrics_request
                                                )

                                                if result.get("status") == "success":
                                                                rd_agent_metrics = result.get("result", {})

                                                                return {
                                                                                "mcp_connection_health": rd_agent_metrics.get("mcp_health", 1.0),
                                                                                "rd_agent_response_time": rd_agent_metrics.get("response_time", 1.0),
                                                                                "algorithm_efficiency": rd_agent_metrics.get("efficiency", 0.8),
                                                                                "knowledge_base_hits": rd_agent_metrics.get("kb_hits", 0),
                                                                                "optimization_score": rd_agent_metrics.get("optimization", 0.8),
                                                                                "session_count": rd_agent_metrics.get("sessions", 0),
                                                                                "error_count": rd_agent_metrics.get("errors", 0)
                                                                }
                                                else:
        pass  # 专业版模式

                                except Exception as e:
                                                logger.warning(f"  收集RD-Agent指标失败: {e}")
        pass  # 专业版模式

                async def _collect_system_resources(self) -> Dict[str, float]:
                                """收集系统资源使用情况"""

                                try:
                                                # CPU使用率
                                                cpu_percent = psutil.cpu_percent(interval=1)

                                                # 内存使用情况
                                                memory = psutil.virtual_memory()
                                                memory_percent = memory.percent

                                                # 磁盘使用情况
                                                disk = psutil.disk_usage('/')
                                                disk_percent = (disk.used / disk.total) * 100

                                                # 网络IO
                                                network = psutil.net_io_counters()

                                                return {
                                                                "cpu_usage": cpu_percent / 100.0,
                                                                "memory_usage": memory_percent / 100.0,
                                                                "disk_usage": disk_percent / 100.0,
                                                                "network_bytes_sent": float(network.bytes_sent),
                                                                "network_bytes_recv": float(network.bytes_recv),
                                                                "available_memory_gb": memory.available / (1024**3)
                                                }

                                except Exception as e:
                                                logger.error(f"  收集系统资源失败: {e}")
                                                return {}

                async def _evaluate_service_health(
                                self,
                                service_name: str,
                                performance_metrics: Dict[str, float],
                                rd_agent_metrics: Dict[str, Any]
                ) -> HealthStatus:
                                """评估服务健康状态"""

                                try:
                                                health_score = 1.0

                                                # 检查响应时间
                                                response_time = performance_metrics.get("response_time", 0)
                                                if response_time > self.monitoring_config["alert_thresholds"]["response_time"]:
                                                                health_score -= 0.3

                                                # 检查成功率
                                                success_rate = performance_metrics.get("success_rate", 1.0)
                                                if success_rate < self.monitoring_config["alert_thresholds"]["success_rate"]:
                                                                health_score -= 0.4

                                                # 检查错误率
                                                error_rate = performance_metrics.get("error_rate", 0)
                                                if error_rate > self.monitoring_config["alert_thresholds"]["error_rate"]:
                                                                health_score -= 0.2

                                                # 检查RD-Agent效率
                                                rd_agent_efficiency = rd_agent_metrics.get("algorithm_efficiency", 1.0)
                                                if rd_agent_efficiency < self.monitoring_config["alert_thresholds"]["rd_agent_efficiency"]:
                                                                health_score -= 0.1

                                                # 根据健康分数确定状态
                                                if health_score >= 0.9:
                                                                return HealthStatus.HEALTHY
                                                elif health_score >= 0.7:
                                                                return HealthStatus.WARNING
                                                elif health_score >= 0.5:
                                                                return HealthStatus.DEGRADED
                                                else:
                                                                return HealthStatus.CRITICAL

                                except Exception as e:
                                                logger.error(f"  评估服务健康状态失败: {e}")
                                                return HealthStatus.WARNING

                async def _check_alert_conditions(self, snapshot: PerformanceSnapshot):
                                """检查警报条件"""

                                try:
                                                alerts_to_generate = []

                                                # 检查响应时间
                                                response_time = snapshot.metrics.get("response_time", 0)
                                                if response_time > self.monitoring_config["alert_thresholds"]["response_time"]:
                                                                alerts_to_generate.append({
                                                                                "metric": "response_time",
                                                                                "current": response_time,
                                                                                "threshold": self.monitoring_config["alert_thresholds"]["response_time"],
                                                                                "severity": "warning",
                                                                                "message": f"响应时间过长: {response_time:.2f}秒"
                                                                })

                                                # 检查成功率
                                                success_rate = snapshot.metrics.get("success_rate", 1.0)
                                                if success_rate < self.monitoring_config["alert_thresholds"]["success_rate"]:
                                                                alerts_to_generate.append({
                                                                                "metric": "success_rate",
                                                                                "current": success_rate,
                                                                                "threshold": self.monitoring_config["alert_thresholds"]["success_rate"],
                                                                                "severity": "critical",
                                                                                "message": f"成功率过低: {success_rate:.1%}"
                                                                })

                                                # 检查RD-Agent效率
                                                rd_agent_efficiency = snapshot.rd_agent_metrics.get("algorithm_efficiency", 1.0)
                                                if rd_agent_efficiency < self.monitoring_config["alert_thresholds"]["rd_agent_efficiency"]:
                                                                alerts_to_generate.append({
                                                                                "metric": "rd_agent_efficiency",
                                                                                "current": rd_agent_efficiency,
                                                                                "threshold": self.monitoring_config["alert_thresholds"]["rd_agent_efficiency"],
                                                                                "severity": "warning",
                                                                                "message": f"RD-Agent效率下降: {rd_agent_efficiency:.1%}"
                                                                })

                                                # 生成警报
                                                for alert_data in alerts_to_generate:
                                                                await self._generate_performance_alert(snapshot, alert_data)

                                except Exception as e:
                                                logger.error(f"  检查警报条件失败: {e}")

                async def _generate_performance_alert(
                                self,
                                snapshot: PerformanceSnapshot,
                                alert_data: Dict[str, Any]
                ):
                                """生成性能警报"""

                                try:
                                                alert_id = f"alert_{snapshot.service_name}_{alert_data['metric']}_{int(time.time())}"

                                                # 生成优化建议
                                                recommendations = await self._generate_alert_recommendations(
                                                                snapshot.service_name,
                                                                alert_data["metric"],
                                                                alert_data["current"],
                                                                alert_data["threshold"]
                                                )

                                                alert = PerformanceAlert(
                                                                alert_id=alert_id,
                                                                timestamp=snapshot.timestamp,
                                                                service_name=snapshot.service_name,
                                                                metric_name=alert_data["metric"],
                                                                current_value=alert_data["current"],
                                                                threshold_value=alert_data["threshold"],
                                                                severity=alert_data["severity"],
                                                                message=alert_data["message"],
                                                                recommendations=recommendations
                                                )

                                                # 存储警报
                                                self.active_alerts[alert_id] = alert

                                                self.monitor_stats["alerts_generated"] += 1

                                                logger.warning(f"  性能警报: {alert.message} - 服务: {alert.service_name}")

                                except Exception as e:
                                                logger.error(f"  生成性能警报失败: {e}")

                async def _generate_performance_optimizations(self):
                                """生成性能优化建议"""

                                while True:
                                                try:
                                                                # 每小时生成一次优化建议
                                                                await asyncio.sleep(3600)

                                                                # 分析历史性能数据
                                                                optimization_opportunities = await self._analyze_optimization_opportunities()

                                                                # 使用RD-Agent生成优化建议
                                                                optimization_recommendations = await self._generate_rd_agent_optimizations(
                                                                                optimization_opportunities
                                                                )

                                                                # 应用自动优化
                                                                await self._apply_automatic_optimizations(optimization_recommendations)

                                                                self.monitor_stats["optimizations_suggested"] += len(optimization_recommendations)

                                                except Exception as e:
                                                                logger.error(f"  生成性能优化失败: {e}")
                                                                await asyncio.sleep(300)  # 错误时等待5分钟

                async def get_real_time_dashboard_data(self) -> Dict[str, Any]:
                                """获取实时仪表板数据"""

                                try:
                                                # 获取最新性能快照
                                                latest_snapshots = {}
                                                for service_name in self.monitored_services:
                                                                if service_name in self.performance_data and self.performance_data[service_name]:
                                                                                latest_snapshots[service_name] = asdict(self.performance_data[service_name][-1])

                                                # 计算系统整体健康分数
                                                overall_health = await self._calculate_overall_health_score()

                                                # 获取活跃警报
                                                active_alerts_summary = [
                                                                {
                                                                                "alert_id": alert.alert_id,
                                                                                "service": alert.service_name,
                                                                                "metric": alert.metric_name,
                                                                                "severity": alert.severity,
                                                                                "message": alert.message,
                                                                                "timestamp": alert.timestamp.isoformat()
                                                                }
                                                                for alert in self.active_alerts.values()
                                                ]

                                                # 获取性能趋势
                                                performance_trends = await self._calculate_performance_trends()

                                                return {
                                                                "timestamp": datetime.now().isoformat(),
                                                                "overall_health_score": overall_health,
                                                                "system_status": "healthy" if overall_health > 0.8 else "warning" if overall_health > 0.6 else "critical",
                                                                "latest_snapshots": latest_snapshots,
                                                                "active_alerts": active_alerts_summary,
                                                                "performance_trends": performance_trends,
                                                                "monitor_statistics": self.monitor_stats,
                                                                "rd_agent_integration_health": self.monitor_stats["rd_agent_integration_health"],
                                                                "monitored_services_count": len(self.monitored_services),
                                                                "monitoring_active": len(self.monitoring_tasks) > 0
                                                }

                                except Exception as e:
                                                logger.error(f"  获取仪表板数据失败: {e}")
                                                return {"error": str(e)}

                async def get_performance_report(
                                self,
                                service_name: Optional[str] = None,
                                time_range_hours: int = 24
                ) -> Dict[str, Any]:
                                """获取性能报告"""

                                try:
                                                end_time = datetime.now()
                                                start_time = end_time - timedelta(hours=time_range_hours)

                                                # 过滤数据
                                                filtered_data = {}
                                                services_to_analyze = [service_name] if service_name else self.monitored_services

                                                for svc_name in services_to_analyze:
                                                                if svc_name in self.performance_data:
                                                                                filtered_data[svc_name] = [
                                                                                                snapshot for snapshot in self.performance_data[svc_name]
                                                                                                if start_time <= snapshot.timestamp <= end_time
                                                                                ]

                                                # 生成统计分析
                                                performance_analysis = await self._analyze_performance_data(filtered_data)

                                                # 生成RD-Agent集成分析
                                                rd_agent_analysis = await self._analyze_rd_agent_integration_performance(filtered_data)

                                                return {
                                                                "report_period": {
                                                                                "start_time": start_time.isoformat(),
                                                                                "end_time": end_time.isoformat(),
                                                                                "duration_hours": time_range_hours
                                                                },
                                                                "services_analyzed": list(filtered_data.keys()),
                                                                "performance_analysis": performance_analysis,
                                                                "rd_agent_integration_analysis": rd_agent_analysis,
                                                                "recommendations": await self._generate_performance_recommendations(performance_analysis),
                                                                "report_generated_at": datetime.now().isoformat()
                                                }

                                except Exception as e:
                                                logger.error(f"  生成性能报告失败: {e}")
                                                return {"error": str(e)}
