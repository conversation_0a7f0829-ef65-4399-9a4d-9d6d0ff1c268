#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一错误处理和日志系统
提供完善的错误处理机制和性能监控
"""

import logging
import traceback
import time
import functools
from datetime import datetime
from typing import Any, Callable, Dict, Optional
from enum import Enum
import asyncio

class ErrorLevel(Enum):
    """错误等级"""
    CRITICAL = "critical"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    DEBUG = "debug"

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.start_times = {}
    
    def start_timing(self, operation: str):
        """开始计时"""
        self.start_times[operation] = time.time()
    
    def end_timing(self, operation: str) -> float:
        """结束计时并返回耗时"""
        if operation in self.start_times:
            duration = time.time() - self.start_times[operation]
            self.metrics[operation] = self.metrics.get(operation, [])
            self.metrics[operation].append(duration)
            del self.start_times[operation]
            return duration
        return 0.0
    
    def get_average_time(self, operation: str) -> float:
        """获取平均耗时"""
        if operation in self.metrics and self.metrics[operation]:
            return sum(self.metrics[operation]) / len(self.metrics[operation])
        return 0.0
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取性能指标摘要"""
        summary = {}
        for operation, times in self.metrics.items():
            if times:
                summary[operation] = {
                    "count": len(times),
                    "avg_time": sum(times) / len(times),
                    "min_time": min(times),
                    "max_time": max(times),
                    "total_time": sum(times)
                }
        return summary

class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.logger = logging.getLogger(service_name)
        self.performance_monitor = PerformanceMonitor()
        self.error_counts = {}
        
        # 配置日志格式
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_error(self, error: Exception, level: ErrorLevel = ErrorLevel.ERROR, 
                  context: Dict[str, Any] = None):
        """记录错误"""
        
        error_type = type(error).__name__
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        error_info = {
            "service": self.service_name,
            "error_type": error_type,
            "error_message": str(error),
            "error_count": self.error_counts[error_type],
            "timestamp": datetime.now().isoformat(),
            "context": context or {}
        }
        
        if level == ErrorLevel.CRITICAL:
            self.logger.critical(f"CRITICAL ERROR: {error_info}")
        elif level == ErrorLevel.ERROR:
            self.logger.error(f"ERROR: {error_info}")
        elif level == ErrorLevel.WARNING:
            self.logger.warning(f"WARNING: {error_info}")
        else:
            self.logger.info(f"INFO: {error_info}")
        
        # 记录详细堆栈信息（仅在DEBUG模式）
        if self.logger.level <= logging.DEBUG:
            self.logger.debug(f"Stack trace: {traceback.format_exc()}")
    
    def handle_service_error(self, operation: str, error: Exception, 
                           default_return: Any = None) -> Dict[str, Any]:
        """处理服务错误并返回标准格式"""
        
        self.log_error(error, ErrorLevel.ERROR, {"operation": operation})
        
        return {
            "success": False,
            "error": str(error),
            "error_type": type(error).__name__,
            "service": self.service_name,
            "operation": operation,
            "timestamp": datetime.now().isoformat(),
            "data": default_return
        }
    
    def create_success_response(self, data: Any, operation: str = None) -> Dict[str, Any]:
        """创建成功响应"""
        
        return {
            "success": True,
            "data": data,
            "service": self.service_name,
            "operation": operation,
            "timestamp": datetime.now().isoformat()
        }
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        
        return {
            "service": self.service_name,
            "error_counts": self.error_counts,
            "total_errors": sum(self.error_counts.values()),
            "performance_metrics": self.performance_monitor.get_metrics_summary()
        }

def with_error_handling(service_name: str, operation: str = None, 
                       default_return: Any = None, log_performance: bool = True):
    """错误处理装饰器"""
    
    def decorator(func: Callable) -> Callable:
        error_handler = ErrorHandler(service_name)
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            op_name = operation or func.__name__
            
            try:
                if log_performance:
                    error_handler.performance_monitor.start_timing(op_name)
                
                result = await func(*args, **kwargs)
                
                if log_performance:
                    duration = error_handler.performance_monitor.end_timing(op_name)
                    if duration > 5.0:  # 超过5秒记录警告
                        error_handler.logger.warning(f"Slow operation {op_name}: {duration:.2f}s")
                
                return result
                
            except Exception as e:
                if log_performance:
                    error_handler.performance_monitor.end_timing(op_name)
                
                return error_handler.handle_service_error(op_name, e, default_return)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            op_name = operation or func.__name__
            
            try:
                if log_performance:
                    error_handler.performance_monitor.start_timing(op_name)
                
                result = func(*args, **kwargs)
                
                if log_performance:
                    duration = error_handler.performance_monitor.end_timing(op_name)
                    if duration > 5.0:
                        error_handler.logger.warning(f"Slow operation {op_name}: {duration:.2f}s")
                
                return result
                
            except Exception as e:
                if log_performance:
                    error_handler.performance_monitor.end_timing(op_name)
                
                return error_handler.handle_service_error(op_name, e, default_return)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator

def validate_input(required_fields: list, optional_fields: list = None):
    """输入验证装饰器"""
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            for field in required_fields:
                if field not in kwargs or kwargs[field] is None:
                    raise ValueError(f"Required field '{field}' is missing or None")
            
            return await func(*args, **kwargs)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            for field in required_fields:
                if field not in kwargs or kwargs[field] is None:
                    raise ValueError(f"Required field '{field}' is missing or None")
            
            return func(*args, **kwargs)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator

class ServiceHealthChecker:
    """服务健康检查器"""
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.last_check = None
        self.health_status = "unknown"
        self.error_handler = ErrorHandler(service_name)
    
    async def check_health(self) -> Dict[str, Any]:
        """检查服务健康状态"""
        
        try:
            self.last_check = datetime.now()
            
            # 基础健康检查
            health_checks = {
                "service_name": self.service_name,
                "timestamp": self.last_check.isoformat(),
                "status": "healthy",
                "checks": {
                    "memory_usage": self._check_memory_usage(),
                    "error_rate": self._check_error_rate(),
                    "response_time": self._check_response_time()
                }
            }
            
            # 判断整体健康状态
            failed_checks = [k for k, v in health_checks["checks"].items() if not v.get("passed", True)]
            
            if len(failed_checks) >= 2:
                health_checks["status"] = "unhealthy"
            elif len(failed_checks) == 1:
                health_checks["status"] = "degraded"
            else:
                health_checks["status"] = "healthy"
            
            self.health_status = health_checks["status"]
            return health_checks
            
        except Exception as e:
            self.error_handler.log_error(e, ErrorLevel.ERROR)
            return {
                "service_name": self.service_name,
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _check_memory_usage(self) -> Dict[str, Any]:
        """检查内存使用"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            return {
                "passed": memory.percent < 90,
                "usage_percent": memory.percent,
                "available_gb": memory.available / (1024**3)
            }
        except:
            return {"passed": True, "error": "无法检查内存"}
    
    def _check_error_rate(self) -> Dict[str, Any]:
        """检查错误率"""
        try:
            stats = self.error_handler.get_error_statistics()
            total_errors = stats.get("total_errors", 0)
            return {
                "passed": total_errors < 10,
                "total_errors": total_errors,
                "error_types": len(stats.get("error_counts", {}))
            }
        except:
            return {"passed": True, "error": "无法检查错误率"}
    
    def _check_response_time(self) -> Dict[str, Any]:
        """检查响应时间"""
        try:
            metrics = self.error_handler.performance_monitor.get_metrics_summary()
            avg_times = [m.get("avg_time", 0) for m in metrics.values()]
            max_avg_time = max(avg_times) if avg_times else 0
            return {
                "passed": max_avg_time < 5.0,
                "max_avg_time": max_avg_time,
                "operations_count": len(metrics)
            }
        except:
            return {"passed": True, "error": "无法检查响应时间"}

# 全局错误处理器实例
global_error_handler = ErrorHandler("GlobalSystem")
