{"fix_time": "2025-06-23T15:31:37.696680", "total_files_fixed": 92, "fixed_files": ["D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\client_service_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\automation\\quantitative_research_automation.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\technical\\real_technical_calculator.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\separated_trading_statistics_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\check_stock_database_structure.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\enhanced_stock_screening_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\collect_all_stocks_eastmoney.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\monitoring\\advanced_monitoring_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\enhanced_learning_support_methods.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\check_annual_data.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\eastmoney_realtime_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\learning_optimization_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\risk\\tianji_risk_integration.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\shared\\data_sources\\unified_data_source_manager.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\simple_tianquan_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\legacy_roles\\yaoguang_star_legacy\\yaoguang_star\\services\\rd_agent_distribution_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\test_complete_tianshu_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\monitor_collection_progress.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\cache\\stock_data_cache.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\real_market_scanning_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianshu_star\\services\\tianshu_automation_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\rd_agent_localized\\rd_loop_controller.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\system_real_status.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\eastmoney_weekly_collector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\force_real_news_collection.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\legacy_roles\\yaoguang_star_legacy\\yaoguang_star\\services\\network_analysis_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\final_real_data_verification.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\final_verification_report.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\new_sina_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\core\\domain\\base_models.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\core\\unified_data_collector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\intelligent_portfolio_builder.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianshu_star\\services\\tianshu_core_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\complete_a_stock_collector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\advanced_execution_system.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\rd_agent_integration_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\complete_a_stock_collection.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\multi_source_data_manager.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\services\\plan_task_management_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\yaoguang\\intelligent_data_collector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\efficient_a_stock_collector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\test_all_fixes.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\services\\risk_assessment_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\main.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\intelligent_execution_and_profit_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\api\\roles\\tianji_star_api.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\services\\rd_agent_system_optimizer.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\fix_specific_issues.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\full_a_stock_collection.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\daily_stock_update_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\analyze_collection_status.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\news\\news_knowledge_base_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\real_opportunity_push_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\stock_selection_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\alert_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\enhanced_sina_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\fix_database_paths.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\workflows\\stress_test_workflow.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\monitor_failure_rate.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\check_databases.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\simple_eastmoney_collector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\daily_stock_database_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\professional_sina_finance_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\unified_financial_api.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\test_tianshu_deep.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\simple_stock_collector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\simple_eastmoney_test.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\safe_api_crawler.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\verify_eastmoney_data.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\risk_manager_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\legacy_roles\\yaoguang_star_legacy\\yaoguang_star\\services\\distribution_management_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\comprehensive_data_collection_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\annual_a_stock_collector.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\kaiyang_star\\services\\real_stock_screening_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianquan_star\\config\\__init__.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\data_management_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\core\\historical_data_manager.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\fix_all_issues_comprehensive.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\core\\domain\\memory\\legendary\\storage.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yaoguang_star\\services\\east_money_data_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\tianji_star\\workflows\\portfolio_analysis_workflow.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\legacy_roles\\yaoguang_star_legacy\\yaoguang_star\\services\\permission_control_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\test_kaiyang_tianquan_transfer.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\legacy_roles\\yaoguang_star_legacy\\yaoguang_star\\services\\commission_calculation_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\scripts\\create_tianquan_databases.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\yuheng_star\\services\\rd_agent_execution_optimizer.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\rd_agent_localized\\config.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\services\\data\\unified_real_price_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\comprehensive_database_audit.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\api\\roles\\yaoguang_star_api.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\roles\\legacy_roles\\yaoguang_star_legacy\\yaoguang_star\\services\\performance_monitoring_service.py", "D:\\kbb-pro\\hope\\s5\\seven\\backend\\fix_all_errors_warnings_comprehensive.py"], "issues_fixed": ["移除新浪API调用（解决超时问题）", "修复JSONP解析问题（解决天枢星API错误）", "修复数据库路径问题（使用绝对路径）", "移除降级模式指标（解决瑶光星简化问题）"], "status": "completed"}