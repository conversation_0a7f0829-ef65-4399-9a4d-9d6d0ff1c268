#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试类的完整性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_class_integrity():
    """测试类的完整性"""
    try:
        print("🔍 检查RDAgentIntegrationService类...")
        
        # 直接导入类
        from roles.yaoguang_star.services.rd_agent_integration_service import RDAgentIntegrationService
        
        print("✅ 类导入成功")
        
        # 检查类的方法
        print("🔍 检查类方法...")
        methods = [method for method in dir(RDAgentIntegrationService) if not method.startswith('__')]
        print(f"类方法数量: {len(methods)}")
        
        # 检查关键方法
        key_methods = [
            'get_service_status',
            'generate_new_factors', 
            'get_alpha158_factors',
            '_generate_professional_factors',

        ]
        
        for method_name in key_methods:
            if hasattr(RDAgentIntegrationService, method_name):
                print(f"✅ {method_name} 在类中存在")
            else:
                print(f"❌ {method_name} 在类中不存在")
        
        # 尝试创建实例
        print("🔍 尝试创建实例...")
        try:
            instance = RDAgentIntegrationService()
            print("✅ 实例创建成功")
            
            # 检查实例方法
            for method_name in key_methods:
                if hasattr(instance, method_name):
                    print(f"✅ {method_name} 在实例中存在")
                else:
                    print(f"❌ {method_name} 在实例中不存在")
                    
        except Exception as e:
            print(f"❌ 实例创建失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_class_integrity()
