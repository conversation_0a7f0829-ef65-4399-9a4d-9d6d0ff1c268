#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整8阶段学习流程测试
详细测试瑶光星学习自动化的每个阶段
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any, List
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Complete8StagesLearningTest:
    """完整8阶段学习流程测试"""
    
    def __init__(self):
        self.test_results = {}
        self.session_id = None
        
    async def run_complete_8_stages_test(self):
        """运行完整的8阶段学习流程测试"""
        print("🌟 开始完整8阶段学习流程测试")
        print("=" * 80)
        
        try:
            # 导入瑶光星统一系统
            from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
            
            # 初始化系统
            init_result = await unified_yaoguang_system.initialize_system()
            print(f"✅ 瑶光星统一系统初始化: {init_result.get('success', False)}")
            
            # 启动学习会话
            session_config = {
                "session_type": "learning",
                "duration_hours": 2,
                "stock_codes": ["000001.XSHE"],
                "enable_four_stars_collaboration": True,
                "enable_rd_agent": True,
                "duration_days": 7
            }
            
            session_result = await unified_yaoguang_system.start_learning_session(session_config)
            if session_result.get("success"):
                self.session_id = session_result.get("session_id")
                print(f"✅ 学习会话启动成功: {self.session_id}")
            else:
                print(f"❌ 学习会话启动失败: {session_result.get('error')}")
                return
            
            # 等待学习流程开始执行
            await asyncio.sleep(5)
            
            # 详细监控8个阶段的执行
            await self.monitor_8_stages_execution(unified_yaoguang_system)
            
            # 生成详细报告
            await self.generate_detailed_report()
            
        except Exception as e:
            print(f"❌ 完整8阶段学习流程测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def monitor_8_stages_execution(self, unified_system):
        """监控8个阶段的执行"""
        print("\n🔍 开始监控8个阶段的执行")
        print("-" * 60)
        
        stages = [
            "阶段1：初始化",
            "阶段2：练习阶段", 
            "阶段3：研究阶段",
            "阶段4：因子开发",
            "阶段5：模型训练",
            "阶段6：策略生成",
            "阶段7：回测验证",
            "阶段8：技能库上传"
        ]
        
        max_wait_time = 300  # 最大等待5分钟
        check_interval = 10  # 每10秒检查一次
        total_waited = 0
        
        while total_waited < max_wait_time:
            try:
                # 获取当前状态
                status = await unified_system.get_learning_status()
                
                if status.get("session_active"):
                    current_phase = status.get("current_phase", "未知")
                    print(f"⏰ [{total_waited}s] 当前阶段: {current_phase}")
                    
                    # 获取学习监控数据
                    monitoring_data = await unified_system.get_learning_monitoring_data()
                    if monitoring_data.get("success"):
                        metrics = monitoring_data.get("metrics", {})
                        progress = metrics.get("learning_progress", 0)
                        completed_phases = metrics.get("completed_phases", 0)
                        print(f"📊 学习进度: {progress:.1%} ({completed_phases}/8 阶段)")
                        
                        # 检查是否完成所有阶段
                        if completed_phases >= 8:
                            print("🎉 所有8个阶段已完成!")
                            break
                        
                        # 记录当前阶段状态
                        self.test_results[f"stage_{completed_phases + 1}"] = {
                            "current_phase": current_phase,
                            "progress": progress,
                            "timestamp": datetime.now().isoformat()
                        }
                    
                    # 获取详细的学习阶段信息
                    if hasattr(unified_system, 'current_session') and unified_system.current_session:
                        learning_phases = unified_system.current_session.get("learning_phases", {})
                        print(f"📋 已完成的学习阶段: {list(learning_phases.keys())}")
                        
                        # 检查每个阶段的完成状态
                        for i, stage_name in enumerate(stages, 1):
                            stage_key = ["initialization", "practice", "research", "factor_development", 
                                        "model_training", "strategy_generation", "backtest_validation", "skill_upload"][i-1]
                            
                            if stage_key in learning_phases:
                                stage_result = learning_phases[stage_key]
                                success = stage_result.get("success", False)
                                status_icon = "✅" if success else "❌"
                                print(f"  {status_icon} {stage_name}: {'完成' if success else '失败'}")
                                
                                self.test_results[f"stage_{i}_detail"] = {
                                    "stage_name": stage_name,
                                    "success": success,
                                    "result": stage_result
                                }
                            else:
                                print(f"  ⏳ {stage_name}: 等待中...")
                else:
                    print("⚠️ 学习会话未激活")
                    break
                
                # 等待下一次检查
                await asyncio.sleep(check_interval)
                total_waited += check_interval
                
            except Exception as e:
                print(f"❌ 监控过程中出现异常: {e}")
                await asyncio.sleep(check_interval)
                total_waited += check_interval
        
        if total_waited >= max_wait_time:
            print("⏰ 监控超时，可能学习流程执行时间较长")
        
        # 最终状态检查
        await self.final_status_check(unified_system)
    
    async def final_status_check(self, unified_system):
        """最终状态检查"""
        print("\n📊 最终状态检查")
        print("-" * 40)
        
        try:
            # 获取最终学习结果
            learning_results = await unified_system.get_learning_results()
            if learning_results.get("success"):
                achievements = learning_results.get("achievements", {})
                print("🏆 学习成果:")
                for key, value in achievements.items():
                    print(f"  📈 {key}: {value}")
                
                self.test_results["final_achievements"] = achievements
            
            # 获取会话报告
            session_report = await unified_system.get_session_report()
            if session_report.get("success"):
                report_data = session_report.get("report_data", {})
                session_stats = report_data.get("session_stats", {})
                
                print("📋 会话统计:")
                print(f"  ⏱️ 会话时长: {session_stats.get('duration_minutes', 0):.1f}分钟")
                print(f"  📊 完成阶段: {session_stats.get('phases_completed', 0)}/8")
                print(f"  📈 完成率: {session_stats.get('completion_rate', 0):.1%}")
                
                self.test_results["session_stats"] = session_stats
                self.test_results["report_data"] = report_data
            
        except Exception as e:
            print(f"❌ 最终状态检查失败: {e}")
    
    async def generate_detailed_report(self):
        """生成详细报告"""
        print("\n📄 生成详细测试报告")
        print("=" * 60)
        
        # 计算测试统计
        total_stages = 8
        completed_stages = len([k for k in self.test_results.keys() if k.startswith("stage_") and k.endswith("_detail")])
        success_rate = (completed_stages / total_stages) * 100
        
        print(f"📊 8阶段学习流程测试结果:")
        print(f"  总阶段数: {total_stages}")
        print(f"  完成阶段: {completed_stages}")
        print(f"  成功率: {success_rate:.1f}%")
        
        # 评估结果
        if success_rate >= 100:
            print("🎉 8阶段学习流程完美执行!")
        elif success_rate >= 75:
            print("✅ 8阶段学习流程执行良好!")
        elif success_rate >= 50:
            print("⚠️ 8阶段学习流程部分完成，需要优化")
        else:
            print("❌ 8阶段学习流程执行不完整，需要修复")
        
        # 保存详细报告
        report = {
            "test_type": "complete_8_stages_learning",
            "session_id": self.session_id,
            "test_results": self.test_results,
            "summary": {
                "total_stages": total_stages,
                "completed_stages": completed_stages,
                "success_rate": success_rate,
                "test_time": datetime.now().isoformat()
            }
        }
        
        report_filename = f"complete_8_stages_learning_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细报告已保存到: {report_filename}")

async def main():
    """主函数"""
    test_runner = Complete8StagesLearningTest()
    await test_runner.run_complete_8_stages_test()

if __name__ == "__main__":
    asyncio.run(main())
