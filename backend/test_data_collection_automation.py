#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星数据收集自动化测试
测试数据收集自动化功能
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any, List
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataCollectionAutomationTest:
    """数据收集自动化测试"""
    
    def __init__(self):
        self.test_results = {}
        
    async def run_data_collection_test(self):
        """运行数据收集自动化测试"""
        print("📊 开始瑶光星数据收集自动化测试")
        print("=" * 80)
        
        try:
            # 导入瑶光星统一系统
            from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
            
            # 初始化系统
            init_result = await unified_yaoguang_system.initialize_system()
            print(f"✅ 瑶光星统一系统初始化: {init_result.get('success', False)}")
            
            # 测试各种数据收集功能
            await self.test_daily_data_collection(unified_yaoguang_system)
            await self.test_realtime_data_collection(unified_yaoguang_system)
            await self.test_news_data_collection(unified_yaoguang_system)
            await self.test_technical_data_collection(unified_yaoguang_system)
            
            # 生成详细报告
            await self.generate_data_collection_report()
            
        except Exception as e:
            print(f"❌ 数据收集自动化测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def test_daily_data_collection(self, unified_system):
        """测试日线数据收集"""
        print("\n📈 测试日线数据收集")
        print("-" * 40)
        
        try:
            # 启动日线数据收集
            collection_config = {
                "data_type": "daily",
                "stock_codes": ["000001.XSHE", "000002.XSHE", "600519.XSHG"],
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
            }
            
            result = await unified_system.start_data_collection(collection_config)
            
            if result.get("success"):
                print("✅ 日线数据收集启动成功")
                collection_id = result.get("collection_id")
                
                # 监控收集进度
                for i in range(6):  # 监控1分钟
                    await asyncio.sleep(10)
                    status = await unified_system.get_data_collection_status(collection_id)
                    
                    if status.get("success"):
                        progress = status.get("progress", 0)
                        collected_count = status.get("collected_count", 0)
                        print(f"  📊 进度: {progress:.1%}, 已收集: {collected_count}条")
                        
                        if status.get("completed"):
                            print("✅ 日线数据收集完成")
                            break
                
                self.test_results["daily_data_collection"] = {
                    "success": True,
                    "collection_id": collection_id,
                    "final_status": status
                }
            else:
                print(f"❌ 日线数据收集启动失败: {result.get('error')}")
                self.test_results["daily_data_collection"] = {
                    "success": False,
                    "error": result.get('error')
                }
                
        except Exception as e:
            print(f"❌ 日线数据收集测试异常: {e}")
            self.test_results["daily_data_collection"] = {
                "success": False,
                "error": str(e)
            }
    
    async def test_realtime_data_collection(self, unified_system):
        """测试实时数据收集"""
        print("\n⚡ 测试实时数据收集")
        print("-" * 40)
        
        try:
            # 启动实时数据收集
            collection_config = {
                "data_type": "realtime",
                "stock_codes": ["000001.XSHE", "000002.XSHE"],
                "duration_minutes": 5
            }
            
            result = await unified_system.start_realtime_data_collection(collection_config)
            
            if result.get("success"):
                print("✅ 实时数据收集启动成功")
                collection_id = result.get("collection_id")
                
                # 监控收集进度
                for i in range(6):  # 监控1分钟
                    await asyncio.sleep(10)
                    status = await unified_system.get_realtime_collection_status(collection_id)
                    
                    if status.get("success"):
                        latest_data = status.get("latest_data", {})
                        data_count = status.get("data_count", 0)
                        print(f"  ⚡ 最新数据: {len(latest_data)}只股票, 总计: {data_count}条")
                
                self.test_results["realtime_data_collection"] = {
                    "success": True,
                    "collection_id": collection_id,
                    "final_status": status
                }
            else:
                print(f"❌ 实时数据收集启动失败: {result.get('error')}")
                self.test_results["realtime_data_collection"] = {
                    "success": False,
                    "error": result.get('error')
                }
                
        except Exception as e:
            print(f"❌ 实时数据收集测试异常: {e}")
            self.test_results["realtime_data_collection"] = {
                "success": False,
                "error": str(e)
            }
    
    async def test_news_data_collection(self, unified_system):
        """测试新闻数据收集"""
        print("\n📰 测试新闻数据收集")
        print("-" * 40)
        
        try:
            # 启动新闻数据收集
            collection_config = {
                "data_type": "news",
                "stock_codes": ["000001.XSHE"],
                "news_sources": ["sina", "eastmoney", "wallstreetcn"],
                "max_news_per_stock": 10
            }
            
            result = await unified_system.start_news_collection(collection_config)
            
            if result.get("success"):
                print("✅ 新闻数据收集启动成功")
                collection_id = result.get("collection_id")
                
                # 监控收集进度
                for i in range(6):  # 监控1分钟
                    await asyncio.sleep(10)
                    status = await unified_system.get_news_collection_status(collection_id)
                    
                    if status.get("success"):
                        news_count = status.get("news_count", 0)
                        sources_completed = status.get("sources_completed", 0)
                        print(f"  📰 已收集: {news_count}条新闻, 完成源: {sources_completed}个")
                        
                        if status.get("completed"):
                            print("✅ 新闻数据收集完成")
                            break
                
                self.test_results["news_data_collection"] = {
                    "success": True,
                    "collection_id": collection_id,
                    "final_status": status
                }
            else:
                print(f"❌ 新闻数据收集启动失败: {result.get('error')}")
                self.test_results["news_data_collection"] = {
                    "success": False,
                    "error": result.get('error')
                }
                
        except Exception as e:
            print(f"❌ 新闻数据收集测试异常: {e}")
            self.test_results["news_data_collection"] = {
                "success": False,
                "error": str(e)
            }
    
    async def test_technical_data_collection(self, unified_system):
        """测试技术指标数据收集"""
        print("\n📊 测试技术指标数据收集")
        print("-" * 40)
        
        try:
            # 启动技术指标数据收集
            collection_config = {
                "data_type": "technical",
                "stock_codes": ["000001.XSHE", "000002.XSHE"],
                "indicators": ["rsi", "macd", "bollinger_bands", "moving_averages"],
                "period": 60
            }
            
            result = await unified_system.start_technical_collection(collection_config)
            
            if result.get("success"):
                print("✅ 技术指标数据收集启动成功")
                collection_id = result.get("collection_id")
                
                # 监控收集进度
                for i in range(6):  # 监控1分钟
                    await asyncio.sleep(10)
                    status = await unified_system.get_technical_collection_status(collection_id)
                    
                    if status.get("success"):
                        indicators_count = status.get("indicators_count", 0)
                        stocks_completed = status.get("stocks_completed", 0)
                        print(f"  📊 已计算: {indicators_count}个指标, 完成股票: {stocks_completed}只")
                        
                        if status.get("completed"):
                            print("✅ 技术指标数据收集完成")
                            break
                
                self.test_results["technical_data_collection"] = {
                    "success": True,
                    "collection_id": collection_id,
                    "final_status": status
                }
            else:
                print(f"❌ 技术指标数据收集启动失败: {result.get('error')}")
                self.test_results["technical_data_collection"] = {
                    "success": False,
                    "error": result.get('error')
                }
                
        except Exception as e:
            print(f"❌ 技术指标数据收集测试异常: {e}")
            self.test_results["technical_data_collection"] = {
                "success": False,
                "error": str(e)
            }
    
    async def generate_data_collection_report(self):
        """生成数据收集测试报告"""
        print("\n📄 生成数据收集测试报告")
        print("=" * 60)
        
        # 计算测试统计
        total_tests = 4
        successful_tests = sum(1 for result in self.test_results.values() if result.get("success", False))
        success_rate = (successful_tests / total_tests) * 100
        
        print(f"📊 数据收集自动化测试结果:")
        print(f"  总测试数: {total_tests}")
        print(f"  成功测试: {successful_tests}")
        print(f"  成功率: {success_rate:.1f}%")
        
        # 详细结果
        test_names = ["日线数据收集", "实时数据收集", "新闻数据收集", "技术指标收集"]
        test_keys = ["daily_data_collection", "realtime_data_collection", "news_data_collection", "technical_data_collection"]
        
        for name, key in zip(test_names, test_keys):
            result = self.test_results.get(key, {})
            status = "✅" if result.get("success", False) else "❌"
            print(f"  {status} {name}")
        
        # 评估结果
        if success_rate >= 100:
            print("🎉 数据收集自动化系统完美运行!")
        elif success_rate >= 75:
            print("✅ 数据收集自动化系统运行良好!")
        elif success_rate >= 50:
            print("⚠️ 数据收集自动化系统部分正常，需要优化")
        else:
            print("❌ 数据收集自动化系统需要修复")
        
        # 保存详细报告
        report = {
            "test_type": "data_collection_automation",
            "test_results": self.test_results,
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "success_rate": success_rate,
                "test_time": datetime.now().isoformat()
            }
        }
        
        report_filename = f"data_collection_automation_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细报告已保存到: {report_filename}")

async def main():
    """主函数"""
    test_runner = DataCollectionAutomationTest()
    await test_runner.run_data_collection_test()

if __name__ == "__main__":
    asyncio.run(main())
