#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能体辩论系统
"""

import asyncio
import logging
import json
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_four_star_debate():
    """测试四星辩论"""
    try:
        print("🎭 测试四星智能体辩论系统")
        print("=" * 60)
        
        from core.intelligent_agent_debate_system import intelligent_agent_debate_system
        
        # 启动四星辩论
        session_id = await intelligent_agent_debate_system.start_four_star_debate(
            topic="000001.XSHE 平安银行投资决策分析",
            stock_code="000001.XSHE",
            context={
                "stock_code": "000001.XSHE",
                "analysis_type": "investment_decision",
                "market_condition": "当前市场环境",
                "time_horizon": "短期投资"
            }
        )
        
        print(f"✅ 四星辩论会话启动成功: {session_id}")
        
        # 获取辩论结果
        status = intelligent_agent_debate_system.get_session_status(session_id)
        
        print("\n📊 辩论结果:")
        print(f"   会话ID: {status['session_id']}")
        print(f"   主题: {status['topic']}")
        print(f"   阶段: {status['stage']}")
        print(f"   参与者: {', '.join(status['participants'])}")
        print(f"   完成轮次: {status['rounds_completed']}")
        print(f"   共识水平: {status['current_consensus']:.2f}")
        print(f"   是否完成: {status['is_completed']}")
        
        if status['final_decision']:
            print(f"\n⚖️ 最终决策:")
            decision = status['final_decision']
            print(f"   决策者: {decision.get('decision_maker', 'N/A')}")
            print(f"   决策内容: {decision.get('decision', 'N/A')}")
            print(f"   置信度: {decision.get('confidence', 0):.2f}")
            print(f"   决策理由: {', '.join(decision.get('reasoning', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 四星辩论测试失败: {e}")
        return False

async def test_seven_star_debate():
    """测试七星辩论"""
    try:
        print("\n🌟 测试七星智能体辩论系统")
        print("=" * 60)
        
        from core.intelligent_agent_debate_system import intelligent_agent_debate_system
        
        # 启动七星辩论
        session_id = await intelligent_agent_debate_system.start_seven_star_debate(
            topic="量化投资策略优化与风险控制",
            context={
                "strategy_type": "量化投资",
                "risk_tolerance": "中等",
                "investment_horizon": "中长期",
                "market_outlook": "震荡市"
            }
        )
        
        print(f"✅ 七星辩论会话启动成功: {session_id}")
        
        # 获取辩论结果
        status = intelligent_agent_debate_system.get_session_status(session_id)
        
        print("\n📊 辩论结果:")
        print(f"   会话ID: {status['session_id']}")
        print(f"   主题: {status['topic']}")
        print(f"   阶段: {status['stage']}")
        print(f"   参与者: {', '.join(status['participants'])}")
        print(f"   完成轮次: {status['rounds_completed']}")
        print(f"   共识水平: {status['current_consensus']:.2f}")
        print(f"   是否完成: {status['is_completed']}")
        
        if status['final_decision']:
            print(f"\n⚖️ 最终决策:")
            decision = status['final_decision']
            print(f"   决策者: {decision.get('decision_maker', 'N/A')}")
            print(f"   决策内容: {decision.get('decision', 'N/A')}")
            print(f"   置信度: {decision.get('confidence', 0):.2f}")
            print(f"   决策理由: {', '.join(decision.get('reasoning', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 七星辩论测试失败: {e}")
        return False

async def test_custom_debate():
    """测试自定义辩论"""
    try:
        print("\n🎯 测试自定义智能体辩论")
        print("=" * 60)
        
        from core.intelligent_agent_debate_system import intelligent_agent_debate_system, AgentRole
        
        # 自定义参与者（技术分析 vs 风险评估）
        participants = [AgentRole.TIANXUAN, AgentRole.TIANJI]
        
        session_id = await intelligent_agent_debate_system.start_debate_session(
            topic="高波动性股票的投资策略",
            participants=participants,
            context={
                "volatility": "high",
                "market_phase": "牛市",
                "sector": "科技股"
            }
        )
        
        print(f"✅ 自定义辩论会话启动成功: {session_id}")
        
        # 获取辩论结果
        status = intelligent_agent_debate_system.get_session_status(session_id)
        
        print("\n📊 辩论结果:")
        print(f"   会话ID: {status['session_id']}")
        print(f"   主题: {status['topic']}")
        print(f"   参与者: {', '.join(status['participants'])}")
        print(f"   完成轮次: {status['rounds_completed']}")
        print(f"   共识水平: {status['current_consensus']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义辩论测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 智能体辩论系统测试开始")
    print("=" * 80)
    
    test_results = []
    
    # 测试四星辩论
    result1 = await test_four_star_debate()
    test_results.append(("四星辩论", result1))
    
    # 测试七星辩论
    result2 = await test_seven_star_debate()
    test_results.append(("七星辩论", result2))
    
    # 测试自定义辩论
    result3 = await test_custom_debate()
    test_results.append(("自定义辩论", result3))
    
    # 输出测试总结
    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)
    
    success_count = 0
    for test_name, result in test_results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(test_results)} 测试通过")
    
    if success_count == len(test_results):
        print("🎉 所有测试通过！智能体辩论系统运行正常")
    else:
        print("⚠️ 部分测试失败，请检查系统配置")

if __name__ == "__main__":
    asyncio.run(main())
