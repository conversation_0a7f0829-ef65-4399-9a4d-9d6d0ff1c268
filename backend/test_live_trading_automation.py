#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星实盘自动化测试
测试实盘交易自动化功能
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any, List
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LiveTradingAutomationTest:
    """实盘自动化测试"""
    
    def __init__(self):
        self.test_results = {}
        self.session_id = None
        
    async def run_live_trading_test(self):
        """运行实盘自动化测试"""
        print("💰 开始瑶光星实盘自动化测试")
        print("=" * 80)
        
        try:
            # 导入瑶光星统一系统
            from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
            
            # 初始化系统
            init_result = await unified_yaoguang_system.initialize_system()
            print(f"✅ 瑶光星统一系统初始化: {init_result.get('success', False)}")
            
            # 启动实盘交易会话
            session_config = {
                "session_type": "live_trading",
                "duration_hours": 1,
                "stock_codes": ["000001.XSHE", "000002.XSHE"],
                "initial_capital": 100000,
                "max_position_per_stock": 0.3,
                "enable_risk_control": True,
                "enable_four_stars_collaboration": True
            }
            
            session_result = await unified_yaoguang_system.start_live_trading_session(session_config)
            print(f"🔍 调试 - session_result: {session_result}")
            print(f"🔍 调试 - session_result type: {type(session_result)}")

            if session_result and session_result.get("success"):
                self.session_id = session_result.get("session_id")
                print(f"✅ 实盘交易会话启动成功: {self.session_id}")
            else:
                error_msg = session_result.get('error') if session_result else "返回值为None"
                print(f"❌ 实盘交易会话启动失败: {error_msg}")
                return
            
            # 等待实盘交易流程开始执行
            await asyncio.sleep(5)
            
            # 详细监控实盘交易执行
            await self.monitor_live_trading_execution(unified_yaoguang_system)
            
            # 生成详细报告
            await self.generate_live_trading_report()
            
        except Exception as e:
            print(f"❌ 实盘自动化测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def monitor_live_trading_execution(self, unified_system):
        """监控实盘交易执行"""
        print("\n🔍 开始监控实盘交易执行")
        print("-" * 60)
        
        max_wait_time = 180  # 最大等待3分钟
        check_interval = 10  # 每10秒检查一次
        total_waited = 0
        
        while total_waited < max_wait_time:
            try:
                # 获取当前状态
                status = await unified_system.get_live_trading_status()
                
                if status.get("session_active"):
                    current_phase = status.get("current_phase", "未知")
                    print(f"⏰ [{total_waited}s] 当前阶段: {current_phase}")
                    
                    # 获取实盘交易监控数据
                    monitoring_data = await unified_system.get_live_trading_monitoring_data()
                    if monitoring_data.get("success"):
                        metrics = monitoring_data.get("metrics", {})
                        total_capital = metrics.get("total_capital", 0)
                        profit_loss = metrics.get("profit_loss", 0)
                        active_positions = metrics.get("active_positions", 0)
                        print(f"💰 总资金: {total_capital:.2f}元, 盈亏: {profit_loss:.2f}元, 持仓: {active_positions}只")
                        
                        # 记录当前状态
                        self.test_results[f"monitoring_{total_waited}"] = {
                            "current_phase": current_phase,
                            "total_capital": total_capital,
                            "profit_loss": profit_loss,
                            "active_positions": active_positions,
                            "timestamp": datetime.now().isoformat()
                        }
                    
                    # 获取交易记录
                    trading_records = await unified_system.get_trading_records()
                    if trading_records.get("success"):
                        records = trading_records.get("records", [])
                        print(f"📊 交易记录: {len(records)}笔")
                        
                        if records:
                            latest_trade = records[-1]
                            print(f"  最新交易: {latest_trade.get('stock_code')} {latest_trade.get('action')} {latest_trade.get('quantity')}股")
                else:
                    print("⚠️ 实盘交易会话未激活")
                    break
                
                # 等待下一次检查
                await asyncio.sleep(check_interval)
                total_waited += check_interval
                
            except Exception as e:
                print(f"❌ 监控过程中出现异常: {e}")
                await asyncio.sleep(check_interval)
                total_waited += check_interval
        
        if total_waited >= max_wait_time:
            print("⏰ 监控超时，实盘交易可能需要更长时间")
        
        # 最终状态检查
        await self.final_trading_status_check(unified_system)
    
    async def final_trading_status_check(self, unified_system):
        """最终交易状态检查"""
        print("\n📊 最终交易状态检查")
        print("-" * 40)
        
        try:
            # 获取最终交易结果
            trading_results = await unified_system.get_live_trading_results()
            if trading_results.get("success"):
                results = trading_results.get("results", {})
                print("🏆 交易成果:")
                for key, value in results.items():
                    print(f"  📈 {key}: {value}")
                
                self.test_results["final_results"] = results
            
            # 获取交易报告
            trading_report = await unified_system.get_live_trading_report()
            if trading_report.get("success"):
                report_data = trading_report.get("report_data", {})
                trading_stats = report_data.get("trading_stats", {})
                
                print("📋 交易统计:")
                print(f"  💰 总资金: {trading_stats.get('total_capital', 0):.2f}元")
                print(f"  📈 总盈亏: {trading_stats.get('total_profit_loss', 0):.2f}元")
                print(f"  📊 交易次数: {trading_stats.get('total_trades', 0)}笔")
                print(f"  🎯 胜率: {trading_stats.get('win_rate', 0):.1%}")
                
                self.test_results["trading_stats"] = trading_stats
                self.test_results["report_data"] = report_data
            
        except Exception as e:
            print(f"❌ 最终状态检查失败: {e}")
    
    async def generate_live_trading_report(self):
        """生成实盘交易测试报告"""
        print("\n📄 生成实盘交易测试报告")
        print("=" * 60)
        
        # 计算测试统计
        monitoring_count = len([k for k in self.test_results.keys() if k.startswith("monitoring_")])
        has_final_results = "final_results" in self.test_results
        has_trading_stats = "trading_stats" in self.test_results
        
        success_rate = 0
        if monitoring_count > 0:
            success_rate += 40
        if has_final_results:
            success_rate += 30
        if has_trading_stats:
            success_rate += 30
        
        print(f"📊 实盘自动化测试结果:")
        print(f"  监控次数: {monitoring_count}")
        print(f"  最终结果: {'✅' if has_final_results else '❌'}")
        print(f"  交易统计: {'✅' if has_trading_stats else '❌'}")
        print(f"  成功率: {success_rate:.1f}%")
        
        # 评估结果
        if success_rate >= 90:
            print("🎉 实盘自动化系统运行完美!")
        elif success_rate >= 70:
            print("✅ 实盘自动化系统运行良好!")
        elif success_rate >= 50:
            print("⚠️ 实盘自动化系统部分正常，需要优化")
        else:
            print("❌ 实盘自动化系统需要修复")
        
        # 保存详细报告
        report = {
            "test_type": "live_trading_automation",
            "session_id": self.session_id,
            "test_results": self.test_results,
            "summary": {
                "monitoring_count": monitoring_count,
                "has_final_results": has_final_results,
                "has_trading_stats": has_trading_stats,
                "success_rate": success_rate,
                "test_time": datetime.now().isoformat()
            }
        }
        
        report_filename = f"live_trading_automation_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细报告已保存到: {report_filename}")

async def main():
    """主函数"""
    test_runner = LiveTradingAutomationTest()
    await test_runner.run_live_trading_test()

if __name__ == "__main__":
    asyncio.run(main())
