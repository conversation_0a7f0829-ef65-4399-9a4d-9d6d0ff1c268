#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试RD-Agent集成服务
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_rd_agent_service():
    """测试RD-Agent服务"""
    try:
        print("🔬 导入RD-Agent集成服务...")
        from roles.yaoguang_star.services.rd_agent_integration_service import rd_agent_integration_service
        
        print("✅ 导入成功")
        
        # 测试服务状态方法
        print("🔍 测试get_service_status方法...")
        if hasattr(rd_agent_integration_service, 'get_service_status'):
            print("✅ get_service_status方法存在")
            
            status = rd_agent_integration_service.get_service_status()
            print(f"✅ 服务状态获取成功: {status}")
            
        else:
            print("❌ get_service_status方法不存在")
            print(f"可用方法: {[method for method in dir(rd_agent_integration_service) if not method.startswith('_')]}")
        
        # 测试其他关键方法
        print("🔍 检查其他关键方法...")
        methods_to_check = [
            'generate_new_factors',
            'get_alpha158_factors', 
            '_generate_professional_factors',

        ]
        
        for method_name in methods_to_check:
            if hasattr(rd_agent_integration_service, method_name):
                print(f"✅ {method_name} 方法存在")
            else:
                print(f"❌ {method_name} 方法不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_rd_agent_service()
