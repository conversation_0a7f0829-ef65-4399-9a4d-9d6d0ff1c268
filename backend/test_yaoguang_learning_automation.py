#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星学习模式自动化全流程测试
测试瑶光星学习自动化的8个阶段完整流程
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any, List
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class YaoguangLearningAutomationTest:
    """瑶光星学习模式自动化全流程测试"""
    
    def __init__(self):
        self.test_results = {
            "stage_1_initiation": {},
            "stage_2_practice": {},
            "stage_3_research_reflection": {},
            "stage_4_factor_development": {},
            "stage_5_model_training": {},
            "stage_6_strategy_generation": {},
            "stage_7_backtesting": {},
            "stage_8_skill_library_upload": {},
            "overall_workflow": {}
        }
        
    async def run_complete_learning_test(self):
        """运行完整的学习自动化测试"""
        print("🌟 开始瑶光星学习模式自动化全流程测试")
        print("=" * 80)
        
        try:
            # 导入瑶光星统一系统 - 使用全局实例
            from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system

            # 初始化系统
            init_result = await unified_yaoguang_system.initialize_system()
            if init_result.get("success"):
                print("✅ 瑶光星统一系统初始化成功")
            else:
                print(f"⚠️ 瑶光星统一系统初始化警告: {init_result.get('error', 'Unknown')}")

            print("✅ 瑶光星学习自动化系统导入成功")
            
            # 测试学习自动化流程
            await self.test_learning_session_start(unified_yaoguang_system)
            await self.test_learning_session_execution(unified_yaoguang_system)
            await self.test_learning_session_monitoring(unified_yaoguang_system)
            await self.test_learning_session_completion(unified_yaoguang_system)

            # 测试完整工作流
            await self.test_complete_learning_workflow(unified_yaoguang_system)
            
            # 生成测试报告
            await self.generate_learning_test_report()
            
        except Exception as e:
            print(f"❌ 瑶光星学习自动化测试失败: {e}")
            self.test_results["overall_workflow"]["error"] = str(e)
    
    async def test_learning_session_start(self, unified_system):
        """测试学习会话启动"""
        print("\n🚀 测试学习会话启动")
        print("-" * 50)

        try:
            # 配置学习会话
            session_config = {
                "session_type": "learning",
                "duration_hours": 2,
                "stock_quota": 3,
                "enable_four_stars_collaboration": True,
                "enable_rd_agent": True
            }

            # 启动学习会话
            result = await unified_system.start_learning_session(session_config)

            if result.get("success"):
                print("  ✅ 学习会话启动成功")
                print(f"  📋 会话ID: {result.get('session_id')}")
                self.test_results["stage_1_initiation"]["startup"] = True

                # 检查会话配置
                if "config" in result:
                    print("  ✅ 会话配置加载成功")
                    print(f"  📋 配置详情: {len(result['config'])}项配置")
                    self.test_results["stage_1_initiation"]["config"] = True
                else:
                    print("  ⚠️ 会话配置缺失")
                    self.test_results["stage_1_initiation"]["config"] = False

            else:
                print(f"  ❌ 学习会话启动失败: {result.get('error')}")
                self.test_results["stage_1_initiation"]["startup"] = False

        except Exception as e:
            print(f"  ❌ 学习会话启动测试异常: {e}")
            self.test_results["stage_1_initiation"]["error"] = str(e)
    
    async def test_learning_session_execution(self, unified_system):
        """测试学习会话执行"""
        print("\n🎓 测试学习会话执行")
        print("-" * 50)

        try:
            # 等待一段时间让学习会话开始执行
            import asyncio
            await asyncio.sleep(5)

            # 获取学习状态
            status = await unified_system.get_learning_status()

            if status.get("session_active"):
                print("  ✅ 学习会话正在执行")
                self.test_results["stage_2_practice"]["session_execution"] = True

                # 检查学习进度
                current_phase = status.get("current_phase", "unknown")
                print(f"  📊 当前阶段: {current_phase}")

                # 检查参与的角色
                participating_roles = status.get("participating_roles", [])
                if participating_roles:
                    print(f"  🤝 参与角色: {', '.join(participating_roles)}")
                    self.test_results["stage_2_practice"]["role_participation"] = True
                else:
                    print("  ⚠️ 未检测到角色参与")
                    self.test_results["stage_2_practice"]["role_participation"] = False

                # 检查选中的股票
                selected_stocks = status.get("selected_stocks", [])
                if selected_stocks:
                    print(f"  📈 选中股票: {', '.join(selected_stocks[:3])}")
                    self.test_results["stage_2_practice"]["stock_selection"] = True
                else:
                    print("  ⚠️ 未检测到选中股票")
                    self.test_results["stage_2_practice"]["stock_selection"] = False

            else:
                print("  ⚠️ 学习会话未在执行")
                self.test_results["stage_2_practice"]["session_execution"] = False

        except Exception as e:
            print(f"  ❌ 学习会话执行测试异常: {e}")
            self.test_results["stage_2_practice"]["error"] = str(e)
    
    async def test_learning_session_monitoring(self, unified_system):
        """测试学习会话监控"""
        print("\n📊 测试学习会话监控")
        print("-" * 50)

        try:
            # 获取学习监控数据
            monitoring_data = await unified_system.get_learning_monitoring_data()

            if monitoring_data.get("success"):
                print("  ✅ 学习监控数据获取成功")
                self.test_results["stage_3_research_reflection"]["monitoring"] = True

                # 检查监控指标
                metrics = monitoring_data.get("metrics", {})
                if metrics:
                    print(f"  📈 监控指标数量: {len(metrics)}")

                    # 显示关键指标
                    if "learning_progress" in metrics:
                        progress = metrics["learning_progress"]
                        print(f"  📊 学习进度: {progress:.1%}")

                    if "performance_score" in metrics:
                        score = metrics["performance_score"]
                        print(f"  🎯 绩效评分: {score:.2f}")

                    self.test_results["stage_3_research_reflection"]["metrics"] = True
                else:
                    print("  ⚠️ 监控指标为空")
                    self.test_results["stage_3_research_reflection"]["metrics"] = False

            else:
                print(f"  ❌ 学习监控数据获取失败: {monitoring_data.get('error')}")
                self.test_results["stage_3_research_reflection"]["monitoring"] = False

        except Exception as e:
            print(f"  ❌ 学习会话监控测试异常: {e}")
            self.test_results["stage_3_research_reflection"]["error"] = str(e)
    
    async def test_learning_session_completion(self, unified_system):
        """测试学习会话完成"""
        print("\n🎓 测试学习会话完成")
        print("-" * 50)

        try:
            # 等待学习会话完成或手动完成
            import asyncio
            await asyncio.sleep(10)  # 等待更长时间

            # 获取学习结果
            learning_results = await unified_system.get_learning_results()

            if learning_results.get("success"):
                print("  ✅ 学习结果获取成功")
                self.test_results["stage_4_factor_development"]["completion"] = True

                # 检查学习成果
                achievements = learning_results.get("achievements", {})
                if achievements:
                    print(f"  🏆 学习成果数量: {len(achievements)}")

                    # 显示关键成果
                    if "factors_developed" in achievements:
                        factors = achievements["factors_developed"]
                        print(f"  🔢 开发因子数量: {factors}")
                        self.test_results["stage_4_factor_development"]["factors_count"] = factors

                    if "strategies_created" in achievements:
                        strategies = achievements["strategies_created"]
                        print(f"  📈 创建策略数量: {strategies}")
                        self.test_results["stage_4_factor_development"]["strategies_count"] = strategies

                    if "models_trained" in achievements:
                        models = achievements["models_trained"]
                        print(f"  🤖 训练模型数量: {models}")
                        self.test_results["stage_4_factor_development"]["models_count"] = models

                    self.test_results["stage_4_factor_development"]["achievements"] = True
                else:
                    print("  ⚠️ 学习成果为空")
                    self.test_results["stage_4_factor_development"]["achievements"] = False

            else:
                print(f"  ❌ 学习结果获取失败: {learning_results.get('error')}")
                self.test_results["stage_4_factor_development"]["completion"] = False

        except Exception as e:
            print(f"  ❌ 学习会话完成测试异常: {e}")
            self.test_results["stage_4_factor_development"]["error"] = str(e)
    

    
    async def test_complete_learning_workflow(self, unified_system):
        """测试完整学习工作流"""
        print("\n🔄 测试完整学习工作流")
        print("-" * 50)

        try:
            # 获取完整的学习会话报告
            session_report = await unified_system.get_session_report()

            if session_report.get("success"):
                print("  ✅ 学习会话报告获取成功")
                self.test_results["overall_workflow"]["report_generation"] = True

                # 检查报告内容
                report_data = session_report.get("report_data", {})
                if report_data:
                    # 会话统计
                    session_stats = report_data.get("session_stats", {})
                    if session_stats:
                        duration = session_stats.get("duration_minutes", 0)
                        phases_completed = session_stats.get("phases_completed", 0)
                        print(f"  ⏱️ 会话时长: {duration:.1f}分钟")
                        print(f"  📊 完成阶段: {phases_completed}")
                        self.test_results["overall_workflow"]["duration"] = duration
                        self.test_results["overall_workflow"]["phases_completed"] = phases_completed

                    # 学习成果
                    learning_outcomes = report_data.get("learning_outcomes", {})
                    if learning_outcomes:
                        total_insights = learning_outcomes.get("total_insights", 0)
                        skills_acquired = learning_outcomes.get("skills_acquired", 0)
                        print(f"  💡 总洞察数: {total_insights}")
                        print(f"  🎯 获得技能: {skills_acquired}")
                        self.test_results["overall_workflow"]["insights"] = total_insights
                        self.test_results["overall_workflow"]["skills"] = skills_acquired

                    # 绩效评估
                    performance = report_data.get("performance_evaluation", {})
                    if performance:
                        overall_score = performance.get("overall_score", 0)
                        print(f"  🏆 总体评分: {overall_score:.2f}")
                        self.test_results["overall_workflow"]["performance_score"] = overall_score

                    self.test_results["overall_workflow"]["execution"] = True
                else:
                    print("  ⚠️ 报告数据为空")
                    self.test_results["overall_workflow"]["execution"] = False

            else:
                print(f"  ❌ 学习会话报告获取失败: {session_report.get('error')}")
                self.test_results["overall_workflow"]["report_generation"] = False

        except Exception as e:
            print(f"  ❌ 完整学习工作流测试异常: {e}")
            self.test_results["overall_workflow"]["error"] = str(e)
    
    async def generate_learning_test_report(self):
        """生成学习测试报告"""
        print("\n📋 生成瑶光星学习自动化测试报告")
        print("=" * 80)
        
        # 计算总体成功率
        total_tests = 0
        passed_tests = 0
        
        for stage, results in self.test_results.items():
            for test_name, result in results.items():
                if isinstance(result, bool):
                    total_tests += 1
                    if result:
                        passed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"📊 学习自动化测试统计:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过测试: {passed_tests}")
        print(f"  成功率: {success_rate:.1f}%")
        
        # 评估学习自动化质量
        if success_rate >= 90:
            print("🎉 瑶光星学习自动化质量优秀!")
        elif success_rate >= 75:
            print("✅ 瑶光星学习自动化质量良好!")
        elif success_rate >= 60:
            print("⚠️ 瑶光星学习自动化质量一般，需要优化")
        else:
            print("❌ 瑶光星学习自动化质量不佳，需要重新设计")
        
        # 保存详细报告
        report = {
            "test_results": self.test_results,
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "success_rate": success_rate,
                "test_time": datetime.now().isoformat()
            }
        }
        
        report_filename = f"yaoguang_learning_automation_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细报告已保存到: {report_filename}")

async def main():
    """主函数"""
    test_runner = YaoguangLearningAutomationTest()
    await test_runner.run_complete_learning_test()

if __name__ == "__main__":
    asyncio.run(main())
