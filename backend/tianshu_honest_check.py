#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星诚实检查 - 不撒谎，只说真话
"""

import asyncio
import requests
import json
import re
from pathlib import Path

async def check_tianshu_crawl4ai_function():
    """检查天枢星Crawl4AI功能是否真的正常"""
    print("🔍 检查天枢星Crawl4AI功能真实状态...")
    
    # 1. 检查Crawl4AI依赖
    try:
        from crawl4ai import AsyncWebCrawler
        print("   ✅ Crawl4AI依赖: 可导入")
    except ImportError as e:
        print(f"   ❌ Crawl4AI依赖: 导入失败 - {e}")
        return False
    
    # 2. 测试真实的Crawl4AI功能
    try:
        print("   🕷️ 测试真实Crawl4AI爬取...")
        
        async with AsyncWebCrawler(verbose=False) as crawler:
            # 测试爬取一个简单的新闻网站
            result = await crawler.arun(
                url="https://finance.sina.com.cn/",
                word_count_threshold=10,
                bypass_cache=True
            )
            
            if result.success and result.markdown:
                print(f"   ✅ Crawl4AI爬取成功: {len(result.markdown)}字符")
                return True
            else:
                print(f"   ❌ Crawl4AI爬取失败: {result.error_message}")
                return False
                
    except Exception as e:
        print(f"   ❌ Crawl4AI测试异常: {e}")
        return False

def find_all_news_urls():
    """查找天枢星配置的所有新闻地址"""
    print("\n📋 查找天枢星配置的所有新闻地址...")
    
    # 检查所有可能包含新闻URL的文件
    files_to_check = [
        "backend/roles/tianshu_star/services/news_collection_service.py",
        "backend/roles/tianshu_star/services/crawl4ai_service.py", 
        "backend/roles/tianshu_star/services/crawl4ai_intelligence_service.py",
        "backend/roles/tianshu_star/services/unified_data_collector.py",
        "backend/services/core/enhanced_crawl4ai_service.py",
        "backend/services/news/config/news_sources.json",
        "backend/roles/tianshu_star/config/news_sources.py"
    ]
    
    all_urls = set()
    file_url_map = {}
    
    for file_path in files_to_check:
        path = Path(file_path)
        if path.exists():
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找URL模式
                url_patterns = [
                    r'https?://[^\s"\']+',
                    r'"[^"]*\.com[^"]*"',
                    r'"[^"]*\.cn[^"]*"',
                    r'"[^"]*\.net[^"]*"',
                    r"'[^']*\.com[^']*'",
                    r"'[^']*\.cn[^']*'",
                ]
                
                file_urls = set()
                for pattern in url_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        clean_url = match.strip('"\'')
                        # 过滤新闻相关的URL
                        if any(keyword in clean_url.lower() for keyword in 
                               ['finance', 'money', 'stock', 'news', 'sina', 'eastmoney', 
                                'xueqiu', '10jqka', 'qq.com', '163.com', 'xinhua']):
                            file_urls.add(clean_url)
                            all_urls.add(clean_url)
                
                if file_urls:
                    file_url_map[path.name] = list(file_urls)
                    print(f"   📄 {path.name}: 发现{len(file_urls)}个URL")
                    
            except Exception as e:
                print(f"   ❌ 无法读取 {path.name}: {e}")
        else:
            print(f"   ⚠️ 文件不存在: {path.name}")
    
    print(f"\n📊 总计发现 {len(all_urls)} 个唯一新闻URL")
    
    # 按域名分类
    domain_groups = {}
    for url in all_urls:
        try:
            if '://' in url:
                domain = url.split('://')[1].split('/')[0]
            else:
                domain = url.split('/')[0]
            
            if domain not in domain_groups:
                domain_groups[domain] = []
            domain_groups[domain].append(url)
        except:
            continue
    
    print(f"\n🌐 按域名分类 ({len(domain_groups)}个域名):")
    for domain, urls in sorted(domain_groups.items()):
        print(f"   📍 {domain}: {len(urls)}个URL")
        for url in urls[:3]:  # 显示前3个
            print(f"      - {url}")
        if len(urls) > 3:
            print(f"      ... 还有{len(urls)-3}个")
    
    return all_urls, file_url_map, domain_groups

def check_tianshu_bugs():
    """检查天枢星的具体Bug"""
    print("\n🐛 检查天枢星具体Bug...")
    
    bugs_found = []
    
    # 检查新闻收集服务
    news_service_file = Path("backend/roles/tianshu_star/services/news_collection_service.py")
    if news_service_file.exists():
        try:
            with open(news_service_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查具体问题
            issues = []

            # 检查示例数据
            if "sample_news" in content or "示例" in content:
                sample_lines = [i+1 for i, line in enumerate(content.split('\n')) 
                              if 'sample_news' in line or '示例' in line]
                issues.append(f"示例数据: 第{sample_lines[:5]}行")
            
            # 检查模拟数据生成
            if "_generate_sample" in content:
                generate_lines = [i+1 for i, line in enumerate(content.split('\n')) 
                                if '_generate_sample' in line]
                issues.append(f"模拟数据生成: 第{generate_lines}行")
            
            if issues:
                bugs_found.append({
                    "文件": "news_collection_service.py",
                    "问题": issues,
                    "文件大小": len(content)
                })
            
        except Exception as e:
            bugs_found.append({
                "文件": "news_collection_service.py", 
                "错误": str(e)
            })
    
    return bugs_found

async def test_tianshu_apis():
    """测试天枢星API的真实响应"""
    print("\n🔌 测试天枢星API真实响应...")
    
    base_url = "http://127.0.0.1:8003"
    
    apis_to_test = [
        "/api/tianshu/news/latest",
        "/api/tianshu/news/collect/start", 
        "/api/tianshu/crawl4ai/status",
        "/api/intelligence/news"
    ]
    
    results = {}
    
    for endpoint in apis_to_test:
        try:
            if "collect/start" in endpoint:
                # POST请求
                response = requests.post(f"{base_url}{endpoint}", 
                                       json={"symbols": ["000001"], "limit": 5}, 
                                       timeout=10)
            else:
                # GET请求
                response = requests.get(f"{base_url}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                response_text = str(data).lower()
                
                # 详细分析响应内容
                analysis = {
                    "状态": "成功",
                    "数据大小": len(str(data)),
                    "包含test": "test" in response_text,
                    "包含sample": "sample" in response_text,
                    "包含示例": "示例" in response_text,

                    "包含crawl4ai": "crawl4ai" in response_text,
                    "包含真实新闻": any(site in response_text for site in ['sina', 'eastmoney', 'xueqiu']),
                    "响应键": list(data.keys()) if isinstance(data, dict) else "非字典"
                }
                
                # 如果有数据，分析数据内容
                if isinstance(data, dict) and "data" in data:
                    data_content = data["data"]
                    if isinstance(data_content, dict) and "news" in data_content:
                        news_list = data_content["news"]
                        analysis["新闻数量"] = len(news_list) if isinstance(news_list, list) else 0
                        if news_list and isinstance(news_list, list):
                            first_news = news_list[0]
                            analysis["第一条新闻标题"] = first_news.get("title", "无标题")[:50]
                            analysis["第一条新闻来源"] = first_news.get("source", "无来源")
                
                results[endpoint] = analysis
                
            else:
                results[endpoint] = {
                    "状态": "失败",
                    "状态码": response.status_code,
                    "错误": response.text[:200]
                }
                
        except Exception as e:
            results[endpoint] = {
                "状态": "异常",
                "错误": str(e)
            }
    
    return results

async def main():
    """主函数 - 天枢星诚实检查"""
    print("🚀 开始天枢星诚实检查...")
    print("=" * 80)
    
    # 1. 检查Crawl4AI功能
    crawl4ai_works = await check_tianshu_crawl4ai_function()
    
    # 2. 查找所有新闻URL
    all_urls, file_url_map, domain_groups = find_all_news_urls()
    
    # 3. 检查Bug
    bugs = check_tianshu_bugs()
    
    # 4. 测试API
    api_results = await test_tianshu_apis()
    
    print("\n" + "=" * 80)
    print("📊 天枢星诚实检查结果")
    print("=" * 80)
    
    print(f"🕷️ Crawl4AI功能: {'✅正常' if crawl4ai_works else '❌异常'}")
    print(f"🌐 配置的新闻地址: {len(all_urls)}个")
    print(f"🐛 发现的Bug: {len(bugs)}个")
    print(f"🔌 API测试: {len(api_results)}个")
    
    print("\n📋 详细Bug列表:")
    for bug in bugs:
        print(f"   ❌ {bug['文件']}: {bug.get('问题', bug.get('错误'))}")
    
    print("\n🔌 API测试结果:")
    for endpoint, result in api_results.items():
        status = result.get("状态", "未知")

        mock_status = "❌有模拟数据" if has_mock else "✅无模拟数据"
        print(f"   {mock_status} {endpoint}: {status}")
        
        if result.get("新闻数量"):
            print(f"      📰 新闻数量: {result['新闻数量']}")
        if result.get("第一条新闻标题"):
            print(f"      📝 第一条: {result['第一条新闻标题']}")
    
    # 保存详细报告
    report = {
        "crawl4ai_works": crawl4ai_works,
        "total_urls": len(all_urls),
        "urls_by_file": file_url_map,
        "domains": {domain: len(urls) for domain, urls in domain_groups.items()},
        "bugs_found": bugs,
        "api_results": api_results
    }
    
    with open("backend/tianshu_honest_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存: backend/tianshu_honest_report.json")
    
    return report

if __name__ == "__main__":
    asyncio.run(main())
