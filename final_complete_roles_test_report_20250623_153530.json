{"timestamp": "2025-06-23T15:35:27.423435", "role_tests": {"tianshu_star": {"automation_loaded": false, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": false, "real_data_processing": false, "no_fallback_mode": false, "errors": ["测试失败: expected 'except' or 'finally' block (news_collection_service.py, line 254)"]}, "tianxuan_star": {"automation_loaded": false, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": false, "real_data_processing": false, "no_fallback_mode": false, "errors": ["测试失败: invalid syntax (technical_analysis_service.py, line 121)"]}, "tianji_star": {"automation_loaded": true, "deepseek_integrated": true, "memory_integrated": true, "performance_integrated": true, "core_method_working": true, "real_data_processing": false, "no_fallback_mode": true, "errors": [], "completion_score": 6, "completion_percentage": 100.0}, "tianquan_star": {"automation_loaded": false, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": false, "real_data_processing": false, "no_fallback_mode": false, "errors": ["测试失败: expected an indented block after 'except' statement on line 697 (strategic_decision_service.py, line 698)"]}, "yuheng_star": {"automation_loaded": false, "deepseek_integrated": false, "memory_integrated": false, "performance_integrated": false, "core_method_working": false, "real_data_processing": false, "no_fallback_mode": false, "errors": ["测试失败: unindent does not match any outer indentation level (trading_execution_service.py, line 22)"]}, "kaiyang_star": {"automation_loaded": true, "deepseek_integrated": true, "memory_integrated": true, "performance_integrated": true, "core_method_working": true, "real_data_processing": false, "no_fallback_mode": true, "errors": [], "completion_score": 6, "completion_percentage": 100.0}, "yaoguang_star": {"automation_loaded": true, "deepseek_integrated": true, "memory_integrated": true, "performance_integrated": true, "core_method_working": true, "real_data_processing": false, "no_fallback_mode": false, "errors": [], "fallback_indicators": ["模拟"], "completion_score": 5, "completion_percentage": 83.33333333333334}}, "integration_tests": {"legendary_memory": {"available": false, "error": "expected an indented block after 'except' statement on line 236 (interface.py, line 237)", "test_passed": false}, "performance_monitor": {"available": true, "status": {"is_initialized": true, "performance_data_count": 0, "stars_monitored": [], "last_update": "2025-06-23T15:35:30.363895", "monitoring_active": true, "service_name": "StarPerformanceMonitor", "version": "1.0.0"}, "test_passed": true}, "deepseek_service": {"available": false, "error": "expected 'except' or 'finally' block (investment_node_service.py, line 1569)", "test_passed": false}}, "performance_tests": {}, "summary": {"total_roles": 7, "excellent_roles": 3, "good_roles": 0, "average_completion_percentage": 40.5, "system_integration_tests_passed": "1/3", "overall_system_health": "needs_improvement", "test_timestamp": "2025-06-23T15:35:30.380308"}}