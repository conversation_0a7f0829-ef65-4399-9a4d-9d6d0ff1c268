#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的辩论系统测试
"""

import os
import sys

# 添加backend路径
backend_path = os.path.join(os.getcwd(), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

def test_debate_systems_import():
    """测试辩论系统导入"""
    print("🗣️ 测试辩论系统导入...")
    
    systems_found = []
    
    # 1. 测试智能体辩论系统
    try:
        from backend.core.intelligent_agent_debate_system import IntelligentAgentDebateSystem
        print("✅ 智能体辩论系统 - 导入成功")
        systems_found.append("intelligent_agent_debate_system")
    except Exception as e:
        print(f"❌ 智能体辩论系统导入失败: {e}")
    
    # 2. 测试真实辩论系统
    try:
        from backend.roles.yaoguang_star.services.real_debate_system import RealDebateSystem
        print("✅ 真实四星辩论系统 - 导入成功")
        systems_found.append("real_debate_system")
    except Exception as e:
        print(f"❌ 真实辩论系统导入失败: {e}")
    
    # 3. 测试增强四星辩论系统（天权星版本）
    try:
        from backend.roles.tianquan_star.services.enhanced_four_stars_debate import EnhancedFourStarsDebate
        print("✅ 增强四星辩论系统（天权星版） - 导入成功")
        systems_found.append("enhanced_four_stars_debate_tianquan")
    except Exception as e:
        print(f"❌ 增强四星辩论系统（天权星版）导入失败: {e}")
    
    # 4. 测试核心增强四星辩论系统
    try:
        from backend.core.enhanced_four_stars_debate import EnhancedFourStarsDebate as CoreEnhancedDebate
        print("✅ 核心增强四星辩论系统 - 导入成功")
        systems_found.append("enhanced_four_stars_debate_core")
    except Exception as e:
        print(f"❌ 核心增强四星辩论系统导入失败: {e}")
    
    # 5. 测试七星聊天辩论系统
    try:
        from backend.api.seven_stars_chat_debate import SevenStarsChatDebate
        print("✅ 七星聊天辩论系统 - 导入成功")
        systems_found.append("seven_stars_chat_debate")
    except Exception as e:
        print(f"❌ 七星聊天辩论系统导入失败: {e}")
    
    return systems_found

def check_debate_files():
    """检查辩论系统文件"""
    print("\n📁 检查辩论系统文件...")
    
    debate_files = [
        "backend/core/intelligent_agent_debate_system.py",
        "backend/roles/yaoguang_star/services/real_debate_system.py", 
        "backend/roles/tianquan_star/services/enhanced_four_stars_debate.py",
        "backend/core/enhanced_four_stars_debate.py",
        "backend/api/seven_stars_chat_debate.py"
    ]
    
    existing_files = []
    for file_path in debate_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
            existing_files.append(file_path)
        else:
            print(f"❌ {file_path} - 文件不存在")
    
    return existing_files

def analyze_debate_systems():
    """分析辩论系统"""
    print("\n📊 辩论系统分析报告")
    print("=" * 50)
    
    # 检查文件
    existing_files = check_debate_files()
    
    # 测试导入
    working_systems = test_debate_systems_import()
    
    print(f"\n📈 发现的辩论系统文件: {len(existing_files)}")
    for i, file_path in enumerate(existing_files, 1):
        print(f"   {i}. {os.path.basename(file_path)}")
    
    print(f"\n✅ 可正常导入的系统: {len(working_systems)}")
    for i, system in enumerate(working_systems, 1):
        print(f"   {i}. {system}")
    
    print(f"\n📊 总计辩论系统: {len(existing_files)}")
    
    # 分析重复性
    four_star_systems = [s for s in working_systems if "four" in s or "enhanced" in s or "real" in s]
    seven_star_systems = [s for s in working_systems if "seven" in s or "chat" in s]
    
    if len(four_star_systems) > 1:
        print(f"\n⚠️  发现 {len(four_star_systems)} 个四星辩论系统，可能存在重复:")
        for system in four_star_systems:
            print(f"     - {system}")
    
    if len(seven_star_systems) > 1:
        print(f"\n⚠️  发现 {len(seven_star_systems)} 个七星辩论系统，可能存在重复:")
        for system in seven_star_systems:
            print(f"     - {system}")
    
    # 建议
    print(f"\n💡 建议:")
    if len(four_star_systems) > 1:
        print("   - 考虑合并或删除重复的四星辩论系统")
    if len(seven_star_systems) > 1:
        print("   - 考虑合并或删除重复的七星辩论系统")
    if len(working_systems) == len(existing_files):
        print("   - 所有辩论系统文件都可以正常导入")
    else:
        print("   - 部分辩论系统存在导入问题，需要修复")

if __name__ == "__main__":
    analyze_debate_systems()
