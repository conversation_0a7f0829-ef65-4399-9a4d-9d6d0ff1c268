#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库修复
"""

import asyncio
import sys
import os

# 添加backend路径
backend_path = os.path.join(os.getcwd(), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

async def test_database_fix():
    """测试数据库修复"""
    try:
        print("🔍 测试数据库查询修复...")
        
        from services.data.daily_stock_database_service import daily_stock_db_service
        
        # 测试原始股票代码
        print("测试 000001.XSHE...")
        result1 = await daily_stock_db_service.get_stock_data('000001.XSHE')
        print(f"查询结果: {result1 is not None}")
        if result1:
            print(f"股票名称: {result1.get('stock_name', 'N/A')}")

        # 测试清理后的代码
        print("\n测试 000001...")
        result2 = await daily_stock_db_service.get_stock_data('000001')
        print(f"查询结果: {result2 is not None}")
        if result2:
            print(f"股票名称: {result2.get('stock_name', 'N/A')}")
            
        print("\n✅ 数据库查询测试完成")
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_data_source_fix():
    """测试数据源修复"""
    try:
        print("\n🔍 测试数据源配置修复...")
        
        from shared.data_sources.unified_data_source_manager import unified_data_source_manager
        
        # 检查新浪数据源是否已禁用
        print("检查数据源配置...")
        print(f"新浪数据源启用状态: {unified_data_source_manager.data_sources['sina']['enabled']}")
        print(f"数据源优先级: {unified_data_source_manager.data_source_priority}")
        
        # 测试数据获取
        print("\n测试数据获取...")
        async with unified_data_source_manager as manager:
            result = await manager.get_stock_data("000001.XSHE", "realtime")
            print(f"数据获取结果: {result.get('success', False)}")
            if result.get('success'):
                print(f"数据源: {result.get('data_source', 'N/A')}")
        
        print("✅ 数据源测试完成")
        
    except Exception as e:
        print(f"❌ 数据源测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_database_fix())
    asyncio.run(test_data_source_fix())
