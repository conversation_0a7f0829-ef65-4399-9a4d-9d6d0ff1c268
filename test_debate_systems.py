#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试辩论系统
"""

import asyncio
import sys
import os

# 添加backend路径
backend_path = os.path.join(os.getcwd(), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

async def test_four_star_debate_systems():
    """测试四星辩论系统"""
    print("🗣️ 测试四星辩论系统...")
    
    systems_tested = []
    
    # 1. 测试智能体辩论系统
    try:
        import sys
        sys.path.append('backend')
        from backend.core.intelligent_agent_debate_system import IntelligentAgentDebateSystem
        debate_system1 = IntelligentAgentDebateSystem()
        print("✅ 智能体辩论系统 - 导入成功")
        
        # 测试四星辩论
        session_id = await debate_system1.start_four_star_debate(
            topic="股票投资分析",
            stock_code="000001.XSHE",
            context={"test": True}
        )
        print(f"   四星辩论会话ID: {session_id}")
        systems_tested.append("intelligent_agent_debate_system")
        
    except Exception as e:
        print(f"❌ 智能体辩论系统测试失败: {e}")
    
    # 2. 测试真实辩论系统
    try:
        from roles.yaoguang_star.services.real_debate_system import RealDebateSystem
        debate_system2 = RealDebateSystem()
        print("✅ 真实四星辩论系统 - 导入成功")
        
        # 测试辩论会话
        result = await debate_system2.start_debate_session({
            "topic_type": "investment_analysis",
            "subject": "股票分析测试",
            "context": {"stock_code": "000001.XSHE"}
        })
        print(f"   辩论结果: {result.get('success', False)}")
        systems_tested.append("real_debate_system")
        
    except Exception as e:
        print(f"❌ 真实辩论系统测试失败: {e}")
    
    # 3. 测试增强四星辩论系统（天权星版本）
    try:
        from roles.tianquan_star.services.enhanced_four_stars_debate import EnhancedFourStarsDebate
        debate_system3 = EnhancedFourStarsDebate()
        print("✅ 增强四星辩论系统（天权星版） - 导入成功")
        
        # 测试辩论
        result = await debate_system3.start_comprehensive_debate(
            target_stock="000001.XSHE",
            initial_analysis={"test": True}
        )
        print(f"   辩论结果: {result.get('success', False)}")
        systems_tested.append("enhanced_four_stars_debate_tianquan")
        
    except Exception as e:
        print(f"❌ 增强四星辩论系统（天权星版）测试失败: {e}")
    
    # 4. 测试核心增强四星辩论系统
    try:
        from core.enhanced_four_stars_debate import EnhancedFourStarsDebate as CoreEnhancedDebate
        debate_system4 = CoreEnhancedDebate()
        print("✅ 核心增强四星辩论系统 - 导入成功")
        systems_tested.append("enhanced_four_stars_debate_core")
        
    except Exception as e:
        print(f"❌ 核心增强四星辩论系统测试失败: {e}")
    
    return systems_tested

async def test_seven_star_debate_systems():
    """测试七星辩论系统"""
    print("\n🌟 测试七星辩论系统...")
    
    systems_tested = []
    
    # 1. 测试七星聊天辩论系统
    try:
        from api.seven_stars_chat_debate import SevenStarsChatDebate
        chat_debate = SevenStarsChatDebate()
        print("✅ 七星聊天辩论系统 - 导入成功")
        systems_tested.append("seven_stars_chat_debate")
        
    except Exception as e:
        print(f"❌ 七星聊天辩论系统测试失败: {e}")
    
    # 2. 测试智能体辩论系统的七星功能
    try:
        from backend.core.intelligent_agent_debate_system import IntelligentAgentDebateSystem
        debate_system = IntelligentAgentDebateSystem()
        
        # 测试七星辩论
        session_id = await debate_system.start_seven_star_debate(
            topic="市场整体分析",
            context={"test": True}
        )
        print(f"✅ 七星辩论功能 - 会话ID: {session_id}")
        systems_tested.append("seven_star_debate_function")
        
    except Exception as e:
        print(f"❌ 七星辩论功能测试失败: {e}")
    
    return systems_tested

async def analyze_debate_systems():
    """分析辩论系统"""
    print("\n📊 辩论系统分析报告")
    print("=" * 50)
    
    four_star_systems = await test_four_star_debate_systems()
    seven_star_systems = await test_seven_star_debate_systems()
    
    print(f"\n📈 四星辩论系统数量: {len(four_star_systems)}")
    for i, system in enumerate(four_star_systems, 1):
        print(f"   {i}. {system}")
    
    print(f"\n🌟 七星辩论系统数量: {len(seven_star_systems)}")
    for i, system in enumerate(seven_star_systems, 1):
        print(f"   {i}. {system}")
    
    print(f"\n📊 总计辩论系统: {len(four_star_systems) + len(seven_star_systems)}")
    
    # 分析重复性
    if len(four_star_systems) > 1:
        print("\n⚠️  发现多个四星辩论系统，可能存在重复")
    
    if len(seven_star_systems) > 1:
        print("⚠️  发现多个七星辩论系统，可能存在重复")

if __name__ == "__main__":
    asyncio.run(analyze_debate_systems())
