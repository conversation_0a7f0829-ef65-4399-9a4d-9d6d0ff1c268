#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试七个智能体的个体功能
"""

import asyncio
import sys
import os

# 添加backend路径
backend_path = os.path.join(os.getcwd(), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

async def test_yaoguang_star():
    """测试瑶光星"""
    print("🌟 测试瑶光星 (Yaoguang Star)")
    print("=" * 50)
    
    try:
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        print("✅ 瑶光星导入成功")
        
        # 测试基本功能
        print("🔍 测试瑶光星基本功能...")
        
        # 测试学习自动化 - 使用正确的参数
        result = await unified_yaoguang_system.start_learning_session({
            "learning_mode": "practice",
            "target_stock": "000001.XSHE",
            "session_type": "practice"
        })
        
        print(f"📊 学习自动化结果: {result.get('success', False)}")
        if result.get('success'):
            print(f"   阶段: {result.get('current_stage', 'N/A')}")
        else:
            print(f"   错误: {result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 瑶光星测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tianquan_star():
    """测试天权星"""
    print("\n🌟 测试天权星 (Tianquan Star)")
    print("=" * 50)
    
    try:
        from roles.tianquan_star.core.tianquan_automation_system import tianquan_automation_system

        print("✅ 天权星导入成功")

        # 测试战略决策
        print("🔍 测试天权星决策自动化...")

        # 使用正确的战略决策服务
        from roles.tianquan_star.services.strategic_decision_service import strategic_decision_service

        result = await strategic_decision_service.decide_trading_strategy(
            stock_code="000001.XSHE",
            market_context={"trend": "neutral"},
            risk_preference="moderate"
        )
        
        print(f"📊 战略决策结果: {result.get('success', False)}")
        if result.get('success'):
            print(f"   决策: {result.get('decision', 'N/A')}")
        else:
            print(f"   错误: {result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 天权星测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tianshu_star():
    """测试天枢星"""
    print("\n🌟 测试天枢星 (Tianshu Star)")
    print("=" * 50)
    
    try:
        from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system

        print("✅ 天枢星导入成功")

        # 测试市场分析
        print("🔍 测试天枢星市场分析...")

        result = await tianshu_automation_system.execute_market_analysis({
            "stock_code": "000001.XSHE",
            "task_type": "market_info_collection",
            "analysis_depth": "comprehensive"
        })
        
        print(f"📊 市场分析结果: {result.get('success', False)}")
        if result.get('success'):
            print(f"   分析类型: {result.get('analysis_type', 'N/A')}")
        else:
            print(f"   错误: {result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 天枢星测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tianxuan_star():
    """测试天璇星"""
    print("\n🌟 测试天璇星 (Tianxuan Star)")
    print("=" * 50)
    
    try:
        from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system

        print("✅ 天璇星导入成功")

        # 测试技术分析
        print("🔍 测试天璇星技术分析...")

        result = await tianxuan_automation_system.execute_technical_analysis({
            "stock_code": "000001.XSHE",
            "task_type": "comprehensive_technical_analysis",
            "analysis_period": "daily"
        })
        
        print(f"📊 技术分析结果: {result.get('success', False)}")
        if result.get('success'):
            print(f"   分析周期: {result.get('analysis_period', 'N/A')}")
        else:
            print(f"   错误: {result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 天璇星测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tianji_star():
    """测试天玑星"""
    print("\n🌟 测试天玑星 (Tianji Star)")
    print("=" * 50)
    
    try:
        from roles.tianji_star.services.tianji_automation_system import tianji_automation_system

        print("✅ 天玑星导入成功")

        # 测试风险分析
        print("🔍 测试天玑星风险分析...")

        result = await tianji_automation_system.execute_risk_analysis({
            "stock_code": "000001.XSHE",
            "task_type": "comprehensive_risk_analysis",
            "risk_type": "comprehensive"
        })
        
        print(f"📊 风险分析结果: {result.get('success', False)}")
        if result.get('success'):
            print(f"   风险类型: {result.get('risk_type', 'N/A')}")
        else:
            print(f"   错误: {result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 天玑星测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_yuheng_star():
    """测试玉衡星"""
    print("\n🌟 测试玉衡星 (Yuheng Star)")
    print("=" * 50)
    
    try:
        from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system

        print("✅ 玉衡星导入成功")

        # 测试交易自动化
        print("🔍 测试玉衡星交易自动化...")

        result = await yuheng_automation_system.execute_trading_automation({
            "stock_code": "000001.XSHE",
            "task_type": "learning_trading",
            "mode": "learning",
            "trading_decision": {"action": "analyze", "quantity": 100}
        })
        
        print(f"📊 交易自动化结果: {result.get('success', False)}")
        if result.get('success'):
            print(f"   操作: {result.get('action', 'N/A')}")
        else:
            print(f"   错误: {result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 玉衡星测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_kaiyang_star():
    """测试开阳星"""
    print("\n🌟 测试开阳星 (Kaiyang Star)")
    print("=" * 50)
    
    try:
        from roles.kaiyang_star.services.stock_selection_service import stock_selection_service

        print("✅ 开阳星导入成功")

        # 测试股票选择
        print("🔍 测试开阳星股票选择...")

        result = await stock_selection_service.execute_intelligent_selection({
            "selection_criteria": {"market_cap": "large", "sector": "finance"},
            "max_stocks": 5,
            "selection_mode": "intelligent"
        })
        
        print(f"📊 股票选择结果: {result.get('success', False)}")
        if result.get('success'):
            selected_stocks = result.get('selected_stocks', [])
            print(f"   选择股票数量: {len(selected_stocks)}")
            if selected_stocks:
                print(f"   首选股票: {selected_stocks[0] if selected_stocks else 'N/A'}")
        else:
            print(f"   错误: {result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 开阳星测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 七个智能体个体功能测试")
    print("=" * 80)
    
    # 测试所有智能体
    test_results = {}
    
    test_results['yaoguang'] = await test_yaoguang_star()
    test_results['tianquan'] = await test_tianquan_star()
    test_results['tianshu'] = await test_tianshu_star()
    test_results['tianxuan'] = await test_tianxuan_star()
    test_results['tianji'] = await test_tianji_star()
    test_results['yuheng'] = await test_yuheng_star()
    test_results['kaiyang'] = await test_kaiyang_star()
    
    # 总结测试结果
    print(f"\n📋 测试总结")
    print("=" * 80)
    
    passed_count = sum(1 for result in test_results.values() if result)
    total_count = len(test_results)
    
    for star_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {star_name.capitalize()} Star")
    
    print(f"\n🎯 总体结果: {passed_count}/{total_count} 个智能体测试通过")
    
    if passed_count == total_count:
        print("🎉 所有智能体功能正常！可以进行辩论系统测试")
    else:
        print("⚠️ 部分智能体需要修复，建议先解决问题再进行辩论测试")
        
        # 列出需要修复的智能体
        failed_stars = [name for name, result in test_results.items() if not result]
        print(f"需要修复的智能体: {', '.join(failed_stars)}")

if __name__ == "__main__":
    asyncio.run(main())
