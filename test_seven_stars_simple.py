#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的七个智能体测试 - 只测试导入和基本功能
"""

import asyncio
import sys
import os

# 添加backend路径
backend_path = os.path.join(os.getcwd(), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

def test_imports():
    """测试所有智能体的导入"""
    print("🚀 测试七个智能体导入")
    print("=" * 60)
    
    import_results = {}
    
    # 1. 测试瑶光星
    try:
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        print("✅ 瑶光星 - 导入成功")
        import_results['yaoguang'] = True
    except Exception as e:
        print(f"❌ 瑶光星 - 导入失败: {e}")
        import_results['yaoguang'] = False
    
    # 2. 测试天权星
    try:
        from roles.tianquan_star.services.strategic_decision_service import strategic_decision_service
        print("✅ 天权星 - 导入成功")
        import_results['tianquan'] = True
    except Exception as e:
        print(f"❌ 天权星 - 导入失败: {e}")
        import_results['tianquan'] = False
    
    # 3. 测试天枢星
    try:
        from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system
        print("✅ 天枢星 - 导入成功")
        import_results['tianshu'] = True
    except Exception as e:
        print(f"❌ 天枢星 - 导入失败: {e}")
        import_results['tianshu'] = False
    
    # 4. 测试天璇星
    try:
        from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system
        print("✅ 天璇星 - 导入成功")
        import_results['tianxuan'] = True
    except Exception as e:
        print(f"❌ 天璇星 - 导入失败: {e}")
        import_results['tianxuan'] = False
    
    # 5. 测试天玑星
    try:
        from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
        print("✅ 天玑星 - 导入成功")
        import_results['tianji'] = True
    except Exception as e:
        print(f"❌ 天玑星 - 导入失败: {e}")
        import_results['tianji'] = False
    
    # 6. 测试玉衡星
    try:
        from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
        print("✅ 玉衡星 - 导入成功")
        import_results['yuheng'] = True
    except Exception as e:
        print(f"❌ 玉衡星 - 导入失败: {e}")
        import_results['yuheng'] = False
    
    # 7. 测试开阳星
    try:
        from roles.kaiyang_star.services.stock_selection_service import stock_selection_service
        print("✅ 开阳星 - 导入成功")
        import_results['kaiyang'] = True
    except Exception as e:
        print(f"❌ 开阳星 - 导入失败: {e}")
        import_results['kaiyang'] = False
    
    return import_results

async def test_basic_functionality():
    """测试基本功能（不涉及复杂操作）"""
    print("\n🔍 测试基本功能")
    print("=" * 60)
    
    function_results = {}
    
    # 1. 测试瑶光星基本状态
    try:
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 只测试获取状态，不启动复杂流程
        status = await unified_yaoguang_system.get_system_status()
        print(f"✅ 瑶光星 - 状态获取成功: {status.get('system_active', False)}")
        function_results['yaoguang'] = True
    except Exception as e:
        print(f"❌ 瑶光星 - 状态获取失败: {e}")
        function_results['yaoguang'] = False
    
    # 2. 测试天权星基本功能
    try:
        from roles.tianquan_star.core.tianquan_automation_system import tianquan_automation_system
        
        # 测试基本的系统状态
        print("✅ 天权星 - 自动化系统可用")
        function_results['tianquan'] = True
    except Exception as e:
        print(f"❌ 天权星 - 自动化系统测试失败: {e}")
        function_results['tianquan'] = False
    
    # 3. 测试天枢星基本功能
    try:
        from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system
        
        # 测试基本的系统状态
        print("✅ 天枢星 - 自动化系统可用")
        function_results['tianshu'] = True
    except Exception as e:
        print(f"❌ 天枢星 - 自动化系统测试失败: {e}")
        function_results['tianshu'] = False
    
    # 4. 测试天璇星基本功能
    try:
        from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system
        
        # 测试基本的系统状态
        print("✅ 天璇星 - 自动化系统可用")
        function_results['tianxuan'] = True
    except Exception as e:
        print(f"❌ 天璇星 - 自动化系统测试失败: {e}")
        function_results['tianxuan'] = False
    
    # 5. 测试天玑星基本功能
    try:
        from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
        
        # 测试基本的系统状态
        print("✅ 天玑星 - 自动化系统可用")
        function_results['tianji'] = True
    except Exception as e:
        print(f"❌ 天玑星 - 自动化系统测试失败: {e}")
        function_results['tianji'] = False
    
    # 6. 测试玉衡星基本功能
    try:
        from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
        
        # 测试基本的系统状态
        print("✅ 玉衡星 - 自动化系统可用")
        function_results['yuheng'] = True
    except Exception as e:
        print(f"❌ 玉衡星 - 自动化系统测试失败: {e}")
        function_results['yuheng'] = False
    
    # 7. 测试开阳星基本功能
    try:
        from roles.kaiyang_star.services.stock_selection_service import stock_selection_service
        
        # 测试基本的系统状态
        print("✅ 开阳星 - 选股服务可用")
        function_results['kaiyang'] = True
    except Exception as e:
        print(f"❌ 开阳星 - 选股服务测试失败: {e}")
        function_results['kaiyang'] = False
    
    return function_results

def test_debate_system_imports():
    """测试辩论系统导入"""
    print("\n🗣️ 测试辩论系统导入")
    print("=" * 60)
    
    debate_results = {}
    
    # 1. 测试四星辩论系统
    try:
        from roles.tianquan_star.services.enhanced_four_stars_debate import enhanced_four_stars_debate
        print("✅ 统一四星智能体辩论系统 - 导入成功")
        debate_results['four_star'] = True
    except Exception as e:
        print(f"❌ 统一四星智能体辩论系统 - 导入失败: {e}")
        debate_results['four_star'] = False
    
    # 2. 测试七星聊天辩论系统
    try:
        from api.seven_stars_chat_debate import seven_stars_debate_system
        print("✅ 七星聊天辩论系统 - 导入成功")
        debate_results['seven_star'] = True
    except Exception as e:
        print(f"❌ 七星聊天辩论系统 - 导入失败: {e}")
        debate_results['seven_star'] = False
    
    return debate_results

async def main():
    """主测试函数"""
    print("🚀 七个智能体简化测试")
    print("=" * 80)
    
    # 1. 测试导入
    import_results = test_imports()
    
    # 2. 测试基本功能
    function_results = await test_basic_functionality()
    
    # 3. 测试辩论系统
    debate_results = test_debate_system_imports()
    
    # 4. 总结结果
    print(f"\n📋 测试总结")
    print("=" * 80)
    
    # 导入测试结果
    import_passed = sum(1 for result in import_results.values() if result)
    import_total = len(import_results)
    print(f"📦 导入测试: {import_passed}/{import_total} 通过")
    
    for star_name, result in import_results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {star_name.capitalize()} Star")
    
    # 功能测试结果
    function_passed = sum(1 for result in function_results.values() if result)
    function_total = len(function_results)
    print(f"\n🔧 功能测试: {function_passed}/{function_total} 通过")
    
    for star_name, result in function_results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {star_name.capitalize()} Star")
    
    # 辩论系统测试结果
    debate_passed = sum(1 for result in debate_results.values() if result)
    debate_total = len(debate_results)
    print(f"\n🗣️ 辩论系统测试: {debate_passed}/{debate_total} 通过")
    
    for system_name, result in debate_results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {system_name.replace('_', ' ').title()} System")
    
    # 总体评估
    total_passed = import_passed + function_passed + debate_passed
    total_tests = import_total + function_total + debate_total
    
    print(f"\n🎯 总体结果: {total_passed}/{total_tests} 测试通过")
    
    if total_passed == total_tests:
        print("🎉 所有测试通过！系统准备就绪，可以进行辩论测试")
    elif total_passed >= total_tests * 0.8:
        print("⚠️ 大部分测试通过，可以进行有限的辩论测试")
    else:
        print("❌ 多个系统存在问题，建议先修复基础问题")
    
    return {
        "import_results": import_results,
        "function_results": function_results,
        "debate_results": debate_results,
        "total_passed": total_passed,
        "total_tests": total_tests
    }

if __name__ == "__main__":
    asyncio.run(main())
