#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一辩论系统
"""

import asyncio
import sys
import os

# 添加backend路径
backend_path = os.path.join(os.getcwd(), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

async def test_unified_four_star_debate():
    """测试统一四星智能体辩论系统"""
    print("🌟 测试统一四星智能体辩论系统")
    print("=" * 60)
    
    try:
        # 导入统一四星辩论系统
        from roles.tianquan_star.services.enhanced_four_stars_debate import enhanced_four_stars_debate
        
        print("✅ 统一四星智能体辩论系统导入成功")
        
        # 测试四星辩论
        print("\n🗣️ 启动四星智能体辩论测试...")
        
        result = await enhanced_four_stars_debate.start_four_star_debate(
            topic="000001.XSHE 平安银行投资分析",
            stock_code="000001.XSHE",
            context={
                "analysis_type": "investment_decision",
                "market_condition": "当前市场环境",
                "time_horizon": "短期投资",
                "test_mode": True
            }
        )
        
        print(f"📊 辩论结果: {result.get('success', False)}")
        
        if result.get('success'):
            final_decision = result.get('final_decision', {})
            print(f"👑 天权星最终决策: {final_decision.get('final_recommendation', 'N/A')}")
            print(f"🎯 决策信心度: {final_decision.get('final_confidence', 0):.2f}")
            print(f"📝 决策理由: {final_decision.get('decision_rationale', 'N/A')}")
            
            # 显示四星参与情况
            initial_positions = result.get('initial_positions', {})
            print(f"\n🌟 四星智能体参与情况:")
            for star, position in initial_positions.items():
                if hasattr(position, 'position'):
                    print(f"   {star}: {position.position} (信心度: {position.confidence:.2f})")
                else:
                    print(f"   {star}: {position}")
        else:
            print(f"❌ 辩论失败: {result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 统一四星智能体辩论系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_seven_star_chat_debate():
    """测试七星聊天辩论系统"""
    print("\n🌟 测试七星聊天辩论系统")
    print("=" * 60)
    
    try:
        # 导入七星聊天辩论系统
        from api.seven_stars_chat_debate import seven_stars_debate_system, ChatDebateRequest, DebateMode
        
        print("✅ 七星聊天辩论系统导入成功")
        
        # 测试辩论必要性分析
        print("\n🔍 测试辩论必要性分析...")
        
        analysis = await seven_stars_debate_system.should_trigger_debate(
            message="请分析一下000001.XSHE平安银行的投资价值，考虑技术面、基本面和风险因素",
            mentioned_roles=["tianquan", "tianji", "tianxuan", "tianshu"]
        )
        
        print(f"📊 是否需要辩论: {analysis.get('should_debate', False)}")
        print(f"🎯 建议模式: {analysis.get('suggested_mode', 'N/A')}")
        print(f"📝 分析原因: {analysis.get('reason', 'N/A')}")
        
        # 测试并行模式聊天辩论
        print("\n⚡ 测试并行模式聊天辩论...")
        
        request = ChatDebateRequest(
            message="请分析000001.XSHE平安银行的投资机会",
            mentioned_roles=["tianquan", "tianji", "tianxuan"],
            debate_mode=DebateMode.PARALLEL,
            max_rounds=1
        )
        
        chat_result = await seven_stars_debate_system.start_chat_debate(request)
        
        print(f"📊 聊天辩论结果: {chat_result.get('debate_id', 'N/A')}")
        print(f"🎯 参与角色: {chat_result.get('participants', [])}")
        
        result_data = chat_result.get('result', {})
        if result_data.get('responses'):
            print(f"💬 回复数量: {len(result_data['responses'])}")
            for response in result_data['responses'][:2]:  # 只显示前2个回复
                print(f"   {response.get('role_name', 'Unknown')}: {response.get('content', 'No content')[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 七星聊天辩论系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_integration_status():
    """测试整合状态"""
    print("\n📊 辩论系统整合状态检查")
    print("=" * 60)
    
    # 检查删除的文件
    deleted_files = [
        "backend/core/enhanced_four_stars_debate.py",
        "backend/roles/yaoguang_star/services/real_debate_system.py",
        "backend/core/intelligent_agent_debate_system.py"
    ]
    
    print("🗑️ 已删除的重复辩论系统:")
    for file_path in deleted_files:
        exists = os.path.exists(file_path)
        status = "❌ 仍存在" if exists else "✅ 已删除"
        print(f"   {status} {file_path}")
    
    # 检查保留的文件
    remaining_files = [
        "backend/roles/tianquan_star/services/enhanced_four_stars_debate.py",
        "backend/api/seven_stars_chat_debate.py"
    ]
    
    print("\n📁 保留的辩论系统:")
    for file_path in remaining_files:
        exists = os.path.exists(file_path)
        status = "✅ 存在" if exists else "❌ 缺失"
        print(f"   {status} {file_path}")
    
    print(f"\n🎯 整合结果:")
    print(f"   - 统一四星智能体辩论系统: 天玑、天璇、天枢、玉衡辩论 + 天权决策")
    print(f"   - 七星聊天辩论系统: 七个智能体辩论，用户决策")
    print(f"   - 删除重复系统: 3个")
    print(f"   - 保留核心系统: 2个")

async def main():
    """主测试函数"""
    print("🚀 统一辩论系统测试")
    print("=" * 80)
    
    # 测试整合状态
    await test_integration_status()
    
    # 测试统一四星辩论系统
    four_star_success = await test_unified_four_star_debate()
    
    # 测试七星聊天辩论系统
    seven_star_success = await test_seven_star_chat_debate()
    
    # 总结
    print(f"\n📋 测试总结")
    print("=" * 60)
    print(f"✅ 统一四星智能体辩论系统: {'通过' if four_star_success else '失败'}")
    print(f"✅ 七星聊天辩论系统: {'通过' if seven_star_success else '失败'}")
    
    if four_star_success and seven_star_success:
        print(f"\n🎉 辩论系统整合成功！")
        print(f"   - 四星智能体辩论（天玑、天璇、天枢、玉衡）+ 天权决策 ✅")
        print(f"   - 七星聊天辩论（七个智能体）+ 用户决策 ✅")
    else:
        print(f"\n⚠️ 部分系统需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
