#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仔细测试瑶光星的原始逻辑
"""

import asyncio
import sys
import os

# 添加backend路径
backend_path = os.path.join(os.getcwd(), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

async def test_yaoguang_original_system():
    """测试瑶光星原始系统"""
    print("🌟 瑶光星原始系统测试")
    print("=" * 80)
    
    try:
        # 导入瑶光星统一系统
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        print("✅ 瑶光星统一系统导入成功")
        
        # 1. 检查系统状态
        print("\n🔍 检查瑶光星系统状态:")
        
        try:
            status = await unified_yaoguang_system.get_system_status()
            print(f"   系统状态: {status}")
            
            if status.get('system_active'):
                print("   ✅ 系统处于活跃状态")
            else:
                print("   ⚠️ 系统未激活")
                
        except Exception as e:
            print(f"   ❌ 获取系统状态失败: {e}")
        
        # 2. 检查自动化引擎
        print(f"\n🔧 检查自动化引擎:")
        
        automation_engine = getattr(unified_yaoguang_system, 'automation_engine', None)
        if automation_engine is None:
            print("   ❌ automation_engine 为 None")
        else:
            print(f"   ✅ automation_engine 存在，类型: {type(automation_engine)}")
            print(f"   引擎键: {list(automation_engine.keys()) if hasattr(automation_engine, 'keys') else 'N/A'}")
        
        # 3. 检查当前会话
        print(f"\n📋 检查当前会话:")
        
        current_session = getattr(unified_yaoguang_system, 'current_session', None)
        if current_session is None:
            print("   ⚠️ 当前无活跃会话")
        else:
            print(f"   ✅ 存在活跃会话: {current_session.get('session_id', 'N/A')}")
        
        # 4. 测试简单的学习会话启动
        print(f"\n🚀 测试学习会话启动:")
        
        test_config = {
            "mode": "practice",
            "stock_codes": ["000001.XSHE"],
            "duration_days": 3,
            "test_mode": True
        }
        
        print(f"   测试配置: {test_config}")
        
        try:
            # 启动学习会话（短超时）
            session_result = await asyncio.wait_for(
                unified_yaoguang_system.start_learning_session(test_config),
                timeout=30.0  # 30秒超时
            )
            
            print(f"📊 学习会话启动: {'成功' if session_result.get('success', False) else '失败'}")
            
            if session_result.get('success'):
                session_id = session_result.get('session_id', 'N/A')
                print(f"   会话ID: {session_id}")
                
                # 检查会话是否真的创建了
                new_current_session = getattr(unified_yaoguang_system, 'current_session', None)
                if new_current_session:
                    print(f"   ✅ 会话已创建: {new_current_session.get('session_id', 'N/A')}")
                    print(f"   会话状态: {new_current_session.get('status', 'N/A')}")
                    print(f"   会话配置: {new_current_session.get('config', {})}")
                else:
                    print(f"   ❌ 会话未创建")
                
                return True
            else:
                error = session_result.get('error', 'Unknown error')
                print(f"   错误: {error}")
                return False
                
        except asyncio.TimeoutError:
            print("   ⏰ 学习会话启动超时")
            
            # 检查是否有会话被创建
            timeout_session = getattr(unified_yaoguang_system, 'current_session', None)
            if timeout_session:
                print(f"   💡 超时但会话已创建: {timeout_session.get('session_id', 'N/A')}")
                return True
            else:
                print(f"   ❌ 超时且无会话创建")
                return False
                
        except Exception as e:
            print(f"   ❌ 学习会话启动异常: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 瑶光星原始系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def analyze_yaoguang_learning_flow():
    """分析瑶光星学习流程"""
    print("\n\n🔍 分析瑶光星学习流程")
    print("=" * 80)
    
    try:
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 检查学习流程方法
        print("📋 检查学习流程方法:")
        
        learning_methods = [
            "start_learning_session",
            "_learning_phase_2_practice", 
            "_four_stars_content_collection",
            "_real_four_stars_debate",
            "_real_yuheng_learning_trading",
            "_real_tianshu_market_info_collection",
            "_real_tianji_risk_analysis",
            "_real_tianxuan_technical_analysis"
        ]
        
        for method_name in learning_methods:
            if hasattr(unified_yaoguang_system, method_name):
                print(f"   ✅ {method_name}")
            else:
                print(f"   ❌ {method_name}")
        
        # 检查四星辩论系统
        print(f"\n🗣️ 检查四星辩论系统:")
        
        try:
            from roles.tianquan_star.services.enhanced_four_stars_debate import enhanced_four_stars_debate
            print("   ✅ enhanced_four_stars_debate 导入成功")
            
            # 检查辩论系统方法
            debate_methods = [
                "start_four_star_debate",
                "conduct_comprehensive_debate",
                "_get_tianshu_detailed_position",
                "_get_tianxuan_detailed_position", 
                "_get_tianji_detailed_position",
                "_get_yuheng_detailed_position"
            ]
            
            for method_name in debate_methods:
                if hasattr(enhanced_four_stars_debate, method_name):
                    print(f"   ✅ {method_name}")
                else:
                    print(f"   ❌ {method_name}")
                    
        except Exception as e:
            print(f"   ❌ 四星辩论系统导入失败: {e}")
        
        # 分析调用流程
        print(f"\n📊 瑶光星学习流程分析:")
        print("   1. start_learning_session() - 启动学习会话")
        print("   2. _learning_phase_2_practice() - 练习阶段")
        print("   3. _four_stars_content_collection() - 四星内容收集 ← 调用四星智能体")
        print("   4. _real_four_stars_debate() - 四星辩论 ← 调用辩论系统")
        print("   5. enhanced_four_stars_debate.start_four_star_debate() - 辩论系统")
        print("   6. _get_xxx_detailed_position() - 再次调用四星智能体 ← 重复调用!")
        
        print(f"\n❌ 重复调用问题:")
        print("   步骤3: 瑶光星直接调用四星智能体收集内容")
        print("   步骤6: 辩论系统再次调用四星智能体获取立场")
        print("   结果: 每个智能体被调用2次!")
        
        return True
        
    except Exception as e:
        print(f"❌ 学习流程分析失败: {e}")
        return False

async def test_four_star_debate_system():
    """测试四星辩论系统"""
    print("\n\n🗣️ 测试四星辩论系统")
    print("=" * 80)
    
    try:
        from roles.tianquan_star.services.enhanced_four_stars_debate import enhanced_four_stars_debate
        
        print("✅ 四星辩论系统导入成功")
        
        # 测试简单的辩论启动
        print("\n🚀 测试辩论启动:")
        
        debate_context = {
            "analysis_type": "investment_decision",
            "market_condition": "测试市场环境",
            "time_horizon": "短期",
            "test_mode": True
        }
        
        try:
            # 启动辩论（短超时）
            debate_result = await asyncio.wait_for(
                enhanced_four_stars_debate.conduct_comprehensive_debate(debate_context),
                timeout=20.0  # 20秒超时
            )
            
            print(f"📊 辩论启动: {'成功' if debate_result.get('success', False) else '失败'}")
            
            if debate_result.get('success'):
                print(f"   最终决策: {debate_result.get('final_decision', {})}")
                return True
            else:
                print(f"   错误: {debate_result.get('error', 'Unknown')}")
                return False
                
        except asyncio.TimeoutError:
            print("   ⏰ 辩论启动超时")
            print("   💡 这说明辩论系统在尝试调用四星智能体")
            return True
            
        except Exception as e:
            print(f"   ❌ 辩论启动异常: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 四星辩论系统测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 瑶光星原始逻辑完整测试")
    print("=" * 100)
    
    # 1. 测试瑶光星原始系统
    yaoguang_success = await test_yaoguang_original_system()
    
    # 2. 分析学习流程
    flow_analysis_success = await analyze_yaoguang_learning_flow()
    
    # 3. 测试四星辩论系统
    debate_success = await test_four_star_debate_system()
    
    # 总结
    print(f"\n\n📋 测试总结")
    print("=" * 100)
    
    print(f"✅ 瑶光星原始系统: {'正常' if yaoguang_success else '异常'}")
    print(f"✅ 学习流程分析: {'完成' if flow_analysis_success else '失败'}")
    print(f"✅ 四星辩论系统: {'正常' if debate_success else '异常'}")
    
    if yaoguang_success and flow_analysis_success and debate_success:
        print(f"\n🎯 结论:")
        print("   ✅ 瑶光星原始系统可以正常启动")
        print("   ✅ 学习流程逻辑清晰")
        print("   ✅ 四星辩论系统可以正常工作")
        print("   ❌ 确实存在重复调用问题")
        
        print(f"\n💡 下一步:")
        print("   1. 原始系统工作正常，可以进行优化")
        print("   2. 重复调用问题确实存在，需要解决")
        print("   3. 可以考虑优化方案")
    else:
        print(f"\n⚠️ 发现问题:")
        if not yaoguang_success:
            print("   - 瑶光星原始系统有问题，需要先修复")
        if not flow_analysis_success:
            print("   - 学习流程分析失败，需要检查")
        if not debate_success:
            print("   - 四星辩论系统有问题，需要修复")

if __name__ == "__main__":
    asyncio.run(main())
