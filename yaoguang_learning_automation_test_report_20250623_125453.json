{"test_results": {"stage_1_initiation": {"error": "QuantitativeResearchAutomation.start_automation() got an unexpected keyword argument 'mode'"}, "stage_2_practice": {"kaiyang_selection": true, "practice_analysis": false}, "stage_3_research_reflection": {"error": "'QuantitativeResearchAutomation' object has no attribute 'execute_research_reflection'"}, "stage_4_factor_development": {"error": "'QuantitativeResearchAutomation' object has no attribute 'execute_factor_development'"}, "stage_5_model_training": {"error": "'QuantitativeResearchAutomation' object has no attribute 'execute_model_training'"}, "stage_6_strategy_generation": {"error": "'QuantitativeResearchAutomation' object has no attribute 'execute_strategy_generation'"}, "stage_7_backtesting": {"error": "'QuantitativeResearchAutomation' object has no attribute 'execute_backtesting'"}, "stage_8_skill_library_upload": {"error": "'QuantitativeResearchAutomation' object has no attribute 'execute_skill_library_upload'"}, "overall_workflow": {"error": "'QuantitativeResearchAutomation' object has no attribute 'execute_complete_learning_workflow'"}}, "summary": {"total_tests": 2, "passed_tests": 1, "success_rate": 50.0, "test_time": "2025-06-23T12:54:53.429272"}}