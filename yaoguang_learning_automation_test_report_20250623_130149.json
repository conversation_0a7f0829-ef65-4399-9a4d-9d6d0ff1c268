{"test_results": {"stage_1_initiation": {"startup": true, "config": false}, "stage_2_practice": {"session_execution": true, "role_participation": false, "stock_selection": false}, "stage_3_research_reflection": {"error": "'UnifiedYaoguangSystem' object has no attribute 'get_learning_monitoring_data'"}, "stage_4_factor_development": {"error": "'UnifiedYaoguangSystem' object has no attribute 'get_learning_results'"}, "stage_5_model_training": {}, "stage_6_strategy_generation": {}, "stage_7_backtesting": {}, "stage_8_skill_library_upload": {}, "overall_workflow": {"error": "'UnifiedYaoguangSystem' object has no attribute 'get_session_report'"}}, "summary": {"total_tests": 5, "passed_tests": 2, "success_rate": 40.0, "test_time": "2025-06-23T13:01:49.648808"}}