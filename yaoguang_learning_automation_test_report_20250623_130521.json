{"test_results": {"stage_1_initiation": {"startup": true, "config": false}, "stage_2_practice": {"session_execution": true, "role_participation": false, "stock_selection": false}, "stage_3_research_reflection": {"monitoring": true, "metrics": true}, "stage_4_factor_development": {"completion": true, "factors_count": 0, "strategies_count": 0, "models_count": 0, "achievements": true}, "stage_5_model_training": {}, "stage_6_strategy_generation": {}, "stage_7_backtesting": {}, "stage_8_skill_library_upload": {}, "overall_workflow": {"report_generation": true, "duration": 0.25019274999999996, "phases_completed": 1, "insights": 0, "skills": 1, "performance_score": 5.0, "execution": true}}, "summary": {"total_tests": 11, "passed_tests": 8, "success_rate": 72.72727272727273, "test_time": "2025-06-23T13:05:21.355007"}}